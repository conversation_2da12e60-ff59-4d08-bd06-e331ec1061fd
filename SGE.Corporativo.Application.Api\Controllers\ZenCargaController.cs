using Framework.CrossCutting.Log;
using Framework.Infrastructure.Web.Controls;
using Framework.Infrastructure.Web.Helpers;
using SGE.Corporativo.Application.Administrativo.DTO;
using SGE.Corporativo.Application.Administrativo.DTO.Api.ZenCarga;
using SGE.Corporativo.Application.Administrativo.Interfaces;
using SGE.Corporativo.Application.Api.Filter;
using SGE.Corporativo.Application.Interfaces.Api;
using SGE.Corporativo.Application.Interfaces.Service;
using SGE.Corporativo.Application.Services;
using SGE.Corporativo.Domain.Common.DTO;
using SGE.Corporativo.Domain.Common.Internationalization;
using SGE.Corporativo.Domain.Entities;
using SGE.Corporativo.Domain.Services;
using SGE.Publico.Application.Interfaces.Service;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security;
using System.Threading.Tasks;
using System.Web.Http;

namespace SGE.Corporativo.Application.Api.Controllers
{
    [BasicAuthenticationFilter]
    [RoutePrefix("api/ZenCarga")]
    public class ZenCargaController : BaseApiController
    {
        private readonly IZenCargaAppService zenCargaService;
        
        public ZenCargaController(IZenCargaAppService zenCargaService)
        {
            this.zenCargaService = zenCargaService;
        }

        [Route("Teste")]
        public DateTime GetTeste()
        {
            DateTime dateTime = DateTime.Now;
            return dateTime;
        }
        [HttpPost]
        [Route("empresa/inserirEmpresaViaZencarga")]
        public async Task<ICollection<EmpresaParaRetornarDTO>> InserirEmpresaViaZencarga([FromBody] InserirEmpresaRequest request)
        {
            var empresas = request.Empresas;
            var usuario = request.Usuario;

            ICollection<EmpresaParaRetornarDTO> listEmpresa = zenCargaService.GetEmpresaByNomeFantasia(empresas);

            if (listEmpresa.Count() != empresas.Count())
            {
                Empresa dadosDaPrimeiraEmpresa = zenCargaService.GetPrimeiraEmpresa();

                var nomesRetornados = listEmpresa
                                        .Select(x => x.NomeFantasia?.Trim().ToLower())
                                        .Where(x => !string.IsNullOrEmpty(x))
                                        .ToList();

                var empresasParaIncluir = empresas
                    .Where(nome => !nomesRetornados.Contains(nome.Trim().ToLower()))
                    .ToList();

                foreach (var item in empresasParaIncluir)
                {
                    EmpresaParaRetornarDTO empresaIncluida = zenCargaService.IncluirEmpresaViaZencarga(item, dadosDaPrimeiraEmpresa, usuario);
                    listEmpresa.Add(empresaIncluida);
                }
            }

            return listEmpresa;
        }

        [HttpPost]
        [Route("localizacaodoequipamento/inserirLocalizacaoDoEquipamentoViaZencarga")]
        public async Task<ICollection<LocalizacaoParaRetornarDTO>> InserirLocalizacaoDoEquipamentoViaZencarga([FromBody] InserirLocalizacaoRequest request)
        {
            var localizacoes = request.Localizacoes;
            var usuario = request.Usuario;

            var descricoes = localizacoes.Select(x => x.Descricao).ToList();
            ICollection<LocalizacaoParaRetornarDTO> listLocalizacao = zenCargaService.GetLocalizacoesPorDescricaoEEmpresaParaZenCarga(localizacoes);

            if (listLocalizacao.Count() < localizacoes.Count())
            {
                var descricoesRetornadas = listLocalizacao
                    .Select(x => x.Descricao?.Trim().ToLower())
                    .Where(x => !string.IsNullOrEmpty(x))
                    .ToList();

                var localizacoesParaIncluir = localizacoes
                    .Where(loc => !descricoesRetornadas.Contains(loc.Descricao.Trim().ToLower()))
                    .ToList();

                foreach (var item in localizacoesParaIncluir)
                {
                    LocalizacaoParaRetornarDTO localizacaoIncluida = zenCargaService.IncluirLocalizacaoDaEmpresaViaZencarga(item, usuario);
                    listLocalizacao.Add(localizacaoIncluida);
                }
            }

            return listLocalizacao;
        }

        [HttpPost]
        [Route("unidadeorganizacionaldaempresa/inserirunidadeorganizacionalDaEmpresaViaZencarga")]
        public async Task<ICollection<UnidadeOrganizacionalParaRetornarDTO>> InserirUnidadeOrganizacionalViaZencarga([FromBody] InserirUnidadeOrganizacionalRequest request)
        {
            var unidades = request.UnidadesOrganizacionais;
            var usuario = request.Usuario;

            ICollection<UnidadeOrganizacionalParaRetornarDTO> listUnidade = zenCargaService.GetUnidadesOrganizacionaisPorNomeParaZenCarga(unidades);

            if (listUnidade.Count() < unidades.Count())
            {
                var nomesRetornados = listUnidade
                    .Select(x => x.Nome?.Trim().ToLower())
                    .Where(x => !string.IsNullOrEmpty(x))
                    .ToList();

                var unidadesParaIncluir = unidades
                    .Where(uni => !nomesRetornados.Contains(uni.Nome.Trim().ToLower()))
                    .ToList();

                foreach (var item in unidadesParaIncluir)
                {
                    UnidadeOrganizacionalParaRetornarDTO unidadeIncluida = zenCargaService.IncluirUnidadeOrganizacionalViaZencarga(item, usuario);
                    listUnidade.Add(unidadeIncluida);
                }
            }

            return listUnidade;
        }

        [HttpPost]
        [Route("recursohumanodaempresa/inserirRecursoHumanoDaEmpresaViaZencarga")]
        public async Task<ICollection<RecursoHumanoParaRetornarDTO>> InserirRecursoHumanoViaZencarga([FromBody] InserirRecursoHumanoRequest request)
        {
            var recursos = request.RecursosHumanos;
            var usuario = request.Usuario;

            ICollection<RecursoHumanoParaRetornarDTO> listRecurso = zenCargaService.GetRecursosHumanosPorNomeEEmpresaParaZenCarga(recursos);

            if (listRecurso.Count() != recursos.Count())
            {
                var nomesRetornados = listRecurso
                    .Select(x => x.Nome?.Trim().ToLower())
                    .Where(x => !string.IsNullOrEmpty(x))
                    .ToList();

                var recursosParaIncluir = recursos
                    .Where(rec => !nomesRetornados.Contains(rec.Nome.Trim().ToLower()))
                    .ToList();

                foreach (var item in recursosParaIncluir)
                {
                    RecursoHumanoParaRetornarDTO recursoIncluido = zenCargaService.IncluirRecursoHumanoViaZencarga(item, usuario);
                    listRecurso.Add(recursoIncluido);
                }
            }

            return listRecurso;
        }

        [HttpPost]
        [Route("extintor/inserirExtintorViaZencarga")]
        public async Task<ICollection<ExtintorParaRetornarDTO>> InserirExtintorViaZencarga([FromBody] InserirExtintorRequest request)
        {
            var extintores = request.Extintores;
            var usuario = request.Usuario;

            ICollection<ExtintorParaRetornarDTO> listExtintor = zenCargaService.GetExtintoresParaZenCarga(extintores);

            if (listExtintor.Count() != extintores.Count())
            {
                var extintoresParaIncluir = extintores
                                          .Where(novo => !listExtintor.Any(cadastrado =>
                                              string.Equals(novo.Chassi?.Trim(), cadastrado.Chassi?.Trim(), StringComparison.OrdinalIgnoreCase) &&
                                              string.Equals(novo.TipoExtintorId?.Trim(), cadastrado.TipoExtintorId?.Trim(), StringComparison.OrdinalIgnoreCase) &&
                                              novo.Carga == cadastrado.Carga &&
                                              string.Equals(novo.CapacidadeExtintora?.Trim(), cadastrado.CapacidadeExtintora?.Trim(), StringComparison.OrdinalIgnoreCase) &&
                                              novo.FabricanteId == cadastrado.FabricanteId
                                          ))
                                          .ToList();


                return zenCargaService.IncluirExtintoresViaZencarga(extintoresParaIncluir, usuario);
            }

            return listExtintor;
        }

        
        public class ValidarLoteRequest
        {
            public int Page { get; set; } = 1;
            public int PageSize { get; set; } = 50;
            public string LoteId { get; set; }
            public List<string> Itens { get; set; }
        }
        public class InserirEmpresaRequest
        {
            public List<string> Empresas { get; set; }
            public Usuario Usuario { get; set; }
        }

        public class InserirLocalizacaoRequest
        {
            public List<SGE.Corporativo.Domain.Common.DTO.LocalizacaoParaInserirDTO> Localizacoes { get; set; }
            public Usuario Usuario { get; set; }
        }

        public class InserirUnidadeOrganizacionalRequest
        {
            public List<SGE.Corporativo.Domain.Common.DTO.UnidadeOrganizacionalParaInserirDTO> UnidadesOrganizacionais { get; set; }
            public Usuario Usuario { get; set; }
        }

        public class InserirRecursoHumanoRequest
        {
            public List<RecursoHumanoParaInserirDTO> RecursosHumanos { get; set; }
            public Usuario Usuario { get; set; }
        }
    }

    public class InserirExtintorRequest
    {
        public List<ExtintorParaInserirDTO> Extintores { get; set; }
        public Usuario Usuario { get; set; }
    }
}
