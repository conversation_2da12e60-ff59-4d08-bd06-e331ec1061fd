﻿using SGE.Corporativo.Application.Administrativo.DTO.Api.ZenCarga;
using SGE.Corporativo.Application.DTO;
using SGE.Corporativo.Domain.Common.DTO;
using SGE.Corporativo.Domain.Entities;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SGE.Corporativo.Application.Interfaces.Api
{
    public interface IZenCargaAppService
    {
        ICollection<EmpresaParaRetornarDTO> GetEmpresaByNomeFantasia(List<string> empresas);
        ICollection<EmpresaParaRetornarDTO> GetEmpresas();
        ICollection<EmpresaParaRetornarDTO> BuscarEmpresasCorrespondentes(List<string> empresas);

        //ICollection<LocalizacaoParaRetornarDTO> GetLocalizacoes();
        //ICollection<LocalizacaoParaRetornarDTO> BuscarLocalizacoesCorrespondentes(List<string> localizacoes);
        //ICollection<LocalizacaoParaRetornarDTO> GetLocalizacoesPorDescricaoParaZenCarga(List<string> descricoes);
        //LocalizacaoParaRetornarDTO IncluirLocalizacaoViaZencarga(LocalizacaoParaInserirDTO localizacao, Usuario usuario);

        //ICollection<UnidadeOrganizacionalParaRetornarDTO> GetUnidadesOrganizacionais();
        //ICollection<UnidadeOrganizacionalParaRetornarDTO> BuscarUnidadesOrganizacionaisCorrespondentes(List<string> unidadesOrganizacionais);
        ICollection<UnidadeOrganizacionalParaRetornarDTO> GetUnidadesOrganizacionaisPorNomeParaZenCarga(List<SGE.Corporativo.Domain.Common.DTO.UnidadeOrganizacionalParaInserirDTO> unidades);
        UnidadeOrganizacionalParaRetornarDTO IncluirUnidadeOrganizacionalViaZencarga(SGE.Corporativo.Domain.Common.DTO.UnidadeOrganizacionalParaInserirDTO unidade, Usuario usuario);

        ICollection<RecursoHumanoParaRetornarDTO> GetRecursosHumanos();
        ICollection<RecursoHumanoParaRetornarDTO> BuscarRecursosHumanosCorrespondentes(List<string> recursosHumanos);
        ICollection<RecursoHumanoParaRetornarDTO> GetRecursosHumanosPorNomeEEmpresaParaZenCarga(List<RecursoHumanoParaInserirDTO> recursos);
        RecursoHumanoParaRetornarDTO IncluirRecursoHumanoViaZencarga(RecursoHumanoParaInserirDTO recurso, Usuario usuario);

        Empresa GetPrimeiraEmpresa();
        EmpresaParaRetornarDTO IncluirEmpresaViaZencarga(string empresa, Empresa primeiraEmpresa, Usuario usuario);
        ICollection<LocalizacaoParaRetornarDTO> GetLocalizacoesPorDescricaoEEmpresaParaZenCarga(List<LocalizacaoParaInserirDTO> localizacoes);
        LocalizacaoParaRetornarDTO IncluirLocalizacaoDaEmpresaViaZencarga(LocalizacaoParaInserirDTO item, Usuario usuario);
        ICollection<ExtintorParaRetornarDTO> GetExtintoresParaZenCarga(List<ExtintorParaInserirDTO> extintores);
        ICollection<ExtintorParaRetornarDTO> IncluirExtintoresViaZencarga(ICollection<ExtintorParaInserirDTO> extintores, Usuario usuario);

        // Métodos assíncronos
        Task<ICollection<EmpresaParaRetornarDTO>> GetEmpresaByNomeFantasiaAsync(List<string> empresas);
        Task<Empresa> GetPrimeiraEmpresaAsync();
        Task<EmpresaParaRetornarDTO> IncluirEmpresaViaZencargaAsync(string empresa, Empresa primeiraEmpresa, Usuario usuario);
    }
}
