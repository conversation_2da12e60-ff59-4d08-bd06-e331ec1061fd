using SGE.Corporativo.Application.Administrativo.DTO.Api.ZenCarga;
using SGE.Corporativo.Domain.Interfaces.Infra;
using SGE.Corporativo.Domain.Interfaces.Services;
using System.Collections.Generic;
using Framework.CrossCutting.Mapper;
using SGE.Corporativo.Application.ViewModels;
using SGE.Corporativo.Domain.Entities;
using SGE.Corporativo.Application.Interfaces.Api;
using SGE.Corporativo.Application.Interfaces.Service;
using System.Linq;
using SGE.Corporativo.Domain.Common.DTO;
using SGE.Publico.Domain.Entities;

namespace SGE.Corporativo.Application.Services
{
    public class ZenCargaAppService : Mapping<Empresa, EmpresaParaRetornarDTO>, IZenCargaAppService
    {
        private readonly IFactoryMethod factoryMethod;
        private readonly IEmpresaService empresaService;
        private readonly IEmpresaAppService empresaAppService;
        private readonly ILocalizacaoAppService localizacaoAppService;
        private readonly IUnidadeOrganizacionalService unidadeOrganizacionalService;
        private readonly IUnidadeOrganizacionalAppService unidadeOrganizacionalAppService;
        private readonly IRecursoHumanoService recursoHumanoService;
        private readonly IRecursoHumanoAppService recursoHumanoAppService;
        private readonly ILocalizacaoAppService localizacao;
        private readonly IExtintorAppService extintorAppService;


        public ZenCargaAppService(IFactoryMethod factoryMethod,
                                 IEmpresaService empresaService,
                                 IEmpresaAppService empresaAppService,
                                 ILocalizacaoAppService localizacaoAppService,
                                 IUnidadeOrganizacionalService unidadeOrganizacionalService,
                                 IUnidadeOrganizacionalAppService unidadeOrganizacionalAppService,
                                 IRecursoHumanoService recursoHumanoService,
                                 IRecursoHumanoAppService recursoHumanoAppService,
                                 ILocalizacaoAppService localizacao,
                                 IExtintorAppService extintorAppService)
        {       
            this.factoryMethod = factoryMethod;       
            this.empresaService = empresaService;
            this.empresaAppService = empresaAppService;
            this.localizacaoAppService = localizacaoAppService;
            this.unidadeOrganizacionalService = unidadeOrganizacionalService;
            this.unidadeOrganizacionalAppService = unidadeOrganizacionalAppService;
            this.recursoHumanoService = recursoHumanoService;
            this.recursoHumanoAppService = recursoHumanoAppService;
            this.localizacao = localizacao;
            this.extintorAppService = extintorAppService;
        }

        public ICollection<EmpresaParaRetornarDTO> GetEmpresas()
        {
            var empresasParaRetornar = Map<EmpresaParaRetornarDTO>(empresaService.GetAll());
            return empresasParaRetornar;
        }

        public ICollection<EmpresaParaRetornarDTO> BuscarEmpresasCorrespondentes(List<string> empresas)
        {         
            var empresasParaRetornar = Map<EmpresaParaRetornarDTO>(empresaService.ObterPorNomesFantasia(empresas));           
            return empresasParaRetornar;
        }
        
        public ICollection<LocalizacaoParaRetornarDTO> BuscarLocalizacoesCorrespondentes(List<string> localizacoes)
        {
            //var localizacoesParaRetornar = Map<LocalizacaoParaRetornarDTO>(localizacaoService.Filter(string.Join(",", localizacoes), null, null, null, null));
            return null;
        }

        public ICollection<UnidadeOrganizacionalParaRetornarDTO> GetUnidadesOrganizacionais()
        {
            //var unidadesParaRetornar = Map<UnidadeOrganizacionalParaRetornarDTO>(unidadeOrganizacionalService.GetAllWithAllIncludes());
            return null;
        }

        public ICollection<UnidadeOrganizacionalParaRetornarDTO> BuscarUnidadesOrganizacionaisCorrespondentes(List<string> unidadesOrganizacionais)
        {
            //var unidadesParaRetornar = Map<UnidadeOrganizacionalParaRetornarDTO>(unidadeOrganizacionalService.GetFilter(string.Join(",", unidadesOrganizacionais)));
            return null;
        }

        public ICollection<RecursoHumanoParaRetornarDTO> GetRecursosHumanos()
        {
            //var recursosParaRetornar = Map<RecursoHumanoParaRetornarDTO>(recursoHumanoService.GetRecursosHumanosAtivos());
            return null;
        }

        public ICollection<RecursoHumanoParaRetornarDTO> BuscarRecursosHumanosCorrespondentes(List<string> recursosHumanos)
        {
            //var recursosParaRetornar = Map<RecursoHumanoParaRetornarDTO>(recursoHumanoService.Filter(null, string.Join(",", recursosHumanos)));
            return null;
        }

        public ICollection<EmpresaParaRetornarDTO> GetEmpresaByNomeFantasia(List<string> empresas)
        {
            var empresasParaRetornar = Map<EmpresaParaRetornarDTO>(empresaService.ObterPorNomesFantasia(empresas));
            return empresasParaRetornar;
        }
        public Empresa GetPrimeiraEmpresa()
        {
            return empresaService.GetPrimeiraEmpresa();
        }
        public EmpresaParaRetornarDTO IncluirEmpresaViaZencarga(string empresa, Empresa primeiraEmpresa, Usuario usuario)
        {
            Empresa empresaParaInserir = new Empresa();
            empresaParaInserir.RazaoSocial = empresa;
            empresaParaInserir.NomeFantasia = empresa;
            empresaParaInserir.CNPJ = primeiraEmpresa.CNPJ;
            empresaParaInserir.CidadeId = primeiraEmpresa.CidadeId;
            empresaParaInserir.Logradouro = primeiraEmpresa.Logradouro;
            empresaParaInserir.NumeroLogradouro = primeiraEmpresa.NumeroLogradouro;
            primeiraEmpresa.ComplementoLogradouro = primeiraEmpresa.ComplementoLogradouro;
            primeiraEmpresa.Bairro = primeiraEmpresa.Bairro;
            primeiraEmpresa.Cep = primeiraEmpresa.Cep;
            empresaParaInserir.AtivoCode = primeiraEmpresa.AtivoCode;
            empresaParaInserir.ParceiroId = primeiraEmpresa.ParceiroId;
            empresaParaInserir.EmailContato = primeiraEmpresa.EmailContato;
            empresaParaInserir.TelefoneContato = primeiraEmpresa.TelefoneContato;
            empresaParaInserir.CelularContato = primeiraEmpresa.CelularContato;
            empresaParaInserir.NomeContato = primeiraEmpresa.NomeContato;
            empresaParaInserir.Observacao = primeiraEmpresa.Observacao;
            empresaParaInserir.TipoDeInstalacaoCode = primeiraEmpresa.TipoDeInstalacaoCode;

           
            var empresasParaRetornar = Map<EmpresaParaRetornarDTO>(empresaService.IncluirEmpresaViaZencarga(empresaParaInserir, usuario));
            return empresasParaRetornar;
        }

  

        public RecursoHumanoParaRetornarDTO IncluirRecursoHumanoViaZencarga(RecursoHumanoParaInserirDTO recurso, Usuario usuario)
        {
            return recursoHumanoAppService.IncluirRecursoHumanoViaZencarga(recurso.Nome, recurso.EmpresaId, usuario);
        }

        public ICollection<RecursoHumanoParaRetornarDTO> GetRecursosHumanosPorNomeEEmpresaParaZenCarga(List<RecursoHumanoParaInserirDTO> recursos)
        {
            return recursoHumanoAppService.GetRecursosHumanosPorNomeEEmpresaParaZenCarga(recursos);
        }


        public ICollection<UnidadeOrganizacionalParaRetornarDTO> GetUnidadesOrganizacionaisPorNomeParaZenCarga(List<SGE.Corporativo.Domain.Common.DTO.UnidadeOrganizacionalParaInserirDTO> unidades)
        {
            return unidadeOrganizacionalAppService.GetUnidadesOrganizacionaisPorNomeEEmpresaParaZenCarga(unidades);
        }

        public UnidadeOrganizacionalParaRetornarDTO IncluirUnidadeOrganizacionalViaZencarga(SGE.Corporativo.Domain.Common.DTO.UnidadeOrganizacionalParaInserirDTO unidade, Usuario usuario)
        {
            return unidadeOrganizacionalAppService.IncluirUnidadeOrganizacionalDaEmpresaViaZencarga(unidade.Nome, unidade.Sigla, unidade.EmpresaId, usuario);
        }

        public ICollection<LocalizacaoParaRetornarDTO> GetLocalizacoesPorDescricaoEEmpresaParaZenCarga(List<LocalizacaoParaInserirDTO> localizacoes)
        {
            return localizacaoAppService.GetLocalizacoesPorDescricaoEEmpresaParaZenCarga(localizacoes);
        }

        public LocalizacaoParaRetornarDTO IncluirLocalizacaoDaEmpresaViaZencarga(LocalizacaoParaInserirDTO localizacao, Usuario usuario)
        {
            return localizacaoAppService.IncluirLocalizacaoDaEmpresaViaZencarga(localizacao.Descricao, localizacao.CodigoLocalizacao, localizacao.TipoLocalizacao, localizacao.ReservaCode ,localizacao.EmpresaId, localizacao.unidadeOrganizacionalId, usuario);
        }

        public ICollection<ExtintorParaRetornarDTO> GetExtintoresParaZenCarga(List<ExtintorParaInserirDTO> extintores)
        {
            return extintorAppService.GetExtintoresParaZenCarga(extintores);
        }
        public ICollection<ExtintorParaRetornarDTO> IncluirExtintoresViaZencarga(ICollection<ExtintorParaInserirDTO> extintores, Usuario usuario)
        {
            return extintorAppService.IncluirExtintoresViaZencarga(extintores, usuario);
        }
    }
}
