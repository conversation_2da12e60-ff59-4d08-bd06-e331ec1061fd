<?xml version="1.0" encoding="utf-8"?>
<doc>
    <assembly>
        <name>Azure.Core</name>
    </assembly>
    <members>
        <member name="T:Azure.Core.AccessToken">
            <summary>
            Represents an Azure service bearer access token with expiry information.
            </summary>
        </member>
        <member name="M:Azure.Core.AccessToken.#ctor(System.String,System.DateTimeOffset)">
            <summary>
            Creates a new instance of <see cref="T:Azure.Core.AccessToken" /> using the provided <paramref name="accessToken" /> and <paramref name="expiresOn" />.
            </summary>
            <param name="accessToken">The bearer access token value.</param>
            <param name="expiresOn">The bearer access token expiry date.</param>
        </member>
        <member name="P:Azure.Core.AccessToken.Token">
            <summary>
            Get the access token value.
            </summary>
        </member>
        <member name="P:Azure.Core.AccessToken.ExpiresOn">
            <summary>
            Gets the time when the provided token expires.
            </summary>
        </member>
        <member name="M:Azure.Core.AccessToken.Equals(System.Object)">
            <summary>Indicates whether this instance and a specified object are equal.</summary><returns>true if <paramref name="obj" /> and this instance are the same type and represent the same value; otherwise, false. </returns><param name="obj">The object to compare with the current instance. </param><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.AccessToken.GetHashCode">
            <summary>Returns the hash code for this instance.</summary><returns>A 32-bit signed integer that is the hash code for this instance.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="T:Azure.Core.AzureLocation">
            <summary>
            Represents an Azure geography region where supported resource providers live.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.EastAsia">
            <summary>
            Public cloud location for East Asia.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.SoutheastAsia">
            <summary>
            Public cloud location for Southeast Asia.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.CentralUS">
            <summary>
            Public cloud location for Central US.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.EastUS">
            <summary>
            Public cloud location for East US.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.EastUS2">
            <summary>
            Public cloud location for East US 2.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.WestUS">
            <summary>
            Public cloud location for West US.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.WestUS2">
            <summary>
            Public cloud location for West US 2.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.WestUS3">
            <summary>
            Public cloud location for West US 3.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.NorthCentralUS">
            <summary>
            Public cloud location for North Central US.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.SouthCentralUS">
            <summary>
            Public cloud location for South Central US.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.NorthEurope">
            <summary>
            Public cloud location for North Europe.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.WestEurope">
            <summary>
            Public cloud location for West Europe.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.JapanWest">
            <summary>
            Public cloud location for Japan West.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.JapanEast">
            <summary>
            Public cloud location for Japan East.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.BrazilSouth">
            <summary>
            Public cloud location for Brazil South.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.AustraliaEast">
            <summary>
            Public cloud location for Australia East.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.AustraliaSoutheast">
            <summary>
            Public cloud location for Australia Southeast.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.SouthIndia">
            <summary>
            Public cloud location for South India.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.CentralIndia">
            <summary>
            Public cloud location for Central India.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.WestIndia">
            <summary>
            Public cloud location for West India.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.CanadaCentral">
            <summary>
            Public cloud location for Canada Central.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.CanadaEast">
            <summary>
            Public cloud location for Canada East.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.UKSouth">
            <summary>
            Public cloud location for UK South.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.UKWest">
            <summary>
            Public cloud location for UK West.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.WestCentralUS">
            <summary>
            Public cloud location for West Central US.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.KoreaCentral">
            <summary>
            Public cloud location for Korea Central.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.KoreaSouth">
            <summary>
            Public cloud location for Korea South.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.FranceCentral">
            <summary>
            Public cloud location for France Central.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.FranceSouth">
            <summary>
            Public cloud location for France South.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.AustraliaCentral">
            <summary>
            Public cloud location for Australia Central.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.AustraliaCentral2">
            <summary>
            Public cloud location for Australia Central 2.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.UAECentral">
            <summary>
            Public cloud location for UAE Central.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.UAENorth">
            <summary>
            Public cloud location for UAE North.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.SouthAfricaNorth">
            <summary>
            Public cloud location for South Africa North.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.SouthAfricaWest">
            <summary>
            Public cloud location for South Africa West.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.SwedenCentral">
            <summary>
            Public cloud location for Sweden Central.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.SwitzerlandNorth">
            <summary>
            Public cloud location for Switzerland North.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.SwitzerlandWest">
            <summary>
            Public cloud location for Switzerland West.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.GermanyNorth">
            <summary>
            Public cloud location for Germany North.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.GermanyWestCentral">
            <summary>
            Public cloud location for Germany West Central.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.GermanyCentral">
            <summary>
            Public cloud location for Germany Central.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.GermanyNorthEast">
            <summary>
            Public cloud location for Germany NorthEast.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.NorwayWest">
            <summary>
            Public cloud location for Norway West.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.NorwayEast">
            <summary>
            Public cloud location for Norway East.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.BrazilSoutheast">
            <summary>
            Public cloud location for Brazil Southeast.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.ChinaNorth">
            <summary>
            Public cloud location for China North.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.ChinaEast">
            <summary>
            Public cloud location for China East.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.ChinaNorth2">
            <summary>
            Public cloud location for China North 2.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.ChinaEast2">
            <summary>
            Public cloud location for China East 2.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.QatarCentral">
            <summary>
            Public cloud location for Qatar Central.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.USDoDCentral">
            <summary>
            Public cloud location for US DoD Central.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.USDoDEast">
            <summary>
            Public cloud location for US DoD East.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.USGovArizona">
            <summary>
            Public cloud location for US Gov Arizona.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.USGovTexas">
            <summary>
            Public cloud location for US Gov Texas.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.USGovVirginia">
            <summary>
            Public cloud location for US Gov Virginia.
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.USGovIowa">
            <summary>
            Public cloud location for US Gov Iowa.
            </summary>
        </member>
        <member name="M:Azure.Core.AzureLocation.#ctor(System.String)">
            <summary> Initializes a new instance of Location. </summary>
            <param name="location"> The location name or the display name. </param>
        </member>
        <member name="M:Azure.Core.AzureLocation.#ctor(System.String,System.String)">
            <summary> Initializes a new instance of Location. </summary>
            <param name="name"> The location name. </param>
            <param name="displayName"> The display name of the location. </param>
        </member>
        <member name="P:Azure.Core.AzureLocation.Name">
            <summary>
            Gets a location name consisting of only lowercase characters without white spaces or any separation character between words, e.g. "westus".
            </summary>
        </member>
        <member name="P:Azure.Core.AzureLocation.DisplayName">
            <summary>
            Gets a location display name consisting of titlecase words or alphanumeric characters separated by whitespaces, e.g. "West US".
            </summary>
        </member>
        <member name="M:Azure.Core.AzureLocation.ToString">
            <summary>
            Gets the name of a location object.
            </summary>
            <returns> The name. </returns>
        </member>
        <member name="M:Azure.Core.AzureLocation.op_Implicit(System.String)~Azure.Core.AzureLocation">
            <summary>
            Creates a new location implicitly from a string.
            </summary>
            <param name="location"> String to be assigned in the Name form. </param>
        </member>
        <member name="M:Azure.Core.AzureLocation.Equals(Azure.Core.AzureLocation)">
            <summary>
            Detects if a location object is equal to another location instance or a string representing the location name.
            </summary>
            <param name="other"> AzureLocation object or name as a string. </param>
            <returns> True or false. </returns>
        </member>
        <member name="M:Azure.Core.AzureLocation.op_Implicit(Azure.Core.AzureLocation)~System.String">
            <summary>
            Creates a string implicitly from a AzureLocation object.
            </summary>
            <param name="location"> AzureLocation object to be assigned. </param>
        </member>
        <member name="M:Azure.Core.AzureLocation.Equals(System.Object)">
            <summary>Indicates whether this instance and a specified object are equal.</summary><returns>true if <paramref name="obj" /> and this instance are the same type and represent the same value; otherwise, false. </returns><param name="obj">The object to compare with the current instance. </param><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.AzureLocation.GetHashCode">
            <summary>Returns the hash code for this instance.</summary><returns>A 32-bit signed integer that is the hash code for this instance.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.AzureLocation.op_Equality(Azure.Core.AzureLocation,Azure.Core.AzureLocation)">
            <summary>
            Compares this <see cref="T:Azure.Core.AzureLocation" /> instance with another object and determines if they are equals.
            </summary>
            <param name="left"> The object on the left side of the operator. </param>
            <param name="right"> The object on the right side of the operator. </param>
            <returns> True if they are equal, otherwise false. </returns>
        </member>
        <member name="M:Azure.Core.AzureLocation.op_Inequality(Azure.Core.AzureLocation,Azure.Core.AzureLocation)">
            <summary>
            Compares this <see cref="T:Azure.Core.AzureLocation" /> instance with another object and determines if they are equals.
            </summary>
            <param name="left"> The object on the left side of the operator. </param>
            <param name="right"> The object on the right side of the operator. </param>
            <returns> True if they are not equal, otherwise false. </returns>
        </member>
        <member name="T:Azure.Core.ChainingClassifier">
            <summary>
            Implements chaining of classifiers for the general case where the end-of-chain
            <see cref="T:Azure.Core.ResponseClassifier" /> is not a <see cref="T:Azure.Core.StatusCodeClassifier" />.
            <see cref="T:Azure.Core.StatusCodeClassifier" /> is preferred due to its enhanced performance
            characteristics.
            The classifier chain is a series of <see cref="T:Azure.Core.ResponseClassificationHandler" /> classifiers
            followed by the "end-of-chain" <see cref="T:Azure.Core.ResponseClassifier" />.  The handlers are
            added to the chain via <see cref="T:Azure.RequestContext" />,
            and all of them are applied starting with the most recently added handler and
            iterating over the list to the least-recently added handler, then applying status code
            classification, and finally by applying the "end-of-chain" classifier.
            </summary>
        </member>
        <member name="T:Azure.Core.ClientOptions">
            <summary>
            Base type for all client option types, exposes various common client options like <see cref="P:Azure.Core.ClientOptions.Diagnostics" />, <see cref="P:Azure.Core.ClientOptions.Retry" />, <see cref="P:Azure.Core.ClientOptions.Transport" />.
            </summary>
        </member>
        <member name="P:Azure.Core.ClientOptions.Default">
            <summary>
            Gets the default set of <see cref="T:Azure.Core.ClientOptions" />. Changes to the <see cref="P:Azure.Core.ClientOptions.Default" /> options would be reflected
            in new instances of <see cref="T:Azure.Core.ClientOptions" /> type created after changes to <see cref="P:Azure.Core.ClientOptions.Default" /> were made.
            </summary>
        </member>
        <member name="M:Azure.Core.ClientOptions.#ctor">
            <summary>
            Creates a new instance of <see cref="T:Azure.Core.ClientOptions" />.
            </summary>
        </member>
        <member name="M:Azure.Core.ClientOptions.#ctor(Azure.Core.DiagnosticsOptions)">
            <summary>
            Creates a new instance of <see cref="T:Azure.Core.ClientOptions" /> with the specificed <see cref="T:Azure.Core.DiagnosticsOptions" />.
            </summary>
            <param name="diagnostics"><see cref="T:Azure.Core.DiagnosticsOptions" /> to be used for <see cref="P:Azure.Core.ClientOptions.Diagnostics" />.</param>
        </member>
        <member name="P:Azure.Core.ClientOptions.Transport">
            <summary>
            The <see cref="T:Azure.Core.Pipeline.HttpPipelineTransport" /> to be used for this client. Defaults to an instance of <see cref="T:Azure.Core.Pipeline.HttpClientTransport" />.
            </summary>
        </member>
        <member name="P:Azure.Core.ClientOptions.Diagnostics">
            <summary>
            Gets the client diagnostic options.
            </summary>
        </member>
        <member name="P:Azure.Core.ClientOptions.Retry">
            <summary>
            Gets the client retry options.
            </summary>
        </member>
        <member name="P:Azure.Core.ClientOptions.RetryPolicy">
            <summary>
            Gets or sets the policy to use for retries. If a policy is specified, it will be used in place of the <see cref="P:Azure.Core.ClientOptions.Retry" /> property.
            The <see cref="P:Azure.Core.ClientOptions.RetryPolicy" /> type can be derived from to modify the default behavior without needing to fully implement the retry logic.
            If <see cref="M:Azure.Core.Pipeline.RetryPolicy.Process(Azure.Core.HttpMessage,System.ReadOnlyMemory{Azure.Core.Pipeline.HttpPipelinePolicy})" /> is overridden or a custom <see cref="T:Azure.Core.Pipeline.HttpPipelinePolicy" /> is specified,
            it is the implementer's responsibility to update the <see cref="P:Azure.Core.HttpMessage.ProcessingContext" /> values.
            </summary>
        </member>
        <member name="M:Azure.Core.ClientOptions.AddPolicy(Azure.Core.Pipeline.HttpPipelinePolicy,Azure.Core.HttpPipelinePosition)">
            <summary>
            Adds an <see cref="T:Azure.Core.Pipeline.HttpPipeline" /> policy into the client pipeline. The position of policy in the pipeline is controlled by the <paramref name="position" /> parameter.
            If you want the policy to execute once per client request use <see cref="F:Azure.Core.HttpPipelinePosition.PerCall" /> otherwise use <see cref="F:Azure.Core.HttpPipelinePosition.PerRetry" />
            to run the policy for every retry. Note that the same instance of <paramref name="policy" /> would be added to all pipelines of client constructed using this <see cref="T:Azure.Core.ClientOptions" /> object.
            </summary>
            <param name="policy">The <see cref="T:Azure.Core.Pipeline.HttpPipelinePolicy" /> instance to be added to the pipeline.</param>
            <param name="position">The position of policy in the pipeline.</param>
        </member>
        <member name="M:Azure.Core.ClientOptions.Equals(System.Object)">
            <summary>Determines whether the specified object is equal to the current object.</summary><returns>true if the specified object  is equal to the current object; otherwise, false.</returns><param name="obj">The object to compare with the current object. </param><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.ClientOptions.GetHashCode">
            <summary>Serves as the default hash function. </summary><returns>A hash code for the current object.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.ClientOptions.ToString">
            <summary>Returns a string that represents the current object.</summary><returns>A string that represents the current object.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="T:Azure.Core.ContentType">
            <summary>
            Represents content type.
            </summary>
        </member>
        <member name="P:Azure.Core.ContentType.ApplicationJson">
            <summary>
            application/json
            </summary>
        </member>
        <member name="P:Azure.Core.ContentType.ApplicationOctetStream">
            <summary>
            application/octet-stream
            </summary>
        </member>
        <member name="P:Azure.Core.ContentType.TextPlain">
            <summary>
            text/plain
            </summary>
        </member>
        <member name="M:Azure.Core.ContentType.#ctor(System.String)">
            <summary>
            Creates an instance of <see cref="T:Azure.Core.ContentType" />.
            </summary>
            <param name="contentType">The content type string.</param>
        </member>
        <member name="M:Azure.Core.ContentType.op_Implicit(System.String)~Azure.Core.ContentType">
            <summary>
            Creates an instance of <see cref="T:Azure.Core.ContentType" />.
            </summary>
            <param name="contentType">The content type string.</param>
        </member>
        <member name="M:Azure.Core.ContentType.Equals(Azure.Core.ContentType)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary><returns>true if the current object is equal to the <paramref name="other" /> parameter; otherwise, false.</returns><param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:Azure.Core.ContentType.Equals(System.String)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary><returns>true if the current object is equal to the <paramref name="other" /> parameter; otherwise, false.</returns><param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:Azure.Core.ContentType.Equals(System.Object)">
            <summary>Indicates whether this instance and a specified object are equal.</summary><returns>true if <paramref name="obj" /> and this instance are the same type and represent the same value; otherwise, false. </returns><param name="obj">The object to compare with the current instance. </param><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.ContentType.GetHashCode">
            <summary>Returns the hash code for this instance.</summary><returns>A 32-bit signed integer that is the hash code for this instance.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.ContentType.op_Equality(Azure.Core.ContentType,Azure.Core.ContentType)">
            <summary>
            Compares equality of two <see cref="T:Azure.Core.ContentType" /> instances.
            </summary>
            <param name="left">The method to compare.</param>
            <param name="right">The method to compare against.</param>
            <returns><c>true</c> if <see cref="T:Azure.Core.ContentType" /> values are equal for <paramref name="left" /> and <paramref name="right" />, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Azure.Core.ContentType.op_Inequality(Azure.Core.ContentType,Azure.Core.ContentType)">
            <summary>
            Compares inequality of two <see cref="T:Azure.Core.ContentType" /> instances.
            </summary>
            <param name="left">The method to compare.</param>
            <param name="right">The method to compare against.</param>
            <returns><c>true</c> if <see cref="T:Azure.Core.ContentType" /> values are equal for <paramref name="left" /> and <paramref name="right" />, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Azure.Core.ContentType.ToString">
            <summary>Returns the fully qualified type name of this instance.</summary><returns>A <see cref="T:System.String" /> containing a fully qualified type name.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="T:Azure.Core.Cryptography.IKeyEncryptionKey">
            <summary>
            A key which is used to encrypt, or wrap, another key.
            </summary>
        </member>
        <member name="P:Azure.Core.Cryptography.IKeyEncryptionKey.KeyId">
            <summary>
            The Id of the key used to perform cryptographic operations for the client.
            </summary>
        </member>
        <member name="M:Azure.Core.Cryptography.IKeyEncryptionKey.WrapKey(System.String,System.ReadOnlyMemory{System.Byte},System.Threading.CancellationToken)">
            <summary>
            Encrypts the specified key using the specified algorithm.
            </summary>
            <param name="algorithm">The key wrap algorithm used to encrypt the specified key.</param>
            <param name="key">The key to be encrypted.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <returns>The encrypted key bytes.</returns>
        </member>
        <member name="M:Azure.Core.Cryptography.IKeyEncryptionKey.WrapKeyAsync(System.String,System.ReadOnlyMemory{System.Byte},System.Threading.CancellationToken)">
            <summary>
            Encrypts the specified key using the specified algorithm.
            </summary>
            <param name="algorithm">The key wrap algorithm used to encrypt the specified key.</param>
            <param name="key">The key to be encrypted.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <returns>The encrypted key bytes.</returns>
        </member>
        <member name="M:Azure.Core.Cryptography.IKeyEncryptionKey.UnwrapKey(System.String,System.ReadOnlyMemory{System.Byte},System.Threading.CancellationToken)">
            <summary>
            Decrypts the specified encrypted key using the specified algorithm.
            </summary>
            <param name="algorithm">The key wrap algorithm which was used to encrypt the specified encrypted key.</param>
            <param name="encryptedKey">The encrypted key to be decrypted.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <returns>The decrypted key bytes.</returns>
        </member>
        <member name="M:Azure.Core.Cryptography.IKeyEncryptionKey.UnwrapKeyAsync(System.String,System.ReadOnlyMemory{System.Byte},System.Threading.CancellationToken)">
            <summary>
            Decrypts the specified encrypted key using the specified algorithm.
            </summary>
            <param name="algorithm">The key wrap algorithm which was used to encrypt the specified encrypted key.</param>
            <param name="encryptedKey">The encrypted key to be decrypted.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <returns>The decrypted key bytes.</returns>
        </member>
        <member name="T:Azure.Core.Cryptography.IKeyEncryptionKeyResolver">
            <summary>
            An object capable of retrieving key encryption keys from a provided key identifier.
            </summary>
        </member>
        <member name="M:Azure.Core.Cryptography.IKeyEncryptionKeyResolver.Resolve(System.String,System.Threading.CancellationToken)">
            <summary>
            Retrieves the key encryption key corresponding to the specified keyId.
            </summary>
            <param name="keyId">The key identifier of the key encryption key to retrieve.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <returns>The key encryption key corresponding to the specified keyId.</returns>
        </member>
        <member name="M:Azure.Core.Cryptography.IKeyEncryptionKeyResolver.ResolveAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Retrieves the key encryption key corresponding to the specified keyId.
            </summary>
            <param name="keyId">The key identifier of the key encryption key to retrieve.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <returns>The key encryption key corresponding to the specified keyId.</returns>
        </member>
        <member name="T:Azure.Core.DelayStrategy">
            <summary>
            An abstraction to control delay behavior.
            </summary>
        </member>
        <member name="M:Azure.Core.DelayStrategy.#ctor(System.Nullable{System.TimeSpan},System.Double)">
            <summary>
            Constructs a new instance of <see cref="T:Azure.Core.DelayStrategy" />. This constructor can be used by derived classes to customize the jitter factor and max delay.
            </summary>
            <param name="maxDelay">The max delay value to apply on an individual delay.</param>
            <param name="jitterFactor">The jitter factor to apply to each delay. For example, if the delay is 1 second with a jitterFactor of 0.2, the actual
            delay used will be a random double between 0.8 and 1.2. If set to 0, no jitter will be applied.</param>
        </member>
        <member name="M:Azure.Core.DelayStrategy.CreateExponentialDelayStrategy(System.Nullable{System.TimeSpan},System.Nullable{System.TimeSpan})">
            <summary>
            Constructs an exponential delay with jitter.
            </summary>
            <param name="initialDelay">The initial delay to use.</param>
            <param name="maxDelay">The maximum delay to use.</param>
            <returns>The <see cref="T:Azure.Core.DelayStrategy" /> instance.</returns>
        </member>
        <member name="M:Azure.Core.DelayStrategy.CreateFixedDelayStrategy(System.Nullable{System.TimeSpan})">
            <summary>
            Constructs a fixed delay with jitter.
            </summary>
            <param name="delay">The delay to use.</param>
            <returns>The <see cref="T:Azure.Core.DelayStrategy" /> instance.</returns>
        </member>
        <member name="M:Azure.Core.DelayStrategy.GetNextDelayCore(Azure.Response,System.Int32)">
            <summary>
            Gets the next delay interval. Implement this method to provide custom delay logic.
            The Max Delay, jitter, and any Retry-After headers will be applied to the value returned from this method.
            </summary>
            <param name="response">The response, if any, returned from the service.</param>
            <param name="retryNumber">The retry number.</param>
            <returns>A <see cref="T:System.TimeSpan" /> representing the next delay interval.</returns>
        </member>
        <member name="M:Azure.Core.DelayStrategy.GetNextDelay(Azure.Response,System.Int32)">
            <summary>
            Gets the next delay interval taking into account the Max Delay, jitter, and any Retry-After headers.
            </summary>
            <param name="response">The response, if any, returned from the service.</param>
            <param name="retryNumber">The retry number.</param>
            <returns>A <see cref="T:System.TimeSpan" /> representing the next delay interval.</returns>
        </member>
        <member name="M:Azure.Core.DelayStrategy.Max(System.TimeSpan,System.TimeSpan)">
            <summary>
            Gets the maximum of two <see cref="T:System.TimeSpan" /> values.
            </summary>
            <param name="val1">The first value.</param>
            <param name="val2">The second value.</param>
            <returns>The maximum of the two <see cref="T:System.TimeSpan" /> values.</returns>
        </member>
        <member name="M:Azure.Core.DelayStrategy.Min(System.TimeSpan,System.TimeSpan)">
            <summary>
            Gets the minimum of two <see cref="T:System.TimeSpan" /> values.
            </summary>
            <param name="val1">The first value.</param>
            <param name="val2">The second value.</param>
            <returns>The minimum of the two <see cref="T:System.TimeSpan" /> values.</returns>
        </member>
        <member name="T:Azure.Core.DelegatedTokenCredential">
            <summary>
            A factory for creating a delegated <see cref="T:Azure.Core.TokenCredential" /> capable of providing an OAuth token.
            </summary>
        </member>
        <member name="M:Azure.Core.DelegatedTokenCredential.Create(System.Func{Azure.Core.TokenRequestContext,System.Threading.CancellationToken,Azure.Core.AccessToken},System.Func{Azure.Core.TokenRequestContext,System.Threading.CancellationToken,System.Threading.Tasks.ValueTask{Azure.Core.AccessToken}})">
            <summary>
            Creates a static <see cref="T:Azure.Core.TokenCredential" /> that accepts delegates which will produce an <see cref="T:Azure.Core.AccessToken" />.
            </summary>
            <remarks>
            Typically, the <see cref="T:Azure.Core.TokenCredential" /> created by this method is for use when you have already obtained an <see cref="T:Azure.Core.AccessToken" />
            from some other source and need a <see cref="T:Azure.Core.TokenCredential" /> that will simply return that token. Because the static token can expire,
            the delegates offer a mechanism to handle <see cref="T:Azure.Core.AccessToken" /> renewal.
            </remarks>
            <param name="getToken">A delegate that returns an <see cref="T:Azure.Core.AccessToken" />.</param>
            <param name="getTokenAsync">A delegate that returns a <see cref="T:System.Threading.Tasks.ValueTask" /> of type <see cref="T:Azure.Core.AccessToken" />.</param>
            <returns></returns>
        </member>
        <member name="M:Azure.Core.DelegatedTokenCredential.Create(System.Func{Azure.Core.TokenRequestContext,System.Threading.CancellationToken,Azure.Core.AccessToken})">
            <summary>
            Creates a static <see cref="T:Azure.Core.TokenCredential" /> that accepts delegates which will produce an <see cref="T:Azure.Core.AccessToken" />.
            </summary>
            <remarks>
            Typically, the <see cref="T:Azure.Core.TokenCredential" /> created by this method is for use when you have already obtained an <see cref="T:Azure.Core.AccessToken" />
            from some other source and need a <see cref="T:Azure.Core.TokenCredential" /> that will simply return that token. Because the static token can expire,
            the delegates offer a mechanism to handle <see cref="T:Azure.Core.AccessToken" /> renewal.
            </remarks>
            <param name="getToken">A delegate that returns an <see cref="T:Azure.Core.AccessToken" />.</param>
            <returns></returns>
        </member>
        <member name="T:Azure.Core.DiagnosticsOptions">
            <summary>
            Exposes client options related to logging, telemetry, and distributed tracing.
            </summary>
        </member>
        <member name="M:Azure.Core.DiagnosticsOptions.#ctor">
            <summary>
            Creates a new instance of <see cref="T:Azure.Core.DiagnosticsOptions" /> with default values.
            </summary>
        </member>
        <member name="M:Azure.Core.DiagnosticsOptions.#ctor(Azure.Core.DiagnosticsOptions)">
            <summary>
            Initializes the newly created <see cref="T:Azure.Core.DiagnosticsOptions" /> with the same settings as the specified <paramref name="diagnosticsOptions" />.
            </summary>
            <param name="diagnosticsOptions">The <see cref="T:Azure.Core.DiagnosticsOptions" /> to model the newly created instance on.</param>
        </member>
        <member name="P:Azure.Core.DiagnosticsOptions.IsLoggingEnabled">
            <summary>
            Get or sets value indicating whether HTTP pipeline logging is enabled.
            </summary>
        </member>
        <member name="P:Azure.Core.DiagnosticsOptions.IsDistributedTracingEnabled">
            <summary>
            Gets or sets value indicating whether distributed tracing activities (<see cref="T:System.Diagnostics.Activity" />) are going to be created for the clients methods calls and HTTP calls.
            </summary>
        </member>
        <member name="P:Azure.Core.DiagnosticsOptions.IsTelemetryEnabled">
            <summary>
            Gets or sets value indicating whether the "User-Agent" header containing <see cref="P:Azure.Core.DiagnosticsOptions.ApplicationId" />, client library package name and version, <see cref="P:System.Runtime.InteropServices.RuntimeInformation.FrameworkDescription" />
            and <see cref="P:System.Runtime.InteropServices.RuntimeInformation.OSDescription" /> should be sent.
            The default value can be controlled process wide by setting <c>AZURE_TELEMETRY_DISABLED</c> to <c>true</c>, <c>false</c>, <c>1</c> or <c>0</c>.
            </summary>
        </member>
        <member name="P:Azure.Core.DiagnosticsOptions.IsLoggingContentEnabled">
            <summary>
            Gets or sets value indicating if request or response content should be logged.
            </summary>
        </member>
        <member name="P:Azure.Core.DiagnosticsOptions.LoggedContentSizeLimit">
            <summary>
            Gets or sets value indicating maximum size of content to log in bytes. Defaults to 4096.
            </summary>
        </member>
        <member name="P:Azure.Core.DiagnosticsOptions.LoggedHeaderNames">
            <summary>
            Gets a list of header names that are not redacted during logging.
            </summary>
        </member>
        <member name="P:Azure.Core.DiagnosticsOptions.LoggedQueryParameters">
            <summary>
            Gets a list of query parameter names that are not redacted during logging.
            </summary>
        </member>
        <member name="P:Azure.Core.DiagnosticsOptions.ApplicationId">
            <summary>
            Gets or sets the value sent as the first part of "User-Agent" headers for all requests issues by this client. Defaults to <see cref="P:Azure.Core.DiagnosticsOptions.DefaultApplicationId" />.
            </summary>
        </member>
        <member name="P:Azure.Core.DiagnosticsOptions.DefaultApplicationId">
            <summary>
            Gets or sets the default application id. Default application id would be set on all instances.
            </summary>
        </member>
        <member name="T:Azure.Core.Diagnostics.AzureEventSourceListener">
            <summary>
            Implementation of <see cref="T:System.Diagnostics.Tracing.EventListener" /> that listens to events produced by Azure SDK client libraries.
            </summary>
        </member>
        <member name="F:Azure.Core.Diagnostics.AzureEventSourceListener.TraitName">
            <summary>
            The trait name that has to be present on all event sources collected by this listener.
            </summary>
        </member>
        <member name="F:Azure.Core.Diagnostics.AzureEventSourceListener.TraitValue">
            <summary>
            The trait value that has to be present on all event sources collected by this listener.
            </summary>
        </member>
        <member name="M:Azure.Core.Diagnostics.AzureEventSourceListener.#ctor(System.Action{System.Diagnostics.Tracing.EventWrittenEventArgs,System.String},System.Diagnostics.Tracing.EventLevel)">
            <summary>
            Creates an instance of <see cref="T:Azure.Core.Diagnostics.AzureEventSourceListener" /> that executes a <paramref name="log" /> callback every time event is written.
            </summary>
            <param name="log">The <see cref="T:System.Action`2" /> to call when event is written. The second parameter is formatted message.</param>
            <param name="level">The level of events to enable.</param>
        </member>
        <member name="M:Azure.Core.Diagnostics.AzureEventSourceListener.OnEventSourceCreated(System.Diagnostics.Tracing.EventSource)">
            <summary>Called for all existing event sources when the event listener is created and when a new event source is attached to the listener.</summary><param name="eventSource">The event source.</param>
        </member>
        <member name="M:Azure.Core.Diagnostics.AzureEventSourceListener.OnEventWritten(System.Diagnostics.Tracing.EventWrittenEventArgs)">
            <summary>Called whenever an event has been written by an event source for which the event listener has enabled events.</summary><param name="eventData">The event arguments that describe the event.</param>
        </member>
        <member name="M:Azure.Core.Diagnostics.AzureEventSourceListener.CreateConsoleLogger(System.Diagnostics.Tracing.EventLevel)">
            <summary>
            Creates a new instance of <see cref="T:Azure.Core.Diagnostics.AzureEventSourceListener" /> that forwards events to <see cref="M:System.Console.WriteLine(System.String)" />.
            </summary>
            <param name="level">The level of events to enable.</param>
        </member>
        <member name="M:Azure.Core.Diagnostics.AzureEventSourceListener.CreateTraceLogger(System.Diagnostics.Tracing.EventLevel)">
            <summary>
            Creates a new instance of <see cref="T:Azure.Core.Diagnostics.AzureEventSourceListener" /> that forwards events to <see cref="M:System.Diagnostics.Trace.WriteLine(System.Object)" />.
            </summary>
            <param name="level">The level of events to enable.</param>
        </member>
        <member name="T:Azure.Core.Serialization.DynamicData">
             <summary>
             A dynamic abstraction over content data, such as JSON.
            
             This and related types are not intended to be mocked.
             </summary>
        </member>
        <member name="T:Azure.Core.Serialization.DynamicData.ArrayEnumerator">
            <summary>
            An enumerable and enumerator for the contents of a mutable JSON array.
            </summary>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.ArrayEnumerator.GetEnumerator">
            <summary> Returns an enumerator that iterates through a collection.</summary>
            <returns> An <see cref="T:Azure.Core.Serialization.DynamicData.ArrayEnumerator" /> value that can be used to iterate through the array.</returns>
        </member>
        <member name="P:Azure.Core.Serialization.DynamicData.ArrayEnumerator.Current">
            <summary>Gets the element in the collection at the current position of the enumerator.</summary><returns>The element in the collection at the current position of the enumerator.</returns>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.ArrayEnumerator.System#Collections#IEnumerable#GetEnumerator">
            <summary>Returns an enumerator that iterates through a collection.</summary><returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.ArrayEnumerator.System#Collections#Generic#IEnumerable{Azure#Core#Serialization#DynamicData}#GetEnumerator">
            <summary>Returns an enumerator that iterates through the collection.</summary><returns>An enumerator that can be used to iterate through the collection.</returns><filterpriority>1</filterpriority>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.ArrayEnumerator.Reset">
            <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary><exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception><filterpriority>2</filterpriority>
        </member>
        <member name="P:Azure.Core.Serialization.DynamicData.ArrayEnumerator.System#Collections#IEnumerator#Current">
            <summary>Gets the current element in the collection.</summary><returns>The current element in the collection.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.ArrayEnumerator.MoveNext">
            <summary>Advances the enumerator to the next element of the collection.</summary><returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns><exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.ArrayEnumerator.Dispose">
            <summary>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</summary><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.ToString">
            <summary>Returns a string that represents the current object.</summary><returns>A string that represents the current object.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.Dispose">
            <summary>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</summary><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.Equals(System.Object)">
            <summary>Determines whether the specified object is equal to the current object.</summary><returns>true if the specified object  is equal to the current object; otherwise, false.</returns><param name="obj">The object to compare with the current object. </param><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.GetHashCode">
            <summary>Serves as the default hash function. </summary><returns>A hash code for the current object.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.System#Dynamic#IDynamicMetaObjectProvider#GetMetaObject(System.Linq.Expressions.Expression)">
            <summary>Returns the <see cref="T:System.Dynamic.DynamicMetaObject" /> responsible for binding operations performed on this object.</summary><returns>The <see cref="T:System.Dynamic.DynamicMetaObject" /> to bind this object.</returns><param name="parameter">The expression tree representation of the runtime value.</param>
        </member>
        <member name="T:Azure.Core.Serialization.DynamicData.ObjectEnumerator">
            <summary>
            An enumerable and enumerator for the properties of a mutable JSON object.
            </summary>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.ObjectEnumerator.GetEnumerator">
            <summary>
              Returns an enumerator that iterates the properties of an object.
            </summary>
            <returns>
              An <see cref="T:Azure.Core.Serialization.DynamicData.ObjectEnumerator" /> value that can be used to iterate
              through the object.
            </returns>
            <remarks>
              The enumerator will enumerate the properties in the order they are
              declared, and when an object has multiple definitions of a single
              property they will all individually be returned (each in the order
              they appear in the content).
            </remarks>
        </member>
        <member name="P:Azure.Core.Serialization.DynamicData.ObjectEnumerator.Current">
            <summary>Gets the element in the collection at the current position of the enumerator.</summary><returns>The element in the collection at the current position of the enumerator.</returns>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.ObjectEnumerator.System#Collections#IEnumerable#GetEnumerator">
            <summary>Returns an enumerator that iterates through a collection.</summary><returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.ObjectEnumerator.System#Collections#Generic#IEnumerable{Azure#Core#Serialization#DynamicDataProperty}#GetEnumerator">
            <summary>Returns an enumerator that iterates through the collection.</summary><returns>An enumerator that can be used to iterate through the collection.</returns><filterpriority>1</filterpriority>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.ObjectEnumerator.Reset">
            <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary><exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception><filterpriority>2</filterpriority>
        </member>
        <member name="P:Azure.Core.Serialization.DynamicData.ObjectEnumerator.System#Collections#IEnumerator#Current">
            <summary>Gets the current element in the collection.</summary><returns>The current element in the collection.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.ObjectEnumerator.MoveNext">
            <summary>Advances the enumerator to the next element of the collection.</summary><returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns><exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.ObjectEnumerator.Dispose">
            <summary>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</summary><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.op_Implicit(Azure.Core.Serialization.DynamicData)~System.Boolean">
            <summary>
            Converts the value to a <see cref="T:System.Boolean" />.
            </summary>
            <param name="value">The value to convert.</param>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.op_Implicit(Azure.Core.Serialization.DynamicData)~System.String">
            <summary>
            Converts the value to a <see cref="T:System.String" />.
            </summary>
            <param name="value">The value to convert.</param>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.op_Implicit(Azure.Core.Serialization.DynamicData)~System.Byte">
            <summary>
            Converts the value to a <see cref="T:System.Byte" />.
            </summary>
            <param name="value">The value to convert.</param>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.op_Implicit(Azure.Core.Serialization.DynamicData)~System.SByte">
            <summary>
            Converts the value to a <see cref="T:System.SByte" />.
            </summary>
            <param name="value">The value to convert.</param>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.op_Implicit(Azure.Core.Serialization.DynamicData)~System.Int16">
            <summary>
            Converts the value to a <see cref="T:System.Int16" />.
            </summary>
            <param name="value">The value to convert.</param>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.op_Implicit(Azure.Core.Serialization.DynamicData)~System.UInt16">
            <summary>
            Converts the value to a <see cref="T:System.UInt16" />.
            </summary>
            <param name="value">The value to convert.</param>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.op_Implicit(Azure.Core.Serialization.DynamicData)~System.Int32">
            <summary>
            Converts the value to a <see cref="T:System.Int32" />.
            </summary>
            <param name="value">The value to convert.</param>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.op_Implicit(Azure.Core.Serialization.DynamicData)~System.UInt32">
            <summary>
            Converts the value to a <see cref="T:System.UInt32" />.
            </summary>
            <param name="value">The value to convert.</param>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.op_Implicit(Azure.Core.Serialization.DynamicData)~System.Int64">
            <summary>
            Converts the value to a <see cref="T:System.Int64" />.
            </summary>
            <param name="value">The value to convert.</param>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.op_Implicit(Azure.Core.Serialization.DynamicData)~System.UInt64">
            <summary>
            Converts the value to a <see cref="T:System.UInt64" />.
            </summary>
            <param name="value">The value to convert.</param>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.op_Implicit(Azure.Core.Serialization.DynamicData)~System.Single">
            <summary>
            Converts the value to a <see cref="T:System.Single" />.
            </summary>
            <param name="value">The value to convert.</param>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.op_Implicit(Azure.Core.Serialization.DynamicData)~System.Double">
            <summary>
            Converts the value to a <see cref="T:System.Double" />.
            </summary>
            <param name="value">The value to convert.</param>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.op_Implicit(Azure.Core.Serialization.DynamicData)~System.Decimal">
            <summary>
            Converts the value to a <see cref="T:System.Decimal" />.
            </summary>
            <param name="value">The value to convert.</param>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.op_Explicit(Azure.Core.Serialization.DynamicData)~System.DateTime">
            <summary>
            Converts the value to a <see cref="T:System.DateTime" />.
            </summary>
            <param name="value">The value to convert.</param>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.op_Explicit(Azure.Core.Serialization.DynamicData)~System.DateTimeOffset">
            <summary>
            Converts the value to a <see cref="T:System.DateTimeOffset" />.
            </summary>
            <param name="value">The value to convert.</param>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.op_Explicit(Azure.Core.Serialization.DynamicData)~System.Guid">
            <summary>
            Converts the value to a <see cref="T:System.Guid" />.
            </summary>
            <param name="value">The value to convert.</param>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.op_Equality(Azure.Core.Serialization.DynamicData,System.Object)">
             <summary>
             Determines whether the specified <see cref="T:Azure.Core.Serialization.DynamicData" /> and <see cref="T:System.Object" /> have the same value.
             </summary>
             <remarks>
             This operator calls through to <see cref="M:Azure.Core.Serialization.DynamicData.Equals(System.Object)" /> when DynamicData is on the left-hand
             side of the operation.  <see cref="M:Azure.Core.Serialization.DynamicData.Equals(System.Object)" /> has value semantics when the DynamicData represents
             a JSON primitive, i.e. string, bool, number, or null, and reference semantics otherwise, i.e. for objects and arrays.
            
             Please note that if DynamicData is on the right-hand side of a <c>==</c> operation, this operator will not be invoked.
             Because of this the result of a <c>==</c> comparison with <c>null</c> on the left and a DynamicData instance on the right will return <c>false</c>.
             </remarks>
             <param name="left">The <see cref="T:Azure.Core.Serialization.DynamicData" /> to compare.</param>
             <param name="right">The <see cref="T:System.Object" /> to compare.</param>
             <returns><c>true</c> if the value of <paramref name="left" /> is the same as the value of <paramref name="right" />; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicData.op_Inequality(Azure.Core.Serialization.DynamicData,System.Object)">
            <summary>
            Determines whether the specified <see cref="T:Azure.Core.Serialization.DynamicData" /> and <see cref="T:System.Object" /> have different values.
            </summary>
            <remarks>
            This operator calls through to <see cref="M:Azure.Core.Serialization.DynamicData.Equals(System.Object)" /> when DynamicData is on the left-hand
            side of the operation.  <see cref="M:Azure.Core.Serialization.DynamicData.Equals(System.Object)" /> has value semantics when the DynamicData represents
            a JSON primitive, i.e. string, bool, number, or null, and reference semantics otherwise, i.e. for objects and arrays.
            </remarks>
            <param name="left">The <see cref="T:Azure.Core.Serialization.DynamicData" /> to compare.</param>
            <param name="right">The <see cref="T:System.Object" /> to compare.</param>
            <returns><c>true</c> if the value of <paramref name="left" /> is different from the value of <paramref name="right" />; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicDataOptions.#ctor(Azure.Core.Serialization.DynamicDataOptions)">
            <summary>
            Copy constructor
            </summary>
            <param name="options"></param>
        </member>
        <member name="T:Azure.Core.Serialization.DynamicDataProperty">
            <summary>
            Represents a single property on a dynamic JSON object.
            </summary>
        </member>
        <member name="P:Azure.Core.Serialization.DynamicDataProperty.Name">
            <summary>
            Gets the name of this property.
            </summary>
        </member>
        <member name="P:Azure.Core.Serialization.DynamicDataProperty.Value">
            <summary>
            Gets the value of this property.
            </summary>
        </member>
        <member name="M:Azure.Core.Serialization.DynamicDataProperty.System#Dynamic#IDynamicMetaObjectProvider#GetMetaObject(System.Linq.Expressions.Expression)">
            <summary>Returns the <see cref="T:System.Dynamic.DynamicMetaObject" /> responsible for binding operations performed on this object.</summary><returns>The <see cref="T:System.Dynamic.DynamicMetaObject" /> to bind this object.</returns><param name="parameter">The expression tree representation of the runtime value.</param>
        </member>
        <member name="T:Azure.Core.Serialization.IMemberNameConverter">
            <summary>
            Converts type member names to serializable member names.
            </summary>
        </member>
        <member name="M:Azure.Core.Serialization.IMemberNameConverter.ConvertMemberName(System.Reflection.MemberInfo)">
            <summary>
            Converts a <see cref="T:System.Reflection.MemberInfo" /> to a serializable member name.
            </summary>
            <param name="member">The <see cref="T:System.Reflection.MemberInfo" /> to convert to a serializable member name.</param>
            <returns>The serializable member name, or null if the member is not defined or ignored by the serializer.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="member" /> is null.</exception>
        </member>
        <member name="T:Azure.Core.Serialization.JsonObjectSerializer">
            <summary>
            An <see cref="T:Azure.Core.Serialization.ObjectSerializer" /> implementation that uses <see cref="T:System.Text.Json.JsonSerializer" /> for serialization/deserialization.
            </summary>
        </member>
        <member name="P:Azure.Core.Serialization.JsonObjectSerializer.Default">
            <summary>
            A shared instance of <see cref="T:Azure.Core.Serialization.JsonObjectSerializer" />, initialized with the default options.
            </summary>
        </member>
        <member name="M:Azure.Core.Serialization.JsonObjectSerializer.#ctor">
            <summary>
            Initializes new instance of <see cref="T:Azure.Core.Serialization.JsonObjectSerializer" />.
            </summary>
        </member>
        <member name="M:Azure.Core.Serialization.JsonObjectSerializer.#ctor(System.Text.Json.JsonSerializerOptions)">
            <summary>
            Initializes new instance of <see cref="T:Azure.Core.Serialization.JsonObjectSerializer" />.
            </summary>
            <param name="options">The <see cref="T:System.Text.Json.JsonSerializerOptions" /> instance to use when serializing/deserializing.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="options" /> is null.</exception>
        </member>
        <member name="M:Azure.Core.Serialization.JsonObjectSerializer.Serialize(System.IO.Stream,System.Object,System.Type,System.Threading.CancellationToken)">
            <summary>
            Convert the provided value to it's binary representation and write it to <see cref="T:System.IO.Stream" />.
            </summary>
            <param name="stream">The <see cref="T:System.IO.Stream" /> to write to.</param>
            <param name="value">The value to convert.</param>
            <param name="inputType">The type of the <paramref name="value" /> to convert.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> to use during serialization.</param>
        </member>
        <member name="M:Azure.Core.Serialization.JsonObjectSerializer.SerializeAsync(System.IO.Stream,System.Object,System.Type,System.Threading.CancellationToken)">
            <summary>
            Convert the provided value to it's binary representation and write it to <see cref="T:System.IO.Stream" />.
            </summary>
            <param name="stream">The <see cref="T:System.IO.Stream" /> to write to.</param>
            <param name="value">The value to convert.</param>
            <param name="inputType">The type of the <paramref name="value" /> to convert.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> to use during serialization.</param>
        </member>
        <member name="M:Azure.Core.Serialization.JsonObjectSerializer.Deserialize(System.IO.Stream,System.Type,System.Threading.CancellationToken)">
            <summary>
            Read the binary representation into a <paramref name="returnType" />.
            The Stream will be read to completion.
            </summary>
            <param name="stream">The <see cref="T:System.IO.Stream" /> to read from.</param>
            <param name="returnType">The type of the object to convert to and return.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> to use during deserialization.</param>
        </member>
        <member name="M:Azure.Core.Serialization.JsonObjectSerializer.DeserializeAsync(System.IO.Stream,System.Type,System.Threading.CancellationToken)">
            <summary>
            Read the binary representation into a <paramref name="returnType" />.
            The Stream will be read to completion.
            </summary>
            <param name="stream">The <see cref="T:System.IO.Stream" /> to read from.</param>
            <param name="returnType">The type of the object to convert to and return.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> to use during deserialization.</param>
        </member>
        <member name="M:Azure.Core.Serialization.JsonObjectSerializer.Serialize(System.Object,System.Type,System.Threading.CancellationToken)">
            <summary>
            Convert the provided value to it's binary representation and return it as a <see cref="T:System.BinaryData" /> instance.
            </summary>
            <param name="value">The value to convert.</param>
            <param name="inputType">The type to use when serializing <paramref name="value" />. If omitted, the type will be determined using <see cref="M:System.Object.GetType" />().</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> to use during serialization.</param>
            <returns>The object's binary representation as <see cref="T:System.BinaryData" />.</returns>
        </member>
        <member name="M:Azure.Core.Serialization.JsonObjectSerializer.SerializeAsync(System.Object,System.Type,System.Threading.CancellationToken)">
            <summary>
            Convert the provided value to it's binary representation and return it as a <see cref="T:System.BinaryData" /> instance.
            </summary>
            <param name="value">The value to convert.</param>
            <param name="inputType">The type to use when serializing <paramref name="value" />. If omitted, the type will be determined using <see cref="M:System.Object.GetType" />().</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> to use during serialization.</param>
            <returns>The object's binary representation as <see cref="T:System.BinaryData" />.</returns>
        </member>
        <member name="M:Azure.Core.Serialization.JsonObjectSerializer.Azure#Core#Serialization#IMemberNameConverter#ConvertMemberName(System.Reflection.MemberInfo)">
            <summary>
            Converts a <see cref="T:System.Reflection.MemberInfo" /> to a serializable member name.
            </summary>
            <param name="member">The <see cref="T:System.Reflection.MemberInfo" /> to convert to a serializable member name.</param>
            <returns>The serializable member name, or null if the member is not defined or ignored by the serializer.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="member" /> is null.</exception>
        </member>
        <member name="T:Azure.Core.Serialization.JsonPropertyNames">
            <summary>
            The format of property names in dynamic and serialized JSON content.
            </summary>
        </member>
        <member name="F:Azure.Core.Serialization.JsonPropertyNames.UseExact">
            <summary>
            Exact property name matches will be used with JSON property names.
            </summary>
        </member>
        <member name="F:Azure.Core.Serialization.JsonPropertyNames.CamelCase">
            <summary>
            Indicates that the JSON content uses a camel-case format for property names.
            </summary>
        </member>
        <member name="T:Azure.Core.Serialization.ObjectSerializer">
            <summary>
            An abstraction for reading typed objects.
            </summary>
        </member>
        <member name="M:Azure.Core.Serialization.ObjectSerializer.Serialize(System.IO.Stream,System.Object,System.Type,System.Threading.CancellationToken)">
            <summary>
            Convert the provided value to it's binary representation and write it to <see cref="T:System.IO.Stream" />.
            </summary>
            <param name="stream">The <see cref="T:System.IO.Stream" /> to write to.</param>
            <param name="value">The value to convert.</param>
            <param name="inputType">The type of the <paramref name="value" /> to convert.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> to use during serialization.</param>
        </member>
        <member name="M:Azure.Core.Serialization.ObjectSerializer.SerializeAsync(System.IO.Stream,System.Object,System.Type,System.Threading.CancellationToken)">
            <summary>
            Convert the provided value to it's binary representation and write it to <see cref="T:System.IO.Stream" />.
            </summary>
            <param name="stream">The <see cref="T:System.IO.Stream" /> to write to.</param>
            <param name="value">The value to convert.</param>
            <param name="inputType">The type of the <paramref name="value" /> to convert.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> to use during serialization.</param>
        </member>
        <member name="M:Azure.Core.Serialization.ObjectSerializer.Deserialize(System.IO.Stream,System.Type,System.Threading.CancellationToken)">
            <summary>
            Read the binary representation into a <paramref name="returnType" />.
            The Stream will be read to completion.
            </summary>
            <param name="stream">The <see cref="T:System.IO.Stream" /> to read from.</param>
            <param name="returnType">The type of the object to convert to and return.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> to use during deserialization.</param>
        </member>
        <member name="M:Azure.Core.Serialization.ObjectSerializer.DeserializeAsync(System.IO.Stream,System.Type,System.Threading.CancellationToken)">
            <summary>
            Read the binary representation into a <paramref name="returnType" />.
            The Stream will be read to completion.
            </summary>
            <param name="stream">The <see cref="T:System.IO.Stream" /> to read from.</param>
            <param name="returnType">The type of the object to convert to and return.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> to use during deserialization.</param>
        </member>
        <member name="M:Azure.Core.Serialization.ObjectSerializer.Serialize(System.Object,System.Type,System.Threading.CancellationToken)">
            <summary>
            Convert the provided value to it's binary representation and return it as a <see cref="T:System.BinaryData" /> instance.
            </summary>
            <param name="value">The value to convert.</param>
            <param name="inputType">The type to use when serializing <paramref name="value" />. If omitted, the type will be determined using <see cref="M:System.Object.GetType" />().</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> to use during serialization.</param>
            <returns>The object's binary representation as <see cref="T:System.BinaryData" />.</returns>
        </member>
        <member name="M:Azure.Core.Serialization.ObjectSerializer.SerializeAsync(System.Object,System.Type,System.Threading.CancellationToken)">
            <summary>
            Convert the provided value to it's binary representation and return it as a <see cref="T:System.BinaryData" /> instance.
            </summary>
            <param name="value">The value to convert.</param>
            <param name="inputType">The type to use when serializing <paramref name="value" />. If omitted, the type will be determined using <see cref="M:System.Object.GetType" />().</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> to use during serialization.</param>
            <returns>The object's binary representation as <see cref="T:System.BinaryData" />.</returns>
        </member>
        <member name="T:Azure.Core.Json.MutableJsonDocument">
            <summary>
            A mutable representation of a JSON value.
            </summary>
        </member>
        <member name="P:Azure.Core.Json.MutableJsonDocument.RootElement">
            <summary>
            Gets the root element of this JSON document.
            </summary>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonDocument.WriteTo(System.IO.Stream,System.Buffers.StandardFormat)">
            <summary>
            Writes the document to the provided stream as a JSON value.
            </summary>
            <param name="stream">The stream to which to write the document.</param>
            <param name="format">A format string indicating the format to use when writing the document.</param>
            <exception cref="T:System.ArgumentNullException">The <paramref name="stream" /> parameter is <see langword="null" />.</exception>
            <exception cref="T:System.FormatException">Thrown if an unsupported value is passed for format.</exception>
            <remarks>The value of <paramref name="format" /> can be default or 'J' to write the document as JSON.</remarks>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonDocument.WriteTo(System.Text.Json.Utf8JsonWriter)">
            <summary>
            Writes the document to the provided stream as a JSON value.
            </summary>
            <param name="writer">The writer to which to write the document.</param>
            <exception cref="T:System.ArgumentNullException">The <paramref name="writer" /> parameter is <see langword="null" />.</exception>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonDocument.Parse(System.ReadOnlyMemory{System.Byte},System.Text.Json.JsonSerializerOptions)">
            <summary>
            Parses a UTF-8 encoded string representing a single JSON value into a <see cref="T:Azure.Core.Json.MutableJsonDocument" />.
            </summary>
            <param name="utf8Json">A UTF-8 encoded string representing a JSON value.</param>
            <returns>A <see cref="T:Azure.Core.Json.MutableJsonDocument" /> representation of the value.</returns>
            <param name="serializerOptions">Serializer options used to serialize and deserialize any changes to the JSON.</param>
            <exception cref="T:System.Text.Json.JsonException"><paramref name="utf8Json" /> does not represent a valid single JSON value.</exception>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonDocument.Parse(System.BinaryData,System.Text.Json.JsonSerializerOptions)">
            <summary>
            Parses a UTF-8 encoded string representing a single JSON value into a <see cref="T:Azure.Core.Json.MutableJsonDocument" />.
            </summary>
            <param name="utf8Json">A UTF-8 encoded string representing a JSON value.</param>
            <param name="serializerOptions">Serializer options used to serialize and deserialize any changes to the JSON.</param>
            <returns>A <see cref="T:Azure.Core.Json.MutableJsonDocument" /> representation of the value.</returns>
            <exception cref="T:System.Text.Json.JsonException"><paramref name="utf8Json" /> does not represent a valid single JSON value.</exception>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonDocument.Parse(System.String,System.Text.Json.JsonSerializerOptions)">
            <summary>
            Parses test representing a single JSON value into a <see cref="T:Azure.Core.Json.MutableJsonDocument" />.
            </summary>
            <param name="json">The JSON string.</param>
            <param name="serializerOptions">Serializer options used to serialize and deserialize any changes to the JSON.</param>
            <returns>A <see cref="T:Azure.Core.Json.MutableJsonDocument" /> representation of the value.</returns>
            <exception cref="T:System.Text.Json.JsonException"><paramref name="json" /> does not represent a valid single JSON value.</exception>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonDocument.Dispose">
            <summary>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</summary><filterpriority>2</filterpriority>
        </member>
        <member name="T:Azure.Core.Json.MutableJsonElement">
            <summary>
            A mutable representation of a JSON element.
            </summary>
            <summary>
            A mutable representation of a JSON element.
            </summary>
        </member>
        <member name="T:Azure.Core.Json.MutableJsonElement.ArrayEnumerator">
            <summary>
            An enumerable and enumerator for the contents of a mutable JSON array.
            </summary>
        </member>
        <member name="P:Azure.Core.Json.MutableJsonElement.ArrayEnumerator.Current">
            <summary>Gets the element in the collection at the current position of the enumerator.</summary><returns>The element in the collection at the current position of the enumerator.</returns>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.ArrayEnumerator.GetEnumerator">
            <summary>
              Returns an enumerator that iterates through a collection.
            </summary>
            <returns>
              An <see cref="T:Azure.Core.Json.MutableJsonElement.ArrayEnumerator" /> value that can be used to iterate
              through the array.
            </returns>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.ArrayEnumerator.System#Collections#IEnumerable#GetEnumerator">
            <summary>Returns an enumerator that iterates through a collection.</summary><returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.ArrayEnumerator.System#Collections#Generic#IEnumerable{Azure#Core#Json#MutableJsonElement}#GetEnumerator">
            <summary>Returns an enumerator that iterates through the collection.</summary><returns>An enumerator that can be used to iterate through the collection.</returns><filterpriority>1</filterpriority>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.ArrayEnumerator.Reset">
            <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary><exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception><filterpriority>2</filterpriority>
        </member>
        <member name="P:Azure.Core.Json.MutableJsonElement.ArrayEnumerator.System#Collections#IEnumerator#Current">
            <summary>Gets the current element in the collection.</summary><returns>The current element in the collection.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.ArrayEnumerator.MoveNext">
            <summary>Advances the enumerator to the next element of the collection.</summary><returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns><exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.ArrayEnumerator.Dispose">
            <summary>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</summary><filterpriority>2</filterpriority>
        </member>
        <member name="P:Azure.Core.Json.MutableJsonElement.ValueKind">
            <summary>
            Gets the type of the current JSON value.
            </summary>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.GetProperty(System.String)">
            <summary>
            Gets the MutableJsonElement for the value of the property with the specified name.
            </summary>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.TryGetProperty(System.String,Azure.Core.Json.MutableJsonElement@)">
            <summary>
            Looks for a property named propertyName in the current object, returning a value that indicates whether or not such a property exists. When the property exists, its value is assigned to the value argument.
            </summary>
            <param name="name"></param>
            <param name="value">The value to assign to the element.</param>
            <returns></returns>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.TryGetDouble(System.Double@)">
            <summary>
            Attempts to represent the current JSON number as a <see cref="T:System.Double" />.
            </summary>
            <returns>
              <see langword="true" /> if the number can be represented as a <see cref="T:System.Double" />,
              <see langword="false" /> otherwise.
            </returns>
            <exception cref="T:System.InvalidOperationException">This value's <see cref="P:Azure.Core.Json.MutableJsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.GetDouble">
            <summary>
            Gets the current JSON number as a <see cref="T:System.Double" />.
            </summary>
            <returns>The current JSON number as a <see cref="T:System.Double" />.</returns>
            <exception cref="T:System.InvalidOperationException">This value's <see cref="P:Azure.Core.Json.MutableJsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
            <exception cref="T:System.FormatException">The value cannot be represented as a <see cref="T:System.Double" />.</exception>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.TryGetInt32(System.Int32@)">
            <summary>
            Attempts to represent the current JSON number as a <see cref="T:System.Int32" />.
            </summary>
            <returns>
              <see langword="true" /> if the number can be represented as a <see cref="T:System.Int32" />,
              <see langword="false" /> otherwise.
            </returns>
            <exception cref="T:System.InvalidOperationException">This value's <see cref="P:Azure.Core.Json.MutableJsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.GetInt32">
            <summary>
            Gets the current JSON number as a <see cref="T:System.Int32" />.
            </summary>
            <returns>The current JSON number as a <see cref="T:System.Int32" />.</returns>
            <exception cref="T:System.InvalidOperationException">This value's <see cref="P:Azure.Core.Json.MutableJsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
            <exception cref="T:System.FormatException">The value cannot be represented as a <see cref="T:System.Int32" />.</exception>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.TryGetInt64(System.Int64@)">
            <summary>
            Attempts to represent the current JSON number as a <see cref="T:System.Int64" />.
            </summary>
            <returns>
              <see langword="true" /> if the number can be represented as a <see cref="T:System.Int64" />,
              <see langword="false" /> otherwise.
            </returns>
            <exception cref="T:System.InvalidOperationException">This value's <see cref="P:Azure.Core.Json.MutableJsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.GetInt64">
            <summary>
            Gets the current JSON number as a <see cref="T:System.Int64" />.
            </summary>
            <returns>The current JSON number as a <see cref="T:System.Int64" />.</returns>
            <exception cref="T:System.InvalidOperationException">This value's <see cref="P:Azure.Core.Json.MutableJsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
            <exception cref="T:System.FormatException">The value cannot be represented as a <see cref="T:System.Int64" />.</exception>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.TryGetSingle(System.Single@)">
            <summary>
            Attempts to represent the current JSON number as a <see cref="T:System.Single" />.
            </summary>
            <returns>
              <see langword="true" /> if the number can be represented as a <see cref="T:System.Single" />,
              <see langword="false" /> otherwise.
            </returns>
            <exception cref="T:System.InvalidOperationException">This value's <see cref="P:Azure.Core.Json.MutableJsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.GetSingle">
            <summary>
            Gets the current JSON number as a <see cref="T:System.Single" />.
            </summary>
            <returns>The current JSON number as a <see cref="T:System.Single" />.</returns>
            <exception cref="T:System.InvalidOperationException">This value's <see cref="P:Azure.Core.Json.MutableJsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
            <exception cref="T:System.FormatException">The value cannot be represented as a <see cref="T:System.Single" />.</exception>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.GetString">
            <summary>
            Gets the value of the element as a string.
            </summary>
            <returns></returns>
            <exception cref="T:System.InvalidOperationException"></exception>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.GetBoolean">
            <summary>
            Gets the value of the element as a bool.
            </summary>
            <returns></returns>
            <exception cref="T:System.InvalidOperationException"></exception>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.EnumerateArray">
            <summary>
            Gets an enumerator to enumerate the values in the JSON array represented by this MutableJsonElement.
            </summary>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.EnumerateObject">
            <summary>
            Gets an enumerator to enumerate the properties in the JSON object represented by this JsonElement.
            </summary>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.SetProperty(System.String,System.Object)">
            <summary>
            Set the value of the property with the specified name to the passed-in value.  If the property is not already present, it will be created.
            </summary>
            <param name="name"></param>
            <param name="value">The value to assign to the element.</param>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.RemoveProperty(System.String)">
            <summary>
            Remove the property with the specified name from the current MutableJsonElement.
            </summary>
            <param name="name"></param>
            <exception cref="T:System.InvalidOperationException"></exception>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.Set(System.Double)">
            <summary>
            Sets the value of this element to the passed-in value.
            </summary>
            <param name="value">The value to assign to the element.</param>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.Set(System.Int32)">
            <summary>
            Sets the value of this element to the passed-in value.
            </summary>
            <param name="value">The value to assign to the element.</param>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.Set(System.Int64)">
            <summary>
            Sets the value of this element to the passed-in value.
            </summary>
            <param name="value">The value to assign to the element.</param>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.Set(System.Single)">
            <summary>
            Sets the value of this element to the passed-in value.
            </summary>
            <param name="value">The value to assign to the element.</param>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.Set(System.String)">
            <summary>
            Sets the value of this element to the passed-in value.
            </summary>
            <param name="value">The value to assign to the element.</param>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.Set(System.Boolean)">
            <summary>
            Sets the value of this element to the passed-in value.
            </summary>
            <param name="value">The value to assign to the element.</param>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.Set(System.Byte)">
            <summary>
            Sets the value of this element to the passed-in value.
            </summary>
            <param name="value">The value to assign to the element.</param>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.Set(System.SByte)">
            <summary>
            Sets the value of this element to the passed-in value.
            </summary>
            <param name="value">The value to assign to the element.</param>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.Set(System.Int16)">
            <summary>
            Sets the value of this element to the passed-in value.
            </summary>
            <param name="value">The value to assign to the element.</param>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.Set(System.UInt16)">
            <summary>
            Sets the value of this element to the passed-in value.
            </summary>
            <param name="value">The value to assign to the element.</param>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.Set(System.UInt32)">
            <summary>
            Sets the value of this element to the passed-in value.
            </summary>
            <param name="value">The value to assign to the element.</param>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.Set(System.UInt64)">
            <summary>
            Sets the value of this element to the passed-in value.
            </summary>
            <param name="value">The value to assign to the element.</param>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.Set(System.Decimal)">
            <summary>
            Sets the value of this element to the passed-in value.
            </summary>
            <param name="value">The value to assign to the element.</param>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.Set(System.Guid)">
            <summary>
            Sets the value of this element to the passed-in value.
            </summary>
            <param name="value">The value to assign to the element.</param>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.Set(System.DateTime)">
            <summary>
            Sets the value of this element to the passed-in value.
            </summary>
            <param name="value">The value to assign to the element.</param>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.Set(System.DateTimeOffset)">
            <summary>
            Sets the value of this element to the passed-in value.
            </summary>
            <param name="value">The value to assign to the element.</param>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.Set(System.Object)">
            <summary>
            Sets the value of this element to the passed-in value.
            </summary>
            <param name="value">The value to assign to the element.</param>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.ToString">
            <summary>Returns the fully qualified type name of this instance.</summary><returns>A <see cref="T:System.String" /> containing a fully qualified type name.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="T:Azure.Core.Json.MutableJsonElement.ObjectEnumerator">
            <summary>
              An enumerable and enumerator for the properties of a JSON object.
            </summary>
        </member>
        <member name="P:Azure.Core.Json.MutableJsonElement.ObjectEnumerator.Current">
            <summary>Gets the element in the collection at the current position of the enumerator.</summary><returns>The element in the collection at the current position of the enumerator.</returns>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.ObjectEnumerator.GetEnumerator">
            <summary>
              Returns an enumerator that iterates the properties of an object.
            </summary>
            <returns>
              An <see cref="T:Azure.Core.Json.MutableJsonElement.ObjectEnumerator" /> value that can be used to iterate
              through the object.
            </returns>
            <remarks>
              The enumerator will enumerate the properties in the order they are
              declared, and when an object has multiple definitions of a single
              property they will all individually be returned (each in the order
              they appear in the content).
            </remarks>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.ObjectEnumerator.System#Collections#IEnumerable#GetEnumerator">
            <summary>
              Returns an enumerator that iterates the properties of an object.
            </summary>
            <returns>
              An <see cref="T:System.Collections.IEnumerator" /> that can be used to iterate
              through the properties of the object.
            </returns>
            <remarks>
              The enumerator will enumerate the properties in the order they are
              declared, and when an object has multiple definitions of a single
              property they will all individually be returned (each in the order
              they appear in the content).
            </remarks>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.ObjectEnumerator.System#Collections#Generic#IEnumerable{(System#StringName,Azure#Core#Json#MutableJsonElementValue)}#GetEnumerator">
            <summary>
              Returns an enumerator that iterates the properties of an object.
            </summary>
            <returns>
              An <see cref="T:System.Collections.Generic.IEnumerator`1" /> over a <see cref="T:System.Tuple`2" />
              that can be used to iterate through the properties of the object.
            </returns>
            <remarks>
              The enumerator will enumerate the properties in the order they are
              declared, and when an object has multiple definitions of a single
              property they will all individually be returned (each in the order
              they appear in the content).
            </remarks>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.ObjectEnumerator.Dispose">
            <summary>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</summary><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.ObjectEnumerator.Reset">
            <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary><exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception><filterpriority>2</filterpriority>
        </member>
        <member name="P:Azure.Core.Json.MutableJsonElement.ObjectEnumerator.System#Collections#IEnumerator#Current">
            <summary>Gets the current element in the collection.</summary><returns>The current element in the collection.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.ObjectEnumerator.MoveNext">
            <summary>Advances the enumerator to the next element of the collection.</summary><returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns><exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception><filterpriority>2</filterpriority>
        </member>
        <member name="T:Azure.Core.Extensions.IAzureClientBuilder`2">
            <summary>
            Marks the type exposing client registration options for clients registered with <see cref="T:Azure.Core.Extensions.IAzureClientFactoryBuilder" />.
            </summary>
            <typeparam name="TClient">The type of the client.</typeparam>
            <typeparam name="TOptions">The options type used by the client.</typeparam>
        </member>
        <member name="T:Azure.Core.Extensions.IAzureClientFactoryBuilder">
            <summary>
            Abstraction for registering Azure clients in dependency injection containers.
            </summary>
        </member>
        <member name="M:Azure.Core.Extensions.IAzureClientFactoryBuilder.RegisterClientFactory``2(System.Func{``1,``0})">
            <summary>
            Registers a client in the dependency injection container using the factory to create a client instance.
            </summary>
            <typeparam name="TClient">The type of the client.</typeparam>
            <typeparam name="TOptions">The client options type used the client.</typeparam>
            <param name="clientFactory">The factory, that given the instance of options, returns a client instance.</param>
            <returns><see cref="T:Azure.Core.Extensions.IAzureClientBuilder`2" /> that allows customizing the client registration.</returns>
        </member>
        <member name="T:Azure.Core.Extensions.IAzureClientFactoryBuilderWithConfiguration`1">
            <summary>
            Abstraction for registering Azure clients in dependency injection containers and initializing them using <c>IConfiguration</c> objects.
            </summary>
        </member>
        <member name="M:Azure.Core.Extensions.IAzureClientFactoryBuilderWithConfiguration`1.RegisterClientFactory``2(`0)">
            <summary>
            Registers a client in the dependency injection container using the configuration to create a client instance.
            </summary>
            <typeparam name="TClient">The type of the client.</typeparam>
            <typeparam name="TOptions">The client options type used the client.</typeparam>
            <param name="configuration">Instance of <typeparamref name="TConfiguration" /> to use.</param>
            <returns><see cref="T:Azure.Core.Extensions.IAzureClientBuilder`2" /> that allows customizing the client registration.</returns>
        </member>
        <member name="T:Azure.Core.Extensions.IAzureClientFactoryBuilderWithCredential">
            <summary>
            Abstraction for registering Azure clients that require <see cref="T:Azure.Core.TokenCredential" /> in dependency injection containers.
            </summary>
        </member>
        <member name="M:Azure.Core.Extensions.IAzureClientFactoryBuilderWithCredential.RegisterClientFactory``2(System.Func{``1,Azure.Core.TokenCredential,``0},System.Boolean)">
            <summary>
            Registers a client in dependency injection container the using the factory to create a client instance.
            </summary>
            <typeparam name="TClient">The type of the client.</typeparam>
            <typeparam name="TOptions">The client options type used the client.</typeparam>
            <param name="clientFactory">The factory, that given the instance of options and credential, returns a client instance.</param>
            <param name="requiresCredential">Specifies whether the credential is optional (client supports anonymous authentication).</param>
            <returns><see cref="T:Azure.Core.Extensions.IAzureClientBuilder`2" /> that allows customizing the client registration.</returns>
        </member>
        <member name="T:Azure.Core.GeoJson.GeoArray`1">
            <summary>
            Represents a geometry coordinates array
            </summary>
            <typeparam name="T">The type of the value.</typeparam>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoArray`1.Item(System.Int32)">
            <summary>
            Returns a value at the provided index.
            </summary>
            <param name="index">The index to retrieve the value from.</param>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoArray`1.Count">
            <summary>
            Returns the size of the array.
            </summary>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoArray`1.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
            <returns>An enumerator that can be used to iterate through the collection.</returns>
        </member>
        <member name="T:Azure.Core.GeoJson.GeoArray`1.Enumerator">
            <summary>
            Enumerates the elements of a <see cref="T:Azure.Core.GeoJson.GeoArray`1" />
            </summary>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoArray`1.Enumerator.MoveNext">
            <summary>Advances the enumerator to the next element of the collection.</summary><returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns><exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoArray`1.Enumerator.Reset">
            <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary><exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception><filterpriority>2</filterpriority>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoArray`1.Enumerator.Current">
            <summary>Gets the element in the collection at the current position of the enumerator.</summary><returns>The element in the collection at the current position of the enumerator.</returns>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoArray`1.Enumerator.Dispose">
            <summary>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</summary><filterpriority>2</filterpriority>
        </member>
        <member name="T:Azure.Core.GeoJson.GeoBoundingBox">
            <summary>
            Represents information about the coordinate range of the <see cref="T:Azure.Core.GeoJson.GeoObject" />.
            </summary>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoBoundingBox.West">
            <summary>
            The westmost value of <see cref="T:Azure.Core.GeoJson.GeoObject" /> coordinates.
            </summary>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoBoundingBox.South">
            <summary>
            The southmost value of <see cref="T:Azure.Core.GeoJson.GeoObject" /> coordinates.
            </summary>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoBoundingBox.East">
            <summary>
            The eastmost value of <see cref="T:Azure.Core.GeoJson.GeoObject" /> coordinates.
            </summary>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoBoundingBox.North">
            <summary>
            The northmost value of <see cref="T:Azure.Core.GeoJson.GeoObject" /> coordinates.
            </summary>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoBoundingBox.MinAltitude">
            <summary>
            The minimum altitude value of <see cref="T:Azure.Core.GeoJson.GeoObject" /> coordinates.
            </summary>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoBoundingBox.MaxAltitude">
            <summary>
            The maximum altitude value of <see cref="T:Azure.Core.GeoJson.GeoObject" /> coordinates.
            </summary>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoBoundingBox.#ctor(System.Double,System.Double,System.Double,System.Double)">
            <summary>
            Initializes a new instance of <see cref="T:Azure.Core.GeoJson.GeoBoundingBox" />.
            </summary>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoBoundingBox.#ctor(System.Double,System.Double,System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Initializes a new instance of <see cref="T:Azure.Core.GeoJson.GeoBoundingBox" />.
            </summary>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoBoundingBox.Equals(Azure.Core.GeoJson.GeoBoundingBox)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary><returns>true if the current object is equal to the <paramref name="other" /> parameter; otherwise, false.</returns><param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoBoundingBox.Equals(System.Object)">
            <summary>Determines whether the specified object is equal to the current object.</summary><returns>true if the specified object  is equal to the current object; otherwise, false.</returns><param name="obj">The object to compare with the current object. </param><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoBoundingBox.GetHashCode">
            <summary>Serves as the default hash function. </summary><returns>A hash code for the current object.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoBoundingBox.Item(System.Int32)">
            <summary>
            Gets the component of the <see cref="T:Azure.Core.GeoJson.GeoBoundingBox" /> based on its index.
            </summary>
            <param name="index">The index of the bounding box component.</param>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoBoundingBox.ToString">
            <summary>Returns a string that represents the current object.</summary><returns>A string that represents the current object.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="T:Azure.Core.GeoJson.GeoCollection">
            <summary>
            Represents a geometry that is composed of multiple geometries.
            </summary>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoCollection.#ctor(System.Collections.Generic.IEnumerable{Azure.Core.GeoJson.GeoObject})">
            <summary>
            Initializes new instance of <see cref="T:Azure.Core.GeoJson.GeoCollection" />.
            </summary>
            <param name="geometries">The collection of inner geometries.</param>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoCollection.#ctor(System.Collections.Generic.IEnumerable{Azure.Core.GeoJson.GeoObject},Azure.Core.GeoJson.GeoBoundingBox,System.Collections.Generic.IReadOnlyDictionary{System.String,System.Object})">
            <summary>
            Initializes new instance of <see cref="T:Azure.Core.GeoJson.GeoCollection" />.
            </summary>
            <param name="geometries">The collection of inner geometries.</param>
            <param name="boundingBox">The <see cref="T:Azure.Core.GeoJson.GeoBoundingBox" /> to use.</param>
            <param name="customProperties">The set of custom properties associated with the <see cref="T:Azure.Core.GeoJson.GeoObject" />.</param>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoCollection.Geometries">
            <summary>
            Gets the list of <see cref="T:Azure.Core.GeoJson.GeoObject" /> geometry is composed of.
            </summary>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoCollection.GetEnumerator">
            <summary>Returns an enumerator that iterates through the collection.</summary><returns>An enumerator that can be used to iterate through the collection.</returns><filterpriority>1</filterpriority>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoCollection.Count">
            <summary>Gets the number of elements in the collection.</summary><returns>The number of elements in the collection. </returns>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoCollection.Item(System.Int32)">
            <summary>Gets the element at the specified index in the read-only list.</summary><returns>The element at the specified index in the read-only list.</returns><param name="index">The zero-based index of the element to get. </param>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoCollection.Type">
            <summary>
            Gets the GeoJSON type of this object.
            </summary>
        </member>
        <member name="T:Azure.Core.GeoJson.GeoJsonConverter">
            <summary>
            Converts a <see cref="T:Azure.Core.GeoJson.GeoObject" /> value from and to JSON in GeoJSON format.
            </summary>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoJsonConverter.CanConvert(System.Type)">
            <summary>Determines whether the specified type can be converted.</summary><param name="typeToConvert">The type to compare against.</param><returns><see langword="true" /> if the type can be converted; otherwise, <see langword="false" />.</returns>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoJsonConverter.Read(System.Text.Json.Utf8JsonReader@,System.Type,System.Text.Json.JsonSerializerOptions)">
            <summary>Reads and converts the JSON to type <typeparamref name="T" />.</summary><param name="reader">The reader.</param><param name="typeToConvert">The type to convert.</param><param name="options">An object that specifies serialization options to use.</param><returns>The converted value.</returns>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoJsonConverter.Write(System.Text.Json.Utf8JsonWriter,Azure.Core.GeoJson.GeoObject,System.Text.Json.JsonSerializerOptions)">
            <summary>Writes a specified value as JSON.</summary><param name="writer">The writer to write to.</param><param name="value">The value to convert to JSON.</param><param name="options">An object that specifies serialization options to use.</param>
        </member>
        <member name="T:Azure.Core.GeoJson.GeoLinearRing">
            <summary>
            Represents a linear ring that's a part of a polygon
            </summary>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoLinearRing.#ctor(System.Collections.Generic.IEnumerable{Azure.Core.GeoJson.GeoPosition})">
            <summary>
            Initializes new instance of <see cref="T:Azure.Core.GeoJson.GeoLinearRing" />.
            </summary>
            <param name="coordinates"></param>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoLinearRing.Coordinates">
            <summary>
            Returns a view over the coordinates array that forms this linear ring.
            </summary>
        </member>
        <member name="T:Azure.Core.GeoJson.GeoLineString">
            <summary>
            Represents a line geometry that consists of multiple coordinates.
            </summary>
            <example>
            Creating a line:
            <code snippet="Snippet:CreateLineString" language="csharp">
            var line = new GeoLineString(new[]
            {
                new GeoPosition(-122.108727, 47.649383),
                new GeoPosition(-122.081538, 47.640846),
                new GeoPosition(-122.078634, 47.576066),
                new GeoPosition(-122.112686, 47.578559),
            });
            </code>
            </example>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoLineString.#ctor(System.Collections.Generic.IEnumerable{Azure.Core.GeoJson.GeoPosition})">
            <summary>
            Initializes new instance of <see cref="T:Azure.Core.GeoJson.GeoLineString" />.
            </summary>
            <param name="coordinates">The collection of <see cref="T:Azure.Core.GeoJson.GeoPosition" /> that make up the line.</param>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoLineString.#ctor(System.Collections.Generic.IEnumerable{Azure.Core.GeoJson.GeoPosition},Azure.Core.GeoJson.GeoBoundingBox,System.Collections.Generic.IReadOnlyDictionary{System.String,System.Object})">
            <summary>
            Initializes new instance of <see cref="T:Azure.Core.GeoJson.GeoLineString" />.
            </summary>
            <param name="coordinates">The collection of <see cref="T:Azure.Core.GeoJson.GeoPosition" /> that make up the line.</param>
            <param name="boundingBox">The <see cref="T:Azure.Core.GeoJson.GeoBoundingBox" /> to use.</param>
            <param name="customProperties">The set of custom properties associated with the <see cref="T:Azure.Core.GeoJson.GeoObject" />.</param>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoLineString.Coordinates">
            <summary>
            Returns a view over the coordinates array that forms this geometry.
            </summary>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoLineString.Type">
            <summary>
            Gets the GeoJSON type of this object.
            </summary>
        </member>
        <member name="T:Azure.Core.GeoJson.GeoLineStringCollection">
            <summary>
            Represents a geometry that is composed of multiple <see cref="T:Azure.Core.GeoJson.GeoLineString" />.
            </summary>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoLineStringCollection.#ctor(System.Collections.Generic.IEnumerable{Azure.Core.GeoJson.GeoLineString})">
            <summary>
            Initializes new instance of <see cref="T:Azure.Core.GeoJson.GeoLineStringCollection" />.
            </summary>
            <param name="lines">The collection of inner lines.</param>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoLineStringCollection.#ctor(System.Collections.Generic.IEnumerable{Azure.Core.GeoJson.GeoLineString},Azure.Core.GeoJson.GeoBoundingBox,System.Collections.Generic.IReadOnlyDictionary{System.String,System.Object})">
            <summary>
            Initializes new instance of <see cref="T:Azure.Core.GeoJson.GeoLineStringCollection" />.
            </summary>
            <param name="lines">The collection of inner lines.</param>
            <param name="boundingBox">The <see cref="T:Azure.Core.GeoJson.GeoBoundingBox" /> to use.</param>
            <param name="customProperties">The set of custom properties associated with the <see cref="T:Azure.Core.GeoJson.GeoObject" />.</param>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoLineStringCollection.GetEnumerator">
            <summary>Returns an enumerator that iterates through the collection.</summary><returns>An enumerator that can be used to iterate through the collection.</returns><filterpriority>1</filterpriority>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoLineStringCollection.Count">
            <summary>Gets the number of elements in the collection.</summary><returns>The number of elements in the collection. </returns>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoLineStringCollection.Item(System.Int32)">
            <summary>Gets the element at the specified index in the read-only list.</summary><returns>The element at the specified index in the read-only list.</returns><param name="index">The zero-based index of the element to get. </param>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoLineStringCollection.Coordinates">
            <summary>
            Returns a view over the coordinates array that forms this geometry.
            </summary>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoLineStringCollection.Type">
            <summary>
            Gets the GeoJSON type of this object.
            </summary>
        </member>
        <member name="T:Azure.Core.GeoJson.GeoObject">
            <summary>
            A base type for all spatial types.
            </summary>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoObject.#ctor(Azure.Core.GeoJson.GeoBoundingBox,System.Collections.Generic.IReadOnlyDictionary{System.String,System.Object})">
            <summary>
            Initializes a new instance of <see cref="T:Azure.Core.GeoJson.GeoObject" />.
            </summary>
            <param name="boundingBox">The <see cref="T:Azure.Core.GeoJson.GeoBoundingBox" /> to use.</param>
            <param name="customProperties">The set of custom properties associated with the <see cref="T:Azure.Core.GeoJson.GeoObject" />.</param>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoObject.Type">
            <summary>
            Gets the GeoJSON type of this object.
            </summary>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoObject.BoundingBox">
            <summary>
            Represents information about the coordinate range of the <see cref="T:Azure.Core.GeoJson.GeoObject" />.
            </summary>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoObject.TryGetCustomProperty(System.String,System.Object@)">
            <summary>
            Tries to get a value of a custom property associated with the <see cref="T:Azure.Core.GeoJson.GeoObject" />.
            </summary>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoObject.ToString">
            <summary>
            Converts an instance of <see cref="T:Azure.Core.GeoJson.GeoObject" /> to a GeoJSON representation.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoObject.Parse(System.String)">
            <summary>
            Parses an instance of see <see cref="T:Azure.Core.GeoJson.GeoObject" /> from provided JSON representation.
            </summary>
            <param name="json">The GeoJSON representation of an object.</param>
            <returns>The resulting <see cref="T:Azure.Core.GeoJson.GeoObject" /> object.</returns>
        </member>
        <member name="T:Azure.Core.GeoJson.GeoObjectType">
            <summary>
            Identifies the type of the <see cref="T:Azure.Core.GeoJson.GeoObject" />
            </summary>
        </member>
        <member name="F:Azure.Core.GeoJson.GeoObjectType.Point">
            <summary>
            The <see cref="T:Azure.Core.GeoJson.GeoObject" /> is of the <see cref="T:Azure.Core.GeoJson.GeoPoint" /> type.
            </summary>
        </member>
        <member name="F:Azure.Core.GeoJson.GeoObjectType.MultiPoint">
            <summary>
            The <see cref="T:Azure.Core.GeoJson.GeoObject" /> is of the <see cref="T:Azure.Core.GeoJson.GeoPointCollection" /> type.
            </summary>
        </member>
        <member name="F:Azure.Core.GeoJson.GeoObjectType.Polygon">
            <summary>
            The <see cref="T:Azure.Core.GeoJson.GeoObject" /> is of the <see cref="T:Azure.Core.GeoJson.GeoPolygon" /> type.
            </summary>
        </member>
        <member name="F:Azure.Core.GeoJson.GeoObjectType.MultiPolygon">
            <summary>
            The <see cref="T:Azure.Core.GeoJson.GeoObject" /> is of the <see cref="T:Azure.Core.GeoJson.GeoPolygonCollection" /> type.
            </summary>
        </member>
        <member name="F:Azure.Core.GeoJson.GeoObjectType.LineString">
            <summary>
            The <see cref="T:Azure.Core.GeoJson.GeoObject" /> is of the <see cref="T:Azure.Core.GeoJson.GeoLineString" /> type.
            </summary>
        </member>
        <member name="F:Azure.Core.GeoJson.GeoObjectType.MultiLineString">
            <summary>
            The <see cref="T:Azure.Core.GeoJson.GeoObject" /> is of the <see cref="T:Azure.Core.GeoJson.GeoLineStringCollection" /> type.
            </summary>
        </member>
        <member name="F:Azure.Core.GeoJson.GeoObjectType.GeometryCollection">
            <summary>
            The <see cref="T:Azure.Core.GeoJson.GeoObject" /> is of the <see cref="T:Azure.Core.GeoJson.GeoCollection" /> type.
            </summary>
        </member>
        <member name="T:Azure.Core.GeoJson.GeoPoint">
            <summary>
            Represents a point geometry.
            </summary>
            <example>
            Creating a point:
            <code snippet="Snippet:CreatePoint" language="csharp">
            var point = new GeoPoint(-122.091954, 47.607148);
            </code>
            </example>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoPoint.#ctor(System.Double,System.Double)">
            <summary>
            Initializes new instance of <see cref="T:Azure.Core.GeoJson.GeoPoint" />.
            </summary>
            <param name="longitude">The longitude of the point.</param>
            <param name="latitude">The latitude of the point.</param>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoPoint.#ctor(System.Double,System.Double,System.Nullable{System.Double})">
            <summary>
            Initializes new instance of <see cref="T:Azure.Core.GeoJson.GeoPoint" />.
            </summary>
            <param name="longitude">The longitude of the point.</param>
            <param name="latitude">The latitude of the point.</param>
            <param name="altitude">The altitude of the point.</param>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoPoint.#ctor(Azure.Core.GeoJson.GeoPosition)">
            <summary>
            Initializes new instance of <see cref="T:Azure.Core.GeoJson.GeoPoint" />.
            </summary>
            <param name="position">The position of the point.</param>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoPoint.#ctor(Azure.Core.GeoJson.GeoPosition,Azure.Core.GeoJson.GeoBoundingBox,System.Collections.Generic.IReadOnlyDictionary{System.String,System.Object})">
            <summary>
            Initializes new instance of <see cref="T:Azure.Core.GeoJson.GeoPoint" />.
            </summary>
            <param name="position">The position of the point.</param>
            <param name="boundingBox">The <see cref="T:Azure.Core.GeoJson.GeoBoundingBox" /> to use.</param>
            <param name="customProperties">The set of custom properties associated with the <see cref="T:Azure.Core.GeoJson.GeoObject" />.</param>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoPoint.Coordinates">
            <summary>
            Gets position of the point.
            </summary>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoPoint.Type">
            <summary>
            Gets the GeoJSON type of this object.
            </summary>
        </member>
        <member name="T:Azure.Core.GeoJson.GeoPointCollection">
            <summary>
            Represents a geometry that is composed of multiple <see cref="T:Azure.Core.GeoJson.GeoPoint" />.
            </summary>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoPointCollection.#ctor(System.Collections.Generic.IEnumerable{Azure.Core.GeoJson.GeoPoint})">
            <summary>
            Initializes new instance of <see cref="T:Azure.Core.GeoJson.GeoPointCollection" />.
            </summary>
            <param name="points">The collection of inner points.</param>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoPointCollection.#ctor(System.Collections.Generic.IEnumerable{Azure.Core.GeoJson.GeoPoint},Azure.Core.GeoJson.GeoBoundingBox,System.Collections.Generic.IReadOnlyDictionary{System.String,System.Object})">
            <summary>
            Initializes new instance of <see cref="T:Azure.Core.GeoJson.GeoPointCollection" />.
            </summary>
            <param name="points">The collection of inner points.</param>
            <param name="boundingBox">The <see cref="T:Azure.Core.GeoJson.GeoBoundingBox" /> to use.</param>
            <param name="customProperties">The set of custom properties associated with the <see cref="T:Azure.Core.GeoJson.GeoObject" />.</param>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoPointCollection.GetEnumerator">
            <summary>Returns an enumerator that iterates through the collection.</summary><returns>An enumerator that can be used to iterate through the collection.</returns><filterpriority>1</filterpriority>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoPointCollection.System#Collections#IEnumerable#GetEnumerator">
            <summary>Returns an enumerator that iterates through a collection.</summary><returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoPointCollection.Count">
            <summary>Gets the number of elements in the collection.</summary><returns>The number of elements in the collection. </returns>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoPointCollection.Item(System.Int32)">
            <summary>Gets the element at the specified index in the read-only list.</summary><returns>The element at the specified index in the read-only list.</returns><param name="index">The zero-based index of the element to get. </param>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoPointCollection.Coordinates">
            <summary>
            Returns a view over the coordinates array that forms this geometry.
            </summary>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoPointCollection.Type">
            <summary>
            Gets the GeoJSON type of this object.
            </summary>
        </member>
        <member name="T:Azure.Core.GeoJson.GeoPolygon">
            <summary>
            Represents a polygon consisting of outer ring and optional inner rings.
            </summary>
            <example>
            Creating a polygon:
            <code snippet="Snippet:CreatePolygon" language="csharp">
            var polygon = new GeoPolygon(new[]
            {
                new GeoPosition(-122.108727, 47.649383),
                new GeoPosition(-122.081538, 47.640846),
                new GeoPosition(-122.078634, 47.576066),
                new GeoPosition(-122.112686, 47.578559),
                new GeoPosition(-122.108727, 47.649383),
            });
            </code>
            Creating a polygon with holes:
            <code snippet="Snippet:CreatePolygonWithHoles" language="csharp">
            var polygon = new GeoPolygon(new[]
            {
                // Outer ring
                new GeoLinearRing(new[]
                {
                    new GeoPosition(-122.108727, 47.649383),
                    new GeoPosition(-122.081538, 47.640846),
                    new GeoPosition(-122.078634, 47.576066),
                    new GeoPosition(-122.112686, 47.578559),
                    // Last position same as first
                    new GeoPosition(-122.108727, 47.649383),
                }),
                // Inner ring
                new GeoLinearRing(new[]
                {
                    new GeoPosition(-122.102370, 47.607370),
                    new GeoPosition(-122.083488, 47.608007),
                    new GeoPosition(-122.085419, 47.597879),
                    new GeoPosition(-122.107005, 47.596895),
                    // Last position same as first
                    new GeoPosition(-122.102370, 47.607370),
                })
            });
            </code>
            </example>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoPolygon.#ctor(System.Collections.Generic.IEnumerable{Azure.Core.GeoJson.GeoPosition})">
            <summary>
            Initializes new instance of <see cref="T:Azure.Core.GeoJson.GeoPolygon" />.
            </summary>
            <param name="positions">The positions that make up the outer ring of the polygon.</param>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoPolygon.#ctor(System.Collections.Generic.IEnumerable{Azure.Core.GeoJson.GeoLinearRing})">
            <summary>
            Initializes new instance of <see cref="T:Azure.Core.GeoJson.GeoPolygon" />.
            </summary>
            <param name="rings">The collection of rings that make up the polygon, first ring is the outer ring others are inner rings.</param>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoPolygon.#ctor(System.Collections.Generic.IEnumerable{Azure.Core.GeoJson.GeoLinearRing},Azure.Core.GeoJson.GeoBoundingBox,System.Collections.Generic.IReadOnlyDictionary{System.String,System.Object})">
            <summary>
            Initializes new instance of <see cref="T:Azure.Core.GeoJson.GeoPolygon" />.
            </summary>
            <param name="rings">The collection of rings that make up the polygon, first ring is the outer ring others are inner rings.</param>
            <param name="boundingBox">The <see cref="T:Azure.Core.GeoJson.GeoBoundingBox" /> to use.</param>
            <param name="customProperties">The set of custom properties associated with the <see cref="T:Azure.Core.GeoJson.GeoObject" />.</param>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoPolygon.Rings">
            <summary>
            Gets a set of rings that form the polygon.
            </summary>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoPolygon.OuterRing">
            <summary>
            Returns the outer ring of the polygon.
            </summary>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoPolygon.Coordinates">
            <summary>
            Returns a view over the coordinates array that forms this geometry.
            </summary>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoPolygon.Type">
            <summary>
            Gets the GeoJSON type of this object.
            </summary>
        </member>
        <member name="T:Azure.Core.GeoJson.GeoPolygonCollection">
            <summary>
            Represents a geometry that is composed of multiple <see cref="T:Azure.Core.GeoJson.GeoPolygon" />.
            </summary>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoPolygonCollection.#ctor(System.Collections.Generic.IEnumerable{Azure.Core.GeoJson.GeoPolygon})">
            <summary>
            Initializes new instance of <see cref="T:Azure.Core.GeoJson.GeoPolygonCollection" />.
            </summary>
            <param name="polygons">The collection of inner polygons.</param>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoPolygonCollection.#ctor(System.Collections.Generic.IEnumerable{Azure.Core.GeoJson.GeoPolygon},Azure.Core.GeoJson.GeoBoundingBox,System.Collections.Generic.IReadOnlyDictionary{System.String,System.Object})">
            <summary>
            Initializes new instance of <see cref="T:Azure.Core.GeoJson.GeoPolygonCollection" />.
            </summary>
            <param name="polygons">The collection of inner geometries.</param>
            <param name="boundingBox">The <see cref="T:Azure.Core.GeoJson.GeoBoundingBox" /> to use.</param>
            <param name="customProperties">The set of custom properties associated with the <see cref="T:Azure.Core.GeoJson.GeoObject" />.</param>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoPolygonCollection.GetEnumerator">
            <summary>Returns an enumerator that iterates through the collection.</summary><returns>An enumerator that can be used to iterate through the collection.</returns><filterpriority>1</filterpriority>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoPolygonCollection.System#Collections#IEnumerable#GetEnumerator">
            <summary>Returns an enumerator that iterates through a collection.</summary><returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoPolygonCollection.Count">
            <summary>Gets the number of elements in the collection.</summary><returns>The number of elements in the collection. </returns>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoPolygonCollection.Item(System.Int32)">
            <summary>Gets the element at the specified index in the read-only list.</summary><returns>The element at the specified index in the read-only list.</returns><param name="index">The zero-based index of the element to get. </param>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoPolygonCollection.Coordinates">
            <summary>
            Returns a view over the coordinates array that forms this geometry.
            </summary>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoPolygonCollection.Type">
            <summary>
            Gets the GeoJSON type of this object.
            </summary>
        </member>
        <member name="T:Azure.Core.GeoJson.GeoPosition">
            <summary>
            Represents a single spatial position with latitude, longitude, and optional altitude.
            </summary>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoPosition.Altitude">
            <summary>
            Gets the altitude of the position.
            </summary>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoPosition.Longitude">
            <summary>
            Gets the longitude of the position.
            </summary>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoPosition.Latitude">
            <summary>
            Gets the latitude of the position.
            </summary>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoPosition.#ctor(System.Double,System.Double)">
            <summary>
            Initializes a new instance of <see cref="T:Azure.Core.GeoJson.GeoPosition" />.
            </summary>
            <param name="longitude">The longitude of the position.</param>
            <param name="latitude">The latitude of the position.</param>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoPosition.#ctor(System.Double,System.Double,System.Nullable{System.Double})">
            <summary>
            Initializes a new instance of <see cref="T:Azure.Core.GeoJson.GeoPosition" />.
            </summary>
            <param name="longitude">The longitude of the position.</param>
            <param name="latitude">The latitude of the position.</param>
            <param name="altitude">The altitude of the position.</param>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoPosition.Equals(Azure.Core.GeoJson.GeoPosition)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary><returns>true if the current object is equal to the <paramref name="other" /> parameter; otherwise, false.</returns><param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoPosition.Equals(System.Object)">
            <summary>Indicates whether this instance and a specified object are equal.</summary><returns>true if <paramref name="obj" /> and this instance are the same type and represent the same value; otherwise, false. </returns><param name="obj">The object to compare with the current instance. </param><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoPosition.GetHashCode">
            <summary>Returns the hash code for this instance.</summary><returns>A 32-bit signed integer that is the hash code for this instance.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoPosition.op_Equality(Azure.Core.GeoJson.GeoPosition,Azure.Core.GeoJson.GeoPosition)">
            <summary>
            Determines whether two specified positions have the same value.
            </summary>
            <param name="left">The first position to compare.</param>
            <param name="right">The first position to compare.</param>
            <returns><c>true</c> if the value of <c>left</c> is the same as the value of <c>b</c>; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoPosition.op_Inequality(Azure.Core.GeoJson.GeoPosition,Azure.Core.GeoJson.GeoPosition)">
            <summary>
            Determines whether two specified positions have the same value.
            </summary>
            <param name="left">The first position to compare.</param>
            <param name="right">The first position to compare.</param>
            <returns><c>false</c> if the value of <c>left</c> is the same as the value of <c>b</c>; otherwise, <c>true</c>.</returns>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoPosition.ToString">
            <summary>Returns the fully qualified type name of this instance.</summary><returns>A <see cref="T:System.String" /> containing a fully qualified type name.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoPosition.Item(System.Int32)">
            <summary>
            Get the value of coordinate component using its index.
            </summary>
            <param name="index"></param>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoPosition.Count">
            <summary>
            Returns the count of the coordinate components.
            </summary>
        </member>
        <member name="T:Azure.Core.HttpHeader">
            <summary>
            Represents an HTTP header.
            </summary>
        </member>
        <member name="M:Azure.Core.HttpHeader.#ctor(System.String,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Azure.Core.HttpHeader" /> with provided name and value.
            </summary>
            <param name="name">The header name.</param>
            <param name="value">The header value.</param>
        </member>
        <member name="P:Azure.Core.HttpHeader.Name">
            <summary>
            Gets header name.
            </summary>
        </member>
        <member name="P:Azure.Core.HttpHeader.Value">
            <summary>
            Gets header value. If the header has multiple values they would be joined with a comma. To get separate values use <see cref="M:Azure.Core.RequestHeaders.TryGetValues(System.String,System.Collections.Generic.IEnumerable{System.String}@)" /> or <see cref="M:Azure.Core.ResponseHeaders.TryGetValues(System.String,System.Collections.Generic.IEnumerable{System.String}@)" />.
            </summary>
        </member>
        <member name="M:Azure.Core.HttpHeader.GetHashCode">
            <summary>Returns the hash code for this instance.</summary><returns>A 32-bit signed integer that is the hash code for this instance.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.HttpHeader.Equals(System.Object)">
            <summary>Indicates whether this instance and a specified object are equal.</summary><returns>true if <paramref name="obj" /> and this instance are the same type and represent the same value; otherwise, false. </returns><param name="obj">The object to compare with the current instance. </param><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.HttpHeader.ToString">
            <summary>Returns the fully qualified type name of this instance.</summary><returns>A <see cref="T:System.String" /> containing a fully qualified type name.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.HttpHeader.Equals(Azure.Core.HttpHeader)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary><returns>true if the current object is equal to the <paramref name="other" /> parameter; otherwise, false.</returns><param name="other">An object to compare with this object.</param>
        </member>
        <member name="T:Azure.Core.HttpHeader.Names">
            <summary>
            Contains names of commonly used headers.
            </summary>
        </member>
        <member name="P:Azure.Core.HttpHeader.Names.Date">
            <summary>
            Returns. <code>"Date"</code>
            </summary>
        </member>
        <member name="P:Azure.Core.HttpHeader.Names.XMsDate">
            <summary>
            Returns. <code>"x-ms-date"</code>
            </summary>
        </member>
        <member name="P:Azure.Core.HttpHeader.Names.ContentType">
            <summary>
            Returns. <code>"Content-Type"</code>
            </summary>
        </member>
        <member name="P:Azure.Core.HttpHeader.Names.ContentLength">
            <summary>
            Returns. <code>"Content-Length"</code>
            </summary>
        </member>
        <member name="P:Azure.Core.HttpHeader.Names.ETag">
            <summary>
            Returns. <code>"ETag"</code>
            </summary>
        </member>
        <member name="P:Azure.Core.HttpHeader.Names.XMsRequestId">
            <summary>
            Returns. <code>"x-ms-request-id"</code>
            </summary>
        </member>
        <member name="P:Azure.Core.HttpHeader.Names.UserAgent">
            <summary>
            Returns. <code>"User-Agent"</code>
            </summary>
        </member>
        <member name="P:Azure.Core.HttpHeader.Names.Accept">
            <summary>
            Returns. <code>"Accept"</code>
            </summary>
        </member>
        <member name="P:Azure.Core.HttpHeader.Names.Authorization">
            <summary>
            Returns. <code>"Authorization"</code>
            </summary>
        </member>
        <member name="P:Azure.Core.HttpHeader.Names.Range">
            <summary>
            Returns. <code>"Range"</code>
            </summary>
        </member>
        <member name="P:Azure.Core.HttpHeader.Names.XMsRange">
            <summary>
            Returns. <code>"x-ms-range"</code>
            </summary>
        </member>
        <member name="P:Azure.Core.HttpHeader.Names.IfMatch">
            <summary>
            Returns. <code>"If-Match"</code>
            </summary>
        </member>
        <member name="P:Azure.Core.HttpHeader.Names.IfNoneMatch">
            <summary>
            Returns. <code>"If-None-Match"</code>
            </summary>
        </member>
        <member name="P:Azure.Core.HttpHeader.Names.IfModifiedSince">
            <summary>
            Returns. <code>"If-Modified-Since"</code>
            </summary>
        </member>
        <member name="P:Azure.Core.HttpHeader.Names.IfUnmodifiedSince">
            <summary>
            Returns. <code>"If-Unmodified-Since"</code>
            </summary>
        </member>
        <member name="P:Azure.Core.HttpHeader.Names.Prefer">
            <summary>
            Returns. <code>"Prefer"</code>
            </summary>
        </member>
        <member name="P:Azure.Core.HttpHeader.Names.Referer">
            <summary>
            Returns. <code>"Referer"</code>
            </summary>
        </member>
        <member name="P:Azure.Core.HttpHeader.Names.Host">
            <summary>
            Returns. <code>"Host"</code>
            </summary>
        </member>
        <member name="P:Azure.Core.HttpHeader.Names.ContentDisposition">
            <summary>
            Returns <code>"Content-Disposition"</code>.
            </summary>
        </member>
        <member name="P:Azure.Core.HttpHeader.Names.WwwAuthenticate">
            <summary>
            Returns <code>"WWW-Authenticate"</code>.
            </summary>
        </member>
        <member name="T:Azure.Core.HttpHeader.Common">
            <summary>
            Commonly defined header values.
            </summary>
        </member>
        <member name="F:Azure.Core.HttpHeader.Common.JsonContentType">
            <summary>
            Returns header with name "ContentType" and value "application/json".
            </summary>
        </member>
        <member name="F:Azure.Core.HttpHeader.Common.JsonAccept">
            <summary>
            Returns header with name "Accept" and value "application/json".
            </summary>
        </member>
        <member name="F:Azure.Core.HttpHeader.Common.OctetStreamContentType">
            <summary>
            Returns header with name "ContentType" and value "application/octet-stream".
            </summary>
        </member>
        <member name="F:Azure.Core.HttpHeader.Common.FormUrlEncodedContentType">
            <summary>
            Returns header with name "ContentType" and value "application/x-www-form-urlencoded".
            </summary>
        </member>
        <member name="T:Azure.Core.HttpMessage">
            <summary>
            Represents a context flowing through the <see cref="T:Azure.Core.Pipeline.HttpPipeline" />.
            </summary>
        </member>
        <member name="M:Azure.Core.HttpMessage.#ctor(Azure.Core.Request,Azure.Core.ResponseClassifier)">
            <summary>
            Creates a new instance of <see cref="T:Azure.Core.HttpMessage" />.
            </summary>
            <param name="request">The request.</param>
            <param name="responseClassifier">The response classifier.</param>
        </member>
        <member name="P:Azure.Core.HttpMessage.Request">
            <summary>
            Gets the <see cref="P:Azure.Core.HttpMessage.Request" /> associated with this message.
            </summary>
        </member>
        <member name="P:Azure.Core.HttpMessage.Response">
            <summary>
            Gets the <see cref="P:Azure.Core.HttpMessage.Response" /> associated with this message. Throws an exception if it wasn't set yet.
            To avoid the exception use <see cref="P:Azure.Core.HttpMessage.HasResponse" /> property to check.
            </summary>
        </member>
        <member name="P:Azure.Core.HttpMessage.HasResponse">
            <summary>
            Gets the value indicating if the response is set on this message.
            </summary>
        </member>
        <member name="P:Azure.Core.HttpMessage.CancellationToken">
            <summary>
            The <see cref="T:System.Threading.CancellationToken" /> to be used during the <see cref="T:Azure.Core.HttpMessage" /> processing.
            </summary>
        </member>
        <member name="P:Azure.Core.HttpMessage.ResponseClassifier">
            <summary>
            The <see cref="P:Azure.Core.HttpMessage.ResponseClassifier" /> instance to use for response classification during pipeline invocation.
            </summary>
        </member>
        <member name="P:Azure.Core.HttpMessage.BufferResponse">
            <summary>
            Gets or sets the value indicating if response would be buffered as part of the pipeline. Defaults to true.
            </summary>
        </member>
        <member name="P:Azure.Core.HttpMessage.NetworkTimeout">
            <summary>
            Gets or sets the network timeout value for this message. If <c>null</c> the value provided in <see cref="P:Azure.Core.RetryOptions.NetworkTimeout" /> would be used instead.
            Defaults to <c>null</c>.
            </summary>
        </member>
        <member name="P:Azure.Core.HttpMessage.ProcessingContext">
            <summary>
            The processing context for the message.
            </summary>
        </member>
        <member name="M:Azure.Core.HttpMessage.TryGetProperty(System.String,System.Object@)">
            <summary>
            Gets a property that modifies the pipeline behavior. Please refer to individual policies documentation on what properties it supports.
            </summary>
            <param name="name">The property name.</param>
            <param name="value">The property value.</param>
            <returns><c>true</c> if property exists, otherwise. <c>false</c>.</returns>
        </member>
        <member name="M:Azure.Core.HttpMessage.SetProperty(System.String,System.Object)">
            <summary>
            Sets a property that modifies the pipeline behavior. Please refer to individual policies documentation on what properties it supports.
            </summary>
            <param name="name">The property name.</param>
            <param name="value">The property value.</param>
        </member>
        <member name="M:Azure.Core.HttpMessage.TryGetProperty(System.Type,System.Object@)">
            <summary>
            Gets a property that is stored with this <see cref="T:Azure.Core.HttpMessage" /> instance and can be used for modifying pipeline behavior.
            </summary>
            <param name="type">The property type.</param>
            <param name="value">The property value.</param>
            <remarks>
            The key value is of type <c>Type</c> for a couple of reasons. Primarily, it allows values to be stored such that though the accessor methods
            are public, storing values keyed by internal types make them inaccessible to other assemblies. This protects internal values from being overwritten
            by external code. See the <see cref="T:Azure.Core.TelemetryDetails" /> and <see cref="T:Azure.Core.Pipeline.UserAgentValueKey" /> types for an example of this usage. Secondly, <c>Type</c>
            comparisons are faster than string comparisons.
            </remarks>
            <returns><c>true</c> if property exists, otherwise. <c>false</c>.</returns>
        </member>
        <member name="M:Azure.Core.HttpMessage.SetProperty(System.Type,System.Object)">
            <summary>
            Sets a property that is stored with this <see cref="T:Azure.Core.HttpMessage" /> instance and can be used for modifying pipeline behavior.
            Internal properties can be keyed with internal types to prevent external code from overwriting these values.
            </summary>
            <param name="type">The key for the value.</param>
            <param name="value">The property value.</param>
        </member>
        <member name="M:Azure.Core.HttpMessage.ExtractResponseContent">
            <summary>
            Returns the response content stream and releases it ownership to the caller. After calling this methods using <see cref="P:Azure.Response.ContentStream" /> or <see cref="P:Azure.Response.Content" /> would result in exception.
            </summary>
            <returns>The content stream or null if response didn't have any.</returns>
        </member>
        <member name="M:Azure.Core.HttpMessage.Dispose">
            <summary>
            Disposes the request and response.
            </summary>
        </member>
        <member name="T:Azure.Core.HttpPipelinePosition">
            <summary>
            Represents a position of the policy in the pipeline.
            </summary>
        </member>
        <member name="F:Azure.Core.HttpPipelinePosition.PerCall">
            <summary>
            The policy would be invoked once per pipeline invocation (service call).
            </summary>
        </member>
        <member name="F:Azure.Core.HttpPipelinePosition.PerRetry">
            <summary>
            The policy would be invoked every time request is retried.
            </summary>
        </member>
        <member name="F:Azure.Core.HttpPipelinePosition.BeforeTransport">
            <summary>
            The policy would be invoked before the request is sent by the transport.
            </summary>
        </member>
        <member name="T:Azure.Core.ArrayBackedPropertyBag`2">
            <summary>
            A property bag which is optimized for storage of a small number of items.
            If the item count is less than 2, there are no allocations. Any additional items are stored in an array which will grow as needed.
            MUST be passed by ref only.
            </summary>
        </member>
        <member name="T:Azure.Core.MessageProcessingContext">
            <summary>
            Contains information related to the processing of the <see cref="T:Azure.Core.HttpMessage" /> as it traverses the pipeline.
            </summary>
        </member>
        <member name="P:Azure.Core.MessageProcessingContext.StartTime">
            <summary>
            The time that the pipeline processing started for the message.
            </summary>
        </member>
        <member name="P:Azure.Core.MessageProcessingContext.RetryNumber">
            <summary>
            The retry number for the request. For the initial request, the value is 0.
            </summary>
        </member>
        <member name="M:Azure.Core.MessageProcessingContext.#ctor(Azure.Core.HttpMessage)">
            <summary>
            Initializes a new instance of <see cref="T:Azure.Core.MessageProcessingContext" />.
            </summary>
            <param name="message">The message that the context is attached to.</param>
        </member>
        <member name="T:Azure.Core.Pipeline.BearerTokenAuthenticationPolicy">
            <summary>
            A policy that sends an <see cref="T:Azure.Core.AccessToken" /> provided by a <see cref="T:Azure.Core.TokenCredential" /> as an Authentication header.
            </summary>
        </member>
        <member name="M:Azure.Core.Pipeline.BearerTokenAuthenticationPolicy.#ctor(Azure.Core.TokenCredential,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Azure.Core.Pipeline.BearerTokenAuthenticationPolicy" /> using provided token credential and scope to authenticate for.
            </summary>
            <param name="credential">The token credential to use for authentication.</param>
            <param name="scope">The scope to be included in acquired tokens.</param>
        </member>
        <member name="M:Azure.Core.Pipeline.BearerTokenAuthenticationPolicy.#ctor(Azure.Core.TokenCredential,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Creates a new instance of <see cref="T:Azure.Core.Pipeline.BearerTokenAuthenticationPolicy" /> using provided token credential and scopes to authenticate for.
            </summary>
            <param name="credential">The token credential to use for authentication.</param>
            <param name="scopes">Scopes to be included in acquired tokens.</param>
            <exception cref="T:System.ArgumentNullException">When <paramref name="credential" /> or <paramref name="scopes" /> is null.</exception>
        </member>
        <member name="M:Azure.Core.Pipeline.BearerTokenAuthenticationPolicy.ProcessAsync(Azure.Core.HttpMessage,System.ReadOnlyMemory{Azure.Core.Pipeline.HttpPipelinePolicy})">
            <summary>
            Applies the policy to the <paramref name="message" />. Implementers are expected to mutate <see cref="P:Azure.Core.HttpMessage.Request" /> before calling <see cref="M:Azure.Core.Pipeline.HttpPipelinePolicy.ProcessNextAsync(Azure.Core.HttpMessage,System.ReadOnlyMemory{Azure.Core.Pipeline.HttpPipelinePolicy})" /> and observe the <see cref="P:Azure.Core.HttpMessage.Response" /> changes after.
            </summary>
            <param name="message">The <see cref="T:Azure.Core.HttpMessage" /> this policy would be applied to.</param>
            <param name="pipeline">The set of <see cref="T:Azure.Core.Pipeline.HttpPipelinePolicy" /> to execute after current one.</param>
            <returns>The <see cref="T:System.Threading.Tasks.ValueTask" /> representing the asynchronous operation.</returns>
        </member>
        <member name="M:Azure.Core.Pipeline.BearerTokenAuthenticationPolicy.Process(Azure.Core.HttpMessage,System.ReadOnlyMemory{Azure.Core.Pipeline.HttpPipelinePolicy})">
            <summary>
            Applies the policy to the <paramref name="message" />. Implementers are expected to mutate <see cref="P:Azure.Core.HttpMessage.Request" /> before calling <see cref="M:Azure.Core.Pipeline.HttpPipelinePolicy.ProcessNextAsync(Azure.Core.HttpMessage,System.ReadOnlyMemory{Azure.Core.Pipeline.HttpPipelinePolicy})" /> and observe the <see cref="P:Azure.Core.HttpMessage.Response" /> changes after.
            </summary>
            <param name="message">The <see cref="T:Azure.Core.HttpMessage" /> this policy would be applied to.</param>
            <param name="pipeline">The set of <see cref="T:Azure.Core.Pipeline.HttpPipelinePolicy" /> to execute after current one.</param>
        </member>
        <member name="M:Azure.Core.Pipeline.BearerTokenAuthenticationPolicy.AuthorizeRequestAsync(Azure.Core.HttpMessage)">
            <summary>
            Executes before <see cref="M:Azure.Core.Pipeline.BearerTokenAuthenticationPolicy.ProcessAsync(Azure.Core.HttpMessage,System.ReadOnlyMemory{Azure.Core.Pipeline.HttpPipelinePolicy})" /> or
            <see cref="M:Azure.Core.Pipeline.BearerTokenAuthenticationPolicy.Process(Azure.Core.HttpMessage,System.ReadOnlyMemory{Azure.Core.Pipeline.HttpPipelinePolicy})" /> is called.
            Implementers of this method are expected to call <see cref="M:Azure.Core.Pipeline.BearerTokenAuthenticationPolicy.AuthenticateAndAuthorizeRequest(Azure.Core.HttpMessage,Azure.Core.TokenRequestContext)" /> or <see cref="M:Azure.Core.Pipeline.BearerTokenAuthenticationPolicy.AuthenticateAndAuthorizeRequestAsync(Azure.Core.HttpMessage,Azure.Core.TokenRequestContext)" />
            if authorization is required for requests not related to handling a challenge response.
            </summary>
            <param name="message">The <see cref="T:Azure.Core.HttpMessage" /> this policy would be applied to.</param>
            <returns>The <see cref="T:System.Threading.Tasks.ValueTask" /> representing the asynchronous operation.</returns>
        </member>
        <member name="M:Azure.Core.Pipeline.BearerTokenAuthenticationPolicy.AuthorizeRequest(Azure.Core.HttpMessage)">
            <summary>
            Executes before <see cref="M:Azure.Core.Pipeline.BearerTokenAuthenticationPolicy.ProcessAsync(Azure.Core.HttpMessage,System.ReadOnlyMemory{Azure.Core.Pipeline.HttpPipelinePolicy})" /> or
            <see cref="M:Azure.Core.Pipeline.BearerTokenAuthenticationPolicy.Process(Azure.Core.HttpMessage,System.ReadOnlyMemory{Azure.Core.Pipeline.HttpPipelinePolicy})" /> is called.
            Implementers of this method are expected to call <see cref="M:Azure.Core.Pipeline.BearerTokenAuthenticationPolicy.AuthenticateAndAuthorizeRequest(Azure.Core.HttpMessage,Azure.Core.TokenRequestContext)" /> or <see cref="M:Azure.Core.Pipeline.BearerTokenAuthenticationPolicy.AuthenticateAndAuthorizeRequestAsync(Azure.Core.HttpMessage,Azure.Core.TokenRequestContext)" />
            if authorization is required for requests not related to handling a challenge response.
            </summary>
            <param name="message">The <see cref="T:Azure.Core.HttpMessage" /> this policy would be applied to.</param>
        </member>
        <member name="M:Azure.Core.Pipeline.BearerTokenAuthenticationPolicy.AuthorizeRequestOnChallengeAsync(Azure.Core.HttpMessage)">
            <summary>
            Executed in the event a 401 response with a WWW-Authenticate authentication challenge header is received after the initial request.
            </summary>
            <remarks>Service client libraries may override this to handle service specific authentication challenges.</remarks>
            <param name="message">The <see cref="T:Azure.Core.HttpMessage" /> to be authenticated.</param>
            <returns>A boolean indicating whether the request was successfully authenticated and should be sent to the transport.</returns>
        </member>
        <member name="M:Azure.Core.Pipeline.BearerTokenAuthenticationPolicy.AuthorizeRequestOnChallenge(Azure.Core.HttpMessage)">
            <summary>
            Executed in the event a 401 response with a WWW-Authenticate authentication challenge header is received after the initial request.
            </summary>
            <remarks>Service client libraries may override this to handle service specific authentication challenges.</remarks>
            <param name="message">The <see cref="T:Azure.Core.HttpMessage" /> to be authenticated.</param>
            <returns>A boolean indicating whether the request was successfully authenticated and should be sent to the transport.</returns>
        </member>
        <member name="M:Azure.Core.Pipeline.BearerTokenAuthenticationPolicy.AuthenticateAndAuthorizeRequestAsync(Azure.Core.HttpMessage,Azure.Core.TokenRequestContext)">
            <summary>
            Sets the Authorization header on the <see cref="T:Azure.Core.Request" /> by calling GetToken, or from cache, if possible.
            </summary>
            <param name="message">The <see cref="T:Azure.Core.HttpMessage" /> with the <see cref="T:Azure.Core.Request" /> to be authorized.</param>
            <param name="context">The <see cref="T:Azure.Core.TokenRequestContext" /> used to authorize the <see cref="T:Azure.Core.Request" />.</param>
        </member>
        <member name="M:Azure.Core.Pipeline.BearerTokenAuthenticationPolicy.AuthenticateAndAuthorizeRequest(Azure.Core.HttpMessage,Azure.Core.TokenRequestContext)">
            <summary>
            Sets the Authorization header on the <see cref="T:Azure.Core.Request" /> by calling GetToken, or from cache, if possible.
            </summary>
            <param name="message">The <see cref="T:Azure.Core.HttpMessage" /> with the <see cref="T:Azure.Core.Request" /> to be authorized.</param>
            <param name="context">The <see cref="T:Azure.Core.TokenRequestContext" /> used to authorize the <see cref="T:Azure.Core.Request" />.</param>
        </member>
        <member name="T:Azure.Core.Pipeline.DisposableHttpPipeline">
            <summary>
            An implementation of <see cref="T:Azure.Core.Pipeline.HttpPipeline" /> that may contain resources that require disposal.
            </summary>
        </member>
        <member name="M:Azure.Core.Pipeline.DisposableHttpPipeline.#ctor(Azure.Core.Pipeline.HttpPipelineTransport,System.Int32,System.Int32,Azure.Core.Pipeline.HttpPipelinePolicy[],Azure.Core.ResponseClassifier,System.Boolean)">
            <summary>
            Creates a new instance of <see cref="T:Azure.Core.Pipeline.HttpPipeline" /> with the provided transport, policies and response classifier.
            </summary>
            <param name="transport">The <see cref="T:Azure.Core.Pipeline.HttpPipelineTransport" /> to use for sending the requests.</param>
            <param name="perCallIndex"></param>
            <param name="perRetryIndex"></param>
            <param name="policies">Policies to be invoked as part of the pipeline in order.</param>
            <param name="responseClassifier">The response classifier to be used in invocations.</param>
            <param name="isTransportOwnedInternally"> </param>
        </member>
        <member name="M:Azure.Core.Pipeline.DisposableHttpPipeline.Dispose">
            <summary>
            Disposes the underlying transport if it is owned by the client, i.e. it was created via the Build method on <see cref="T:Azure.Core.Pipeline.HttpPipelineBuilder" />. If the underlying transport is not owned by the client, i.e. it was supplied as a custom transport on <see cref="T:Azure.Core.ClientOptions" />, it will not be disposed.
            <remarks>
            The reason not to dispose a transport owned outside the client, i.e. one that was provided via <see cref="T:Azure.Core.ClientOptions" /> is to account for scenarios
            where the custom transport may be shared across clients. In this case, it is possible to dispose of a transport
            still in use by other clients. When the transport is created internally, it can properly determine if a shared instance is in use.
            </remarks>
            </summary>
        </member>
        <member name="T:Azure.Core.Pipeline.HttpClientTransport">
            <summary>
            An <see cref="T:Azure.Core.Pipeline.HttpPipelineTransport" /> implementation that uses <see cref="T:System.Net.Http.HttpClient" /> as the transport.
            </summary>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpClientTransport.#ctor">
            <summary>
            Creates a new <see cref="T:Azure.Core.Pipeline.HttpClientTransport" /> instance using default configuration.
            </summary>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpClientTransport.#ctor(Azure.Core.Pipeline.HttpPipelineTransportOptions)">
            <summary>
            Creates a new <see cref="T:Azure.Core.Pipeline.HttpClientTransport" /> instance using default configuration.
            </summary>
            <param name="options">The <see cref="T:Azure.Core.Pipeline.HttpPipelineTransportOptions" /> that to configure the behavior of the transport.</param>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpClientTransport.#ctor(System.Net.Http.HttpMessageHandler)">
            <summary>
            Creates a new instance of <see cref="T:Azure.Core.Pipeline.HttpClientTransport" /> using the provided client instance.
            </summary>
            <param name="messageHandler">The instance of <see cref="T:System.Net.Http.HttpMessageHandler" /> to use.</param>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpClientTransport.#ctor(System.Net.Http.HttpClient)">
            <summary>
            Creates a new instance of <see cref="T:Azure.Core.Pipeline.HttpClientTransport" /> using the provided client instance.
            </summary>
            <param name="client">The instance of <see cref="T:System.Net.Http.HttpClient" /> to use.</param>
        </member>
        <member name="F:Azure.Core.Pipeline.HttpClientTransport.Shared">
            <summary>
            A shared instance of <see cref="T:Azure.Core.Pipeline.HttpClientTransport" /> with default parameters.
            </summary>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpClientTransport.CreateRequest">
            <summary>
            Creates a new transport specific instance of <see cref="T:Azure.Core.Request" />. This should not be called directly, <see cref="M:Azure.Core.Pipeline.HttpPipeline.CreateRequest" /> or
            <see cref="M:Azure.Core.Pipeline.HttpPipeline.CreateMessage" /> should be used instead.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpClientTransport.Process(Azure.Core.HttpMessage)">
            <summary>
            Sends the request contained by the <paramref name="message" /> and sets the <see cref="P:Azure.Core.HttpMessage.Response" /> property to received response synchronously.
            </summary>
            <param name="message">The <see cref="T:Azure.Core.HttpMessage" /> containing request and response.</param>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpClientTransport.ProcessAsync(Azure.Core.HttpMessage)">
            <summary>
            Sends the request contained by the <paramref name="message" /> and sets the <see cref="P:Azure.Core.HttpMessage.Response" /> property to received response asynchronously.
            </summary>
            <param name="message">The <see cref="T:Azure.Core.HttpMessage" /> containing request and response.</param>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpClientTransport.Dispose">
            <summary>
            Disposes the underlying <see cref="T:System.Net.Http.HttpClient" />.
            </summary>
        </member>
        <member name="T:Azure.Core.Pipeline.HttpPipeline">
            <summary>
            Represents a primitive for sending HTTP requests and receiving responses extensible by adding <see cref="T:Azure.Core.Pipeline.HttpPipelinePolicy" /> processing steps.
            </summary>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpPipeline.#ctor(Azure.Core.Pipeline.HttpPipelineTransport,Azure.Core.Pipeline.HttpPipelinePolicy[],Azure.Core.ResponseClassifier)">
            <summary>
            Creates a new instance of <see cref="T:Azure.Core.Pipeline.HttpPipeline" /> with the provided transport, policies and response classifier.
            </summary>
            <param name="transport">The <see cref="T:Azure.Core.Pipeline.HttpPipelineTransport" /> to use for sending the requests.</param>
            <param name="policies">Policies to be invoked as part of the pipeline in order.</param>
            <param name="responseClassifier">The response classifier to be used in invocations.</param>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpPipeline.CreateRequest">
            <summary>
            Creates a new <see cref="T:Azure.Core.Request" /> instance.
            </summary>
            <returns>The request.</returns>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpPipeline.CreateMessage">
            <summary>
            Creates a new <see cref="T:Azure.Core.HttpMessage" /> instance.
            </summary>
            <returns>The message.</returns>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpPipeline.CreateMessage(Azure.RequestContext)">
            <summary>
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpPipeline.CreateMessage(Azure.RequestContext,Azure.Core.ResponseClassifier)">
            <summary>
            Creates a new <see cref="T:Azure.Core.HttpMessage" /> instance.
            </summary>
            <param name="context">Context specifying the message options.</param>
            <param name="classifier"></param>
            <returns>The message.</returns>
        </member>
        <member name="P:Azure.Core.Pipeline.HttpPipeline.ResponseClassifier">
            <summary>
            The <see cref="P:Azure.Core.Pipeline.HttpPipeline.ResponseClassifier" /> instance used in this pipeline invocations.
            </summary>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpPipeline.SendAsync(Azure.Core.HttpMessage,System.Threading.CancellationToken)">
            <summary>
            Invokes the pipeline asynchronously. After the task completes response would be set to the <see cref="P:Azure.Core.HttpMessage.Response" /> property.
            </summary>
            <param name="message">The <see cref="T:Azure.Core.HttpMessage" /> to send.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> to use.</param>
            <returns>The <see cref="T:System.Threading.Tasks.ValueTask" /> representing the asynchronous operation.</returns>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpPipeline.Send(Azure.Core.HttpMessage,System.Threading.CancellationToken)">
            <summary>
            Invokes the pipeline synchronously. After the task completes response would be set to the <see cref="P:Azure.Core.HttpMessage.Response" /> property.
            </summary>
            <param name="message">The <see cref="T:Azure.Core.HttpMessage" /> to send.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> to use.</param>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpPipeline.SendRequestAsync(Azure.Core.Request,System.Threading.CancellationToken)">
            <summary>
            Invokes the pipeline asynchronously with the provided request.
            </summary>
            <param name="request">The <see cref="T:Azure.Core.Request" /> to send.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> to use.</param>
            <returns>The <see cref="T:System.Threading.Tasks.ValueTask`1" /> representing the asynchronous operation.</returns>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpPipeline.SendRequest(Azure.Core.Request,System.Threading.CancellationToken)">
            <summary>
            Invokes the pipeline synchronously with the provided request.
            </summary>
            <param name="request">The <see cref="T:Azure.Core.Request" /> to send.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> to use.</param>
            <returns>The <see cref="T:Azure.Response" /> from the server.</returns>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpPipeline.CreateClientRequestIdScope(System.String)">
             <summary>
             Creates a scope in which all outgoing requests would use the provided
             </summary>
             <param name="clientRequestId">The client request id value to be sent with request.</param>
             <returns>The <see cref="T:System.IDisposable" /> instance that needs to be disposed when client request id shouldn't be sent anymore.</returns>
             <example>
             Sample usage:
             <code snippet="Snippet:ClientRequestId" language="csharp">
             var secretClient = new SecretClient(new Uri("http://example.com"), new DefaultAzureCredential());
            
             using (HttpPipeline.CreateClientRequestIdScope("&lt;custom-client-request-id&gt;"))
             {
                 // The HTTP request resulting from the client call would have x-ms-client-request-id value set to &lt;custom-client-request-id&gt;
                 secretClient.GetSecret("&lt;secret-name&gt;");
             }
             </code>
             </example>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpPipeline.CreateHttpMessagePropertiesScope(System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Creates a scope in which all <see cref="T:Azure.Core.HttpMessage" />s would have provided properties.
            </summary>
            <param name="messageProperties">Properties to be added to <see cref="T:Azure.Core.HttpMessage" />s</param>
            <returns>The <see cref="T:System.IDisposable" /> instance that needs to be disposed when properties shouldn't be used anymore.</returns>
        </member>
        <member name="T:Azure.Core.Pipeline.HttpPipelineBuilder">
            <summary>
            Factory for creating instances of <see cref="T:Azure.Core.Pipeline.HttpPipeline" /> populated with default policies.
            </summary>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpPipelineBuilder.Build(Azure.Core.ClientOptions,Azure.Core.Pipeline.HttpPipelinePolicy[])">
            <summary>
            Creates an instance of <see cref="T:Azure.Core.Pipeline.HttpPipeline" /> populated with default policies, customer provided policies from <paramref name="options" /> and client provided per call policies.
            </summary>
            <param name="options">The customer provided client options object.</param>
            <param name="perRetryPolicies">Client provided per-retry policies.</param>
            <returns>A new instance of <see cref="T:Azure.Core.Pipeline.HttpPipeline" /></returns>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpPipelineBuilder.Build(Azure.Core.ClientOptions,Azure.Core.Pipeline.HttpPipelinePolicy[],Azure.Core.Pipeline.HttpPipelinePolicy[],Azure.Core.ResponseClassifier)">
            <summary>
            Creates an instance of <see cref="T:Azure.Core.Pipeline.HttpPipeline" /> populated with default policies, customer provided policies from <paramref name="options" /> and client provided per call policies.
            </summary>
            <param name="options">The customer provided client options object.</param>
            <param name="perCallPolicies">Client provided per-call policies.</param>
            <param name="perRetryPolicies">Client provided per-retry policies.</param>
            <param name="responseClassifier">The client provided response classifier.</param>
            <returns>A new instance of <see cref="T:Azure.Core.Pipeline.HttpPipeline" /></returns>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpPipelineBuilder.Build(Azure.Core.ClientOptions,Azure.Core.Pipeline.HttpPipelinePolicy[],Azure.Core.Pipeline.HttpPipelinePolicy[],Azure.Core.Pipeline.HttpPipelineTransportOptions,Azure.Core.ResponseClassifier)">
            <summary>
            Creates an instance of <see cref="T:Azure.Core.Pipeline.DisposableHttpPipeline" /> populated with default policies, customer provided policies from <paramref name="options" />, client provided per call policies, and the supplied <see cref="T:Azure.Core.Pipeline.HttpPipelineTransportOptions" />.
            </summary>
            <param name="options">The customer provided client options object.</param>
            <param name="perCallPolicies">Client provided per-call policies.</param>
            <param name="perRetryPolicies">Client provided per-retry policies.</param>
            <param name="transportOptions">The customer provided transport options which will be applied to the default transport. Note: If a custom transport has been supplied via the <paramref name="options" />, these <paramref name="transportOptions" /> will be ignored.</param>
            <param name="responseClassifier">The client provided response classifier.</param>
            <returns>A new instance of <see cref="T:Azure.Core.Pipeline.DisposableHttpPipeline" /></returns>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpPipelineBuilder.Build(Azure.Core.Pipeline.HttpPipelineOptions)">
            <summary>
            Creates an instance of <see cref="T:Azure.Core.Pipeline.HttpPipeline" /> populated with default policies, customer provided policies from <paramref name="options" /> and client provided per call policies.
            </summary>
            <param name="options">The configuration options used to build the <see cref="T:Azure.Core.Pipeline.HttpPipeline" /></param>
            <returns>A new instance of <see cref="T:Azure.Core.Pipeline.HttpPipeline" /></returns>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpPipelineBuilder.Build(Azure.Core.Pipeline.HttpPipelineOptions,Azure.Core.Pipeline.HttpPipelineTransportOptions)">
            <summary>
            Creates an instance of <see cref="T:Azure.Core.Pipeline.DisposableHttpPipeline" /> populated with default policies, customer provided policies from <paramref name="options" />, client provided per call policies, and the supplied <see cref="T:Azure.Core.Pipeline.HttpPipelineTransportOptions" />.
            </summary>
            <param name="options">The configuration options used to build the <see cref="T:Azure.Core.Pipeline.DisposableHttpPipeline" /></param>
            <param name="transportOptions">The customer provided transport options which will be applied to the default transport. Note: If a custom transport has been supplied via the <paramref name="options" />, these <paramref name="transportOptions" /> will be ignored.</param>
            <returns>A new instance of <see cref="T:Azure.Core.Pipeline.DisposableHttpPipeline" /></returns>
        </member>
        <member name="T:Azure.Core.Pipeline.HttpPipelineOptions">
            <summary>
            Specifies configuration of options for building the <see cref="T:Azure.Core.Pipeline.HttpPipeline" />
            </summary>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpPipelineOptions.#ctor(Azure.Core.ClientOptions)">
            <summary>
            Initializes a new instance of <see cref="T:Azure.Core.Pipeline.HttpPipelineOptions" />.
            </summary>
            <param name="options">The customer provided client options object.</param>
        </member>
        <member name="P:Azure.Core.Pipeline.HttpPipelineOptions.ClientOptions">
            <summary>
            The customer provided client options object.
            </summary>
        </member>
        <member name="P:Azure.Core.Pipeline.HttpPipelineOptions.PerCallPolicies">
            <summary>
            Client provided per-call policies.
            </summary>
        </member>
        <member name="P:Azure.Core.Pipeline.HttpPipelineOptions.PerRetryPolicies">
            <summary>
            Client provided per-retry policies.
            </summary>
        </member>
        <member name="P:Azure.Core.Pipeline.HttpPipelineOptions.ResponseClassifier">
            <summary>
            The client provided response classifier.
            </summary>
        </member>
        <member name="P:Azure.Core.Pipeline.HttpPipelineOptions.RequestFailedDetailsParser">
            <summary>
            Responsible for parsing the error content related to a failed request from the service.
            </summary>
        </member>
        <member name="T:Azure.Core.Pipeline.HttpPipelinePolicy">
            <summary>
            Represent an extension point for the <see cref="T:Azure.Core.Pipeline.HttpPipeline" /> that can mutate the <see cref="T:Azure.Core.Request" /> and react to received <see cref="T:Azure.Response" />.
            </summary>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpPipelinePolicy.ProcessAsync(Azure.Core.HttpMessage,System.ReadOnlyMemory{Azure.Core.Pipeline.HttpPipelinePolicy})">
            <summary>
            Applies the policy to the <paramref name="message" />. Implementers are expected to mutate <see cref="P:Azure.Core.HttpMessage.Request" /> before calling <see cref="M:Azure.Core.Pipeline.HttpPipelinePolicy.ProcessNextAsync(Azure.Core.HttpMessage,System.ReadOnlyMemory{Azure.Core.Pipeline.HttpPipelinePolicy})" /> and observe the <see cref="P:Azure.Core.HttpMessage.Response" /> changes after.
            </summary>
            <param name="message">The <see cref="T:Azure.Core.HttpMessage" /> this policy would be applied to.</param>
            <param name="pipeline">The set of <see cref="T:Azure.Core.Pipeline.HttpPipelinePolicy" /> to execute after current one.</param>
            <returns>The <see cref="T:System.Threading.Tasks.ValueTask" /> representing the asynchronous operation.</returns>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpPipelinePolicy.Process(Azure.Core.HttpMessage,System.ReadOnlyMemory{Azure.Core.Pipeline.HttpPipelinePolicy})">
            <summary>
            Applies the policy to the <paramref name="message" />. Implementers are expected to mutate <see cref="P:Azure.Core.HttpMessage.Request" /> before calling <see cref="M:Azure.Core.Pipeline.HttpPipelinePolicy.ProcessNextAsync(Azure.Core.HttpMessage,System.ReadOnlyMemory{Azure.Core.Pipeline.HttpPipelinePolicy})" /> and observe the <see cref="P:Azure.Core.HttpMessage.Response" /> changes after.
            </summary>
            <param name="message">The <see cref="T:Azure.Core.HttpMessage" /> this policy would be applied to.</param>
            <param name="pipeline">The set of <see cref="T:Azure.Core.Pipeline.HttpPipelinePolicy" /> to execute after current one.</param>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpPipelinePolicy.ProcessNextAsync(Azure.Core.HttpMessage,System.ReadOnlyMemory{Azure.Core.Pipeline.HttpPipelinePolicy})">
            <summary>
            Invokes the next <see cref="T:Azure.Core.Pipeline.HttpPipelinePolicy" /> in the <paramref name="pipeline" />.
            </summary>
            <param name="message">The <see cref="T:Azure.Core.HttpMessage" /> next policy would be applied to.</param>
            <param name="pipeline">The set of <see cref="T:Azure.Core.Pipeline.HttpPipelinePolicy" /> to execute after next one.</param>
            <returns>The <see cref="T:System.Threading.Tasks.ValueTask" /> representing the asynchronous operation.</returns>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpPipelinePolicy.ProcessNext(Azure.Core.HttpMessage,System.ReadOnlyMemory{Azure.Core.Pipeline.HttpPipelinePolicy})">
            <summary>
            Invokes the next <see cref="T:Azure.Core.Pipeline.HttpPipelinePolicy" /> in the <paramref name="pipeline" />.
            </summary>
            <param name="message">The <see cref="T:Azure.Core.HttpMessage" /> next policy would be applied to.</param>
            <param name="pipeline">The set of <see cref="T:Azure.Core.Pipeline.HttpPipelinePolicy" /> to execute after next one.</param>
        </member>
        <member name="T:Azure.Core.Pipeline.HttpPipelineSynchronousPolicy">
            <summary>
            Represents a <see cref="T:Azure.Core.Pipeline.HttpPipelinePolicy" /> that doesn't do any asynchronous or synchronously blocking operations.
            </summary>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpPipelineSynchronousPolicy.#ctor">
            <summary>
            Initializes a new instance of <see cref="T:Azure.Core.Pipeline.HttpPipelineSynchronousPolicy" />
            </summary>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpPipelineSynchronousPolicy.Process(Azure.Core.HttpMessage,System.ReadOnlyMemory{Azure.Core.Pipeline.HttpPipelinePolicy})">
            <summary>
            Applies the policy to the <paramref name="message" />. Implementers are expected to mutate <see cref="P:Azure.Core.HttpMessage.Request" /> before calling <see cref="M:Azure.Core.Pipeline.HttpPipelinePolicy.ProcessNextAsync(Azure.Core.HttpMessage,System.ReadOnlyMemory{Azure.Core.Pipeline.HttpPipelinePolicy})" /> and observe the <see cref="P:Azure.Core.HttpMessage.Response" /> changes after.
            </summary>
            <param name="message">The <see cref="T:Azure.Core.HttpMessage" /> this policy would be applied to.</param>
            <param name="pipeline">The set of <see cref="T:Azure.Core.Pipeline.HttpPipelinePolicy" /> to execute after current one.</param>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpPipelineSynchronousPolicy.ProcessAsync(Azure.Core.HttpMessage,System.ReadOnlyMemory{Azure.Core.Pipeline.HttpPipelinePolicy})">
            <summary>
            Applies the policy to the <paramref name="message" />. Implementers are expected to mutate <see cref="P:Azure.Core.HttpMessage.Request" /> before calling <see cref="M:Azure.Core.Pipeline.HttpPipelinePolicy.ProcessNextAsync(Azure.Core.HttpMessage,System.ReadOnlyMemory{Azure.Core.Pipeline.HttpPipelinePolicy})" /> and observe the <see cref="P:Azure.Core.HttpMessage.Response" /> changes after.
            </summary>
            <param name="message">The <see cref="T:Azure.Core.HttpMessage" /> this policy would be applied to.</param>
            <param name="pipeline">The set of <see cref="T:Azure.Core.Pipeline.HttpPipelinePolicy" /> to execute after current one.</param>
            <returns>The <see cref="T:System.Threading.Tasks.ValueTask" /> representing the asynchronous operation.</returns>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpPipelineSynchronousPolicy.OnSendingRequest(Azure.Core.HttpMessage)">
            <summary>
            Method is invoked before the request is sent.
            </summary>
            <param name="message">The <see cref="T:Azure.Core.HttpMessage" /> containing the request.</param>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpPipelineSynchronousPolicy.OnReceivedResponse(Azure.Core.HttpMessage)">
            <summary>
            Method is invoked after the response is received.
            </summary>
            <param name="message">The <see cref="T:Azure.Core.HttpMessage" /> containing the response.</param>
        </member>
        <member name="T:Azure.Core.Pipeline.HttpPipelineTransport">
            <summary>
            Represents an HTTP pipeline transport used to send HTTP requests and receive responses.
            </summary>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpPipelineTransport.Process(Azure.Core.HttpMessage)">
            <summary>
            Sends the request contained by the <paramref name="message" /> and sets the <see cref="P:Azure.Core.HttpMessage.Response" /> property to received response synchronously.
            </summary>
            <param name="message">The <see cref="T:Azure.Core.HttpMessage" /> containing request and response.</param>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpPipelineTransport.ProcessAsync(Azure.Core.HttpMessage)">
            <summary>
            Sends the request contained by the <paramref name="message" /> and sets the <see cref="P:Azure.Core.HttpMessage.Response" /> property to received response asynchronously.
            </summary>
            <param name="message">The <see cref="T:Azure.Core.HttpMessage" /> containing request and response.</param>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpPipelineTransport.CreateRequest">
            <summary>
            Creates a new transport specific instance of <see cref="T:Azure.Core.Request" />. This should not be called directly, <see cref="M:Azure.Core.Pipeline.HttpPipeline.CreateRequest" /> or
            <see cref="M:Azure.Core.Pipeline.HttpPipeline.CreateMessage" /> should be used instead.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpPipelineTransport.Create(Azure.Core.Pipeline.HttpPipelineTransportOptions)">
            <summary>
            Creates the default <see cref="T:Azure.Core.Pipeline.HttpPipelineTransport" /> based on the current environment and configuration.
            </summary>
            <param name="options"><see cref="T:Azure.Core.Pipeline.HttpPipelineTransportOptions" /> that affect how the transport is configured.</param>
            <returns></returns>
        </member>
        <member name="T:Azure.Core.Pipeline.HttpPipelineTransportOptions">
            <summary>
            Enables configuration of options for the <see cref="T:Azure.Core.Pipeline.HttpClientTransport" />
            </summary>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpPipelineTransportOptions.#ctor">
            <summary>
            Initializes an instance of <see cref="T:Azure.Core.Pipeline.HttpPipelineTransportOptions" />.
            </summary>
        </member>
        <member name="P:Azure.Core.Pipeline.HttpPipelineTransportOptions.ServerCertificateCustomValidationCallback">
            <summary>
            A delegate that validates the certificate presented by the server.
            </summary>
        </member>
        <member name="P:Azure.Core.Pipeline.HttpPipelineTransportOptions.ClientCertificates">
            <summary>
            The client certificate collection that will be configured for the transport.
            </summary>
            <value></value>
        </member>
        <member name="P:Azure.Core.Pipeline.HttpPipelineTransportOptions.IsClientRedirectEnabled">
            <summary>
            Gets or sets a value that indicates whether the redirect policy should follow redirection responses.
            </summary>
            <value>
            <c>true</c> if the redirect policy should follow redirection responses; otherwise <c>false</c>. The default value is <c>false</c>.
            </value>
        </member>
        <member name="T:Azure.Core.Pipeline.HttpWebRequestTransport">
            <summary>
            The <see cref="T:System.Net.HttpWebRequest" /> based <see cref="T:Azure.Core.Pipeline.HttpPipelineTransport" /> implementation.
            </summary>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpWebRequestTransport.#ctor">
            <summary>
            Creates a new instance of <see cref="T:Azure.Core.Pipeline.HttpWebRequestTransport" />
            </summary>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpWebRequestTransport.Process(Azure.Core.HttpMessage)">
            <summary>
            Sends the request contained by the <paramref name="message" /> and sets the <see cref="P:Azure.Core.HttpMessage.Response" /> property to received response synchronously.
            </summary>
            <param name="message">The <see cref="T:Azure.Core.HttpMessage" /> containing request and response.</param>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpWebRequestTransport.ProcessAsync(Azure.Core.HttpMessage)">
            <summary>
            Sends the request contained by the <paramref name="message" /> and sets the <see cref="P:Azure.Core.HttpMessage.Response" /> property to received response asynchronously.
            </summary>
            <param name="message">The <see cref="T:Azure.Core.HttpMessage" /> containing request and response.</param>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpWebRequestTransport.CreateRequest">
            <summary>
            Creates a new transport specific instance of <see cref="T:Azure.Core.Request" />. This should not be called directly, <see cref="M:Azure.Core.Pipeline.HttpPipeline.CreateRequest" /> or
            <see cref="M:Azure.Core.Pipeline.HttpPipeline.CreateMessage" /> should be used instead.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Azure.Core.Pipeline.DefaultRequestFailedDetailsParser">
            <summary>
            The default <see cref="T:Azure.Core.RequestFailedDetailsParser" />.
            </summary>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpEnvironmentProxy.GetProxy(System.Uri)">
            <summary>
            Gets the proxy URI. (iWebProxy interface).
            </summary>
        </member>
        <member name="M:Azure.Core.Pipeline.HttpEnvironmentProxy.IsBypassed(System.Uri)">
            <summary>
            Checks if URI is subject to proxy or not.
            </summary>
        </member>
        <member name="T:Azure.Core.Pipeline.ReadTimeoutStream">
            <summary>
            Read-only Stream that will throw a <see cref="T:System.OperationCanceledException" /> if it has to wait longer than a configurable timeout to read more data
            </summary>
        </member>
        <member name="T:Azure.Core.Pipeline.ResponseBodyPolicy">
            <summary>
            Pipeline policy to buffer response content or add a timeout to response content managed by the client
            </summary>
        </member>
        <member name="M:Azure.Core.Pipeline.ResponseBodyPolicy.ThrowIfCancellationRequestedOrTimeout(System.Threading.CancellationToken,System.Threading.CancellationToken,System.Exception,System.TimeSpan)">
            <summary>Throws a cancellation exception if cancellation has been requested via <paramref name="originalToken" /> or <paramref name="timeoutToken" />.</summary>
            <param name="originalToken">The customer provided token.</param>
            <param name="timeoutToken">The linked token that is cancelled on timeout provided token.</param>
            <param name="inner">The inner exception to use.</param>
            <param name="timeout">The timeout used for the operation.</param>
        </member>
        <member name="T:Azure.Core.Pipeline.UserAgentValueKey">
            <summary>
            Class that serves as the key for <see cref="T:Azure.Core.TelemetryDetails" /> UserAgent strings on <see cref="T:Azure.Core.HttpMessage" />.
            </summary>
        </member>
        <member name="T:Azure.Core.Pipeline.RedirectPolicy">
            <summary>
            A pipeline policy that detects a redirect response code and resends the request to the
            location specified by the response.
            </summary>
        </member>
        <member name="M:Azure.Core.Pipeline.RedirectPolicy.#ctor(System.Boolean)">
            <summary>
            Creates a new instance of the <see cref="T:Azure.Core.Pipeline.RedirectPolicy" /> class.
            </summary>
            <param name="allowAutoRedirect">Determinds whether redirects will be handled by this policy. Rather than passing false, consider using the static <see cref="P:Azure.Core.Pipeline.RedirectPolicy.Shared" /> instance instead which defaults to false.</param>
        </member>
        <member name="M:Azure.Core.Pipeline.RedirectPolicy.SetAllowAutoRedirect(Azure.Core.HttpMessage,System.Boolean)">
            <summary>
            Sets a value that indicates whether redirects will be automatically followed for this message.
            </summary>
            <param name="message"></param>
            <param name="allowAutoRedirect"></param>
        </member>
        <member name="M:Azure.Core.Pipeline.RedirectPolicy.ProcessAsync(Azure.Core.HttpMessage,System.ReadOnlyMemory{Azure.Core.Pipeline.HttpPipelinePolicy})">
            <summary>
            Applies the policy to the <paramref name="message" />. Implementers are expected to mutate <see cref="P:Azure.Core.HttpMessage.Request" /> before calling <see cref="M:Azure.Core.Pipeline.HttpPipelinePolicy.ProcessNextAsync(Azure.Core.HttpMessage,System.ReadOnlyMemory{Azure.Core.Pipeline.HttpPipelinePolicy})" /> and observe the <see cref="P:Azure.Core.HttpMessage.Response" /> changes after.
            </summary>
            <param name="message">The <see cref="T:Azure.Core.HttpMessage" /> this policy would be applied to.</param>
            <param name="pipeline">The set of <see cref="T:Azure.Core.Pipeline.HttpPipelinePolicy" /> to execute after current one.</param>
            <returns>The <see cref="T:System.Threading.Tasks.ValueTask" /> representing the asynchronous operation.</returns>
        </member>
        <member name="M:Azure.Core.Pipeline.RedirectPolicy.Process(Azure.Core.HttpMessage,System.ReadOnlyMemory{Azure.Core.Pipeline.HttpPipelinePolicy})">
            <summary>
            Applies the policy to the <paramref name="message" />. Implementers are expected to mutate <see cref="P:Azure.Core.HttpMessage.Request" /> before calling <see cref="M:Azure.Core.Pipeline.HttpPipelinePolicy.ProcessNextAsync(Azure.Core.HttpMessage,System.ReadOnlyMemory{Azure.Core.Pipeline.HttpPipelinePolicy})" /> and observe the <see cref="P:Azure.Core.HttpMessage.Response" /> changes after.
            </summary>
            <param name="message">The <see cref="T:Azure.Core.HttpMessage" /> this policy would be applied to.</param>
            <param name="pipeline">The set of <see cref="T:Azure.Core.Pipeline.HttpPipelinePolicy" /> to execute after current one.</param>
        </member>
        <member name="T:Azure.Core.Pipeline.RetryPolicy">
            <summary>
            Represents a policy that can be overriden to customize whether or not a request will be retried and how long to wait before retrying.
            </summary>
        </member>
        <member name="M:Azure.Core.Pipeline.RetryPolicy.#ctor(System.Int32,Azure.Core.DelayStrategy)">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.Core.Pipeline.RetryPolicy" /> class.
            </summary>
            <param name="maxRetries">The maximum number of retries to attempt.</param>
            <param name="delayStrategy">The delay to use for computing the interval between retry attempts.</param>
        </member>
        <member name="M:Azure.Core.Pipeline.RetryPolicy.ProcessAsync(Azure.Core.HttpMessage,System.ReadOnlyMemory{Azure.Core.Pipeline.HttpPipelinePolicy})">
            <summary>
            This method can be overriden to take full control over the retry policy. If this is overriden and the base method isn't called,
            it is the implementer's responsibility to populate the <see cref="P:Azure.Core.HttpMessage.ProcessingContext" /> property.
            This method will only be called for async methods.
            </summary>
            <param name="message">The <see cref="T:Azure.Core.HttpMessage" /> this policy would be applied to.</param>
            <param name="pipeline">The set of <see cref="T:Azure.Core.Pipeline.HttpPipelinePolicy" /> to execute after current one.</param>
            <returns>The <see cref="T:System.Threading.Tasks.ValueTask" /> representing the asynchronous operation.</returns>
        </member>
        <member name="M:Azure.Core.Pipeline.RetryPolicy.Process(Azure.Core.HttpMessage,System.ReadOnlyMemory{Azure.Core.Pipeline.HttpPipelinePolicy})">
            <summary>
            This method can be overriden to take full control over the retry policy. If this is overriden and the base method isn't called,
            it is the implementer's responsibility to populate the <see cref="P:Azure.Core.HttpMessage.ProcessingContext" /> property.
            This method will only be called for sync methods.
            </summary>
            <param name="message">The <see cref="T:Azure.Core.HttpMessage" /> this policy would be applied to.</param>
            <param name="pipeline">The set of <see cref="T:Azure.Core.Pipeline.HttpPipelinePolicy" /> to execute after current one.</param>
        </member>
        <member name="M:Azure.Core.Pipeline.RetryPolicy.ShouldRetry(Azure.Core.HttpMessage,System.Exception)">
            <summary>
            This method can be overriden to control whether a request should be retried. It will be called for any response where
            <see cref="P:Azure.Response.IsError" /> is true, or if an exception is thrown from any subsequent pipeline policies or the transport.
            This method will only be called for sync methods.
            </summary>
            <param name="message">The message containing the request and response.</param>
            <param name="exception">The exception that occurred, if any, which can be used to determine if a retry should occur.</param>
            <returns>Whether or not to retry.</returns>
        </member>
        <member name="M:Azure.Core.Pipeline.RetryPolicy.ShouldRetryAsync(Azure.Core.HttpMessage,System.Exception)">
            <summary>
            This method can be overriden to control whether a request should be retried.  It will be called for any response where
            <see cref="P:Azure.Response.IsError" /> is true, or if an exception is thrown from any subsequent pipeline policies or the transport.
            This method will only be called for async methods.
            </summary>
            <param name="message">The message containing the request and response.</param>
            <param name="exception">The exception that occurred, if any, which can be used to determine if a retry should occur.</param>
            <returns>Whether or not to retry.</returns>
        </member>
        <member name="M:Azure.Core.Pipeline.RetryPolicy.GetNextDelay(Azure.Core.HttpMessage,System.Nullable{System.TimeSpan})">
            <summary>
            This method can be overriden to control how long to delay before retrying. This method will only be called for sync methods.
            </summary>
            <param name="message">The message containing the request and response.</param>
            <param name="retryAfter">The Retry-After header value, if any, returned from the service.</param>
            <returns>The amount of time to delay before retrying.</returns>
        </member>
        <member name="M:Azure.Core.Pipeline.RetryPolicy.GetNextDelayAsync(Azure.Core.HttpMessage,System.Nullable{System.TimeSpan})">
            <summary>
            This method can be overriden to control how long to delay before retrying. This method will only be called for async methods.
            </summary>
            <param name="message">The message containing the request and response.</param>
            <param name="retryAfter">The Retry-After header value, if any, returned from the service.</param>
            <returns>The amount of time to delay before retrying.</returns>
        </member>
        <member name="M:Azure.Core.Pipeline.RetryPolicy.OnSendingRequest(Azure.Core.HttpMessage)">
            <summary>
            This method can be overridden to introduce logic before each request attempt is sent. This will run even for the first attempt.
            This method will only be called for sync methods.
            </summary>
            <param name="message">The message containing the request and response.</param>
        </member>
        <member name="M:Azure.Core.Pipeline.RetryPolicy.OnSendingRequestAsync(Azure.Core.HttpMessage)">
            <summary>
            This method can be overriden to introduce logic that runs before the request is sent. This will run even for the first attempt.
            This method will only be called for async methods.
            </summary>
            <param name="message">The message containing the request and response.</param>
        </member>
        <member name="M:Azure.Core.Pipeline.RetryPolicy.OnRequestSent(Azure.Core.HttpMessage)">
            <summary>
            This method can be overridden to introduce logic that runs after the request is sent through the pipeline and control is returned to the retry
            policy. This method will only be called for sync methods.
            </summary>
            <param name="message">The message containing the request and response.</param>
        </member>
        <member name="M:Azure.Core.Pipeline.RetryPolicy.OnRequestSentAsync(Azure.Core.HttpMessage)">
            <summary>
            This method can be overridden to introduce logic that runs after the request is sent through the pipeline and control is returned to the retry
            policy. This method will only be called for async methods.
            </summary>
            <param name="message">The message containing the request and response.</param>
        </member>
        <member name="T:Azure.Core.Pipeline.ServerCertificateCustomValidationArgs">
            <summary>
            Enables configuration of options for the <see cref="T:Azure.Core.Pipeline.HttpClientTransport" />
            </summary>
        </member>
        <member name="P:Azure.Core.Pipeline.ServerCertificateCustomValidationArgs.Certificate">
            <summary>
            The certificate used to authenticate the remote party.
            </summary>
        </member>
        <member name="P:Azure.Core.Pipeline.ServerCertificateCustomValidationArgs.CertificateAuthorityChain">
            <summary>
            The chain of certificate authorities associated with the remote certificate.
            </summary>
        </member>
        <member name="P:Azure.Core.Pipeline.ServerCertificateCustomValidationArgs.SslPolicyErrors">
            <summary>
            One or more errors associated with the remote certificate.
            </summary>
        </member>
        <member name="M:Azure.Core.Pipeline.ServerCertificateCustomValidationArgs.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2,System.Security.Cryptography.X509Certificates.X509Chain,System.Net.Security.SslPolicyErrors)">
            <summary>
            Initializes an instance of <see cref="T:Azure.Core.Pipeline.ServerCertificateCustomValidationArgs" />.
            </summary>
            <param name="certificate">The certificate</param>
            <param name="certificateAuthorityChain"></param>
            <param name="sslPolicyErrors"></param>
        </member>
        <member name="M:Azure.Core.Pipeline.ClientDiagnostics.#ctor(Azure.Core.ClientOptions,System.Nullable{System.Boolean})">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.Core.Pipeline.ClientDiagnostics" /> class.
            </summary>
            <param name="options">The customer provided client options object.</param>
            <param name="suppressNestedClientActivities">Flag controlling if <see cref="T:System.Diagnostics.Activity" />
             created by this <see cref="T:Azure.Core.Pipeline.ClientDiagnostics" /> for client method calls should be suppressed when called
             by other Azure SDK client methods.  It's recommended to set it to true for new clients; use default (null)
             for backward compatibility reasons, or set it to false to explicitly disable suppression for specific cases.
             The default value could change in the future, the flag should be only set to false if suppression for the client
             should never be enabled.</param>
        </member>
        <member name="M:Azure.Core.Pipeline.ClientDiagnostics.#ctor(System.String,System.String,Azure.Core.DiagnosticsOptions,System.Nullable{System.Boolean})">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.Core.Pipeline.ClientDiagnostics" /> class.
            </summary>
            <param name="optionsNamespace">Namespace of the client class, such as Azure.Storage or Azure.AppConfiguration.</param>
            <param name="providerNamespace">Azure Resource Provider namespace of the Azure service SDK is primarily used for.</param>
            <param name="diagnosticsOptions">The customer provided client diagnostics options.</param>
            <param name="suppressNestedClientActivities">Flag controlling if <see cref="T:System.Diagnostics.Activity" />
             created by this <see cref="T:Azure.Core.Pipeline.ClientDiagnostics" /> for client method calls should be suppressed when called
             by other Azure SDK client methods.  It's recommended to set it to true for new clients, use default (null) for old clients
             for backward compatibility reasons, or set it to false to explicitly disable suppression for specific cases.
             The default value could change in the future, the flag should be only set to false if suppression for the client
             should never be enabled.</param>
        </member>
        <member name="M:Azure.Core.Pipeline.DiagnosticScope.AddLink(System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Adds a link to the scope. This must be called before <see cref="M:Azure.Core.Pipeline.DiagnosticScope.Start" /> has been called for the DiagnosticScope.
            </summary>
            <param name="traceparent">The traceparent for the link.</param>
            <param name="tracestate">The tracestate for the link.</param>
            <param name="attributes">Optional attributes to associate with the link.</param>
        </member>
        <member name="M:Azure.Core.Pipeline.DiagnosticScope.SetTraceContext(System.String,System.String)">
            <summary>
            Sets the trace context for the current scope.
            </summary>
            <param name="traceparent">The trace parent to set for the current scope.</param>
            <param name="tracestate">The trace state to set for the current scope.</param>
        </member>
        <member name="M:Azure.Core.Pipeline.DiagnosticScope.Failed(System.Exception)">
            <summary>
            Marks the scope as failed.
            </summary>
            <param name="exception">The exception to associate with the failed scope.</param>
        </member>
        <member name="T:Azure.Core.Pipeline.DiagnosticScope.ActivityKind">
            <summary>
            Kind describes the relationship between the Activity, its parents, and its children in a Trace.
            </summary>
        </member>
        <member name="F:Azure.Core.Pipeline.DiagnosticScope.ActivityKind.Internal">
            <summary>
            Default value.
            Indicates that the Activity represents an internal operation within an application, as opposed to an operations with remote parents or children.
            </summary>
        </member>
        <member name="F:Azure.Core.Pipeline.DiagnosticScope.ActivityKind.Server">
            <summary>
            Server activity represents request incoming from external component.
            </summary>
        </member>
        <member name="F:Azure.Core.Pipeline.DiagnosticScope.ActivityKind.Client">
            <summary>
            Client activity represents outgoing request to the external component.
            </summary>
        </member>
        <member name="F:Azure.Core.Pipeline.DiagnosticScope.ActivityKind.Producer">
            <summary>
            Producer activity represents output provided to external components.
            </summary>
        </member>
        <member name="F:Azure.Core.Pipeline.DiagnosticScope.ActivityKind.Consumer">
            <summary>
            Consumer activity represents output received from an external component.
            </summary>
        </member>
        <member name="T:Azure.Core.Pipeline.ActivityExtensions">
            <summary>
            Until we can reference the 5.0 of System.Diagnostics.DiagnosticSource
            </summary>
        </member>
        <member name="T:Azure.Core.Pipeline.TaskExtensions.Enumerable`1">
            <summary>
            Both <see cref="T:Azure.Core.Pipeline.TaskExtensions.Enumerable`1" /> and <see cref="T:Azure.Core.Pipeline.TaskExtensions.Enumerator`1" /> are defined as public structs so that foreach can use duck typing
            to call <see cref="M:Azure.Core.Pipeline.TaskExtensions.Enumerable`1.GetEnumerator" /> and avoid heap memory allocation.
            Please don't delete this method and don't make these types private.
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="T:Azure.Core.BufferedReadStream">
            <summary>
            A Stream that wraps another stream and allows reading lines.
            The data is buffered in memory.
            </summary>
        </member>
        <member name="M:Azure.Core.BufferedReadStream.#ctor(System.IO.Stream,System.Int32)">
            <summary>
            Creates a new stream.
            </summary>
            <param name="inner">The stream to wrap.</param>
            <param name="bufferSize">Size of buffer in bytes.</param>
        </member>
        <member name="M:Azure.Core.BufferedReadStream.#ctor(System.IO.Stream,System.Int32,System.Buffers.ArrayPool{System.Byte})">
            <summary>
            Creates a new stream.
            </summary>
            <param name="inner">The stream to wrap.</param>
            <param name="bufferSize">Size of buffer in bytes.</param>
            <param name="bytePool">ArrayPool for the buffer.</param>
        </member>
        <member name="P:Azure.Core.BufferedReadStream.BufferedData">
            <summary>
            The currently buffered data.
            </summary>
        </member>
        <member name="P:Azure.Core.BufferedReadStream.CanRead">
            <summary>When overridden in a derived class, gets a value indicating whether the current stream supports reading.</summary><returns>true if the stream supports reading; otherwise, false.</returns><filterpriority>1</filterpriority>
        </member>
        <member name="P:Azure.Core.BufferedReadStream.CanSeek">
            <summary>When overridden in a derived class, gets a value indicating whether the current stream supports seeking.</summary><returns>true if the stream supports seeking; otherwise, false.</returns><filterpriority>1</filterpriority>
        </member>
        <member name="P:Azure.Core.BufferedReadStream.CanTimeout">
            <summary>Gets a value that determines whether the current stream can time out.</summary><returns>A value that determines whether the current stream can time out.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="P:Azure.Core.BufferedReadStream.CanWrite">
            <summary>When overridden in a derived class, gets a value indicating whether the current stream supports writing.</summary><returns>true if the stream supports writing; otherwise, false.</returns><filterpriority>1</filterpriority>
        </member>
        <member name="P:Azure.Core.BufferedReadStream.Length">
            <summary>When overridden in a derived class, gets the length in bytes of the stream.</summary><returns>A long value representing the length of the stream in bytes.</returns><exception cref="T:System.NotSupportedException">A class derived from Stream does not support seeking. </exception><exception cref="T:System.ObjectDisposedException">Methods were called after the stream was closed. </exception><filterpriority>1</filterpriority>
        </member>
        <member name="P:Azure.Core.BufferedReadStream.Position">
            <summary>When overridden in a derived class, gets or sets the position within the current stream.</summary><returns>The current position within the stream.</returns><exception cref="T:System.IO.IOException">An I/O error occurs. </exception><exception cref="T:System.NotSupportedException">The stream does not support seeking. </exception><exception cref="T:System.ObjectDisposedException">Methods were called after the stream was closed. </exception><filterpriority>1</filterpriority>
        </member>
        <member name="M:Azure.Core.BufferedReadStream.Seek(System.Int64,System.IO.SeekOrigin)">
            <summary>When overridden in a derived class, sets the position within the current stream.</summary><returns>The new position within the current stream.</returns><param name="offset">A byte offset relative to the <paramref name="origin" /> parameter. </param><param name="origin">A value of type <see cref="T:System.IO.SeekOrigin" /> indicating the reference point used to obtain the new position. </param><exception cref="T:System.IO.IOException">An I/O error occurs. </exception><exception cref="T:System.NotSupportedException">The stream does not support seeking, such as if the stream is constructed from a pipe or console output. </exception><exception cref="T:System.ObjectDisposedException">Methods were called after the stream was closed. </exception><filterpriority>1</filterpriority>
        </member>
        <member name="M:Azure.Core.BufferedReadStream.SetLength(System.Int64)">
            <summary>When overridden in a derived class, sets the length of the current stream.</summary><param name="value">The desired length of the current stream in bytes. </param><exception cref="T:System.IO.IOException">An I/O error occurs. </exception><exception cref="T:System.NotSupportedException">The stream does not support both writing and seeking, such as if the stream is constructed from a pipe or console output. </exception><exception cref="T:System.ObjectDisposedException">Methods were called after the stream was closed. </exception><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.BufferedReadStream.Dispose(System.Boolean)">
            <summary>Releases the unmanaged resources used by the <see cref="T:System.IO.Stream" /> and optionally releases the managed resources.</summary><param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
        </member>
        <member name="M:Azure.Core.BufferedReadStream.Flush">
            <summary>When overridden in a derived class, clears all buffers for this stream and causes any buffered data to be written to the underlying device.</summary><exception cref="T:System.IO.IOException">An I/O error occurs. </exception><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.BufferedReadStream.FlushAsync(System.Threading.CancellationToken)">
            <summary>Asynchronously clears all buffers for this stream, causes any buffered data to be written to the underlying device, and monitors cancellation requests.</summary><returns>A task that represents the asynchronous flush operation.</returns><param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param><exception cref="T:System.ObjectDisposedException">The stream has been disposed.</exception>
        </member>
        <member name="M:Azure.Core.BufferedReadStream.Write(System.Byte[],System.Int32,System.Int32)">
            <summary>When overridden in a derived class, writes a sequence of bytes to the current stream and advances the current position within this stream by the number of bytes written.</summary><param name="buffer">An array of bytes. This method copies <paramref name="count" /> bytes from <paramref name="buffer" /> to the current stream. </param><param name="offset">The zero-based byte offset in <paramref name="buffer" /> at which to begin copying bytes to the current stream. </param><param name="count">The number of bytes to be written to the current stream. </param><exception cref="T:System.ArgumentException">The sum of <paramref name="offset" /> and <paramref name="count" /> is greater than the buffer length.</exception><exception cref="T:System.ArgumentNullException"><paramref name="buffer" />  is null.</exception><exception cref="T:System.ArgumentOutOfRangeException"><paramref name="offset" /> or <paramref name="count" /> is negative.</exception><exception cref="T:System.IO.IOException">An I/O error occured, such as the specified file cannot be found.</exception><exception cref="T:System.NotSupportedException">The stream does not support writing.</exception><exception cref="T:System.ObjectDisposedException"><see cref="M:System.IO.Stream.Write(System.Byte[],System.Int32,System.Int32)" /> was called after the stream was closed.</exception><filterpriority>1</filterpriority>
        </member>
        <member name="M:Azure.Core.BufferedReadStream.WriteAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>Asynchronously writes a sequence of bytes to the current stream, advances the current position within this stream by the number of bytes written, and monitors cancellation requests.</summary><returns>A task that represents the asynchronous write operation.</returns><param name="buffer">The buffer to write data from.</param><param name="offset">The zero-based byte offset in <paramref name="buffer" /> from which to begin copying bytes to the stream.</param><param name="count">The maximum number of bytes to write.</param><param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param><exception cref="T:System.ArgumentNullException"><paramref name="buffer" /> is null.</exception><exception cref="T:System.ArgumentOutOfRangeException"><paramref name="offset" /> or <paramref name="count" /> is negative.</exception><exception cref="T:System.ArgumentException">The sum of <paramref name="offset" /> and <paramref name="count" /> is larger than the buffer length.</exception><exception cref="T:System.NotSupportedException">The stream does not support writing.</exception><exception cref="T:System.ObjectDisposedException">The stream has been disposed.</exception><exception cref="T:System.InvalidOperationException">The stream is currently in use by a previous write operation. </exception>
        </member>
        <member name="M:Azure.Core.BufferedReadStream.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>When overridden in a derived class, reads a sequence of bytes from the current stream and advances the position within the stream by the number of bytes read.</summary><returns>The total number of bytes read into the buffer. This can be less than the number of bytes requested if that many bytes are not currently available, or zero (0) if the end of the stream has been reached.</returns><param name="buffer">An array of bytes. When this method returns, the buffer contains the specified byte array with the values between <paramref name="offset" /> and (<paramref name="offset" /> + <paramref name="count" /> - 1) replaced by the bytes read from the current source. </param><param name="offset">The zero-based byte offset in <paramref name="buffer" /> at which to begin storing the data read from the current stream. </param><param name="count">The maximum number of bytes to be read from the current stream. </param><exception cref="T:System.ArgumentException">The sum of <paramref name="offset" /> and <paramref name="count" /> is larger than the buffer length. </exception><exception cref="T:System.ArgumentNullException"><paramref name="buffer" /> is null. </exception><exception cref="T:System.ArgumentOutOfRangeException"><paramref name="offset" /> or <paramref name="count" /> is negative. </exception><exception cref="T:System.IO.IOException">An I/O error occurs. </exception><exception cref="T:System.NotSupportedException">The stream does not support reading. </exception><exception cref="T:System.ObjectDisposedException">Methods were called after the stream was closed. </exception><filterpriority>1</filterpriority>
        </member>
        <member name="M:Azure.Core.BufferedReadStream.ReadAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>Asynchronously reads a sequence of bytes from the current stream, advances the position within the stream by the number of bytes read, and monitors cancellation requests.</summary><returns>A task that represents the asynchronous read operation. The value of the <paramref name="TResult" /> parameter contains the total number of bytes read into the buffer. The result value can be less than the number of bytes requested if the number of bytes currently available is less than the requested number, or it can be 0 (zero) if the end of the stream has been reached. </returns><param name="buffer">The buffer to write the data into.</param><param name="offset">The byte offset in <paramref name="buffer" /> at which to begin writing data from the stream.</param><param name="count">The maximum number of bytes to read.</param><param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param><exception cref="T:System.ArgumentNullException"><paramref name="buffer" /> is null.</exception><exception cref="T:System.ArgumentOutOfRangeException"><paramref name="offset" /> or <paramref name="count" /> is negative.</exception><exception cref="T:System.ArgumentException">The sum of <paramref name="offset" /> and <paramref name="count" /> is larger than the buffer length.</exception><exception cref="T:System.NotSupportedException">The stream does not support reading.</exception><exception cref="T:System.ObjectDisposedException">The stream has been disposed.</exception><exception cref="T:System.InvalidOperationException">The stream is currently in use by a previous read operation. </exception>
        </member>
        <member name="M:Azure.Core.BufferedReadStream.EnsureBuffered">
            <summary>
            Ensures that the buffer is not empty.
            </summary>
            <returns>Returns <c>true</c> if the buffer is not empty; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Azure.Core.BufferedReadStream.EnsureBufferedAsync(System.Threading.CancellationToken)">
            <summary>
            Ensures that the buffer is not empty.
            </summary>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>Returns <c>true</c> if the buffer is not empty; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Azure.Core.BufferedReadStream.EnsureBuffered(System.Int32)">
            <summary>
            Ensures that a minimum amount of buffered data is available.
            </summary>
            <param name="minCount">Minimum amount of buffered data.</param>
            <returns>Returns <c>true</c> if the minimum amount of buffered data is available; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Azure.Core.BufferedReadStream.EnsureBufferedAsync(System.Int32,System.Threading.CancellationToken)">
            <summary>
            Ensures that a minimum amount of buffered data is available.
            </summary>
            <param name="minCount">Minimum amount of buffered data.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>Returns <c>true</c> if the minimum amount of buffered data is available; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Azure.Core.BufferedReadStream.ReadLine(System.Int32)">
            <summary>
            Reads a line. A line is defined as a sequence of characters followed by
            a carriage return immediately followed by a line feed. The resulting string does not
            contain the terminating carriage return and line feed.
            </summary>
            <param name="lengthLimit">Maximum allowed line length.</param>
            <returns>A line.</returns>
        </member>
        <member name="M:Azure.Core.BufferedReadStream.ReadLineAsync(System.Int32,System.Threading.CancellationToken)">
            <summary>
            Reads a line. A line is defined as a sequence of characters followed by
            a carriage return immediately followed by a line feed. The resulting string does not
            contain the terminating carriage return and line feed.
            </summary>
            <param name="lengthLimit">Maximum allowed line length.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>A line.</returns>
        </member>
        <member name="P:Azure.Core.MultipartReader.HeadersCountLimit">
            <summary>
            The limit for the number of headers to read.
            </summary>
        </member>
        <member name="P:Azure.Core.MultipartReader.HeadersLengthLimit">
            <summary>
            The combined size limit for headers per multipart section.
            </summary>
        </member>
        <member name="P:Azure.Core.MultipartReader.BodyLengthLimit">
            <summary>
            The optional limit for the total response body length.
            </summary>
        </member>
        <member name="P:Azure.Core.MultipartReader.ExpectBoundariesWithCRLF">
            <summary>
            Sets whether the reader will always expect CRLF around boundaries.
            </summary>
            <value></value>
        </member>
        <member name="M:Azure.Core.MultipartReaderStream.#ctor(Azure.Core.BufferedReadStream,Azure.Core.MultipartBoundary)">
            <summary>
            Creates a stream that reads until it reaches the given boundary pattern.
            </summary>
            <param name="stream">The <see cref="T:Azure.Core.BufferedReadStream" />.</param>
            <param name="boundary">The boundary pattern to use.</param>
        </member>
        <member name="M:Azure.Core.MultipartReaderStream.#ctor(Azure.Core.BufferedReadStream,Azure.Core.MultipartBoundary,System.Buffers.ArrayPool{System.Byte})">
            <summary>
            Creates a stream that reads until it reaches the given boundary pattern.
            </summary>
            <param name="stream">The <see cref="T:Azure.Core.BufferedReadStream" />.</param>
            <param name="boundary">The boundary pattern to use.</param>
            <param name="bytePool">The ArrayPool pool to use for temporary byte arrays.</param>
        </member>
        <member name="P:Azure.Core.MultipartSection.Body">
            <summary>
            Gets or sets the body.
            </summary>
        </member>
        <member name="P:Azure.Core.MultipartSection.BaseStreamOffset">
            <summary>
            The position where the body starts in the total multipart body.
            This may not be available if the total multipart body is not seekable.
            </summary>
        </member>
        <member name="T:Azure.Core.MultipartResponse">
            <summary>
            Provides support for creating and parsing multipart/mixed content.
            This is implementing a couple of layered standards as mentioned at
            https://docs.microsoft.com/en-us/rest/api/storageservices/blob-batch and
            https://docs.microsoft.com/en-us/rest/api/storageservices/performing-entity-group-transactions
            including https://www.odata.org/documentation/odata-version-3-0/batch-processing/
            and https://www.ietf.org/rfc/rfc2046.txt.
            </summary>
        </member>
        <member name="M:Azure.Core.MultipartResponse.Parse(Azure.Response,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Parse a multipart/mixed response body into several responses.
            </summary>
            <param name="response">The response containing multi-part content.</param>
            <param name="expectCrLf">Controls whether the parser will expect all multi-part boundaries to use CRLF line breaks. This should be true unless more permissive line break parsing is required.</param>
            <param name="cancellationToken">
            Optional <see cref="T:System.Threading.CancellationToken" /> to propagate notifications
            that the operation should be cancelled.
            </param>
            <returns>The parsed <see cref="T:Azure.Response" />s.</returns>
        </member>
        <member name="M:Azure.Core.MultipartResponse.ParseAsync(Azure.Response,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Parse a multipart/mixed response body into several responses.
            </summary>
            <param name="response">The response containing multi-part content.</param>
            <param name="expectCrLf">Controls whether the parser will expect all multi-part boundaries to use CRLF line breaks. This should be true unless more permissive line break parsing is required.</param>
            <param name="cancellationToken">
            Optional <see cref="T:System.Threading.CancellationToken" /> to propagate notifications
            that the operation should be cancelled.
            </param>
            <returns>The parsed <see cref="T:Azure.Response" />s.</returns>
        </member>
        <member name="M:Azure.Core.MultipartResponse.ParseAsync(Azure.Response,System.Boolean,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Parse a multipart/mixed response body into several responses.
            </summary>
            <param name="parentResponse">The response containing multi-part content.</param>
            <param name="expectBoundariesWithCRLF">Controls whether the parser will expect all multi-part boundaries to use CRLF line breaks. This should be true unless more permissive line break parsing is required.</param>
            <param name="async">
            Whether to invoke the operation asynchronously.
            </param>
            <param name="cancellationToken">
            Optional <see cref="T:System.Threading.CancellationToken" /> to propagate notifications
            that the operation should be cancelled.
            </param>
            <returns>The parsed <see cref="T:Azure.Response" />s.</returns>
        </member>
        <member name="M:Azure.Core.MultipartResponse.ReadLineAsync(Azure.Core.BufferedReadStream,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Read the next line of text.
            </summary>
            <param name="stream">The stream to read from.</param>
            <param name="async">
            Whether to invoke the operation asynchronously.
            </param>
            <param name="cancellationToken">
            Optional <see cref="T:System.Threading.CancellationToken" /> to propagate notifications
            that the operation should be cancelled.
            </param>
            <returns>The next line of text.</returns>
        </member>
        <member name="M:Azure.Core.MultipartResponse.GetNextSectionAsync(Azure.Core.MultipartReader,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Read the next multipart section.
            </summary>
            <param name="reader">The reader to parse with.</param>
            <param name="async">
            Whether to invoke the operation asynchronously.
            </param>
            <param name="cancellationToken">
            Optional <see cref="T:System.Threading.CancellationToken" /> to propagate notifications
            that the operation should be cancelled.
            </param>
            <returns>The next multipart section.</returns>
        </member>
        <member name="T:Azure.Core.RequestFailedDetailsParser">
            <summary>
            Controls how error response content should be parsed.
            </summary>
        </member>
        <member name="M:Azure.Core.RequestFailedDetailsParser.TryParse(Azure.Response,Azure.ResponseError@,System.Collections.Generic.IDictionary{System.String,System.String}@)">
            <summary>
            Parses the error details from the provided <see cref="T:Azure.Response" />.
            </summary>
            <param name="response">The <see cref="T:Azure.Response" /> to parse. The <see cref="P:Azure.Response.ContentStream" /> will already be buffered.</param>
            <param name="error">The <see cref="T:Azure.ResponseError" /> describing the parsed error details.</param>
            <param name="data">Data to be applied to the <see cref="P:System.Exception.Data" /> property.</param>
            <returns><c>true</c> if successful, otherwise <c>false</c>.</returns>
        </member>
        <member name="T:Azure.Core.Request">
            <summary>
            Represents an HTTP request. Use <see cref="M:Azure.Core.Pipeline.HttpPipeline.CreateMessage" /> or <see cref="M:Azure.Core.Pipeline.HttpPipeline.CreateRequest" /> to create an instance.
            </summary>
        </member>
        <member name="P:Azure.Core.Request.Uri">
            <summary>
            Gets or sets and instance of <see cref="T:Azure.Core.RequestUriBuilder" /> used to create the Uri.
            </summary>
        </member>
        <member name="P:Azure.Core.Request.Method">
            <summary>
            Gets or sets the request HTTP method.
            </summary>
        </member>
        <member name="P:Azure.Core.Request.Content">
            <summary>
            Gets or sets the request content.
            </summary>
        </member>
        <member name="M:Azure.Core.Request.AddHeader(System.String,System.String)">
            <summary>
            Adds a header value to the header collection.
            </summary>
            <param name="name">The header name.</param>
            <param name="value">The header value.</param>
        </member>
        <member name="M:Azure.Core.Request.TryGetHeader(System.String,System.String@)">
            <summary>
            Returns header value if the header is stored in the collection. If the header has multiple values they are going to be joined with a comma.
            </summary>
            <param name="name">The header name.</param>
            <param name="value">The reference to populate with value.</param>
            <returns><c>true</c> if the specified header is stored in the collection, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Azure.Core.Request.TryGetHeaderValues(System.String,System.Collections.Generic.IEnumerable{System.String}@)">
            <summary>
            Returns header values if the header is stored in the collection.
            </summary>
            <param name="name">The header name.</param>
            <param name="values">The reference to populate with values.</param>
            <returns><c>true</c> if the specified header is stored in the collection, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Azure.Core.Request.ContainsHeader(System.String)">
            <summary>
            Returns <c>true</c> if the header is stored in the collection.
            </summary>
            <param name="name">The header name.</param>
            <returns><c>true</c> if the specified header is stored in the collection, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Azure.Core.Request.SetHeader(System.String,System.String)">
            <summary>
            Sets a header value the header collection.
            </summary>
            <param name="name">The header name.</param>
            <param name="value">The header value.</param>
        </member>
        <member name="M:Azure.Core.Request.RemoveHeader(System.String)">
            <summary>
            Removes the header from the collection.
            </summary>
            <param name="name">The header name.</param>
        </member>
        <member name="M:Azure.Core.Request.EnumerateHeaders">
            <summary>
            Returns an iterator enumerating <see cref="T:Azure.Core.HttpHeader" /> in the request.
            </summary>
            <returns>The <see cref="T:System.Collections.Generic.IEnumerable`1" /> enumerating <see cref="T:Azure.Core.HttpHeader" /> in the response.</returns>
        </member>
        <member name="P:Azure.Core.Request.ClientRequestId">
            <summary>
            Gets or sets the client request id that was sent to the server as <c>x-ms-client-request-id</c> headers.
            </summary>
        </member>
        <member name="P:Azure.Core.Request.Headers">
            <summary>
            Gets the response HTTP headers.
            </summary>
        </member>
        <member name="M:Azure.Core.Request.Dispose">
            <summary>
            Frees resources held by this <see cref="T:Azure.Response" /> instance.
            </summary>
        </member>
        <member name="T:Azure.Core.RequestContent">
            <summary>
            Represents the content sent as part of the <see cref="T:Azure.Core.Request" />.
            </summary>
        </member>
        <member name="M:Azure.Core.RequestContent.Create(System.IO.Stream)">
            <summary>
            Creates an instance of <see cref="T:Azure.Core.RequestContent" /> that wraps a <see cref="T:System.IO.Stream" />.
            </summary>
            <param name="stream">The <see cref="T:System.IO.Stream" /> to use.</param>
            <returns>An instance of <see cref="T:Azure.Core.RequestContent" /> that wraps a <see cref="T:System.IO.Stream" />.</returns>
        </member>
        <member name="M:Azure.Core.RequestContent.Create(System.Byte[])">
            <summary>
            Creates an instance of <see cref="T:Azure.Core.RequestContent" /> that wraps an <see cref="T:System.Array" />of <see cref="T:System.Byte" />.
            </summary>
            <param name="bytes">The <see cref="T:System.Array" />of <see cref="T:System.Byte" /> to use.</param>
            <returns>An instance of <see cref="T:Azure.Core.RequestContent" /> that wraps provided <see cref="T:System.Array" />of <see cref="T:System.Byte" />.</returns>
        </member>
        <member name="M:Azure.Core.RequestContent.Create(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Creates an instance of <see cref="T:Azure.Core.RequestContent" /> that wraps an <see cref="T:System.Array" />of <see cref="T:System.Byte" />.
            </summary>
            <param name="bytes">The <see cref="T:System.Array" />of <see cref="T:System.Byte" /> to use.</param>
            <param name="index">The offset in <paramref name="bytes" /> to start from.</param>
            <param name="length">The length of the segment to use.</param>
            <returns>An instance of <see cref="T:Azure.Core.RequestContent" /> that wraps provided <see cref="T:System.Array" />of <see cref="T:System.Byte" />.</returns>
        </member>
        <member name="M:Azure.Core.RequestContent.Create(System.ReadOnlyMemory{System.Byte})">
            <summary>
            Creates an instance of <see cref="T:Azure.Core.RequestContent" /> that wraps a <see cref="T:System.IO.Stream" />.
            </summary>
            <param name="bytes">The <see cref="T:System.ReadOnlyMemory`1" /> to use.</param>
            <returns>An instance of <see cref="T:Azure.Core.RequestContent" /> that wraps a <see cref="T:System.ReadOnlyMemory`1" />.</returns>
        </member>
        <member name="M:Azure.Core.RequestContent.Create(System.Buffers.ReadOnlySequence{System.Byte})">
            <summary>
            Creates an instance of <see cref="T:Azure.Core.RequestContent" /> that wraps a <see cref="T:System.Buffers.ReadOnlySequence`1" />.
            </summary>
            <param name="bytes">The <see cref="T:System.Buffers.ReadOnlySequence`1" /> to use.</param>
            <returns>An instance of <see cref="T:Azure.Core.RequestContent" /> that wraps a <see cref="T:System.Buffers.ReadOnlySequence`1" />.</returns>
        </member>
        <member name="M:Azure.Core.RequestContent.Create(System.String)">
            <summary>
            Creates a RequestContent representing the UTF-8 Encoding of the given <see cref="T:System.String" />/
            </summary>
            <param name="content">The <see cref="T:System.String" /> to use.</param>
            <returns>An instance of <see cref="T:Azure.Core.RequestContent" /> that wraps a <see cref="T:System.String" />.</returns>
            <remarks>The returned content represents the UTF-8 Encoding of the given string.</remarks>
        </member>
        <member name="M:Azure.Core.RequestContent.Create(System.BinaryData)">
            <summary>
            Creates an instance of <see cref="T:Azure.Core.RequestContent" /> that wraps a <see cref="T:System.BinaryData" />.
            </summary>
            <param name="content">The <see cref="T:System.BinaryData" /> to use.</param>
            <returns>An instance of <see cref="T:Azure.Core.RequestContent" /> that wraps a <see cref="T:System.BinaryData" />.</returns>
        </member>
        <member name="M:Azure.Core.RequestContent.Create(Azure.Core.Serialization.DynamicData)">
            <summary>
            Creates an instance of <see cref="T:Azure.Core.RequestContent" /> that wraps a <see cref="T:Azure.Core.Serialization.DynamicData" />.
            </summary>
            <param name="content">The <see cref="T:Azure.Core.Serialization.DynamicData" /> to use.</param>
            <returns>An instance of <see cref="T:Azure.Core.RequestContent" /> that wraps a <see cref="T:Azure.Core.Serialization.DynamicData" />.</returns>
        </member>
        <member name="M:Azure.Core.RequestContent.Create(System.Object)">
            <summary>
            Creates an instance of <see cref="T:Azure.Core.RequestContent" /> that wraps a serialized version of an object.
            </summary>
            <param name="serializable">The <see cref="T:System.Object" /> to serialize.</param>
            <returns>An instance of <see cref="T:Azure.Core.RequestContent" /> that wraps a serialized version of the object.</returns>
        </member>
        <member name="M:Azure.Core.RequestContent.Create(System.Object,Azure.Core.Serialization.ObjectSerializer)">
            <summary>
            Creates an instance of <see cref="T:Azure.Core.RequestContent" /> that wraps a serialized version of an object.
            </summary>
            <param name="serializable">The <see cref="T:System.Object" /> to serialize.</param>
            <param name="serializer">The <see cref="T:Azure.Core.Serialization.ObjectSerializer" /> to use to convert the object to bytes. If not provided, <see cref="T:Azure.Core.Serialization.JsonObjectSerializer" /> is used.</param>
            <returns>An instance of <see cref="T:Azure.Core.RequestContent" /> that wraps a serialized version of the object.</returns>
        </member>
        <member name="M:Azure.Core.RequestContent.Create(System.Object,Azure.Core.Serialization.JsonPropertyNames,System.String)">
            <summary>
            Creates an instance of <see cref="T:Azure.Core.RequestContent" /> that wraps a serialized version of an object.
            </summary>
            <param name="serializable">The <see cref="T:System.Object" /> to serialize.</param>
            <param name="propertyNameFormat">The format to use for property names in the serialized content.</param>
            <param name="dateTimeFormat">The format to use for DateTime and DateTimeOffset values in the serialized content.</param>
            <returns>An instance of <see cref="T:Azure.Core.RequestContent" /> that wraps a serialized version of the object.</returns>
        </member>
        <member name="M:Azure.Core.RequestContent.op_Implicit(System.String)~Azure.Core.RequestContent">
            <summary>
            Creates a RequestContent representing the UTF-8 Encoding of the given <see cref="T:System.String" />.
            </summary>
            <param name="content">The <see cref="T:System.String" /> to use.</param>
        </member>
        <member name="M:Azure.Core.RequestContent.op_Implicit(System.BinaryData)~Azure.Core.RequestContent">
            <summary>
            Creates a RequestContent that wraps a <see cref="T:System.BinaryData" />.
            </summary>
            <param name="content">The <see cref="T:System.BinaryData" /> to use.</param>
        </member>
        <member name="M:Azure.Core.RequestContent.op_Implicit(Azure.Core.Serialization.DynamicData)~Azure.Core.RequestContent">
            <summary>
            Creates a RequestContent that wraps a <see cref="T:Azure.Core.Serialization.DynamicData" />.
            </summary>
            <param name="content">The <see cref="T:Azure.Core.Serialization.DynamicData" /> to use.</param>
        </member>
        <member name="M:Azure.Core.RequestContent.WriteToAsync(System.IO.Stream,System.Threading.CancellationToken)">
            <summary>
            Writes contents of this object to an instance of <see cref="T:System.IO.Stream" />.
            </summary>
            <param name="stream">The stream to write to.</param>
            <param name="cancellation">To cancellation token to use.</param>
        </member>
        <member name="M:Azure.Core.RequestContent.WriteTo(System.IO.Stream,System.Threading.CancellationToken)">
            <summary>
            Writes contents of this object to an instance of <see cref="T:System.IO.Stream" />.
            </summary>
            <param name="stream">The stream to write to.</param>
            <param name="cancellation">To cancellation token to use.</param>
        </member>
        <member name="M:Azure.Core.RequestContent.TryComputeLength(System.Int64@)">
            <summary>
            Attempts to compute the length of the underlying content, if available.
            </summary>
            <param name="length">The length of the underlying data.</param>
        </member>
        <member name="M:Azure.Core.RequestContent.Dispose">
            <summary>
            Frees resources held by the <see cref="T:Azure.Core.RequestContent" /> object.
            </summary>
        </member>
        <member name="T:Azure.Core.RequestHeaders">
            <summary>
            Headers to be sent as part of the <see cref="T:Azure.Core.Request" />.
            </summary>
        </member>
        <member name="M:Azure.Core.RequestHeaders.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the <see cref="T:Azure.Core.RequestHeaders" />.
            </summary>
            <returns>A <see cref="T:System.Collections.Generic.IEnumerator`1" /> for the <see cref="T:Azure.Core.RequestHeaders" />.</returns>
        </member>
        <member name="M:Azure.Core.RequestHeaders.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the <see cref="T:Azure.Core.RequestHeaders" />.
            </summary>
            <returns>A <see cref="T:System.Collections.IEnumerator" /> for the <see cref="T:Azure.Core.RequestHeaders" />.</returns>
        </member>
        <member name="M:Azure.Core.RequestHeaders.Add(Azure.Core.HttpHeader)">
            <summary>
            Adds the <see cref="T:Azure.Core.HttpHeader" /> instance to the collection.
            </summary>
            <param name="header">The header to add.</param>
        </member>
        <member name="M:Azure.Core.RequestHeaders.Add(System.String,System.String)">
            <summary>
            Adds the header to the collection. If a header with this name already exist adds an additional value to the header values.
            </summary>
            <param name="name">The header name.</param>
            <param name="value">The header value.</param>
        </member>
        <member name="M:Azure.Core.RequestHeaders.TryGetValue(System.String,System.String@)">
            <summary>
            Returns header value if the headers is stored in the collection. If the header has multiple values they are going to be joined with a comma.
            </summary>
            <param name="name">The header name.</param>
            <param name="value">The reference to populate with value.</param>
            <returns><c>true</c> if the specified header is stored in the collection, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Azure.Core.RequestHeaders.TryGetValues(System.String,System.Collections.Generic.IEnumerable{System.String}@)">
            <summary>
            Returns header values if the header is stored in the collection.
            </summary>
            <param name="name">The header name.</param>
            <param name="values">The reference to populate with values.</param>
            <returns><c>true</c> if the specified header is stored in the collection, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Azure.Core.RequestHeaders.Contains(System.String)">
            <summary>
            Returns <c>true</c> if the headers is stored in the collection.
            </summary>
            <param name="name">The header name.</param>
            <returns><c>true</c> if the specified header is stored in the collection, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Azure.Core.RequestHeaders.SetValue(System.String,System.String)">
            <summary>
            Sets the header value name. If a header with this name already exist replaces it's value.
            </summary>
            <param name="name">The header name.</param>
            <param name="value">The header value.</param>
        </member>
        <member name="M:Azure.Core.RequestHeaders.Remove(System.String)">
            <summary>
            Removes the header from the collection.
            </summary>
            <param name="name">The header name.</param>
            <returns><c>true</c> if the header existed, otherwise <c>false</c>.</returns>
        </member>
        <member name="T:Azure.Core.RequestMethod">
            <summary>
            Represents HTTP methods sent as part of a <see cref="T:Azure.Core.Request" />.
            </summary>
        </member>
        <member name="P:Azure.Core.RequestMethod.Method">
            <summary>
            Gets the HTTP method.
            </summary>
        </member>
        <member name="P:Azure.Core.RequestMethod.Get">
            <summary>
            Gets <see cref="T:Azure.Core.RequestMethod" /> instance for GET method.
            </summary>
        </member>
        <member name="P:Azure.Core.RequestMethod.Post">
            <summary>
            Gets <see cref="T:Azure.Core.RequestMethod" /> instance for POST method.
            </summary>
        </member>
        <member name="P:Azure.Core.RequestMethod.Put">
            <summary>
            Gets <see cref="T:Azure.Core.RequestMethod" /> instance for PUT method.
            </summary>
        </member>
        <member name="P:Azure.Core.RequestMethod.Patch">
            <summary>
            Gets <see cref="T:Azure.Core.RequestMethod" /> instance for PATCH method.
            </summary>
        </member>
        <member name="P:Azure.Core.RequestMethod.Delete">
            <summary>
            Gets <see cref="T:Azure.Core.RequestMethod" /> instance for DELETE method.
            </summary>
        </member>
        <member name="P:Azure.Core.RequestMethod.Head">
            <summary>
            Gets <see cref="T:Azure.Core.RequestMethod" /> instance for HEAD method.
            </summary>
        </member>
        <member name="P:Azure.Core.RequestMethod.Options">
            <summary>
            Gets <see cref="T:Azure.Core.RequestMethod" /> instance for OPTIONS method.
            </summary>
        </member>
        <member name="P:Azure.Core.RequestMethod.Trace">
            <summary>
            Gets <see cref="T:Azure.Core.RequestMethod" /> instance for TRACE method.
            </summary>
        </member>
        <member name="M:Azure.Core.RequestMethod.#ctor(System.String)">
            <summary>
            Creates an instance of <see cref="T:Azure.Core.RequestMethod" /> with provided method. Method must be all uppercase.
            Prefer <see cref="M:Azure.Core.RequestMethod.Parse(System.String)" /> if <paramref name="method" /> can be one of predefined method names.
            </summary>
            <param name="method">The method to use.</param>
        </member>
        <member name="M:Azure.Core.RequestMethod.Parse(System.String)">
            <summary>
            Parses string to it's <see cref="T:Azure.Core.RequestMethod" /> representation.
            </summary>
            <param name="method">The method string to parse.</param>
        </member>
        <member name="M:Azure.Core.RequestMethod.Equals(Azure.Core.RequestMethod)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary><returns>true if the current object is equal to the <paramref name="other" /> parameter; otherwise, false.</returns><param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:Azure.Core.RequestMethod.Equals(System.Object)">
            <summary>Indicates whether this instance and a specified object are equal.</summary><returns>true if <paramref name="obj" /> and this instance are the same type and represent the same value; otherwise, false. </returns><param name="obj">The object to compare with the current instance. </param><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.RequestMethod.GetHashCode">
            <summary>Returns the hash code for this instance.</summary><returns>A 32-bit signed integer that is the hash code for this instance.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.RequestMethod.op_Equality(Azure.Core.RequestMethod,Azure.Core.RequestMethod)">
            <summary>
            Compares equality of two <see cref="T:Azure.Core.RequestMethod" /> instances.
            </summary>
            <param name="left">The method to compare.</param>
            <param name="right">The method to compare against.</param>
            <returns><c>true</c> if <see cref="P:Azure.Core.RequestMethod.Method" /> values are equal for <paramref name="left" /> and <paramref name="right" />, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Azure.Core.RequestMethod.op_Inequality(Azure.Core.RequestMethod,Azure.Core.RequestMethod)">
            <summary>
            Compares inequality of two <see cref="T:Azure.Core.RequestMethod" /> instances.
            </summary>
            <param name="left">The method to compare.</param>
            <param name="right">The method to compare against.</param>
            <returns><c>true</c> if <see cref="P:Azure.Core.RequestMethod.Method" /> values are equal for <paramref name="left" /> and <paramref name="right" />, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Azure.Core.RequestMethod.ToString">
            <summary>Returns the fully qualified type name of this instance.</summary><returns>A <see cref="T:System.String" /> containing a fully qualified type name.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="T:Azure.Core.RequestUriBuilder">
            <summary>
            Provides a custom builder for Uniform Resource Identifiers (URIs) and modifies URIs for the <see cref="T:System.Uri" /> class.
            </summary>
        </member>
        <member name="P:Azure.Core.RequestUriBuilder.Scheme">
            <summary>
            Gets or sets the scheme name of the URI.
            </summary>
        </member>
        <member name="P:Azure.Core.RequestUriBuilder.Host">
            <summary>
            Gets or sets the Domain Name System (DNS) host name or IP address of a server.
            </summary>
        </member>
        <member name="P:Azure.Core.RequestUriBuilder.Port">
            <summary>
            Gets or sets the port number of the URI.
            </summary>
        </member>
        <member name="P:Azure.Core.RequestUriBuilder.Query">
            <summary>
            Gets or sets any query information included in the URI.
            </summary>
        </member>
        <member name="P:Azure.Core.RequestUriBuilder.Path">
            <summary>
            Gets or sets the path to the resource referenced by the URI.
            </summary>
        </member>
        <member name="P:Azure.Core.RequestUriBuilder.HasPath">
            <summary> Gets whether or not this instance of <see cref="T:Azure.Core.RequestUriBuilder" /> has a path. </summary>
        </member>
        <member name="P:Azure.Core.RequestUriBuilder.HasQuery">
            <summary> Gets whether or not this instance of <see cref="T:Azure.Core.RequestUriBuilder" /> has a query. </summary>
        </member>
        <member name="P:Azure.Core.RequestUriBuilder.PathAndQuery">
            <summary>
            Gets the path and query string to the resource referenced by the URI.
            </summary>
        </member>
        <member name="M:Azure.Core.RequestUriBuilder.Reset(System.Uri)">
            <summary>
            Replaces values inside this instance with values provided in the <paramref name="value" /> parameter.
            </summary>
            <param name="value">The <see cref="T:System.Uri" /> instance to get values from.</param>
        </member>
        <member name="M:Azure.Core.RequestUriBuilder.ToUri">
            <summary>
            Gets the <see cref="T:System.Uri" /> instance constructed by the specified <see cref="T:Azure.Core.RequestUriBuilder" /> instance.
            </summary>
            <returns>
            A <see cref="T:System.Uri" /> that contains the URI constructed by the <see cref="T:Azure.Core.RequestUriBuilder" />.
            </returns>
        </member>
        <member name="M:Azure.Core.RequestUriBuilder.AppendQuery(System.String,System.String)">
            <summary>
            Appends a query parameter adding separator if required. Escapes the value.
            </summary>
            <param name="name">The name of parameter.</param>
            <param name="value">The value of parameter.</param>
        </member>
        <member name="M:Azure.Core.RequestUriBuilder.AppendQuery(System.String,System.String,System.Boolean)">
            <summary>
            Appends a query parameter adding separator if required.
            </summary>
            <param name="name">The name of parameter.</param>
            <param name="value">The value of parameter.</param>
            <param name="escapeValue">Whether value should be escaped.</param>
        </member>
        <member name="M:Azure.Core.RequestUriBuilder.AppendQuery(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Char},System.Boolean)">
            <summary>
            Appends a query parameter adding separator if required.
            </summary>
            <param name="name">The name of parameter.</param>
            <param name="value">The value of parameter.</param>
            <param name="escapeValue">Whether value should be escaped.</param>
        </member>
        <member name="M:Azure.Core.RequestUriBuilder.AppendPath(System.String)">
            <summary>
            Escapes and appends the <paramref name="value" /> to <see cref="P:Azure.Core.RequestUriBuilder.Path" /> without adding path separator.
            Path segments and any other characters will be escaped, e.g. ":" will be escaped as "%3a".
            </summary>
            <param name="value">The value to escape and append.</param>
        </member>
        <member name="M:Azure.Core.RequestUriBuilder.AppendPath(System.String,System.Boolean)">
            <summary>
            Optionally escapes and appends the <paramref name="value" /> to <see cref="P:Azure.Core.RequestUriBuilder.Path" /> without adding path separator.
            If <paramref name="escape" /> is true, path segments and any other characters will be escaped, e.g. ":" will be escaped as "%3a".
            </summary>
            <param name="value">The value to optionally escape and append.</param>
            <param name="escape">Whether value should be escaped.</param>
        </member>
        <member name="M:Azure.Core.RequestUriBuilder.AppendPath(System.ReadOnlySpan{System.Char},System.Boolean)">
            <summary>
            Optionally escapes and appends the <paramref name="value" /> to <see cref="P:Azure.Core.RequestUriBuilder.Path" /> without adding path separator.
            If <paramref name="escape" /> is true, path segments and any other characters will be escaped, e.g. ":" will be escaped as "%3a".
            </summary>
            <param name="value">The value to optionally escape and append.</param>
            <param name="escape">Whether value should be escaped.</param>
        </member>
        <member name="M:Azure.Core.RequestUriBuilder.ToString">
            <summary>
            Returns a string representation of this <see cref="T:Azure.Core.RequestUriBuilder" />.
            </summary>
            <returns>A string representation of this <see cref="T:Azure.Core.RequestUriBuilder" />.</returns>
        </member>
        <member name="T:Azure.Core.ResourceIdentifier">
            <summary>
            An Azure Resource Manager resource identifier.
            </summary>
        </member>
        <member name="F:Azure.Core.ResourceIdentifier.Root">
            <summary>
            The root of the resource hierarchy.
            </summary>
        </member>
        <member name="M:Azure.Core.ResourceIdentifier.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.Core.ResourceIdentifier" /> class.
            </summary>
            <param name="resourceId"> The id string to create the ResourceIdentifier from. </param>
            <remarks>
            For more information on ResourceIdentifier format see the following.
            ResourceGroup level id https://docs.microsoft.com/en-us/azure/azure-resource-manager/templates/template-functions-resource#resourceid
            Subscription level id https://docs.microsoft.com/en-us/azure/azure-resource-manager/templates/template-functions-resource#subscriptionresourceid
            Tenant level id https://docs.microsoft.com/en-us/azure/azure-resource-manager/templates/template-functions-resource#tenantresourceid
            Extension id https://docs.microsoft.com/en-us/azure/azure-resource-manager/templates/template-functions-resource#extensionresourceid
            </remarks>
        </member>
        <member name="P:Azure.Core.ResourceIdentifier.ResourceType">
            <summary>
            The resource type of the resource.
            </summary>
        </member>
        <member name="P:Azure.Core.ResourceIdentifier.Name">
            <summary>
            The name of the resource.
            </summary>
        </member>
        <member name="P:Azure.Core.ResourceIdentifier.Parent">
            <summary>
            The immediate parent containing this resource.
            </summary>
        </member>
        <member name="P:Azure.Core.ResourceIdentifier.IsProviderResource">
            <summary>
            Determines whether this resource is in the same namespace as its parent.
            </summary>
        </member>
        <member name="P:Azure.Core.ResourceIdentifier.SubscriptionId">
            <summary>
            Gets the subscription id if it exists otherwise null.
            </summary>
        </member>
        <member name="P:Azure.Core.ResourceIdentifier.Provider">
            <summary>
            Gets the provider namespace if it exists otherwise null.
            </summary>
        </member>
        <member name="P:Azure.Core.ResourceIdentifier.Location">
            <summary>
            Gets the location if it exists otherwise null.
            </summary>
        </member>
        <member name="P:Azure.Core.ResourceIdentifier.ResourceGroupName">
            <summary>
            The name of the resource group if it exists otherwise null.
            </summary>
        </member>
        <member name="M:Azure.Core.ResourceIdentifier.ToString">
            <summary>
            Return the string representation of the resource identifier.
            </summary>
            <returns> The string representation of this resource identifier. </returns>
        </member>
        <member name="M:Azure.Core.ResourceIdentifier.Equals(Azure.Core.ResourceIdentifier)">
            <summary>
            Determine if this resource identifier is equivalent to the given resource identifier.
            </summary>
            <param name="other"> The resource identifier to compare to. </param>
            <returns>True if the resource identifiers are equivalent, otherwise false. </returns>
        </member>
        <member name="M:Azure.Core.ResourceIdentifier.CompareTo(Azure.Core.ResourceIdentifier)">
            <summary>
            Compre this resource identifier to the given resource identifier.
            </summary>
            <param name="other"> The resource identifier to compare to. </param>
            <returns> 0 if the resource identifiers are equivalent, less than 0 if this resource identifier
            should be ordered before the given resource identifier, greater than 0 if this resource identifier
            should be ordered after the given resource identifier. </returns>
        </member>
        <member name="M:Azure.Core.ResourceIdentifier.Equals(System.Object)">
            <summary>Determines whether the specified object is equal to the current object.</summary><returns>true if the specified object  is equal to the current object; otherwise, false.</returns><param name="obj">The object to compare with the current object. </param><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.ResourceIdentifier.GetHashCode">
            <summary>Serves as the default hash function. </summary><returns>A hash code for the current object.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.ResourceIdentifier.op_Implicit(Azure.Core.ResourceIdentifier)~System.String">
            <summary>
            Convert a resource identifier to a string.
            </summary>
            <param name="id"> The resource identifier. </param>
        </member>
        <member name="M:Azure.Core.ResourceIdentifier.op_Equality(Azure.Core.ResourceIdentifier,Azure.Core.ResourceIdentifier)">
            <summary>
            Operator overloading for '=='.
            </summary>
            <param name="left"> Left ResourceIdentifier object to compare. </param>
            <param name="right"> Right ResourceIdentifier object to compare. </param>
            <returns></returns>
        </member>
        <member name="M:Azure.Core.ResourceIdentifier.op_Inequality(Azure.Core.ResourceIdentifier,Azure.Core.ResourceIdentifier)">
            <summary>
            Operator overloading for '!='.
            </summary>
            <param name="left"> Left ResourceIdentifier object to compare. </param>
            <param name="right"> Right ResourceIdentifier object to compare. </param>
            <returns></returns>
        </member>
        <member name="M:Azure.Core.ResourceIdentifier.op_LessThan(Azure.Core.ResourceIdentifier,Azure.Core.ResourceIdentifier)">
            <summary>
            Compares one <see cref="T:Azure.Core.ResourceIdentifier" /> with another instance.
            </summary>
            <param name="left"> The object on the left side of the operator. </param>
            <param name="right"> The object on the right side of the operator. </param>
            <returns> True if the left object is less than the right. </returns>
        </member>
        <member name="M:Azure.Core.ResourceIdentifier.op_LessThanOrEqual(Azure.Core.ResourceIdentifier,Azure.Core.ResourceIdentifier)">
            <summary>
            Compares one <see cref="T:Azure.Core.ResourceIdentifier" /> with another instance.
            </summary>
            <param name="left"> The object on the left side of the operator. </param>
            <param name="right"> The object on the right side of the operator. </param>
            <returns> True if the left object is less than or equal to the right. </returns>
        </member>
        <member name="M:Azure.Core.ResourceIdentifier.op_GreaterThan(Azure.Core.ResourceIdentifier,Azure.Core.ResourceIdentifier)">
            <summary>
            Compares one <see cref="T:Azure.Core.ResourceIdentifier" /> with another instance.
            </summary>
            <param name="left"> The object on the left side of the operator. </param>
            <param name="right"> The object on the right side of the operator. </param>
            <returns> True if the left object is greater than the right. </returns>
        </member>
        <member name="M:Azure.Core.ResourceIdentifier.op_GreaterThanOrEqual(Azure.Core.ResourceIdentifier,Azure.Core.ResourceIdentifier)">
            <summary>
            Compares one <see cref="T:Azure.Core.ResourceIdentifier" /> with another instance.
            </summary>
            <param name="left"> The object on the left side of the operator. </param>
            <param name="right"> The object on the right side of the operator. </param>
            <returns> True if the left object is greater than or equal to the right. </returns>
        </member>
        <member name="M:Azure.Core.ResourceIdentifier.Parse(System.String)">
            <summary>
            Converts the string representation of a ResourceIdentifier to the equivalent <see cref="T:Azure.Core.ResourceIdentifier" /> structure.
            </summary>
            <param name="input"> The id string to convert. </param>
            <returns> A class that contains the value that was parsed. </returns>
            <exception cref="T:System.FormatException"> when resourceId is not a valid <see cref="T:Azure.Core.ResourceIdentifier" /> format. </exception>
            <exception cref="T:System.ArgumentNullException"> when resourceId is null. </exception>
            <exception cref="T:System.ArgumentException"> when resourceId is empty. </exception>
        </member>
        <member name="M:Azure.Core.ResourceIdentifier.TryParse(System.String,Azure.Core.ResourceIdentifier@)">
            <summary>
            Converts the string representation of a ResourceIdentifier to the equivalent <see cref="T:Azure.Core.ResourceIdentifier" /> structure.
            </summary>
            <param name="input"> The id string to convert. </param>
            <param name="result">
            The structure that will contain the parsed value.
            If the method returns true result contains a valid ResourceIdentifier.
            If the method returns false, result will be null.
            </param>
            <returns> True if the parse operation was successful; otherwise, false. </returns>
        </member>
        <member name="M:Azure.Core.ResourceIdentifier.AppendProviderResource(System.String,System.String,System.String)">
            <summary>
            Add a provider resource to an existing resource id.
            </summary>
            <param name="providerNamespace"> The provider namespace of the added resource. </param>
            <param name="resourceType"> The simple type of the added resource, without slashes (/),
            for example, 'virtualMachines'. </param>
            <param name="resourceName"> The name of the resource.</param>
            <returns> The combined resource id. </returns>
        </member>
        <member name="M:Azure.Core.ResourceIdentifier.AppendChildResource(System.String,System.String)">
            <summary>
            Add a provider resource to an existing resource id.
            </summary>
            <param name="childResourceType"> The simple type of the child resource, without slashes (/),
            for example, 'subnets'. </param>
            <param name="childResourceName"> The name of the resource. </param>
            <returns> The combined resource id. </returns>
        </member>
        <member name="T:Azure.Core.ResourceType">
            <summary>
            Structure representing a resource type.
            </summary>
            <remarks> See https://docs.microsoft.com/en-us/azure/azure-resource-manager/management/resource-providers-and-types for more info. </remarks>
        </member>
        <member name="M:Azure.Core.ResourceType.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.Core.ResourceType" /> class.
            </summary>
            <param name="resourceType"> The resource type string to convert. </param>
        </member>
        <member name="M:Azure.Core.ResourceType.GetLastType">
            <summary>
            Gets the last resource type name.
            </summary>
        </member>
        <member name="P:Azure.Core.ResourceType.Namespace">
            <summary>
            Gets the resource type Namespace.
            </summary>
        </member>
        <member name="P:Azure.Core.ResourceType.Type">
            <summary>
            Gets the resource Type.
            </summary>
        </member>
        <member name="M:Azure.Core.ResourceType.op_Implicit(System.String)~Azure.Core.ResourceType">
            <summary>
            Implicit operator for initializing a <see cref="T:Azure.Core.ResourceType" /> instance from a string.
            </summary>
            <param name="resourceType"> String to be converted into a <see cref="T:Azure.Core.ResourceType" /> object. </param>
        </member>
        <member name="M:Azure.Core.ResourceType.op_Implicit(Azure.Core.ResourceType)~System.String">
            <summary>
            Implicit operator for initializing a string from a <see cref="T:Azure.Core.ResourceType" />.
            </summary>
            <param name="resourceType"> <see cref="T:Azure.Core.ResourceType" /> to be converted into a string. </param>
        </member>
        <member name="M:Azure.Core.ResourceType.op_Equality(Azure.Core.ResourceType,Azure.Core.ResourceType)">
            <summary>
            Compares two <see cref="T:Azure.Core.ResourceType" /> objects.
            </summary>
            <param name="left"> First <see cref="T:Azure.Core.ResourceType" /> object. </param>
            <param name="right"> Second <see cref="T:Azure.Core.ResourceType" /> object. </param>
            <returns> True if they are equal, otherwise False. </returns>
        </member>
        <member name="M:Azure.Core.ResourceType.op_Inequality(Azure.Core.ResourceType,Azure.Core.ResourceType)">
            <summary>
            Compares two <see cref="T:Azure.Core.ResourceType" /> objects.
            </summary>
            <param name="left"> First <see cref="T:Azure.Core.ResourceType" /> object. </param>
            <param name="right"> Second <see cref="T:Azure.Core.ResourceType" /> object. </param>
            <returns> False if they are equal, otherwise True. </returns>
        </member>
        <member name="M:Azure.Core.ResourceType.Equals(Azure.Core.ResourceType)">
            <summary>
            Compares this <see cref="T:Azure.Core.ResourceType" /> instance with another object and determines if they are equals.
            </summary>
            <param name="other"> <see cref="T:Azure.Core.ResourceType" /> object to compare. </param>
            <returns> True if they are equals, otherwise false. </returns>
        </member>
        <member name="M:Azure.Core.ResourceType.ToString">
            <summary>Returns the fully qualified type name of this instance.</summary><returns>A <see cref="T:System.String" /> containing a fully qualified type name.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.ResourceType.Equals(System.Object)">
            <summary>Indicates whether this instance and a specified object are equal.</summary><returns>true if <paramref name="other" /> and this instance are the same type and represent the same value; otherwise, false. </returns><param name="other">The object to compare with the current instance. </param><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.ResourceType.GetHashCode">
            <summary>Returns the hash code for this instance.</summary><returns>A 32-bit signed integer that is the hash code for this instance.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="T:Azure.Core.ResponseClassificationHandler">
            <summary>
            A type that analyzes an HTTP message and determines if the response it holds
            should be treated as an error response. A classifier of this type may use information
            from the request, the response, or other message property to decide
            whether and how to classify the message.
            <para />
            This type's <code>TryClassify</code> method allows chaining together handlers before
            applying default classifier logic.
            If a handler in the chain returns false from <code>TryClassify</code>,
            the next handler will be tried, and so on.  The first handler that returns true
            will determine whether the response is an error.
            </summary>
        </member>
        <member name="M:Azure.Core.ResponseClassificationHandler.TryClassify(Azure.Core.HttpMessage,System.Boolean@)">
            <summary>
            Populates the <code>isError</code> out parameter to indicate whether or not
            to classify the message's response as an error.
            </summary>
            <param name="message">The message to classify.</param>
            <param name="isError">Whether the message's response should be considered an error.</param>
            <returns><code>true</code> if the handler had a classification for this message; <code>false</code> otherwise.</returns>
        </member>
        <member name="T:Azure.Core.ResponseClassifier">
            <summary>
            A type that analyzes HTTP responses and exceptions and determines if they should be retried,
            and/or analyzes responses and determines if they should be treated as error responses.
            </summary>
        </member>
        <member name="M:Azure.Core.ResponseClassifier.IsRetriableResponse(Azure.Core.HttpMessage)">
            <summary>
            Specifies if the request contained in the <paramref name="message" /> should be retried.
            </summary>
        </member>
        <member name="M:Azure.Core.ResponseClassifier.IsRetriableException(System.Exception)">
            <summary>
            Specifies if the operation that caused the exception should be retried.
            </summary>
        </member>
        <member name="M:Azure.Core.ResponseClassifier.IsRetriable(Azure.Core.HttpMessage,System.Exception)">
            <summary>
            Specifies if the operation that caused the exception should be retried taking the <see cref="T:Azure.Core.HttpMessage" /> into consideration.
            </summary>
        </member>
        <member name="M:Azure.Core.ResponseClassifier.IsErrorResponse(Azure.Core.HttpMessage)">
            <summary>
            Specifies if the response contained in the <paramref name="message" /> is not successful.
            </summary>
        </member>
        <member name="T:Azure.Core.ResponseHeaders">
            <summary>
            Headers received as part of the <see cref="T:Azure.Response" />.
            </summary>
        </member>
        <member name="P:Azure.Core.ResponseHeaders.Date">
            <summary>
            Gets the parsed value of "Date" or "x-ms-date" header.
            </summary>
        </member>
        <member name="P:Azure.Core.ResponseHeaders.ContentType">
            <summary>
            Gets the value of "Content-Type" header.
            </summary>
        </member>
        <member name="P:Azure.Core.ResponseHeaders.ContentLength">
            <summary>
            Gets the parsed value of "Content-Length" header.
            </summary>
        </member>
        <member name="P:Azure.Core.ResponseHeaders.ETag">
            <summary>
            Gets the parsed value of "ETag" header.
            </summary>
        </member>
        <member name="P:Azure.Core.ResponseHeaders.RequestId">
            <summary>
            Gets the value of "x-ms-request-id" header.
            </summary>
        </member>
        <member name="P:Azure.Core.ResponseHeaders.RetryAfter">
            <summary>
            Gets the value of the retry after header, one of "Retry-After", "retry-after-ms", or "x-ms-retry-after-ms".
            </summary>
        </member>
        <member name="M:Azure.Core.ResponseHeaders.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the <see cref="T:Azure.Core.ResponseHeaders" />.
            </summary>
            <returns>A <see cref="T:System.Collections.Generic.IEnumerator`1" /> for the <see cref="T:Azure.Core.ResponseHeaders" />.</returns>
        </member>
        <member name="M:Azure.Core.ResponseHeaders.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the <see cref="T:Azure.Core.ResponseHeaders" />.
            </summary>
            <returns>A <see cref="T:System.Collections.IEnumerator" /> for the <see cref="T:Azure.Core.ResponseHeaders" />.</returns>
        </member>
        <member name="M:Azure.Core.ResponseHeaders.TryGetValue(System.String,System.String@)">
            <summary>
            Returns header value if the header is stored in the collection. If header has multiple values they are going to be joined with a comma.
            </summary>
            <param name="name">The header name.</param>
            <param name="value">The reference to populate with value.</param>
            <returns><c>true</c> if the specified header is stored in the collection, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Azure.Core.ResponseHeaders.TryGetValues(System.String,System.Collections.Generic.IEnumerable{System.String}@)">
            <summary>
            Returns header values if the header is stored in the collection.
            </summary>
            <param name="name">The header name.</param>
            <param name="values">The reference to populate with values.</param>
            <returns><c>true</c> if the specified header is stored in the collection, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Azure.Core.ResponseHeaders.Contains(System.String)">
            <summary>
            Returns <c>true</c> if the header is stored in the collection.
            </summary>
            <param name="name">The header name.</param>
            <returns><c>true</c> if the specified header is stored in the collection, otherwise <c>false</c>.</returns>
        </member>
        <member name="T:Azure.Core.RetryMode">
            <summary>
            The type of approach to apply when calculating the delay
            between retry attempts.
            </summary>
        </member>
        <member name="F:Azure.Core.RetryMode.Fixed">
            <summary>
            Retry attempts happen at fixed intervals; each delay is a consistent duration.
            </summary>
        </member>
        <member name="F:Azure.Core.RetryMode.Exponential">
            <summary>
            Retry attempts will delay based on a backoff strategy, where each attempt will increase
            the duration that it waits before retrying.
            </summary>
        </member>
        <member name="T:Azure.Core.RetryOptions">
            <summary>
             The set of options that can be specified to influence how
             retry attempts are made, and a failure is eligible to be retried.
            </summary>
        </member>
        <member name="M:Azure.Core.RetryOptions.#ctor">
            <summary>
            Creates a new <see cref="T:Azure.Core.RetryOptions" /> instance with default values.
            </summary>
        </member>
        <member name="M:Azure.Core.RetryOptions.#ctor(Azure.Core.RetryOptions)">
            <summary>
            Initializes the newly created <see cref="T:Azure.Core.RetryOptions" /> with the same settings as the specified <paramref name="retryOptions" />.
            </summary>
            <param name="retryOptions">The <see cref="T:Azure.Core.RetryOptions" /> to model the newly created instance on.</param>
        </member>
        <member name="P:Azure.Core.RetryOptions.MaxRetries">
            <summary>
            The maximum number of retry attempts before giving up.
            </summary>
        </member>
        <member name="P:Azure.Core.RetryOptions.Delay">
            <summary>
            The delay between retry attempts for a fixed approach or the delay
            on which to base calculations for a backoff-based approach.
            If the service provides a Retry-After response header, the next retry will be delayed by the duration specified by the header value.
            </summary>
        </member>
        <member name="P:Azure.Core.RetryOptions.MaxDelay">
            <summary>
            The maximum permissible delay between retry attempts when the service does not provide a Retry-After response header.
            If the service provides a Retry-After response header, the next retry will be delayed by the duration specified by the header value.
            </summary>
        </member>
        <member name="P:Azure.Core.RetryOptions.Mode">
            <summary>
            The approach to use for calculating retry delays.
            </summary>
        </member>
        <member name="P:Azure.Core.RetryOptions.NetworkTimeout">
            <summary>
            The timeout applied to an individual network operations.
            </summary>
        </member>
        <member name="T:Azure.Core.StatusCodeClassifier">
            <summary>
            This type inherits from ResponseClassifier and is designed to work
            efficiently with classifier customizations specified in <see cref="T:Azure.RequestContext" />.
            </summary>
        </member>
        <member name="M:Azure.Core.StatusCodeClassifier.#ctor(System.ReadOnlySpan{System.UInt16})">
            <summary>
            Creates a new instance of <see cref="T:Azure.Core.StatusCodeClassifier" />
            </summary>
            <param name="successStatusCodes">The status codes that this classifier will consider
            not to be errors.</param>
        </member>
        <member name="M:Azure.Core.StatusCodeClassifier.IsErrorResponse(Azure.Core.HttpMessage)">
            <summary>
            Specifies if the response contained in the <paramref name="message" /> is not successful.
            </summary>
        </member>
        <member name="T:Azure.Core.SyncAsyncEventHandler`1">
             <summary>
             Represents a method that can handle an event and execute either
             synchronously or asynchronously.
             </summary>
             <typeparam name="T">
             Type of the event arguments deriving or equal to
             <see cref="T:Azure.SyncAsyncEventArgs" />.
             </typeparam>
             <param name="e">
             An <see cref="T:Azure.SyncAsyncEventArgs" /> instance that contains the event
             data.
             </param>
             <returns>
             A task that represents the handler.  You can return
             <see cref="P:System.Threading.Tasks.Task.CompletedTask" /> if implementing a sync handler.
             Please see the Remarks section for more details.
             </returns>
             <example>
             <para>
             If you're using the synchronous, blocking methods of a client (i.e.,
             methods without an Async suffix), they will raise events that require
             handlers to execute synchronously as well.  Even though the signature
             of your handler returns a <see cref="T:System.Threading.Tasks.Task" />, you should write regular
             sync code that blocks and return <see cref="P:System.Threading.Tasks.Task.CompletedTask" /> when
             finished.
             <code snippet="Snippet:Azure_Core_Samples_EventSamples_SyncHandler" language="csharp">
             var client = new AlarmClient();
             client.Ring += (SyncAsyncEventArgs e) =&gt;
             {
                 Console.WriteLine("Wake up!");
                 return Task.CompletedTask;
             };
            
             client.Snooze();
             </code>
             If you need to call an async method from a synchronous event handler,
             you have two options.  You can use <see cref="M:System.Threading.Tasks.Task.Run(System.Action)" /> to
             queue a task for execution on the ThreadPool without waiting on it to
             complete.  This "fire and forget" approach may not run before your
             handler finishes executing.  Be sure to understand
             <see href="https://docs.microsoft.com/dotnet/standard/parallel-programming/exception-handling-task-parallel-library">
             exception handling in the Task Parallel Library</see> to avoid
             unhandled exceptions tearing down your process.  If you absolutely need
             the async method to execute before returning from your handler, you can
             call <c>myAsyncTask.GetAwaiter().GetResult()</c>.  Please be aware
             this may cause ThreadPool starvation.  See the sync-over-async note in
             Remarks for more details.
             </para>
             <para>
             If you're using the asynchronous, non-blocking methods of a client
             (i.e., methods with an Async suffix), they will raise events that
             expect handlers to execute asynchronously.
             <code snippet="Snippet:Azure_Core_Samples_EventSamples_AsyncHandler" language="csharp">
             var client = new AlarmClient();
             client.Ring += async (SyncAsyncEventArgs e) =&gt;
             {
                 await Console.Out.WriteLineAsync("Wake up!");
             };
            
             await client.SnoozeAsync();
             </code>
             </para>
             <para>
             The same event can be raised from both synchronous and asynchronous
             code paths depending on whether you're calling sync or async methods
             on a client.  If you write an async handler but raise it from a sync
             method, the handler will be doing sync-over-async and may cause
             ThreadPool starvation.  See the note in Remarks for more details.  You
             should use the <see cref="P:Azure.SyncAsyncEventArgs.IsRunningSynchronously" />
             property to check how the event is being raised and implement your
             handler accordingly.  Here's an example handler that's safe to invoke
             from both sync and async code paths.
             <code snippet="Snippet:Azure_Core_Samples_EventSamples_CombinedHandler" language="csharp">
             var client = new AlarmClient();
             client.Ring += async (SyncAsyncEventArgs e) =&gt;
             {
                 if (e.IsRunningSynchronously)
                 {
                     Console.WriteLine("Wake up!");
                 }
                 else
                 {
                     await Console.Out.WriteLineAsync("Wake up!");
                 }
             };
            
             client.Snooze(); // sync call that blocks
             await client.SnoozeAsync(); // async call that doesn't block
             </code>
             </para>
             </example>
             <example>
             </example>
             <exception cref="T:System.AggregateException">
             Any exceptions thrown by an event handler will be wrapped in a single
             AggregateException and thrown from the code that raised the event.  You
             can check the <see cref="P:System.AggregateException.InnerExceptions" /> property
             to see the original exceptions thrown by your event handlers.
             AggregateException also provides
             <see href="https://docs.microsoft.com/en-us/archive/msdn-magazine/2009/brownfield/aggregating-exceptions">
             a number of helpful methods</see> like
             <see cref="M:System.AggregateException.Flatten" /> and
             <see cref="M:System.AggregateException.Handle(System.Func{System.Exception,System.Boolean})" /> to make
             complex failures easier to work with.
             <code snippet="Snippet:Azure_Core_Samples_EventSamples_Exceptions" language="csharp">
             var client = new AlarmClient();
             client.Ring += (SyncAsyncEventArgs e) =&gt;
                 throw new InvalidOperationException("Alarm unplugged.");
            
             try
             {
                 client.Snooze();
             }
             catch (AggregateException ex)
             {
                 ex.Handle(e =&gt; e is InvalidOperationException);
                 Console.WriteLine("Please switch to your backup alarm.");
             }
             </code>
             </exception>
             <remarks>
             <para>
             Most Azure client libraries for .NET offer both synchronous and
             asynchronous methods for calling Azure services.  You can distinguish
             the asynchronous methods by their Async suffix.  For example,
             BlobClient.Download and BlobClient.DownloadAsync make the same
             underlying REST call and only differ in whether they block.  We
             recommend using our async methods for new applications, but there are
             perfectly valid cases for using sync methods as well.  These dual
             method invocation semantics allow for flexibility, but require a little
             extra care when writing event handlers.
             </para>
             <para>
             The SyncAsyncEventHandler is a delegate used by events in Azure client
             libraries to represent an event handler that can be invoked from either
             sync or async code paths.  It takes event arguments deriving from
             <see cref="T:Azure.SyncAsyncEventArgs" /> that contain important information for
             writing your event handler:
             <list type="bullet">
             <item>
             <description>
             <see cref="P:Azure.SyncAsyncEventArgs.CancellationToken" /> is a cancellation
             token related to the original operation that raised the event.  It's
             important for your handler to pass this token along to any asynchronous
             or long-running synchronous operations that take a token so cancellation
             (via something like
             <c>new CancellationTokenSource(TimeSpan.FromSeconds(10)).Token</c>,
             for example) will correctly propagate.
             </description>
             </item>
             <item>
             <description>
             <see cref="P:Azure.SyncAsyncEventArgs.IsRunningSynchronously" /> is a flag indicating
             whether your handler was invoked synchronously or asynchronously.  If
             you're calling sync methods on your client, you should use sync methods
             to implement your event handler (you can return
             <see cref="P:System.Threading.Tasks.Task.CompletedTask" />).  If you're calling async methods on
             your client, you should use async methods where possible to implement
             your event handler.  If you're not in control of how the client will be
             used or want to write safer code, you should check the
             <see cref="P:Azure.SyncAsyncEventArgs.IsRunningSynchronously" /> property and call
             either sync or async methods as directed.
             </description>
             </item>
             <item>
             <description>
             Most events will customize the event data by deriving from
             <see cref="T:Azure.SyncAsyncEventArgs" /> and including details about what
             triggered the event or providing options to react.  Many times this
             will include a reference to the client that raised the event in case
             you need it for additional processing.
             </description>
             </item>
             </list>
             </para>
             <para>
             When an event using SyncAsyncEventHandler is raised, the handlers will
             be executed sequentially to avoid introducing any unintended
             parallelism.  The event handlers will finish before returning control
             to the code path raising the event.  This means blocking for events
             raised synchronously and waiting for the returned <see cref="T:System.Threading.Tasks.Task" /> to
             complete for events raised asynchronously.
             </para>
             <para>
             Any exceptions thrown from a handler will be wrapped in a single
             <see cref="T:System.AggregateException" />.  If one handler throws an exception,
             it will not prevent other handlers from running.  This is also relevant
             for cancellation because all handlers are still raised if cancellation
             occurs.  You should both pass <see cref="P:Azure.SyncAsyncEventArgs.CancellationToken" />
             to asynchronous or long-running synchronous operations and consider
             calling <see cref="M:System.Threading.CancellationToken.ThrowIfCancellationRequested" />
             in compute heavy handlers.
             </para>
             <para>
             A <see href="https://github.com/Azure/azure-sdk-for-net/blob/main/sdk/core/Azure.Core/samples/Diagnostics.md#distributed-tracing">
             distributed tracing span</see> is wrapped around your handlers using
             the event name so you can see how long your handlers took to run,
             whether they made other calls to Azure services, and details about any
             exceptions that were thrown.
             </para>
             <para>
             Executing asynchronous code from a sync code path is commonly referred
             to as sync-over-async because you're getting sync behavior but still
             invoking all the async machinery. See
             <see href="https://docs.microsoft.com/archive/blogs/vancem/diagnosing-net-core-threadpool-starvation-with-perfview-why-my-service-is-not-saturating-all-cores-or-seems-to-stall">
             Diagnosing.NET Core ThreadPool Starvation with PerfView</see>
             for a detailed explanation of how that can cause serious performance
             problems.  We recommend you use the
             <see cref="P:Azure.SyncAsyncEventArgs.IsRunningSynchronously" /> flag to avoid
             ThreadPool starvation.
             </para>
             </remarks>
        </member>
        <member name="T:Azure.Core.TelemetryDetails">
            <summary>
            Details about the package to be included in UserAgent telemetry
            </summary>
        </member>
        <member name="P:Azure.Core.TelemetryDetails.Assembly">
            <summary>
            The package type represented by this <see cref="T:Azure.Core.TelemetryDetails" /> instance.
            </summary>
        </member>
        <member name="P:Azure.Core.TelemetryDetails.ApplicationId">
            <summary>
            The value of the applicationId used to initialize this <see cref="T:Azure.Core.TelemetryDetails" /> instance.
            </summary>
        </member>
        <member name="M:Azure.Core.TelemetryDetails.#ctor(System.Reflection.Assembly,System.String)">
            <summary>
            Initialize an instance of <see cref="T:Azure.Core.TelemetryDetails" /> by extracting the name and version information from the <see cref="T:System.Reflection.Assembly" /> associated with the <paramref name="assembly" />.
            </summary>
            <param name="assembly">The <see cref="T:System.Reflection.Assembly" /> used to generate the package name and version information for the <see cref="T:Azure.Core.TelemetryDetails" /> value.</param>
            <param name="applicationId">An optional value to be prepended to the <see cref="T:Azure.Core.TelemetryDetails" />.
            This value overrides the behavior of the <see cref="P:Azure.Core.DiagnosticsOptions.ApplicationId" /> property for the <see cref="T:Azure.Core.HttpMessage" /> it is applied to.</param>
        </member>
        <member name="M:Azure.Core.TelemetryDetails.Apply(Azure.Core.HttpMessage)">
            <summary>
            Sets the package name and version portion of the UserAgent telemetry value for the context of the <paramref name="message" />
            Note: If <see cref="P:Azure.Core.DiagnosticsOptions.IsTelemetryEnabled" /> is false, this value is never used.
            </summary>
            <param name="message">The <see cref="T:Azure.Core.HttpMessage" /> that will use this <see cref="T:Azure.Core.TelemetryDetails" />.</param>
        </member>
        <member name="M:Azure.Core.TelemetryDetails.ToString">
            <summary>
            The properly formatted UserAgent string based on this <see cref="T:Azure.Core.TelemetryDetails" /> instance.
            </summary>
        </member>
        <member name="T:Azure.Core.TokenCredential">
            <summary>
            Represents a credential capable of providing an OAuth token.
            </summary>
        </member>
        <member name="M:Azure.Core.TokenCredential.GetTokenAsync(Azure.Core.TokenRequestContext,System.Threading.CancellationToken)">
            <summary>
            Gets an <see cref="T:Azure.Core.AccessToken" /> for the specified set of scopes.
            </summary>
            <param name="requestContext">The <see cref="T:Azure.Core.TokenRequestContext" /> with authentication information.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> to use.</param>
            <returns>A valid <see cref="T:Azure.Core.AccessToken" />.</returns>
            <remarks>Caching and management of the lifespan for the <see cref="T:Azure.Core.AccessToken" /> is considered the responsibility of the caller: each call should request a fresh token being requested.</remarks>
        </member>
        <member name="M:Azure.Core.TokenCredential.GetToken(Azure.Core.TokenRequestContext,System.Threading.CancellationToken)">
            <summary>
            Gets an <see cref="T:Azure.Core.AccessToken" /> for the specified set of scopes.
            </summary>
            <param name="requestContext">The <see cref="T:Azure.Core.TokenRequestContext" /> with authentication information.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> to use.</param>
            <returns>A valid <see cref="T:Azure.Core.AccessToken" />.</returns>
            <remarks>Caching and management of the lifespan for the <see cref="T:Azure.Core.AccessToken" /> is considered the responsibility of the caller: each call should request a fresh token being requested.</remarks>
        </member>
        <member name="T:Azure.Core.TokenRequestContext">
            <summary>
            Contains the details of an authentication token request.
            </summary>
        </member>
        <member name="M:Azure.Core.TokenRequestContext.#ctor(System.String[],System.String)">
            <summary>
            Creates a new TokenRequest with the specified scopes.
            </summary>
            <param name="scopes">The scopes required for the token.</param>
            <param name="parentRequestId">The <see cref="P:Azure.Core.Request.ClientRequestId" /> of the request requiring a token for authentication, if applicable.</param>
        </member>
        <member name="M:Azure.Core.TokenRequestContext.#ctor(System.String[],System.String,System.String)">
            <summary>
            Creates a new TokenRequest with the specified scopes.
            </summary>
            <param name="scopes">The scopes required for the token.</param>
            <param name="parentRequestId">The <see cref="P:Azure.Core.Request.ClientRequestId" /> of the request requiring a token for authentication, if applicable.</param>
            <param name="claims">Additional claims to be included in the token.</param>
        </member>
        <member name="M:Azure.Core.TokenRequestContext.#ctor(System.String[],System.String,System.String,System.String)">
            <summary>
            Creates a new TokenRequest with the specified scopes.
            </summary>
            <param name="scopes">The scopes required for the token.</param>
            <param name="parentRequestId">The <see cref="P:Azure.Core.Request.ClientRequestId" /> of the request requiring a token for authentication, if applicable.</param>
            <param name="claims">Additional claims to be included in the token.</param>
            <param name="tenantId"> The tenantId to be included in the token request. </param>
        </member>
        <member name="P:Azure.Core.TokenRequestContext.Scopes">
            <summary>
            The scopes required for the token.
            </summary>
        </member>
        <member name="P:Azure.Core.TokenRequestContext.ParentRequestId">
            <summary>
            The <see cref="P:Azure.Core.Request.ClientRequestId" /> of the request requiring a token for authentication, if applicable.
            </summary>
        </member>
        <member name="P:Azure.Core.TokenRequestContext.Claims">
            <summary>
            Additional claims to be included in the token. See <see href="https://openid.net/specs/openid-connect-core-1_0-final.html#ClaimsParameter">https://openid.net/specs/openid-connect-core-1_0-final.html#ClaimsParameter</see> for more information on format and content.
            </summary>
        </member>
        <member name="P:Azure.Core.TokenRequestContext.TenantId">
            <summary>
            The tenantId to be included in the token request.
            </summary>
        </member>
        <member name="T:Azure.Core.AppContextSwitchHelper">
            <summary>
            Helper for interacting with AppConfig settings and their related Environment variable settings.
            </summary>
        </member>
        <member name="M:Azure.Core.AppContextSwitchHelper.GetConfigValue(System.String,System.String)">
            <summary>
            Determines if either an AppContext switch or its corresponding Environment Variable is set
            </summary>
            <param name="appContexSwitchName">Name of the AppContext switch.</param>
            <param name="environmentVariableName">Name of the Environment variable.</param>
            <returns>If the AppContext switch has been set, returns the value of the switch.
            If the AppContext switch has not been set, returns the value of the environment variable.
            False if neither is set.
            </returns>
        </member>
        <member name="T:Azure.Core.AsyncLockWithValue`1">
            <summary>
            Primitive that combines async lock and value cache
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="M:Azure.Core.AsyncLockWithValue`1.GetLockOrValueAsync(System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Method that either returns cached value or acquire a lock.
            If one caller has acquired a lock, other callers will be waiting for the lock to be released.
            If value is set, lock is released and all waiters get that value.
            If value isn't set, the next waiter in the queue will get the lock.
            </summary>
            <param name="async"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="P:Azure.Core.AsyncLockWithValue`1.LockOrValue.HasValue">
            <summary>
            Returns true if lock contains the cached value. Otherwise false.
            </summary>
        </member>
        <member name="P:Azure.Core.AsyncLockWithValue`1.LockOrValue.Value">
            <summary>
            Returns cached value if it was set when lock has been created. Throws exception otherwise.
            </summary>
            <exception cref="T:System.InvalidOperationException">Value isn't set.</exception>
        </member>
        <member name="M:Azure.Core.AsyncLockWithValue`1.LockOrValue.SetValue(`0)">
            <summary>
            Set value to the cache and to all the waiters.
            </summary>
            <param name="value"></param>
            <exception cref="T:System.InvalidOperationException">Value is set already.</exception>
        </member>
        <member name="T:Azure.Core.Argument">
            <summary>
            Argument validation.
            </summary>
            <remarks>
              <para>This class should be shared via source using Azure.Core.props and contain only common argument validation.
                It is declared partial so that you can use the same familiar class name but extend it with project-specific validation.
                To extend the functionality of this class, just declare your own partial <see cref="T:Azure.Core.Argument" /> class with project-specific methods.
              </para>
              <para>
                Be sure to document exceptions thrown by these methods on your public methods.
              </para>
            </remarks>
        </member>
        <member name="M:Azure.Core.Argument.AssertNotNull``1(``0,System.String)">
            <summary>
            Throws if <paramref name="value" /> is null.
            </summary>
            <param name="value">The value to validate.</param>
            <param name="name">The name of the parameter.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="value" /> is null.</exception>
        </member>
        <member name="M:Azure.Core.Argument.AssertNotNull``1(System.Nullable{``0},System.String)">
            <summary>
            Throws if <paramref name="value" /> has not been initialized.
            </summary>
            <param name="value">The value to validate.</param>
            <param name="name">The name of the parameter.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="value" /> has not been initialized.</exception>
        </member>
        <member name="M:Azure.Core.Argument.AssertNotNullOrEmpty``1(System.Collections.Generic.IEnumerable{``0},System.String)">
            <summary>
            Throws if <paramref name="value" /> is null or an empty collection.
            </summary>
            <param name="value">The value to validate.</param>
            <param name="name">The name of the parameter.</param>
            <exception cref="T:System.ArgumentException"><paramref name="value" /> is an empty collection.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="value" /> is null.</exception>
        </member>
        <member name="M:Azure.Core.Argument.AssertNotNullOrEmpty(System.String,System.String)">
            <summary>
            Throws if <paramref name="value" /> is null or an empty string.
            </summary>
            <param name="value">The value to validate.</param>
            <param name="name">The name of the parameter.</param>
            <exception cref="T:System.ArgumentException"><paramref name="value" /> is an empty string.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="value" /> is null.</exception>
        </member>
        <member name="M:Azure.Core.Argument.AssertNotNullOrWhiteSpace(System.String,System.String)">
            <summary>
            Throws if <paramref name="value" /> is null, an empty string, or consists only of white-space characters.
            </summary>
            <param name="value">The value to validate.</param>
            <param name="name">The name of the parameter.</param>
            <exception cref="T:System.ArgumentException"><paramref name="value" /> is an empty string or consists only of white-space characters.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="value" /> is null.</exception>
        </member>
        <member name="M:Azure.Core.Argument.AssertNotDefault``1(``0@,System.String)">
            <summary>
            Throws if <paramref name="value" /> is the default value for type <typeparamref name="T" />.
            </summary>
            <typeparam name="T">The type of structure to validate which implements <see cref="T:System.IEquatable`1" />.</typeparam>
            <param name="value">The value to validate.</param>
            <param name="name">The name of the parameter.</param>
            <exception cref="T:System.ArgumentException"><paramref name="value" /> is the default value for type <typeparamref name="T" />.</exception>
        </member>
        <member name="M:Azure.Core.Argument.AssertInRange``1(``0,``0,``0,System.String)">
            <summary>
            Throws if <paramref name="value" /> is less than the <paramref name="minimum" /> or greater than the <paramref name="maximum" />.
            </summary>
            <typeparam name="T">The type of to validate which implements <see cref="T:System.IComparable`1" />.</typeparam>
            <param name="value">The value to validate.</param>
            <param name="minimum">The minimum value to compare.</param>
            <param name="maximum">The maximum value to compare.</param>
            <param name="name">The name of the parameter.</param>
        </member>
        <member name="M:Azure.Core.Argument.AssertEnumDefined(System.Type,System.Object,System.String)">
            <summary>
            Throws if <paramref name="value" /> is not defined for <paramref name="enumType" />.
            </summary>
            <param name="enumType">The type to validate against.</param>
            <param name="value">The value to validate.</param>
            <param name="name">The name of the parameter.</param>
            <exception cref="T:System.ArgumentException"><paramref name="value" /> is not defined for <paramref name="enumType" />.</exception>
        </member>
        <member name="M:Azure.Core.Argument.CheckNotNull``1(``0,System.String)">
            <summary>
            Throws if <paramref name="value" /> has not been initialized; otherwise, returns <paramref name="value" />.
            </summary>
            <param name="value">The value to validate.</param>
            <param name="name">The name of the parameter.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="value" /> has not been initialized.</exception>
        </member>
        <member name="M:Azure.Core.Argument.CheckNotNullOrEmpty(System.String,System.String)">
            <summary>
            Throws if <paramref name="value" /> is null or an empty string; otherwise, returns <paramref name="value" />.
            </summary>
            <param name="value">The value to validate.</param>
            <param name="name">The name of the parameter.</param>
            <exception cref="T:System.ArgumentException"><paramref name="value" /> is an empty string.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="value" /> is null.</exception>
        </member>
        <member name="M:Azure.Core.Argument.AssertNull``1(``0,System.String,System.String)">
            <summary>
            Throws if <paramref name="value" /> is not null.
            </summary>
            <param name="value">The value to validate.</param>
            <param name="name">The name of the parameter.</param>
            <param name="message">The error message.</param>
            <exception cref="T:System.ArgumentException"><paramref name="value" /> is not null.</exception>
        </member>
        <member name="T:Azure.Core.AuthorizationChallengeParser">
            <summary>
            A helper class for parsing Authorization challenge headers.
            </summary>
        </member>
        <member name="M:Azure.Core.AuthorizationChallengeParser.GetChallengeParameterFromResponse(Azure.Response,System.String,System.String)">
            <summary>
            Parses the specified parameter from a challenge hearder found in the specified <see cref="T:Azure.Response" />.
            </summary>
            <param name="response">The <see cref="T:Azure.Response" /> to parse.</param>
            <param name="challengeScheme">The challenge scheme containing the <paramref name="challengeParameter" />. For example: "Bearer"</param>
            <param name="challengeParameter">The parameter key name containing the value to return.</param>
            <returns>The value of the parameter name specified in <paramref name="challengeParameter" /> if it is found in the specified <paramref name="challengeScheme" />.</returns>
        </member>
        <member name="M:Azure.Core.AuthorizationChallengeParser.TryGetNextChallenge(System.ReadOnlySpan{System.Char}@,System.ReadOnlySpan{System.Char}@)">
            <summary>
            Iterates through the challenge schemes present in a challenge header.
            </summary>
            <param name="headerValue">
            The header value which will be sliced to remove the first parsed <paramref name="challengeKey" />.
            </param>
            <param name="challengeKey">The parsed challenge scheme.</param>
            <returns>
            <c>true</c> if a challenge scheme was successfully parsed.
            The value of <paramref name="headerValue" /> should be passed to <see cref="M:Azure.Core.AuthorizationChallengeParser.TryGetNextParameter(System.ReadOnlySpan{System.Char}@,System.ReadOnlySpan{System.Char}@,System.ReadOnlySpan{System.Char}@,System.Char)" /> to parse the challenge parameters if <c>true</c>.
            </returns>
        </member>
        <member name="M:Azure.Core.AuthorizationChallengeParser.TryGetNextParameter(System.ReadOnlySpan{System.Char}@,System.ReadOnlySpan{System.Char}@,System.ReadOnlySpan{System.Char}@,System.Char)">
            <summary>
            Iterates through a challenge header value after being parsed by <see cref="M:Azure.Core.AuthorizationChallengeParser.TryGetNextChallenge(System.ReadOnlySpan{System.Char}@,System.ReadOnlySpan{System.Char}@)" />.
            </summary>
            <param name="headerValue">The header value after being parsed by <see cref="M:Azure.Core.AuthorizationChallengeParser.TryGetNextChallenge(System.ReadOnlySpan{System.Char}@,System.ReadOnlySpan{System.Char}@)" />.</param>
            <param name="paramKey">The parsed challenge parameter key.</param>
            <param name="paramValue">The parsed challenge parameter value.</param>
            <param name="separator">The challenge parameter key / value pair separator. The default is '='.</param>
            <returns>
            <c>true</c> if the next available challenge parameter was successfully parsed.
            <c>false</c> if there are no more parameters for the current challenge scheme or an additional challenge scheme was encountered in the <paramref name="headerValue" />.
            The value of <paramref name="headerValue" /> should be passed again to <see cref="M:Azure.Core.AuthorizationChallengeParser.TryGetNextChallenge(System.ReadOnlySpan{System.Char}@,System.ReadOnlySpan{System.Char}@)" /> to attempt to parse any additional challenge schemes if <c>false</c>.
            </returns>
        </member>
        <member name="M:Azure.Core.AzureKeyCredentialPolicy.#ctor(Azure.AzureKeyCredential,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.Core.AzureKeyCredentialPolicy" /> class.
            </summary>
            <param name="credential">The <see cref="T:Azure.AzureKeyCredential" /> used to authenticate requests.</param>
            <param name="name">The name of the key header used for the credential.</param>
            <param name="prefix">The prefix to apply before the credential key. For example, a prefix of "SharedAccessKey" would result in
            a value of "SharedAccessKey {credential.Key}" being stamped on the request header with header key of <paramref name="name" />.</param>
        </member>
        <member name="M:Azure.Core.AzureKeyCredentialPolicy.OnSendingRequest(Azure.Core.HttpMessage)">
            <summary>
            Method is invoked before the request is sent.
            </summary>
            <param name="message">The <see cref="T:Azure.Core.HttpMessage" /> containing the request.</param>
        </member>
        <member name="M:Azure.Core.AzureSasCredentialSynchronousPolicy.#ctor(Azure.AzureSasCredential)">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.Core.AzureSasCredentialSynchronousPolicy" /> class.
            </summary>
            <param name="credential">The <see cref="T:Azure.Core.AzureSasCredentialSynchronousPolicy" /> used to authenticate requests.</param>
        </member>
        <member name="M:Azure.Core.AzureSasCredentialSynchronousPolicy.OnSendingRequest(Azure.Core.HttpMessage)">
            <summary>
            Method is invoked before the request is sent.
            </summary>
            <param name="message">The <see cref="T:Azure.Core.HttpMessage" /> containing the request.</param>
        </member>
        <member name="M:Azure.Core.Base64Url.Decode(System.String)">
            <summary> Converts a Base64URL encoded string to a string.</summary>
            <param name="encoded">The Base64Url encoded string containing UTF8 bytes for a string.</param>
            <returns>The string represented by the Base64URL encoded string.</returns>
        </member>
        <member name="M:Azure.Core.Base64Url.Encode(System.Byte[])">
            <summary>Encode a byte array as a Base64URL encoded string.</summary>
            <param name="bytes">Raw byte input buffer.</param>
            <returns>The bytes, encoded as a Base64URL string.</returns>
        </member>
        <member name="M:Azure.Core.Base64Url.DecodeString(System.String)">
            <summary> Converts a Base64URL encoded string to a string.</summary>
            <param name="encoded">The Base64Url encoded string containing UTF8 bytes for a string.</param>
            <returns>The string represented by the Base64URL encoded string.</returns>
        </member>
        <member name="M:Azure.Core.Base64Url.EncodeString(System.String)">
            <summary>Encode a string as a Base64URL encoded string.</summary>
            <param name="value">String input buffer.</param>
            <returns>The UTF8 bytes for the string, encoded as a Base64URL string.</returns>
        </member>
        <member name="T:Azure.Core.DictionaryHeaders">
            <summary>
            An implementation for manipulating headers on <see cref="T:Azure.Core.Request" />.
            </summary>
        </member>
        <member name="M:Azure.Core.DictionaryHeaders.#ctor">
            <summary>
            Initializes an instance of <see cref="T:Azure.Core.DictionaryHeaders" />
            </summary>
        </member>
        <member name="M:Azure.Core.DictionaryHeaders.AddHeader(System.String,System.String)">
            <summary>
            Adds a header value to the header collection.
            </summary>
            <param name="name">The header name.</param>
            <param name="value">The header value.</param>
        </member>
        <member name="M:Azure.Core.DictionaryHeaders.TryGetHeader(System.String,System.String@)">
            <summary>
            Returns header value if the header is stored in the collection. If the header has multiple values they are going to be joined with a comma.
            </summary>
            <param name="name">The header name.</param>
            <param name="value">The reference to populate with value.</param>
            <returns><c>true</c> if the specified header is stored in the collection, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Azure.Core.DictionaryHeaders.TryGetHeaderValues(System.String,System.Collections.Generic.IEnumerable{System.String}@)">
            <summary>
            Returns header values if the header is stored in the collection.
            </summary>
            <param name="name">The header name.</param>
            <param name="values">The reference to populate with values.</param>
            <returns><c>true</c> if the specified header is stored in the collection, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Azure.Core.DictionaryHeaders.ContainsHeader(System.String)">
            <summary>
            Returns <c>true</c> if the header is stored in the collection.
            </summary>
            <param name="name">The header name.</param>
            <returns><c>true</c> if the specified header is stored in the collection, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Azure.Core.DictionaryHeaders.SetHeader(System.String,System.String)">
            <summary>
            Sets a header value the header collection.
            </summary>
            <param name="name">The header name.</param>
            <param name="value">The header value.</param>
        </member>
        <member name="M:Azure.Core.DictionaryHeaders.RemoveHeader(System.String)">
            <summary>
            Removes the header from the collection.
            </summary>
            <param name="name">The header name.</param>
        </member>
        <member name="M:Azure.Core.DictionaryHeaders.EnumerateHeaders">
            <summary>
            Returns an iterator enumerating <see cref="T:Azure.Core.HttpHeader" /> in the request.
            </summary>
            <returns>The <see cref="T:System.Collections.Generic.IEnumerable`1" /> enumerating <see cref="T:Azure.Core.HttpHeader" /> in the response.</returns>
        </member>
        <member name="T:Azure.Core.Shared.GeoRedundantFallbackPolicy">
            <summary>
            Internal policy that can be used to support georedundant fallbacks for Azure services. The policy maintains the current healthy host
            across requests. It falls back only if no response is received from a request, i.e. any response is treated as an indication that the
            host is healthy.
            </summary>
        </member>
        <member name="M:Azure.Core.Shared.GeoRedundantFallbackPolicy.#ctor(System.String[],System.String[],System.Nullable{System.TimeSpan})">
            <summary>
            Construct a new instance of the GeoRedundantFallbackPolicy.
            </summary>
            <param name="readFallbackHosts">The hosts to use as fallbacks for read operations.</param>
            <param name="writeFallbackHosts">The hosts to use as fallbacks for write operations.</param>
            <param name="primaryCoolDown">The amount of time to wait before the primary host will be used again after a failure.</param>
        </member>
        <member name="M:Azure.Core.Shared.GeoRedundantFallbackPolicy.SetHostAffinity(Azure.Core.HttpMessage,System.Boolean)">
            <summary>
            This can be used to indicate that the current host cannot be swapped for a specific request. This is useful when a client method
            must make multiple requests against the same endpoint.
            </summary>
            <param name="message">The message to mark the host affinity for.</param>
            <param name="hostAffinity">True if the host should not be swapped.</param>
        </member>
        <member name="T:Azure.Core.FixedDelayWithNoJitterStrategy">
            <summary>
            A delay strategy that uses a fixed delay with no jitter applied. This is used by data plane LROs.
            </summary>
        </member>
        <member name="T:Azure.Core.HashCodeBuilder">
            <summary>
            Copied from https://github.com/dotnet/corefx/blob/master/src/Common/src/CoreLib/System/HashCode.cs.
            </summary>
        </member>
        <member name="T:Azure.Core.InitializationConstructorAttribute">
            <summary>
            An attribute class indicating to AutoRest which constructor to use for initialization.
            </summary>
        </member>
        <member name="T:Azure.Core.MemoryResponse">
            <summary>
            A Response that can be constructed in memory without being tied to a
            live request.
            </summary>
        </member>
        <member name="P:Azure.Core.MemoryResponse.Status">
            <summary>
            Gets the HTTP status code.
            </summary>
        </member>
        <member name="P:Azure.Core.MemoryResponse.ReasonPhrase">
            <summary>
            Gets the HTTP reason phrase.
            </summary>
        </member>
        <member name="P:Azure.Core.MemoryResponse.ContentStream">
            <summary>
            Gets the contents of HTTP response. Returns <c>null</c> for responses without content.
            </summary>
        </member>
        <member name="P:Azure.Core.MemoryResponse.ClientRequestId">
            <summary>
            Gets the client request id that was sent to the server as <c>x-ms-client-request-id</c> headers.
            </summary>
        </member>
        <member name="M:Azure.Core.MemoryResponse.SetStatus(System.Int32)">
            <summary>
            Set the Response <see cref="P:Azure.Core.MemoryResponse.Status" />.
            </summary>
            <param name="status">The Response status.</param>
        </member>
        <member name="M:Azure.Core.MemoryResponse.SetReasonPhrase(System.String)">
            <summary>
            Set the Response <see cref="P:Azure.Core.MemoryResponse.ReasonPhrase" />.
            </summary>
            <param name="reasonPhrase">The Response ReasonPhrase.</param>
        </member>
        <member name="M:Azure.Core.MemoryResponse.SetContent(System.Byte[])">
            <summary>
            Set the Response <see cref="P:Azure.Core.MemoryResponse.ContentStream" />.
            </summary>
            <param name="content">The response content.</param>
        </member>
        <member name="M:Azure.Core.MemoryResponse.SetContent(System.String)">
            <summary>
            Set the Response <see cref="P:Azure.Core.MemoryResponse.ContentStream" />.
            </summary>
            <param name="content">The response content.</param>
        </member>
        <member name="M:Azure.Core.MemoryResponse.Dispose">
            <summary>
            Dispose the Response.
            </summary>
        </member>
        <member name="M:Azure.Core.MemoryResponse.SetHeader(System.String,System.String)">
            <summary>
            Set the value of a response header (and overwrite any existing
            values).
            </summary>
            <param name="name">The name of the response header.</param>
            <param name="value">The response header value.</param>
        </member>
        <member name="M:Azure.Core.MemoryResponse.SetHeader(System.String,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Set the values of a response header (and overwrite any existing
            values).
            </summary>
            <param name="name">The name of the response header.</param>
            <param name="values">The response header values.</param>
        </member>
        <member name="M:Azure.Core.MemoryResponse.AddHeader(System.String,System.String)">
            <summary>
            Add a response header value.
            </summary>
            <param name="name">The name of the response header.</param>
            <param name="value">The response header value.</param>
        </member>
        <member name="M:Azure.Core.MemoryResponse.ContainsHeader(System.String)">
            <summary>
            Returns <c>true</c> if the header is stored in the collection.
            </summary>
            <param name="name">The header name.</param>
            <returns><c>true</c> if the specified header is stored in the collection, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Azure.Core.MemoryResponse.EnumerateHeaders">
            <summary>
            Returns an iterator for enumerating <see cref="T:Azure.Core.HttpHeader" /> in the response.
            </summary>
            <returns>The <see cref="T:System.Collections.Generic.IEnumerable`1" /> enumerating <see cref="T:Azure.Core.HttpHeader" /> in the response.</returns>
        </member>
        <member name="M:Azure.Core.MemoryResponse.TryGetHeader(System.String,System.String@)">
            <summary>
            Returns header value if the header is stored in the collection. If header has multiple values they are going to be joined with a comma.
            </summary>
            <param name="name">The header name.</param>
            <param name="value">The reference to populate with value.</param>
            <returns><c>true</c> if the specified header is stored in the collection, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Azure.Core.MemoryResponse.TryGetHeaderValues(System.String,System.Collections.Generic.IEnumerable{System.String}@)">
            <summary>
            Returns header values if the header is stored in the collection.
            </summary>
            <param name="name">The header name.</param>
            <param name="values">The reference to populate with values.</param>
            <returns><c>true</c> if the specified header is stored in the collection, otherwise <c>false</c>.</returns>
        </member>
        <member name="P:Azure.Core.OperationInternalBase.RawResponse">
            <summary>
            The last HTTP response received from the server. Its update already handled in calls to "<c>UpdateStatus</c>" and
            "<c>WaitForCompletionAsync</c>", but custom methods not supported by this class, such as "<c>CancelOperation</c>",
            must update it as well.
            <example>Usage example:
            <code>
              public Response GetRawResponse() =&gt; _operationInternal.RawResponse;
            </code>
            </example>
            </summary>
        </member>
        <member name="P:Azure.Core.OperationInternalBase.HasCompleted">
            <summary>
            Returns <c>true</c> if the long-running operation has completed.
            <example>Usage example:
            <code>
              public bool HasCompleted =&gt; _operationInternal.HasCompleted;
            </code>
            </example>
            </summary>
        </member>
        <member name="M:Azure.Core.OperationInternalBase.UpdateStatusAsync(System.Threading.CancellationToken)">
            <summary>
            Calls the server to get the latest status of the long-running operation, handling diagnostic scope creation for distributed
            tracing. The default scope name can be changed with the "<c>operationTypeName</c>" parameter passed to the constructor.
            <example>Usage example:
            <code>
              public async ValueTask&lt;Response&gt; UpdateStatusAsync(CancellationToken cancellationToken) =&gt;
                await _operationInternal.UpdateStatusAsync(cancellationToken).ConfigureAwait(false);
            </code>
            </example>
            </summary>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <returns>The HTTP response received from the server.</returns>
            <remarks>
            After a successful run, this method will update <see cref="P:Azure.Core.OperationInternalBase.RawResponse" /> and might update <see cref="P:Azure.Core.OperationInternalBase.HasCompleted" />.
            </remarks>
            <exception cref="T:Azure.RequestFailedException">Thrown if there's been any issues during the connection, or if the operation has completed with failures.</exception>
        </member>
        <member name="M:Azure.Core.OperationInternalBase.UpdateStatus(System.Threading.CancellationToken)">
            <summary>
            Calls the server to get the latest status of the long-running operation, handling diagnostic scope creation for distributed
            tracing. The default scope name can be changed with the "<c>operationTypeName</c>" parameter passed to the constructor.
            <example>Usage example:
            <code>
              public Response UpdateStatus(CancellationToken cancellationToken) =&gt; _operationInternal.UpdateStatus(cancellationToken);
            </code>
            </example>
            </summary>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <returns>The HTTP response received from the server.</returns>
            <remarks>
            After a successful run, this method will update <see cref="P:Azure.Core.OperationInternalBase.RawResponse" /> and might update <see cref="P:Azure.Core.OperationInternalBase.HasCompleted" />.
            </remarks>
            <exception cref="T:Azure.RequestFailedException">Thrown if there's been any issues during the connection, or if the operation has completed with failures.</exception>
        </member>
        <member name="M:Azure.Core.OperationInternalBase.WaitForCompletionResponseAsync(System.Threading.CancellationToken)">
            <summary>
            Periodically calls <see cref="M:Azure.Core.OperationInternalBase.UpdateStatusAsync(System.Threading.CancellationToken)" /> until the long-running operation completes.
            After each service call, a retry-after header may be returned to communicate that there is no reason to poll
            for status change until the specified time has passed.  The maximum of the retry after value and the fallback strategy
            is then used as the wait interval.
            Headers supported are: "Retry-After", "retry-after-ms", and "x-ms-retry-after-ms",
            <example>Usage example:
            <code>
              public async ValueTask&lt;Response&lt;T&gt;&gt; WaitForCompletionAsync(CancellationToken cancellationToken) =&gt;
                await _operationInternal.WaitForCompletionAsync(cancellationToken).ConfigureAwait(false);
            </code>
            </example>
            </summary>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <returns>The last HTTP response received from the server, including the final result of the long-running operation.</returns>
            <exception cref="T:Azure.RequestFailedException">Thrown if there's been any issues during the connection, or if the operation has completed with failures.</exception>
        </member>
        <member name="M:Azure.Core.OperationInternalBase.WaitForCompletionResponseAsync(System.TimeSpan,System.Threading.CancellationToken)">
            <summary>
            Periodically calls <see cref="M:Azure.Core.OperationInternalBase.UpdateStatusAsync(System.Threading.CancellationToken)" /> until the long-running operation completes. The interval
            between calls is defined by the parameter <paramref name="pollingInterval" />, but it can change based on information returned
            from the server. After each service call, a retry-after header may be returned to communicate that there is no reason to poll
            for status change until the specified time has passed. In this case, the maximum value between the <paramref name="pollingInterval" />
            parameter and the retry-after header is chosen as the wait interval. Headers supported are: "Retry-After", "retry-after-ms",
            and "x-ms-retry-after-ms".
            <example>Usage example:
            <code>
              public async ValueTask&lt;Response&lt;T&gt;&gt; WaitForCompletionAsync(TimeSpan pollingInterval, CancellationToken cancellationToken) =&gt;
                await _operationInternal.WaitForCompletionAsync(pollingInterval, cancellationToken).ConfigureAwait(false);
            </code>
            </example>
            </summary>
            <param name="pollingInterval">The interval between status requests to the server. <strong></strong></param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <returns>The last HTTP response received from the server, including the final result of the long-running operation.</returns>
            <exception cref="T:Azure.RequestFailedException">Thrown if there's been any issues during the connection, or if the operation has completed with failures.</exception>
        </member>
        <member name="M:Azure.Core.OperationInternalBase.WaitForCompletionResponse(System.Threading.CancellationToken)">
            <summary>
            Periodically calls <see cref="M:Azure.Core.OperationInternalBase.UpdateStatus(System.Threading.CancellationToken)" /> until the long-running operation completes.
            After each service call, a retry-after header may be returned to communicate that there is no reason to poll
            for status change until the specified time has passed.  The maximum of the retry after value and the fallback strategy
            is then used as the wait interval.
            Headers supported are: "Retry-After", "retry-after-ms", and "x-ms-retry-after-ms",
            and "x-ms-retry-after-ms".
            <example>Usage example:
            <code>
              public async ValueTask&lt;Response&lt;T&gt;&gt; WaitForCompletionAsync(TimeSpan pollingInterval, CancellationToken cancellationToken) =&gt;
                await _operationInternal.WaitForCompletionAsync(pollingInterval, cancellationToken).ConfigureAwait(false);
            </code>
            </example>
            </summary>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <returns>The last HTTP response received from the server, including the final result of the long-running operation.</returns>
            <exception cref="T:Azure.RequestFailedException">Thrown if there's been any issues during the connection, or if the operation has completed with failures.</exception>
        </member>
        <member name="M:Azure.Core.OperationInternalBase.WaitForCompletionResponse(System.TimeSpan,System.Threading.CancellationToken)">
            <summary>
            Periodically calls <see cref="M:Azure.Core.OperationInternalBase.UpdateStatus(System.Threading.CancellationToken)" /> until the long-running operation completes. The interval
            between calls is defined by the parameter <paramref name="pollingInterval" />, but it can change based on information returned
            from the server. After each service call, a retry-after header may be returned to communicate that there is no reason to poll
            for status change until the specified time has passed. In this case, the maximum value between the <paramref name="pollingInterval" />
            parameter and the retry-after header is chosen as the wait interval. Headers supported are: "Retry-After", "retry-after-ms",
            and "x-ms-retry-after-ms".
            <example>Usage example:
            <code>
              public async ValueTask&lt;Response&lt;T&gt;&gt; WaitForCompletionAsync(TimeSpan pollingInterval, CancellationToken cancellationToken) =&gt;
                await _operationInternal.WaitForCompletionAsync(pollingInterval, cancellationToken).ConfigureAwait(false);
            </code>
            </example>
            </summary>
            <param name="pollingInterval">The interval between status requests to the server.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <returns>The last HTTP response received from the server, including the final result of the long-running operation.</returns>
            <exception cref="T:Azure.RequestFailedException">Thrown if there's been any issues during the connection, or if the operation has completed with failures.</exception>
        </member>
        <member name="T:Azure.Core.OperationInternal">
            <summary>
            A helper class used to build long-running operation instances. In order to use this helper:
            <list type="number">
              <item>Make sure your LRO implements the <see cref="T:Azure.Core.IOperation" /> interface.</item>
              <item>Add a private <see cref="T:Azure.Core.OperationInternal" /> field to your LRO, and instantiate it during construction.</item>
              <item>Delegate method calls to the <see cref="T:Azure.Core.OperationInternal" /> implementations.</item>
            </list>
            Supported members:
            <list type="bullet">
              <item>
                <description><see cref="P:Azure.Core.OperationInternalBase.HasCompleted" /></description>
              </item>
              <item>
                <description><see cref="P:Azure.Core.OperationInternalBase.RawResponse" />, used for <see cref="M:Azure.Operation.GetRawResponse" /></description>
              </item>
              <item>
                <description><see cref="M:Azure.Core.OperationInternalBase.UpdateStatus(System.Threading.CancellationToken)" /></description>
              </item>
              <item>
                <description><see cref="M:Azure.Core.OperationInternalBase.UpdateStatusAsync(System.Threading.CancellationToken)" /></description>
              </item>
              <item>
                <description><see cref="M:Azure.Core.OperationInternalBase.WaitForCompletionResponseAsync(System.Threading.CancellationToken)" /></description>
              </item>
              <item>
                <description><see cref="M:Azure.Core.OperationInternalBase.WaitForCompletionResponseAsync(System.TimeSpan,System.Threading.CancellationToken)" /></description>
              </item>
            </list>
            </summary>
        </member>
        <member name="M:Azure.Core.OperationInternal.Succeeded(Azure.Response)">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.Core.OperationInternal" /> class in a final successful state.
            </summary>
            <param name="rawResponse">The final value of <see cref="P:Azure.Core.OperationInternalBase.RawResponse" />.</param>
        </member>
        <member name="M:Azure.Core.OperationInternal.Failed(Azure.Response,Azure.RequestFailedException)">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.Core.OperationInternal" /> class in a final failed state.
            </summary>
            <param name="rawResponse">The final value of <see cref="P:Azure.Core.OperationInternalBase.RawResponse" />.</param>
            <param name="operationFailedException">The exception that will be thrown by <c>UpdateStatusAsync</c>.</param>
        </member>
        <member name="M:Azure.Core.OperationInternal.#ctor(Azure.Core.IOperation,Azure.Core.Pipeline.ClientDiagnostics,Azure.Response,System.String,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}},Azure.Core.DelayStrategy)">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.Core.OperationInternal" /> class.
            </summary>
            <param name="operation">The long-running operation making use of this class. Passing "<c>this</c>" is expected.</param>
            <param name="clientDiagnostics">Used for diagnostic scope and exception creation. This is expected to be the instance created during the construction of your main client.</param>
            <param name="rawResponse">
                The initial value of <see cref="P:Azure.Core.OperationInternalBase.RawResponse" />. Usually, long-running operation objects can be instantiated in two ways:
                <list type="bullet">
                    <item>
                        When calling a client's "<c>Start&lt;OperationName&gt;</c>" method, a service call is made to start the operation, and an <see cref="T:Azure.Operation" /> instance is returned.
                        In this case, the response received from this service call can be passed here.
                    </item>
                    <item>
                        When a user instantiates an <see cref="T:Azure.Operation" /> directly using a public constructor, there's no previous service call. In this case, passing <c>null</c> is expected.
                    </item>
                </list>
            </param>
            <param name="operationTypeName">
                The type name of the long-running operation making use of this class. Used when creating diagnostic scopes. If left <c>null</c>, the type name will be inferred based on the
                parameter <paramref name="operation" />.
            </param>
            <param name="scopeAttributes">The attributes to use during diagnostic scope creation.</param>
            <param name="fallbackStrategy"> The delay strategy to use. Default is <see cref="T:Azure.Core.FixedDelayWithNoJitterStrategy" />.</param>
        </member>
        <member name="T:Azure.Core.IOperation">
            <summary>
            An interface used by <see cref="T:Azure.Core.OperationInternal" /> for making service calls and updating state. It's expected that
            your long-running operation classes implement this interface.
            </summary>
        </member>
        <member name="M:Azure.Core.IOperation.UpdateStateAsync(System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Calls the service and updates the state of the long-running operation. Properties directly handled by the
            <see cref="T:Azure.Core.OperationInternal" /> class, such as <see cref="P:Azure.Core.OperationInternalBase.RawResponse" />
            don't need to be updated. Operation-specific properties, such as "<c>CreateOn</c>" or "<c>LastModified</c>",
            must be manually updated by the operation implementing this method.
            <example>Usage example:
            <code>
              async ValueTask&lt;OperationState&gt; IOperation.UpdateStateAsync(bool async, CancellationToken cancellationToken)<br />
              {<br />
                Response&lt;R&gt; response = async ? &lt;async service call&gt; : &lt;sync service call&gt;;<br />
                if (&lt;operation succeeded&gt;) return OperationState.Success(response.GetRawResponse(), &lt;parse response&gt;);<br />
                if (&lt;operation failed&gt;) return OperationState.Failure(response.GetRawResponse());<br />
                return OperationState.Pending(response.GetRawResponse());<br />
              }
            </code>
            </example>
            </summary>
            <param name="async"><c>true</c> if the call should be executed asynchronously. Otherwise, <c>false</c>.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <returns>
            A structure indicating the current operation state. The <see cref="T:Azure.Core.OperationState" /> structure must be instantiated by one of
            its static methods:
            <list type="bullet">
              <item>Use <see cref="M:Azure.Core.OperationState.Success(Azure.Response)" /> when the operation has completed successfully.</item>
              <item>Use <see cref="M:Azure.Core.OperationState.Failure(Azure.Response,Azure.RequestFailedException)" /> when the operation has completed with failures.</item>
              <item>Use <see cref="M:Azure.Core.OperationState.Pending(Azure.Response)" /> when the operation has not completed yet.</item>
            </list>
            </returns>
        </member>
        <member name="T:Azure.Core.OperationState">
            <summary>
            A helper structure passed to <see cref="T:Azure.Core.OperationInternal" /> to indicate the current operation state. This structure must be
            instantiated by one of its static methods, depending on the operation state:
            <list type="bullet">
              <item>Use <see cref="M:Azure.Core.OperationState.Success(Azure.Response)" /> when the operation has completed successfully.</item>
              <item>Use <see cref="M:Azure.Core.OperationState.Failure(Azure.Response,Azure.RequestFailedException)" /> when the operation has completed with failures.</item>
              <item>Use <see cref="M:Azure.Core.OperationState.Pending(Azure.Response)" /> when the operation has not completed yet.</item>
            </list>
            </summary>
        </member>
        <member name="M:Azure.Core.OperationState.Success(Azure.Response)">
            <summary>
            Instantiates an <see cref="T:Azure.Core.OperationState" /> indicating the operation has completed successfully.
            </summary>
            <param name="rawResponse">The HTTP response obtained during the status update.</param>
            <returns>A new <see cref="T:Azure.Core.OperationState" /> instance.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="rawResponse" /> is <c>null</c>.</exception>
        </member>
        <member name="M:Azure.Core.OperationState.Failure(Azure.Response,Azure.RequestFailedException)">
            <summary>
            Instantiates an <see cref="T:Azure.Core.OperationState" /> indicating the operation has completed with failures.
            </summary>
            <param name="rawResponse">The HTTP response obtained during the status update.</param>
            <param name="operationFailedException">
            The exception to throw from <c>UpdateStatus</c> because of the operation failure. If left <c>null</c>,
            a default exception is created based on the <paramref name="rawResponse" /> parameter.
            </param>
            <returns>A new <see cref="T:Azure.Core.OperationState" /> instance.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="rawResponse" /> is <c>null</c>.</exception>
        </member>
        <member name="M:Azure.Core.OperationState.Pending(Azure.Response)">
            <summary>
            Instantiates an <see cref="T:Azure.Core.OperationState" /> indicating the operation has not completed yet.
            </summary>
            <param name="rawResponse">The HTTP response obtained during the status update.</param>
            <returns>A new <see cref="T:Azure.Core.OperationState" /> instance.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="rawResponse" /> is <c>null</c>.</exception>
        </member>
        <member name="T:Azure.Core.OperationInternal`1">
            <summary>
            A helper class used to build long-running operation instances. In order to use this helper:
            <list type="number">
              <item>Make sure your LRO implements the <see cref="T:Azure.Core.IOperation`1" /> interface.</item>
              <item>Add a private <see cref="T:Azure.Core.OperationInternal`1" /> field to your LRO, and instantiate it during construction.</item>
              <item>Delegate method calls to the <see cref="T:Azure.Core.OperationInternal`1" /> implementations.</item>
            </list>
            Supported members:
            <list type="bullet">
              <item>
                <description><see cref="P:Azure.Core.OperationInternal`1.HasValue" /></description>
              </item>
              <item>
                <description><see cref="P:Azure.Core.OperationInternalBase.HasCompleted" /></description>
              </item>
              <item>
                <description><see cref="P:Azure.Core.OperationInternal`1.Value" /></description>
              </item>
              <item>
                <description><see cref="P:Azure.Core.OperationInternalBase.RawResponse" />, used for <see cref="M:Azure.Operation.GetRawResponse" /></description>
              </item>
              <item>
                <description><see cref="M:Azure.Core.OperationInternalBase.UpdateStatus(System.Threading.CancellationToken)" /></description>
              </item>
              <item>
                <description><see cref="M:Azure.Core.OperationInternalBase.UpdateStatusAsync(System.Threading.CancellationToken)" /></description>
              </item>
              <item>
                <description><see cref="M:Azure.Core.OperationInternal`1.WaitForCompletionAsync(System.Threading.CancellationToken)" /></description>
              </item>
              <item>
                <description><see cref="M:Azure.Core.OperationInternal`1.WaitForCompletionAsync(System.TimeSpan,System.Threading.CancellationToken)" /></description>
              </item>
            </list>
            </summary>
            <typeparam name="T">The final result of the long-running operation. Must match the type used in <see cref="T:Azure.Operation`1" />.</typeparam>
        </member>
        <member name="M:Azure.Core.OperationInternal`1.Succeeded(Azure.Response,`0)">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.Core.OperationInternal" /> class in a final successful state.
            </summary>
            <param name="rawResponse">The final value of <see cref="P:Azure.Core.OperationInternalBase.RawResponse" />.</param>
            <param name="value">The final result of the long-running operation.</param>
        </member>
        <member name="M:Azure.Core.OperationInternal`1.Failed(Azure.Response,Azure.RequestFailedException)">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.Core.OperationInternal" /> class in a final failed state.
            </summary>
            <param name="rawResponse">The final value of <see cref="P:Azure.Core.OperationInternalBase.RawResponse" />.</param>
            <param name="operationFailedException">The exception that will be thrown by <c>UpdateStatusAsync</c>.</param>
        </member>
        <member name="M:Azure.Core.OperationInternal`1.#ctor(Azure.Core.IOperation{`0},Azure.Core.Pipeline.ClientDiagnostics,Azure.Response,System.String,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}},Azure.Core.DelayStrategy)">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.Core.OperationInternal`1" /> class.
            </summary>
            <param name="operation">The long-running operation making use of this class. Passing "<c>this</c>" is expected.</param>
            <param name="clientDiagnostics">Used for diagnostic scope and exception creation. This is expected to be the instance created during the construction of your main client.</param>
            <param name="rawResponse">
                The initial value of <see cref="P:Azure.Core.OperationInternalBase.RawResponse" />. Usually, long-running operation objects can be instantiated in two ways:
                <list type="bullet">
                    <item>
                        When calling a client's "<c>Start&lt;OperationName&gt;</c>" method, a service call is made to start the operation, and an <see cref="T:Azure.Operation`1" /> instance is returned.
                        In this case, the response received from this service call can be passed here.
                    </item>
                    <item>
                        When a user instantiates an <see cref="T:Azure.Operation`1" /> directly using a public constructor, there's no previous service call. In this case, passing <c>null</c> is expected.
                    </item>
                </list>
            </param>
            <param name="operationTypeName">
                The type name of the long-running operation making use of this class. Used when creating diagnostic scopes. If left <c>null</c>, the type name will be inferred based on the
                parameter <paramref name="operation" />.
            </param>
            <param name="scopeAttributes">The attributes to use during diagnostic scope creation.</param>
            <param name="fallbackStrategy">The delay strategy when Retry-After header is not present.  When it is present, the longer of the two delays will be used.
                Default is <see cref="T:Azure.Core.FixedDelayWithNoJitterStrategy" />.</param>
        </member>
        <member name="P:Azure.Core.OperationInternal`1.HasValue">
            <summary>
            Returns <c>true</c> if the long-running operation completed successfully and has produced a final result.
            <example>Usage example:
            <code>
              public bool HasValue =&gt; _operationInternal.HasValue;
            </code>
            </example>
            </summary>
        </member>
        <member name="P:Azure.Core.OperationInternal`1.Value">
            <summary>
            The final result of the long-running operation.
            <example>Usage example:
            <code>
              public T Value =&gt; _operationInternal.Value;
            </code>
            </example>
            </summary>
            <exception cref="T:System.InvalidOperationException">Thrown when the operation has not completed yet.</exception>
            <exception cref="T:Azure.RequestFailedException">Thrown when the operation has completed with failures.</exception>
        </member>
        <member name="M:Azure.Core.OperationInternal`1.WaitForCompletionAsync(System.Threading.CancellationToken)">
            <summary>
            Periodically calls <see cref="M:Azure.Core.OperationInternalBase.UpdateStatusAsync(System.Threading.CancellationToken)" /> until the long-running operation completes.
            After each service call, a retry-after header may be returned to communicate that there is no reason to poll
            for status change until the specified time has passed.
            Headers supported are: "Retry-After", "retry-after-ms", and "x-ms-retry-after-ms",
            <example>Usage example:
            <code>
              public async ValueTask&lt;Response&lt;T&gt;&gt; WaitForCompletionAsync(CancellationToken cancellationToken) =&gt;
                await _operationInternal.WaitForCompletionAsync(cancellationToken).ConfigureAwait(false);
            </code>
            </example>
            </summary>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <returns>The last HTTP response received from the server, including the final result of the long-running operation.</returns>
            <exception cref="T:Azure.RequestFailedException">Thrown if there's been any issues during the connection, or if the operation has completed with failures.</exception>
        </member>
        <member name="M:Azure.Core.OperationInternal`1.WaitForCompletionAsync(System.TimeSpan,System.Threading.CancellationToken)">
            <summary>
            Periodically calls <see cref="M:Azure.Core.OperationInternalBase.UpdateStatusAsync(System.Threading.CancellationToken)" /> until the long-running operation completes. The interval
            between calls is defined by the parameter <paramref name="pollingInterval" />, but it can change based on information returned
            from the server. After each service call, a retry-after header may be returned to communicate that there is no reason to poll
            for status change until the specified time has passed. In this case, the maximum value between the <paramref name="pollingInterval" />
            parameter and the retry-after header is chosen as the wait interval. Headers supported are: "Retry-After", "retry-after-ms",
            and "x-ms-retry-after-ms".
            <example>Usage example:
            <code>
              public async ValueTask&lt;Response&lt;T&gt;&gt; WaitForCompletionAsync(TimeSpan pollingInterval, CancellationToken cancellationToken) =&gt;
                await _operationInternal.WaitForCompletionAsync(pollingInterval, cancellationToken).ConfigureAwait(false);
            </code>
            </example>
            </summary>
            <param name="pollingInterval">The interval between status requests to the server.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <returns>The last HTTP response received from the server, including the final result of the long-running operation.</returns>
            <exception cref="T:Azure.RequestFailedException">Thrown if there's been any issues during the connection, or if the operation has completed with failures.</exception>
        </member>
        <member name="M:Azure.Core.OperationInternal`1.WaitForCompletion(System.Threading.CancellationToken)">
            <summary>
            Periodically calls <see cref="M:Azure.Core.OperationInternalBase.UpdateStatus(System.Threading.CancellationToken)" /> until the long-running operation completes.
            After each service call, a retry-after header may be returned to communicate that there is no reason to poll
            for status change until the specified time has passed.
            Headers supported are: "Retry-After", "retry-after-ms", and "x-ms-retry-after-ms",
            <example>Usage example:
            <code>
              public async ValueTask&lt;Response&lt;T&gt;&gt; WaitForCompletionAsync(CancellationToken cancellationToken) =&gt;
                await _operationInternal.WaitForCompletionAsync(cancellationToken).ConfigureAwait(false);
            </code>
            </example>
            </summary>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <returns>The last HTTP response received from the server, including the final result of the long-running operation.</returns>
            <exception cref="T:Azure.RequestFailedException">Thrown if there's been any issues during the connection, or if the operation has completed with failures.</exception>
        </member>
        <member name="M:Azure.Core.OperationInternal`1.WaitForCompletion(System.TimeSpan,System.Threading.CancellationToken)">
            <summary>
            Periodically calls <see cref="M:Azure.Core.OperationInternalBase.UpdateStatus(System.Threading.CancellationToken)" /> until the long-running operation completes. The interval
            between calls is defined by the <see cref="T:Azure.Core.FixedDelayWithNoJitterStrategy" />, which takes into account any retry-after header that is returned
            from the server.
            <example>Usage example:
            <code>
              public async ValueTask&lt;Response&lt;T&gt;&gt; WaitForCompletionAsync(CancellationToken cancellationToken) =&gt;
                await _operationInternal.WaitForCompletionAsync(cancellationToken).ConfigureAwait(false);
            </code>
            </example>
            </summary>
            <param name="pollingInterval">The interval between status requests to the server.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <returns>The last HTTP response received from the server, including the final result of the long-running operation.</returns>
            <exception cref="T:Azure.RequestFailedException">Thrown if there's been any issues during the connection, or if the operation has completed with failures.</exception>
        </member>
        <member name="T:Azure.Core.IOperation`1">
            <summary>
            An interface used by <see cref="T:Azure.Core.OperationInternal`1" /> for making service calls and updating state. It's expected that
            your long-running operation classes implement this interface.
            </summary>
            <typeparam name="T">The final result of the long-running operation. Must match the type used in <see cref="T:Azure.Operation`1" />.</typeparam>
        </member>
        <member name="M:Azure.Core.IOperation`1.UpdateStateAsync(System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Calls the service and updates the state of the long-running operation. Properties directly handled by the
            <see cref="T:Azure.Core.OperationInternal`1" /> class, such as <see cref="P:Azure.Core.OperationInternalBase.RawResponse" /> or
            <see cref="P:Azure.Core.OperationInternal`1.Value" />, don't need to be updated. Operation-specific properties, such
            as "<c>CreateOn</c>" or "<c>LastModified</c>", must be manually updated by the operation implementing this
            method.
            <example>Usage example:
            <code>
              async ValueTask&lt;OperationState&lt;T&gt;&gt; IOperation&lt;T&gt;.UpdateStateAsync(bool async, CancellationToken cancellationToken)<br />
              {<br />
                Response&lt;R&gt; response = async ? &lt;async service call&gt; : &lt;sync service call&gt;;<br />
                if (&lt;operation succeeded&gt;) return OperationState&lt;T&gt;.Success(response.GetRawResponse(), &lt;parse response&gt;);<br />
                if (&lt;operation failed&gt;) return OperationState&lt;T&gt;.Failure(response.GetRawResponse());<br />
                return OperationState&lt;T&gt;.Pending(response.GetRawResponse());<br />
              }
            </code>
            </example>
            </summary>
            <param name="async"><c>true</c> if the call should be executed asynchronously. Otherwise, <c>false</c>.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <returns>
            A structure indicating the current operation state. The <see cref="T:Azure.Core.OperationState`1" /> structure must be instantiated by one of
            its static methods:
            <list type="bullet">
              <item>Use <see cref="M:Azure.Core.OperationState`1.Success(Azure.Response,`0)" /> when the operation has completed successfully.</item>
              <item>Use <see cref="M:Azure.Core.OperationState`1.Failure(Azure.Response,Azure.RequestFailedException)" /> when the operation has completed with failures.</item>
              <item>Use <see cref="M:Azure.Core.OperationState`1.Pending(Azure.Response)" /> when the operation has not completed yet.</item>
            </list>
            </returns>
        </member>
        <member name="T:Azure.Core.OperationState`1">
            <summary>
            A helper structure passed to <see cref="T:Azure.Core.OperationInternal`1" /> to indicate the current operation state. This structure must be
            instantiated by one of its static methods, depending on the operation state:
            <list type="bullet">
              <item>Use <see cref="M:Azure.Core.OperationState`1.Success(Azure.Response,`0)" /> when the operation has completed successfully.</item>
              <item>Use <see cref="M:Azure.Core.OperationState`1.Failure(Azure.Response,Azure.RequestFailedException)" /> when the operation has completed with failures.</item>
              <item>Use <see cref="M:Azure.Core.OperationState`1.Pending(Azure.Response)" /> when the operation has not completed yet.</item>
            </list>
            </summary>
            <typeparam name="T">The final result of the long-running operation. Must match the type used in <see cref="T:Azure.Operation`1" />.</typeparam>
        </member>
        <member name="M:Azure.Core.OperationState`1.Success(Azure.Response,`0)">
            <summary>
            Instantiates an <see cref="T:Azure.Core.OperationState`1" /> indicating the operation has completed successfully.
            </summary>
            <param name="rawResponse">The HTTP response obtained during the status update.</param>
            <param name="value">The final result of the long-running operation.</param>
            <returns>A new <see cref="T:Azure.Core.OperationState`1" /> instance.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="rawResponse" /> or <paramref name="value" /> is <c>null</c>.</exception>
        </member>
        <member name="M:Azure.Core.OperationState`1.Failure(Azure.Response,Azure.RequestFailedException)">
            <summary>
            Instantiates an <see cref="T:Azure.Core.OperationState`1" /> indicating the operation has completed with failures.
            </summary>
            <param name="rawResponse">The HTTP response obtained during the status update.</param>
            <param name="operationFailedException">
            The exception to throw from <c>UpdateStatus</c> because of the operation failure. The same exception will be thrown when
            <see cref="P:Azure.Core.OperationInternal`1.Value" /> is called. If left <c>null</c>, a default exception is created based on the
            <paramref name="rawResponse" /> parameter.
            </param>
            <returns>A new <see cref="T:Azure.Core.OperationState`1" /> instance.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="rawResponse" /> is <c>null</c>.</exception>
        </member>
        <member name="M:Azure.Core.OperationState`1.Pending(Azure.Response)">
            <summary>
            Instantiates an <see cref="T:Azure.Core.OperationState`1" /> indicating the operation has not completed yet.
            </summary>
            <param name="rawResponse">The HTTP response obtained during the status update.</param>
            <returns>A new <see cref="T:Azure.Core.OperationState`1" /> instance.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="rawResponse" /> is <c>null</c>.</exception>
        </member>
        <member name="T:Azure.Core.SequentialDelayStrategy">
            <summary>
            A delay strategy that uses a fixed sequence of delays with no jitter applied. This is used by management LROs.
            </summary>
        </member>
        <member name="T:Azure.Core.SerializationConstructorAttribute">
            <summary>
            An attribute class indicating to AutoRest which constructor to use for serialization.
            </summary>
        </member>
        <member name="M:Azure.Core.CancellationHelper.ShouldWrapInOperationCanceledException(System.Exception,System.Threading.CancellationToken)">
            <summary>Determines whether to wrap an <see cref="T:System.Exception" /> in a cancellation exception.</summary>
            <param name="exception">The exception.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> that may have triggered the exception.</param>
            <returns>true if the exception should be wrapped; otherwise, false.</returns>
        </member>
        <member name="M:Azure.Core.CancellationHelper.CreateOperationCanceledException(System.Exception,System.Threading.CancellationToken,System.String)">
            <summary>Creates a cancellation exception.</summary>
            <param name="innerException">The inner exception to wrap. May be null.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> that triggered the cancellation.</param>
            <param name="message">The custom message to use.</param>
            <returns>The cancellation exception.</returns>
        </member>
        <member name="M:Azure.Core.CancellationHelper.ThrowIfCancellationRequested(System.Threading.CancellationToken)">
            <summary>Throws a cancellation exception if cancellation has been requested via <paramref name="cancellationToken" />.</summary>
            <param name="cancellationToken">The token to check for a cancellation request.</param>
        </member>
        <member name="T:Azure.Core.OperationPoller">
            <summary>
            Implementation of LRO polling logic.
            </summary>
        </member>
        <member name="T:Azure.Core.TypeReferenceTypeAttribute">
            <summary>
            An attribute class indicating to Autorest a reference type which can replace a type in target SDKs.
            </summary>
        </member>
        <member name="T:Azure.AsyncPageable`1">
             <summary>
             A collection of values that may take multiple service requests to
             iterate over.
             </summary>
             <typeparam name="T">The type of the values.</typeparam>
             <example>
             Example of enumerating an AsyncPageable using the <c> async foreach </c> loop:
             <code snippet="Snippet:AsyncPageable" language="csharp">
             // call a service method, which returns AsyncPageable&lt;T&gt;
             AsyncPageable&lt;SecretProperties&gt; allSecretProperties = client.GetPropertiesOfSecretsAsync();
            
             await foreach (SecretProperties secretProperties in allSecretProperties)
             {
                 Console.WriteLine(secretProperties.Name);
             }
             </code>
             or using a while loop:
             <code snippet="Snippet:AsyncPageableLoop" language="csharp">
             // call a service method, which returns AsyncPageable&lt;T&gt;
             AsyncPageable&lt;SecretProperties&gt; allSecretProperties = client.GetPropertiesOfSecretsAsync();
            
             IAsyncEnumerator&lt;SecretProperties&gt; enumerator = allSecretProperties.GetAsyncEnumerator();
             try
             {
                 while (await enumerator.MoveNextAsync())
                 {
                     SecretProperties secretProperties = enumerator.Current;
                     Console.WriteLine(secretProperties.Name);
                 }
             }
             finally
             {
                 await enumerator.DisposeAsync();
             }
             </code>
             </example>
        </member>
        <member name="P:Azure.AsyncPageable`1.CancellationToken">
            <summary>
            Gets a <see cref="P:Azure.AsyncPageable`1.CancellationToken" /> used for requests made while
            enumerating asynchronously.
            </summary>
        </member>
        <member name="M:Azure.AsyncPageable`1.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.AsyncPageable`1" />
            class for mocking.
            </summary>
        </member>
        <member name="M:Azure.AsyncPageable`1.#ctor(System.Threading.CancellationToken)">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.AsyncPageable`1" />
            class.
            </summary>
            <param name="cancellationToken">
            The <see cref="P:Azure.AsyncPageable`1.CancellationToken" /> used for requests made while
            enumerating asynchronously.
            </param>
        </member>
        <member name="M:Azure.AsyncPageable`1.AsPages(System.String,System.Nullable{System.Int32})">
            <summary>
            Enumerate the values a <see cref="T:Azure.Page`1" /> at a time.  This may
            make multiple service requests.
            </summary>
            <param name="continuationToken">
            A continuation token indicating where to resume paging or null to
            begin paging from the beginning.
            </param>
            <param name="pageSizeHint">
            The number of items per <see cref="T:Azure.Page`1" /> that should be requested (from
            service operations that support it). It's not guaranteed that the value will be respected.
            </param>
            <returns>
            An async sequence of <see cref="T:Azure.Page`1" />s.
            </returns>
        </member>
        <member name="M:Azure.AsyncPageable`1.GetAsyncEnumerator(System.Threading.CancellationToken)">
            <summary>
            Enumerate the values in the collection asynchronously.  This may
            make multiple service requests.
            </summary>
            <param name="cancellationToken">
            The <see cref="P:Azure.AsyncPageable`1.CancellationToken" /> used for requests made while
            enumerating asynchronously.
            </param>
            <returns>An async sequence of values.</returns>
        </member>
        <member name="M:Azure.AsyncPageable`1.FromPages(System.Collections.Generic.IEnumerable{Azure.Page{`0}})">
            <summary>
            Creates an instance of <see cref="T:Azure.Pageable`1" /> using the provided pages.
            </summary>
            <param name="pages">The pages of values to list as part of net new pageable instance.</param>
            <returns>A new instance of <see cref="T:Azure.Pageable`1" /></returns>
        </member>
        <member name="M:Azure.AsyncPageable`1.ToString">
            <summary>
            Creates a string representation of an <see cref="T:Azure.AsyncPageable`1" />.
            </summary>
            <returns>
            A string representation of an <see cref="T:Azure.AsyncPageable`1" />.
            </returns>
        </member>
        <member name="M:Azure.AsyncPageable`1.Equals(System.Object)">
            <summary>
            Check if two <see cref="T:Azure.AsyncPageable`1" /> instances are equal.
            </summary>
            <param name="obj">The instance to compare to.</param>
            <returns>True if they're equal, false otherwise.</returns>
        </member>
        <member name="M:Azure.AsyncPageable`1.GetHashCode">
            <summary>
            Get a hash code for the <see cref="T:Azure.AsyncPageable`1" />.
            </summary>
            <returns>Hash code for the <see cref="T:Azure.Page`1" />.</returns>
        </member>
        <member name="T:Azure.AzureKeyCredential">
            <summary>
            Key credential used to authenticate to an Azure Service.
            It provides the ability to update the key without creating a new client.
            </summary>
        </member>
        <member name="P:Azure.AzureKeyCredential.Key">
            <summary>
            Key used to authenticate to an Azure service.
            </summary>
        </member>
        <member name="M:Azure.AzureKeyCredential.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.AzureKeyCredential" /> class.
            </summary>
            <param name="key">Key to use to authenticate with the Azure service.</param>
            <exception cref="T:System.ArgumentNullException">
            Thrown when the <paramref name="key" /> is null.
            </exception>
            <exception cref="T:System.ArgumentException">
            Thrown when the <paramref name="key" /> is empty.
            </exception>
        </member>
        <member name="M:Azure.AzureKeyCredential.Update(System.String)">
            <summary>
            Updates the service key.
            This is intended to be used when you've regenerated your service key
            and want to update long lived clients.
            </summary>
            <param name="key">Key to authenticate the service against.</param>
            <exception cref="T:System.ArgumentNullException">
            Thrown when the <paramref name="key" /> is null.
            </exception>
            <exception cref="T:System.ArgumentException">
            Thrown when the <paramref name="key" /> is empty.
            </exception>
        </member>
        <member name="T:Azure.AzureNamedKeyCredential">
            <summary>
            Credential allowing a named key to be used for authenticating to an Azure Service.
            It provides the ability to update the key without creating a new client.
            </summary>
        </member>
        <member name="P:Azure.AzureNamedKeyCredential.Name">
            <summary>
            Name of the key used to authenticate to an Azure service.
            </summary>
        </member>
        <member name="M:Azure.AzureNamedKeyCredential.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.AzureNamedKeyCredential" /> class.
            </summary>
            <param name="name">The name of the <paramref name="key" />.</param>
            <param name="key">The key to use for authenticating with the Azure service.</param>
            <exception cref="T:System.ArgumentNullException">
            Thrown when the <paramref name="name" /> or <paramref name="key" /> is null.
            </exception>
            <exception cref="T:System.ArgumentException">
            Thrown when the <paramref name="name" /> or <paramref name="key" /> is empty.
            </exception>
        </member>
        <member name="M:Azure.AzureNamedKeyCredential.Update(System.String,System.String)">
            <summary>
            Updates the named key.  This is intended to be used when you've regenerated your
            service key and want to update long-lived clients.
            </summary>
            <param name="name">The name of the <paramref name="key" />.</param>
            <param name="key">The key to use for authenticating with the Azure service.</param>
            <exception cref="T:System.ArgumentNullException">
            Thrown when the <paramref name="name" /> or <paramref name="key" /> is null.
            </exception>
            <exception cref="T:System.ArgumentException">
            Thrown when the <paramref name="name" /> or <paramref name="key" /> is empty.
            </exception>
        </member>
        <member name="M:Azure.AzureNamedKeyCredential.Deconstruct(System.String@,System.String@)">
             <summary>
             Allows deconstruction of the credential into the associated name and key as an atomic operation.
             </summary>
             <param name="name">The name of the <paramref name="key" />.</param>
             <param name="key">The key to use for authenticating with the Azure service.</param>
             <example>
             <code snippet="Snippet:AzureNamedKeyCredential_Deconstruct" language="csharp">
             var credential = new AzureNamedKeyCredential("SomeName", "SomeKey");
            
             (string name, string key) = credential;
             </code>
             </example>
             <seealso href="https://docs.microsoft.com/dotnet/csharp/deconstruct">Deconstructing tuples and other types</seealso>
        </member>
        <member name="T:Azure.AzureSasCredential">
            <summary>
            Shared access signature credential used to authenticate to an Azure Service.
            It provides the ability to update the shared access signature without creating a new client.
            </summary>
        </member>
        <member name="P:Azure.AzureSasCredential.Signature">
            <summary>
            Shared access signature used to authenticate to an Azure service.
            </summary>
        </member>
        <member name="M:Azure.AzureSasCredential.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.AzureSasCredential" /> class.
            </summary>
            <param name="signature">Shared access signature to use to authenticate with the Azure service.</param>
            <exception cref="T:System.ArgumentNullException">
            Thrown when the <paramref name="signature" /> is null.
            </exception>
            <exception cref="T:System.ArgumentException">
            Thrown when the <paramref name="signature" /> is empty.
            </exception>
        </member>
        <member name="M:Azure.AzureSasCredential.Update(System.String)">
            <summary>
            Updates the shared access signature.
            This is intended to be used when you've regenerated your shared access signature
            and want to update long lived clients.
            </summary>
            <param name="signature">Shared access signature to authenticate the service against.</param>
            <exception cref="T:System.ArgumentNullException">
            Thrown when the <paramref name="signature" /> is null.
            </exception>
            <exception cref="T:System.ArgumentException">
            Thrown when the <paramref name="signature" /> is empty.
            </exception>
        </member>
        <member name="T:Azure.ErrorOptions">
            <summary>
            ErrorOptions controls the behavior of an operation when an unexpected response status code is received.
            </summary>
        </member>
        <member name="F:Azure.ErrorOptions.Default">
            <summary>
            Indicates that an operation should throw an exception when the response indicates a failure.
            </summary>
        </member>
        <member name="F:Azure.ErrorOptions.NoThrow">
            <summary>
            Indicates that an operation should not throw an exception when the response indicates a failure.
            Callers should check the Response.IsError property instead of catching exceptions.
            </summary>
        </member>
        <member name="T:Azure.ETag">
            <summary>
            Represents an HTTP ETag.
            </summary>
        </member>
        <member name="M:Azure.ETag.#ctor(System.String)">
            <summary>
            Creates a new instance of <see cref="T:Azure.ETag" />.
            </summary>
            <param name="etag">The string value of the ETag.</param>
        </member>
        <member name="M:Azure.ETag.op_Equality(Azure.ETag,Azure.ETag)">
            <summary>
            Compares equality of two <see cref="T:Azure.ETag" /> instances.
            </summary>
            <param name="left">The <see cref="T:Azure.ETag" /> to compare.</param>
            <param name="right">The <see cref="T:Azure.ETag" /> to compare to.</param>
            <returns><c>true</c> if values of both ETags are equal, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Azure.ETag.op_Inequality(Azure.ETag,Azure.ETag)">
            <summary>
            Compares inequality of two <see cref="T:Azure.ETag" /> instances.
            </summary>
            <param name="left">The <see cref="T:Azure.ETag" /> to compare.</param>
            <param name="right">The <see cref="T:Azure.ETag" /> to compare to.</param>
            <returns><c>true</c> if values of both ETags are not equal, otherwise <c>false</c>.</returns>
        </member>
        <member name="F:Azure.ETag.All">
            <summary>
            Instance of <see cref="T:Azure.ETag" /> with the value. <code>*</code>
            </summary>
        </member>
        <member name="M:Azure.ETag.Equals(Azure.ETag)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary><returns>true if the current object is equal to the <paramref name="other" /> parameter; otherwise, false.</returns><param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:Azure.ETag.Equals(System.String)">
            <summary>
            Indicates whether the value of current <see cref="T:Azure.ETag" /> is equal to the provided string.</summary>
            <param name="other">An object to compare with this object.</param>
            <returns>true if the current object is equal to the <paramref name="other">other</paramref> parameter; otherwise, false.</returns>
        </member>
        <member name="M:Azure.ETag.Equals(System.Object)">
            <summary>Indicates whether this instance and a specified object are equal.</summary><returns>true if <paramref name="obj" /> and this instance are the same type and represent the same value; otherwise, false. </returns><param name="obj">The object to compare with the current instance. </param><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.ETag.GetHashCode">
            <summary>Returns the hash code for this instance.</summary><returns>A 32-bit signed integer that is the hash code for this instance.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.ETag.ToString">
             <summary>
            
             </summary>
             <returns>The string representation of this <see cref="T:Azure.ETag" />.</returns>
        </member>
        <member name="M:Azure.ETag.ToString(System.String)">
            <summary>
            Returns the string representation of the <see cref="T:Azure.ETag" />.
            </summary>
            <param name="format">A format string. Valid values are "G" for standard format and "H" for header format.</param>
            <returns>The formatted string representation of this <see cref="T:Azure.ETag" />. This includes outer quotes and the W/ prefix in the case of weak ETags.</returns>
            <example>
            <code>
            ETag tag = ETag.Parse("\"sometag\"");
            Console.WriteLine(tag.ToString("G"));
            // Displays: sometag
            Console.WriteLine(tag.ToString("H"));
            // Displays: "sometag"
            </code>
            </example>
        </member>
        <member name="T:Azure.HttpAuthorization">
            <summary>
            Represents authentication information in Authorization, ProxyAuthorization,
            WWW-Authenticate, and Proxy-Authenticate header values.
            </summary>
        </member>
        <member name="P:Azure.HttpAuthorization.Scheme">
            <summary>
            Gets the scheme to use for authorization.
            </summary>
        </member>
        <member name="P:Azure.HttpAuthorization.Parameter">
            <summary>
            Gets the credentials containing the authentication information of the
            user agent for the resource being requested.
            </summary>
        </member>
        <member name="M:Azure.HttpAuthorization.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.HttpAuthorization" /> class.
            </summary>
            <param name="scheme">
            The scheme to use for authorization.
            </param>
            <param name="parameter">
            The credentials containing the authentication information of the
            user agent for the resource being requested.
            </param>
        </member>
        <member name="M:Azure.HttpAuthorization.ToString">
            <summary>
            Returns a string that represents the current <see cref="T:Azure.HttpAuthorization" /> object.
            </summary>
        </member>
        <member name="T:Azure.HttpRange">
            <summary>
            Defines a range of bytes within an HTTP resource, starting at an offset and
            ending at offset+count-1 inclusively.
            </summary>
        </member>
        <member name="P:Azure.HttpRange.Offset">
            <summary>
            Gets the starting offset of the <see cref="T:Azure.HttpRange" />.
            </summary>
        </member>
        <member name="P:Azure.HttpRange.Length">
            <summary>
            Gets the size of the <see cref="T:Azure.HttpRange" />.  null means the range
            extends all the way to the end.
            </summary>
        </member>
        <member name="M:Azure.HttpRange.#ctor(System.Int64,System.Nullable{System.Int64})">
            <summary>
            Creates an instance of HttpRange.
            </summary>
            <param name="offset">The starting offset of the <see cref="T:Azure.HttpRange" />. Defaults to 0.</param>
            <param name="length">The length of the range. null means to the end.</param>
        </member>
        <member name="M:Azure.HttpRange.ToString">
            <summary>
            Converts the specified range to a string.
            </summary>
            <returns>String representation of the range.</returns>
            <remarks>For more information, see https://docs.microsoft.com/en-us/rest/api/storageservices/specifying-the-range-header-for-file-service-operations. </remarks>
        </member>
        <member name="M:Azure.HttpRange.op_Equality(Azure.HttpRange,Azure.HttpRange)">
            <summary>
            Check if two <see cref="T:Azure.HttpRange" /> instances are equal.
            </summary>
            <param name="left">The first instance to compare.</param>
            <param name="right">The second instance to compare.</param>
            <returns>True if they're equal, false otherwise.</returns>
        </member>
        <member name="M:Azure.HttpRange.op_Inequality(Azure.HttpRange,Azure.HttpRange)">
            <summary>
            Check if two <see cref="T:Azure.HttpRange" /> instances are not equal.
            </summary>
            <param name="left">The first instance to compare.</param>
            <param name="right">The second instance to compare.</param>
            <returns>True if they're not equal, false otherwise.</returns>
        </member>
        <member name="M:Azure.HttpRange.Equals(Azure.HttpRange)">
            <summary>
            Check if two <see cref="T:Azure.HttpRange" /> instances are equal.
            </summary>
            <param name="other">The instance to compare to.</param>
            <returns>True if they're equal, false otherwise.</returns>
        </member>
        <member name="M:Azure.HttpRange.Equals(System.Object)">
            <summary>
            Check if two <see cref="T:Azure.HttpRange" /> instances are equal.
            </summary>
            <param name="obj">The instance to compare to.</param>
            <returns>True if they're equal, false otherwise.</returns>
        </member>
        <member name="M:Azure.HttpRange.GetHashCode">
            <summary>
            Get a hash code for the <see cref="T:Azure.HttpRange" />.
            </summary>
            <returns>Hash code for the <see cref="T:Azure.HttpRange" />.</returns>
        </member>
        <member name="T:Azure.JsonPatchDocument">
            <summary>
            Represents a JSON Patch document.
            </summary>
        </member>
        <member name="M:Azure.JsonPatchDocument.#ctor">
            <summary>
            Initializes a new instance of <see cref="T:Azure.JsonPatchDocument" /> that uses <see cref="T:Azure.Core.Serialization.JsonObjectSerializer" /> as the default serializer.
            </summary>
        </member>
        <member name="M:Azure.JsonPatchDocument.#ctor(Azure.Core.Serialization.ObjectSerializer)">
            <summary>
            Initializes a new instance of <see cref="T:Azure.JsonPatchDocument" />
            </summary>
            <param name="serializer">The <see cref="T:Azure.Core.Serialization.ObjectSerializer" /> instance to use for value serialization.</param>
        </member>
        <member name="M:Azure.JsonPatchDocument.#ctor(System.ReadOnlyMemory{System.Byte})">
            <summary>
            Initializes a new instance of <see cref="T:Azure.JsonPatchDocument" />
            </summary>
            <param name="rawDocument">The binary representation of JSON Patch document.</param>
        </member>
        <member name="M:Azure.JsonPatchDocument.#ctor(System.ReadOnlyMemory{System.Byte},Azure.Core.Serialization.ObjectSerializer)">
            <summary>
            Initializes a new instance of <see cref="T:Azure.JsonPatchDocument" /> using an existing UTF8-encoded JSON Patch document.
            </summary>
            <param name="rawDocument">The binary representation of JSON Patch document.</param>
            <param name="serializer">The <see cref="T:Azure.Core.Serialization.ObjectSerializer" /> instance to use for value serialization.</param>
        </member>
        <member name="M:Azure.JsonPatchDocument.AppendAddRaw(System.String,System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Azure.JsonPatchDocument" /> using an existing UTF8-encoded JSON Patch document.
            </summary>
            <param name="path">The path to apply the addition to.</param>
            <param name="rawJsonValue">The raw JSON value to add to the path.</param>
        </member>
        <member name="M:Azure.JsonPatchDocument.AppendAdd``1(System.String,``0)">
            <summary>
            Appends an "add" operation to this <see cref="T:Azure.JsonPatchDocument" />.
            </summary>
            <param name="path">The path to apply the addition to.</param>
            <param name="value">The value to add to the path.</param>
        </member>
        <member name="M:Azure.JsonPatchDocument.AppendReplaceRaw(System.String,System.String)">
            <summary>
            Appends a "replace" operation to this <see cref="T:Azure.JsonPatchDocument" />.
            </summary>
            <param name="path">The path to replace.</param>
            <param name="rawJsonValue">The raw JSON value to replace with.</param>
        </member>
        <member name="M:Azure.JsonPatchDocument.AppendReplace``1(System.String,``0)">
            <summary>
            Appends a "replace" operation to this <see cref="T:Azure.JsonPatchDocument" />.
            </summary>
            <param name="path">The path to replace.</param>
            <param name="value">The value to replace with.</param>
        </member>
        <member name="M:Azure.JsonPatchDocument.AppendCopy(System.String,System.String)">
            <summary>
            Appends a "copy" operation to this <see cref="T:Azure.JsonPatchDocument" />.
            </summary>
            <param name="from">The path to copy from.</param>
            <param name="path">The path to copy to.</param>
        </member>
        <member name="M:Azure.JsonPatchDocument.AppendMove(System.String,System.String)">
            <summary>
            Appends a "move" operation to this <see cref="T:Azure.JsonPatchDocument" />.
            </summary>
            <param name="from">The path to move from.</param>
            <param name="path">The path to move to.</param>
        </member>
        <member name="M:Azure.JsonPatchDocument.AppendRemove(System.String)">
            <summary>
            Appends a "remove" operation to this <see cref="T:Azure.JsonPatchDocument" />.
            </summary>
            <param name="path">The path to remove.</param>
        </member>
        <member name="M:Azure.JsonPatchDocument.AppendTestRaw(System.String,System.String)">
            <summary>
            Appends a "test" operation to this <see cref="T:Azure.JsonPatchDocument" />.
            </summary>
            <param name="path">The path to test.</param>
            <param name="rawJsonValue">The raw JSON value to test against.</param>
        </member>
        <member name="M:Azure.JsonPatchDocument.AppendTest``1(System.String,``0)">
            <summary>
            Appends a "test" operation to this <see cref="T:Azure.JsonPatchDocument" />.
            </summary>
            <param name="path">The path to test.</param>
            <param name="value">The value to replace with.</param>
        </member>
        <member name="M:Azure.JsonPatchDocument.ToBytes">
            <summary>
            Returns a UTF8-encoded representation of this <see cref="T:Azure.JsonPatchDocument" /> instance.
            </summary>
            <returns>The UTF8-encoded JSON.</returns>
        </member>
        <member name="M:Azure.JsonPatchDocument.ToString">
            <summary>
            Returns a formatted JSON string representation of this <see cref="T:Azure.JsonPatchDocument" />.
            </summary>
            <returns>A formatted JSON string representation of this <see cref="T:Azure.JsonPatchDocument" />.</returns>
        </member>
        <member name="T:Azure.MatchConditions">
            <summary>
            Specifies HTTP options for conditional requests.
            </summary>
        </member>
        <member name="P:Azure.MatchConditions.IfMatch">
            <summary>
            Optionally limit requests to resources that have a matching ETag.
            </summary>
        </member>
        <member name="P:Azure.MatchConditions.IfNoneMatch">
            <summary>
            Optionally limit requests to resources that do not match the ETag.
            </summary>
        </member>
        <member name="T:Azure.Messaging.CloudEvent">
            <summary> Represents a CloudEvent conforming to the 1.0 schema. This type has built-in serialization using System.Text.Json.</summary>
        </member>
        <member name="M:Azure.Messaging.CloudEvent.#ctor(System.String,System.String,System.Object,System.Type)">
            <summary> Initializes a new instance of the <see cref="T:Azure.Messaging.CloudEvent" /> class. </summary>
            <param name="source"> Identifies the context in which an event happened. The combination of id and source must be unique for each distinct event. </param>
            <param name="type"> Type of event related to the originating occurrence. For example, "Contoso.Items.ItemReceived". </param>
            <param name="jsonSerializableData"> Event data specific to the event type. </param>
            <param name="dataSerializationType">The type to use when serializing the data.
            If not specified, <see cref="M:System.Object.GetType" /> will be used on <paramref name="jsonSerializableData" />.</param>
            <exception cref="T:System.ArgumentNullException">
            <paramref name="source" /> or <paramref name="type" /> was null.
            </exception>
        </member>
        <member name="M:Azure.Messaging.CloudEvent.#ctor(System.String,System.String,System.BinaryData,System.String,Azure.Messaging.CloudEventDataFormat)">
            <summary> Initializes a new instance of the <see cref="T:Azure.Messaging.CloudEvent" /> class using binary event data.</summary>
            <param name="source"> Identifies the context in which an event happened. The combination of id and source must be unique for each distinct event. </param>
            <param name="type"> Type of event related to the originating occurrence. For example, "Contoso.Items.ItemReceived". </param>
            <param name="data"> Binary event data specific to the event type. </param>
            <param name="dataContentType"> Content type of the payload. A content type different from "application/json" should be specified if payload is not JSON. </param>
            <param name="dataFormat">The format that the data of a <see cref="T:Azure.Messaging.CloudEvent" /> should be sent in
            when using the JSON envelope format.</param>
            <exception cref="T:System.ArgumentNullException">
            <paramref name="source" /> or <paramref name="type" /> was null.
            </exception>
        </member>
        <member name="P:Azure.Messaging.CloudEvent.Data">
            <summary>
            Gets or sets the event data as <see cref="T:System.BinaryData" />. Using BinaryData,
            one can deserialize the payload into rich data, or access the raw JSON data using <see cref="M:System.BinaryData.ToString" />.
            </summary>
        </member>
        <member name="P:Azure.Messaging.CloudEvent.Id">
            <summary>
            Gets or sets an identifier for the event. The combination of <see cref="P:Azure.Messaging.CloudEvent.Id" /> and <see cref="P:Azure.Messaging.CloudEvent.Source" /> must be unique for each distinct event.
            If not explicitly set, this will default to a <see cref="T:System.Guid" />.
            </summary>
        </member>
        <member name="P:Azure.Messaging.CloudEvent.Source">
            <summary>Gets or sets the context in which an event happened. The combination of <see cref="P:Azure.Messaging.CloudEvent.Id" />
            and <see cref="P:Azure.Messaging.CloudEvent.Source" /> must be unique for each distinct event.</summary>
        </member>
        <member name="P:Azure.Messaging.CloudEvent.Type">
            <summary>Gets or sets the type of event related to the originating occurrence.</summary>
        </member>
        <member name="P:Azure.Messaging.CloudEvent.SpecVersion">
            <summary>
            The spec version of the cloud event.
            </summary>
        </member>
        <member name="P:Azure.Messaging.CloudEvent.Time">
            <summary>
            Gets or sets the time (in UTC) the event was generated, in RFC3339 format.
            If not explicitly set, this will default to the time that the event is constructed.
            </summary>
        </member>
        <member name="P:Azure.Messaging.CloudEvent.DataSchema">
            <summary>Gets or sets the schema that the data adheres to.</summary>
        </member>
        <member name="P:Azure.Messaging.CloudEvent.DataContentType">
            <summary>Gets or sets the content type of the data.</summary>
        </member>
        <member name="P:Azure.Messaging.CloudEvent.Subject">
            <summary>Gets or sets the subject of the event in the context of the event producer (identified by source). </summary>
        </member>
        <member name="P:Azure.Messaging.CloudEvent.ExtensionAttributes">
            <summary>
            Gets extension attributes that can be additionally added to the CloudEvent envelope.
            </summary>
        </member>
        <member name="M:Azure.Messaging.CloudEvent.ParseMany(System.BinaryData,System.Boolean)">
            <summary>
            Given JSON-encoded events, parses the event envelope and returns an array of CloudEvents.
            If the specified event is not valid JSON an exception is thrown.
            By default, if the event is missing required properties, an exception is thrown though this can be relaxed
            by setting the <paramref name="skipValidation" /> parameter.
            </summary>
            <param name="json">An instance of <see cref="T:System.BinaryData" /> containing the JSON for one or more CloudEvents.</param>
            <param name="skipValidation">Set to <see langword="true" /> to allow missing or invalid properties to still parse into a CloudEvent.
            In particular, by setting strict to <see langword="true" />, the source, id, specversion and type properties are no longer required
            to be present in the JSON. Additionally, the casing requirements of the extension attribute names are relaxed.
            </param>
            <returns> An array of <see cref="T:Azure.Messaging.CloudEvent" /> instances.</returns>
        </member>
        <member name="M:Azure.Messaging.CloudEvent.Parse(System.BinaryData,System.Boolean)">
            <summary>
            Given a single JSON-encoded event, parses the event envelope and returns a <see cref="T:Azure.Messaging.CloudEvent" />.
            If the specified event is not valid JSON an exception is thrown.
            By default, if the event is missing required properties, an exception is thrown though this can be relaxed
            by setting the <paramref name="skipValidation" /> parameter.
            </summary>
            <param name="json">An instance of <see cref="T:System.BinaryData" /> containing the JSON for the CloudEvent.</param>
            <param name="skipValidation">Set to <see langword="true" /> to allow missing or invalid properties to still parse into a CloudEvent.
            In particular, by setting strict to <see langword="true" />, the source, id, specversion and type properties are no longer required
            to be present in the JSON. Additionally, the casing requirements of the extension attribute names are relaxed.
            </param>
            <returns> A <see cref="T:Azure.Messaging.CloudEvent" />. </returns>
            <exception cref="T:System.ArgumentException">
            <paramref name="json" /> contained multiple events. <see cref="M:Azure.Messaging.CloudEvent.ParseMany(System.BinaryData,System.Boolean)" /> should be used instead.
            </exception>
        </member>
        <member name="T:Azure.Messaging.CloudEventConverter">
            <summary>
            A custom converter that attributes the <see cref="T:Azure.Messaging.CloudEvent" /> type.
            This allows System.Text.Json to serialize and deserialize CloudEvents by default.
            </summary>
        </member>
        <member name="M:Azure.Messaging.CloudEventConverter.Read(System.Text.Json.Utf8JsonReader@,System.Type,System.Text.Json.JsonSerializerOptions)">
            <summary>
            Gets or sets the serializer to use for the data portion of the <see cref="T:Azure.Messaging.CloudEvent" />. If not specified,
            JsonObjectSerializer is used.
            </summary>
            <param name="reader">The reader.</param><param name="typeToConvert">The type to convert.</param><param name="options">An object that specifies serialization options to use.</param><returns>The converted value.</returns>
        </member>
        <member name="M:Azure.Messaging.CloudEventConverter.Write(System.Text.Json.Utf8JsonWriter,Azure.Messaging.CloudEvent,System.Text.Json.JsonSerializerOptions)">
            <summary>Writes a specified value as JSON.</summary><param name="writer">The writer to write to.</param><param name="value">The value to convert to JSON.</param><param name="options">An object that specifies serialization options to use.</param>
        </member>
        <member name="T:Azure.Messaging.CloudEventDataFormat">
            <summary>
            Specifies the format that the data of a <see cref="T:Azure.Messaging.CloudEvent" /> should be sent in
            when using the JSON envelope format for a <see cref="T:Azure.Messaging.CloudEvent" />.
            <see href="https://github.com/cloudevents/spec/blob/v1.0/json-format.md#31-handling-of-data" />.
            </summary>
        </member>
        <member name="F:Azure.Messaging.CloudEventDataFormat.Binary">
            <summary>
            Indicates the <see cref="P:Azure.Messaging.CloudEvent.Data" /> should be serialized as binary data.
            This data will be included as a Base64 encoded string in the "data_base64"
            field of the JSON payload.
            </summary>
        </member>
        <member name="F:Azure.Messaging.CloudEventDataFormat.Json">
            <summary>
            Indicates the <see cref="P:Azure.Messaging.CloudEvent.Data" /> should be serialized as JSON.
            The data will be included in the "data" field of the JSON payload.
            </summary>
        </member>
        <member name="T:Azure.Messaging.MessageContent">
            <summary>
            The content of a message containing a content type along with the message data.
            </summary>
        </member>
        <member name="P:Azure.Messaging.MessageContent.Data">
            <summary>
            Gets or sets the data.
            </summary>
        </member>
        <member name="P:Azure.Messaging.MessageContent.ContentType">
            <summary>
            Gets or sets the content type.
            </summary>
        </member>
        <member name="P:Azure.Messaging.MessageContent.ContentTypeCore">
            <summary>
            For inheriting types that have a string ContentType property, this property should be overriden to forward
            the <see cref="P:Azure.Messaging.MessageContent.ContentType" /> property into the inheriting type's string property, and vice versa.
            For types that have a <see cref="T:Azure.Core.ContentType" /> ContentType property, it is not necessary to override this member.
            </summary>
        </member>
        <member name="P:Azure.Messaging.MessageContent.IsReadOnly">
            <summary>
            Gets whether the message is read only or not. This
            can be overriden by inheriting classes to specify whether or
            not the message can be modified.
            </summary>
        </member>
        <member name="T:Azure.NullableResponse`1">
            <summary>
            Represents a result of Azure operation.
            </summary>
            <typeparam name="T">The type of returned value.</typeparam>
        </member>
        <member name="P:Azure.NullableResponse`1.HasValue">
            <summary>
            Gets a value indicating whether the current instance has a valid value of its underlying type.
            </summary>
        </member>
        <member name="P:Azure.NullableResponse`1.Value">
            <summary>
            Gets the value returned by the service. Accessing this property will throw if <see cref="P:Azure.NullableResponse`1.HasValue" /> is false.
            </summary>
        </member>
        <member name="M:Azure.NullableResponse`1.GetRawResponse">
            <summary>
            Returns the HTTP response returned by the service.
            </summary>
            <returns>The HTTP response returned by the service.</returns>
        </member>
        <member name="M:Azure.NullableResponse`1.Equals(System.Object)">
            <summary>Determines whether the specified object is equal to the current object.</summary><returns>true if the specified object  is equal to the current object; otherwise, false.</returns><param name="obj">The object to compare with the current object. </param><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.NullableResponse`1.GetHashCode">
            <summary>Serves as the default hash function. </summary><returns>A hash code for the current object.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.NullableResponse`1.ToString">
            <summary>Returns a string that represents the current object.</summary><returns>A string that represents the current object.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="T:Azure.Operation">
            <summary>
            Represents a long-running operation.
            </summary>
        </member>
        <member name="P:Azure.Operation.Id">
            <summary>
            Gets an ID representing the operation that can be used to poll for
            the status of the long-running operation.
            </summary>
        </member>
        <member name="M:Azure.Operation.GetRawResponse">
            <summary>
            The last HTTP response received from the server.
            </summary>
            <remarks>
            The last response returned from the server during the lifecycle of this instance.
            An instance of <see cref="T:Azure.Operation`1" /> sends requests to a server in UpdateStatusAsync, UpdateStatus, and other methods.
            Responses from these requests can be accessed using GetRawResponse.
            </remarks>
        </member>
        <member name="P:Azure.Operation.HasCompleted">
            <summary>
            Returns true if the long-running operation completed.
            </summary>
        </member>
        <member name="M:Azure.Operation.UpdateStatusAsync(System.Threading.CancellationToken)">
            <summary>
            Calls the server to get updated status of the long-running operation.
            </summary>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> used for the service call.</param>
            <returns>The HTTP response received from the server.</returns>
            <remarks>
            This operation will update the value returned from GetRawResponse and might update HasCompleted.
            </remarks>
        </member>
        <member name="M:Azure.Operation.UpdateStatus(System.Threading.CancellationToken)">
            <summary>
            Calls the server to get updated status of the long-running operation.
            </summary>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> used for the service call.</param>
            <returns>The HTTP response received from the server.</returns>
            <remarks>
            This operation will update the value returned from GetRawResponse and might update HasCompleted.
            </remarks>
        </member>
        <member name="M:Azure.Operation.WaitForCompletionResponseAsync(System.Threading.CancellationToken)">
            <summary>
            Periodically calls the server till the long-running operation completes.
            </summary>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> used for the periodical service calls.</param>
            <returns>The last HTTP response received from the server.</returns>
            <remarks>
            This method will periodically call UpdateStatusAsync till HasCompleted is true, then return the final response of the operation.
            </remarks>
        </member>
        <member name="M:Azure.Operation.WaitForCompletionResponseAsync(System.TimeSpan,System.Threading.CancellationToken)">
            <summary>
            Periodically calls the server till the long-running operation completes.
            </summary>
            <param name="pollingInterval">
            The interval between status requests to the server.
            The interval can change based on information returned from the server.
            For example, the server might communicate to the client that there is not reason to poll for status change sooner than some time.
            </param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> used for the periodical service calls.</param>
            <returns>The last HTTP response received from the server.</returns>
            <remarks>
            This method will periodically call UpdateStatusAsync till HasCompleted is true, then return the final response of the operation.
            </remarks>
        </member>
        <member name="M:Azure.Operation.WaitForCompletionResponseAsync(Azure.Core.DelayStrategy,System.Threading.CancellationToken)">
            <summary>
            Periodically calls the server till the long-running operation completes.
            </summary>
            <param name="delayStrategy">
            The strategy to use to determine the delay between status requests to the server. If the server returns retry-after header,
            the delay used will be the maximum specified by the strategy and the header value.
            </param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> used for the periodical service calls.</param>
            <returns>The last HTTP response received from the server.</returns>
            <remarks>
            This method will periodically call UpdateStatusAsync till HasCompleted is true, then return the final response of the operation.
            </remarks>
        </member>
        <member name="M:Azure.Operation.WaitForCompletionResponse(System.Threading.CancellationToken)">
            <summary>
            Periodically calls the server till the long-running operation completes.
            </summary>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> used for the periodical service calls.</param>
            <returns>The last HTTP response received from the server.</returns>
            <remarks>
            This method will periodically call UpdateStatusAsync till HasCompleted is true, then return the final response of the operation.
            </remarks>
        </member>
        <member name="M:Azure.Operation.WaitForCompletionResponse(System.TimeSpan,System.Threading.CancellationToken)">
            <summary>
            Periodically calls the server till the long-running operation completes.
            </summary>
            <param name="pollingInterval">
            The interval between status requests to the server.
            The interval can change based on information returned from the server.
            For example, the server might communicate to the client that there is not reason to poll for status change sooner than some time.
            </param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> used for the periodical service calls.</param>
            <returns>The last HTTP response received from the server.</returns>
            <remarks>
            This method will periodically call UpdateStatusAsync till HasCompleted is true, then return the final response of the operation.
            </remarks>
        </member>
        <member name="M:Azure.Operation.WaitForCompletionResponse(Azure.Core.DelayStrategy,System.Threading.CancellationToken)">
            <summary>
            Periodically calls the server till the long-running operation completes.
            </summary>
            <param name="delayStrategy">
            The strategy to use to determine the delay between status requests to the server. If the server returns retry-after header,
            the delay used will be the maximum specified by the strategy and the header value.
            </param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> used for the periodical service calls.</param>
            <returns>The last HTTP response received from the server.</returns>
            <remarks>
            This method will periodically call UpdateStatusAsync till HasCompleted is true, then return the final response of the operation.
            </remarks>
        </member>
        <member name="M:Azure.Operation.Equals(System.Object)">
            <summary>Determines whether the specified object is equal to the current object.</summary><returns>true if the specified object  is equal to the current object; otherwise, false.</returns><param name="obj">The object to compare with the current object. </param><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Operation.GetHashCode">
            <summary>Serves as the default hash function. </summary><returns>A hash code for the current object.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Operation.ToString">
            <summary>Returns a string that represents the current object.</summary><returns>A string that represents the current object.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="T:Azure.Operation`1">
            <summary>
            Represents a long-running operation that returns a value when it completes.
            </summary>
            <typeparam name="T">The final result of the long-running operation.</typeparam>
        </member>
        <member name="P:Azure.Operation`1.Value">
            <summary>
            Final result of the long-running operation.
            </summary>
            <remarks>
            This property can be accessed only after the operation completes successfully (HasValue is true).
            </remarks>
        </member>
        <member name="P:Azure.Operation`1.HasValue">
            <summary>
            Returns true if the long-running operation completed successfully and has produced final result (accessible by Value property).
            </summary>
        </member>
        <member name="M:Azure.Operation`1.WaitForCompletion(System.Threading.CancellationToken)">
            <summary>
            Periodically calls the server till the long-running operation completes.
            </summary>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> used for the periodical service calls.</param>
            <returns>The last HTTP response received from the server.</returns>
            <remarks>
            This method will periodically call UpdateStatusAsync till HasCompleted is true, then return the final result of the operation.
            </remarks>
        </member>
        <member name="M:Azure.Operation`1.WaitForCompletion(System.TimeSpan,System.Threading.CancellationToken)">
            <summary>
            Periodically calls the server till the long-running operation completes.
            </summary>
            <param name="pollingInterval">
            The interval between status requests to the server.
            The interval can change based on information returned from the server.
            For example, the server might communicate to the client that there is not reason to poll for status change sooner than some time.
            </param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> used for the periodical service calls.</param>
            <returns>The last HTTP response received from the server.</returns>
            <remarks>
            This method will periodically call UpdateStatusAsync till HasCompleted is true, then return the final result of the operation.
            </remarks>
        </member>
        <member name="M:Azure.Operation`1.WaitForCompletionAsync(System.Threading.CancellationToken)">
            <summary>
            Periodically calls the server till the long-running operation completes.
            </summary>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> used for the periodical service calls.</param>
            <returns>The last HTTP response received from the server.</returns>
            <remarks>
            This method will periodically call UpdateStatusAsync till HasCompleted is true, then return the final result of the operation.
            </remarks>
        </member>
        <member name="M:Azure.Operation`1.WaitForCompletionAsync(System.TimeSpan,System.Threading.CancellationToken)">
            <summary>
            Periodically calls the server till the long-running operation completes.
            </summary>
            <param name="pollingInterval">
            The interval between status requests to the server.
            The interval can change based on information returned from the server.
            For example, the server might communicate to the client that there is not reason to poll for status change sooner than some time.
            </param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> used for the periodical service calls.</param>
            <returns>The last HTTP response received from the server.</returns>
            <remarks>
            This method will periodically call UpdateStatusAsync till HasCompleted is true, then return the final result of the operation.
            </remarks>
        </member>
        <member name="M:Azure.Operation`1.WaitForCompletion(Azure.Core.DelayStrategy,System.Threading.CancellationToken)">
            <summary>
            Periodically calls the server till the long-running operation completes.
            </summary>
            <param name="delayStrategy">
            The strategy to use to determine the delay between status requests to the server. If the server returns retry-after header,
            the delay used will be the maximum specified by the strategy and the header value.
            </param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> used for the periodical service calls.</param>
            <returns>The last HTTP response received from the server.</returns>
            <remarks>
            This method will periodically call UpdateStatusAsync till HasCompleted is true, then return the final result of the operation.
            </remarks>
        </member>
        <member name="M:Azure.Operation`1.WaitForCompletionAsync(Azure.Core.DelayStrategy,System.Threading.CancellationToken)">
            <summary>
            Periodically calls the server till the long-running operation completes.
            </summary>
            <param name="delayStrategy">
            The strategy to use to determine the delay between status requests to the server. If the server returns retry-after header,
            the delay used will be the maximum specified by the strategy and the header value.
            </param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> used for the periodical service calls.</param>
            <returns>The last HTTP response received from the server.</returns>
            <remarks>
            This method will periodically call UpdateStatusAsync till HasCompleted is true, then return the final result of the operation.
            </remarks>
        </member>
        <member name="M:Azure.Operation`1.WaitForCompletionResponseAsync(System.Threading.CancellationToken)">
            <summary>
            Periodically calls the server till the long-running operation completes.
            </summary>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> used for the periodical service calls.</param>
            <returns>The last HTTP response received from the server.</returns>
            <remarks>
            This method will periodically call UpdateStatusAsync till HasCompleted is true, then return the final response of the operation.
            </remarks>
        </member>
        <member name="M:Azure.Operation`1.WaitForCompletionResponseAsync(System.TimeSpan,System.Threading.CancellationToken)">
            <summary>
            Periodically calls the server till the long-running operation completes.
            </summary>
            <param name="pollingInterval">
            The interval between status requests to the server.
            The interval can change based on information returned from the server.
            For example, the server might communicate to the client that there is not reason to poll for status change sooner than some time.
            </param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> used for the periodical service calls.</param>
            <returns>The last HTTP response received from the server.</returns>
            <remarks>
            This method will periodically call UpdateStatusAsync till HasCompleted is true, then return the final response of the operation.
            </remarks>
        </member>
        <member name="T:Azure.Page`1">
            <summary>
            A single <see cref="T:Azure.Page`1" /> of values from a request that may return
            zero or more <see cref="T:Azure.Page`1" />s of values.
            </summary>
            <typeparam name="T">The type of values.</typeparam>
        </member>
        <member name="P:Azure.Page`1.Values">
            <summary>
            Gets the values in this <see cref="T:Azure.Page`1" />.
            </summary>
        </member>
        <member name="P:Azure.Page`1.ContinuationToken">
            <summary>
            Gets the continuation token used to request the next
            <see cref="T:Azure.Page`1" />.  The continuation token may be null or
            empty when there are no more pages.
            </summary>
        </member>
        <member name="M:Azure.Page`1.GetRawResponse">
            <summary>
            Gets the <see cref="T:Azure.Response" /> that provided this
            <see cref="T:Azure.Page`1" />.
            </summary>
        </member>
        <member name="M:Azure.Page`1.FromValues(System.Collections.Generic.IReadOnlyList{`0},System.String,Azure.Response)">
            <summary>
            Creates a new <see cref="T:Azure.Page`1" />.
            </summary>
            <param name="values">
            The values in this <see cref="T:Azure.Page`1" />.
            </param>
            <param name="continuationToken">
            The continuation token used to request the next <see cref="T:Azure.Page`1" />.
            </param>
            <param name="response">
            The <see cref="T:Azure.Response" /> that provided this <see cref="T:Azure.Page`1" />.
            </param>
        </member>
        <member name="M:Azure.Page`1.ToString">
            <summary>
            Creates a string representation of an <see cref="T:Azure.Page`1" />.
            </summary>
            <returns>
            A string representation of an <see cref="T:Azure.Page`1" />.
            </returns>
        </member>
        <member name="M:Azure.Page`1.Equals(System.Object)">
            <summary>
            Check if two <see cref="T:Azure.Page`1" /> instances are equal.
            </summary>
            <param name="obj">The instance to compare to.</param>
            <returns>True if they're equal, false otherwise.</returns>
        </member>
        <member name="M:Azure.Page`1.GetHashCode">
            <summary>
            Get a hash code for the <see cref="T:Azure.Page`1" />.
            </summary>
            <returns>Hash code for the <see cref="T:Azure.Page`1" />.</returns>
        </member>
        <member name="T:Azure.Pageable`1">
            <summary>
            A collection of values that may take multiple service requests to
            iterate over.
            </summary>
            <typeparam name="T">The type of the values.</typeparam>
        </member>
        <member name="P:Azure.Pageable`1.CancellationToken">
            <summary>
            Gets a <see cref="P:Azure.Pageable`1.CancellationToken" /> used for requests made while
            enumerating asynchronously.
            </summary>
        </member>
        <member name="M:Azure.Pageable`1.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.Pageable`1" />
            class for mocking.
            </summary>
        </member>
        <member name="M:Azure.Pageable`1.#ctor(System.Threading.CancellationToken)">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.Pageable`1" />
            class.
            </summary>
            <param name="cancellationToken">
            The <see cref="P:Azure.Pageable`1.CancellationToken" /> used for requests made while
            enumerating asynchronously.
            </param>
        </member>
        <member name="M:Azure.Pageable`1.AsPages(System.String,System.Nullable{System.Int32})">
            <summary>
            Enumerate the values a <see cref="T:Azure.Page`1" /> at a time.  This may
            make multiple service requests.
            </summary>
            <param name="continuationToken">
            A continuation token indicating where to resume paging or null to
            begin paging from the beginning.
            </param>
            <param name="pageSizeHint">
            The number of items per <see cref="T:Azure.Page`1" /> that should be requested (from
            service operations that support it). It's not guaranteed that the value will be respected.
            </param>
            <returns>
            An async sequence of <see cref="T:Azure.Page`1" />s.
            </returns>
        </member>
        <member name="M:Azure.Pageable`1.ToString">
            <summary>
            Creates a string representation of an <see cref="T:Azure.Pageable`1" />.
            </summary>
            <returns>
            A string representation of an <see cref="T:Azure.Pageable`1" />.
            </returns>
        </member>
        <member name="M:Azure.Pageable`1.GetEnumerator">
            <summary>
            Enumerate the values in the collection.  This may make multiple service requests.
            </summary>
        </member>
        <member name="M:Azure.Pageable`1.FromPages(System.Collections.Generic.IEnumerable{Azure.Page{`0}})">
            <summary>
            Creates an instance of <see cref="T:Azure.Pageable`1" /> using the provided pages.
            </summary>
            <param name="pages">The pages of values to list as part of net new pageable instance.</param>
            <returns>A new instance of <see cref="T:Azure.Pageable`1" /></returns>
        </member>
        <member name="M:Azure.Pageable`1.Equals(System.Object)">
            <summary>
            Check if two <see cref="T:Azure.Pageable`1" /> instances are equal.
            </summary>
            <param name="obj">The instance to compare to.</param>
            <returns>True if they're equal, false otherwise.</returns>
        </member>
        <member name="M:Azure.Pageable`1.GetHashCode">
            <summary>
            Get a hash code for the <see cref="T:Azure.Pageable`1" />.
            </summary>
            <returns>Hash code for the <see cref="T:Azure.Pageable`1" />.</returns>
        </member>
        <member name="T:Azure.PageableOperation`1">
            <summary>
            Represents a pageable long-running operation that exposes the results
            in either synchronous or asynchronous format.
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="P:Azure.PageableOperation`1.Value">
            <summary>
            Gets the final result of the long-running operation asynchronously.
            </summary>
            <remarks>
            This property can be accessed only after the operation completes successfully (HasValue is true).
            </remarks>
        </member>
        <member name="M:Azure.PageableOperation`1.GetValuesAsync(System.Threading.CancellationToken)">
            <summary>
            Gets the final result of the long-running operation asynchronously.
            </summary>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> used for the periodical service calls.</param>
            <returns>The final result of the long-running operation asynchronously.</returns>
            <remarks>
            Operation must complete successfully (HasValue is true) for it to provide values.
            </remarks>
        </member>
        <member name="M:Azure.PageableOperation`1.GetValues(System.Threading.CancellationToken)">
            <summary>
            Gets the final result of the long-running operation synchronously.
            </summary>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> used for the periodical service calls.</param>
            <returns>The final result of the long-running operation synchronously.</returns>
            <remarks>
            Operation must complete successfully (HasValue is true) for it to provide values.
            </remarks>
        </member>
        <member name="T:Azure.RequestConditions">
            <summary>
            Specifies HTTP options for conditional requests based on modification time.
            </summary>
        </member>
        <member name="P:Azure.RequestConditions.IfModifiedSince">
            <summary>
            Optionally limit requests to resources that have only been
            modified since this point in time.
            </summary>
        </member>
        <member name="P:Azure.RequestConditions.IfUnmodifiedSince">
            <summary>
            Optionally limit requests to resources that have remained
            unmodified.
            </summary>
        </member>
        <member name="T:Azure.RequestContext">
            <summary>
            Options that can be used to control the behavior of a request sent by a client.
            </summary>
        </member>
        <member name="P:Azure.RequestContext.ErrorOptions">
            <summary>
            Controls under what conditions the operation raises an exception if the underlying response indicates a failure.
            </summary>
        </member>
        <member name="P:Azure.RequestContext.CancellationToken">
            <summary>
            The token to check for cancellation.
            </summary>
        </member>
        <member name="M:Azure.RequestContext.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.RequestContext" /> class.
            </summary>
        </member>
        <member name="M:Azure.RequestContext.op_Implicit(Azure.ErrorOptions)~Azure.RequestContext">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.RequestContext" /> class using the given <see cref="P:Azure.RequestContext.ErrorOptions" />.
            </summary>
            <param name="options"></param>
        </member>
        <member name="M:Azure.RequestContext.AddPolicy(Azure.Core.Pipeline.HttpPipelinePolicy,Azure.Core.HttpPipelinePosition)">
            <summary>
            Adds an <see cref="T:Azure.Core.Pipeline.HttpPipelinePolicy" /> into the pipeline for the duration of this request.
            The position of policy in the pipeline is controlled by <paramref name="position" /> parameter.
            If you want the policy to execute once per client request use <see cref="F:Azure.Core.HttpPipelinePosition.PerCall" />
            otherwise use <see cref="F:Azure.Core.HttpPipelinePosition.PerRetry" /> to run the policy for every retry.
            </summary>
            <param name="policy">The <see cref="T:Azure.Core.Pipeline.HttpPipelinePolicy" /> instance to be added to the pipeline.</param>
            <param name="position">The position of the policy in the pipeline.</param>
        </member>
        <member name="M:Azure.RequestContext.AddClassifier(System.Int32,System.Boolean)">
            <summary>
            Customizes the <see cref="T:Azure.Core.ResponseClassifier" /> for this operation to change
            the default <see cref="T:Azure.Response" /> classification behavior so that it considers
            the passed-in status code to be an error or not, as specified.
            Status code classifiers are applied after all <see cref="T:Azure.Core.ResponseClassificationHandler" /> classifiers.
            This is useful for cases where you'd like to prevent specific response status codes from being treated as errors by
            logging and distributed tracing policies -- that is, if a response is not classified as an error, it will not appear as an error in
            logs or distributed traces.
            </summary>
            <param name="statusCode">The status code to customize classification for.</param>
            <param name="isError">Whether the passed-in status code should be classified as an error.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">statusCode is not between 100 and 599 (inclusive).</exception>
            <exception cref="T:System.InvalidOperationException">If this method is called after the <see cref="T:Azure.RequestContext" /> has been
            used in a method call.</exception>
        </member>
        <member name="M:Azure.RequestContext.AddClassifier(Azure.Core.ResponseClassificationHandler)">
            <summary>
            Customizes the <see cref="T:Azure.Core.ResponseClassifier" /> for this operation.
            Adding a <see cref="T:Azure.Core.ResponseClassificationHandler" /> changes the classification
            behavior so that it first tries to classify a response via the handler, and if
            the handler doesn't have an opinion, it instead uses the default classifier.
            Handlers are applied in order so the most recently added takes precedence.
            This is useful for cases where you'd like to prevent specific response status codes from being treated as errors by
            logging and distributed tracing policies -- that is, if a response is not classified as an error, it will not appear as an error in
            logs or distributed traces.
            </summary>
            <param name="classifier">The custom classifier.</param>
            <exception cref="T:System.InvalidOperationException">If this method is called after the <see cref="T:Azure.RequestContext" /> has been
            used in a method call.</exception>
        </member>
        <member name="T:Azure.RequestFailedException">
            <summary>
            An exception thrown when service request fails.
            </summary>
        </member>
        <member name="P:Azure.RequestFailedException.Status">
            <summary>
            Gets the HTTP status code of the response. Returns. <code>0</code> if response was not received.
            </summary>
        </member>
        <member name="P:Azure.RequestFailedException.ErrorCode">
            <summary>
            Gets the service specific error code if available. Please refer to the client documentation for the list of supported error codes.
            </summary>
        </member>
        <member name="M:Azure.RequestFailedException.#ctor(System.String)">
            <summary>Initializes a new instance of the <see cref="T:Azure.RequestFailedException"></see> class with a specified error message.</summary>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:Azure.RequestFailedException.#ctor(System.String,System.Exception)">
            <summary>Initializes a new instance of the <see cref="T:Azure.RequestFailedException"></see> class with a specified error message, HTTP status code and a reference to the inner exception that is the cause of this exception.</summary>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (Nothing in Visual Basic) if no inner exception is specified.</param>
        </member>
        <member name="M:Azure.RequestFailedException.#ctor(System.Int32,System.String)">
            <summary>Initializes a new instance of the <see cref="T:Azure.RequestFailedException"></see> class with a specified error message and HTTP status code.</summary>
            <param name="status">The HTTP status code, or <c>0</c> if not available.</param>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:Azure.RequestFailedException.#ctor(System.Int32,System.String,System.Exception)">
            <summary>Initializes a new instance of the <see cref="T:Azure.RequestFailedException"></see> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
            <param name="status">The HTTP status code, or <c>0</c> if not available.</param>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (Nothing in Visual Basic) if no inner exception is specified.</param>
        </member>
        <member name="M:Azure.RequestFailedException.#ctor(System.Int32,System.String,System.String,System.Exception)">
            <summary>Initializes a new instance of the <see cref="T:Azure.RequestFailedException"></see> class with a specified error message, HTTP status code, error code, and a reference to the inner exception that is the cause of this exception.</summary>
            <param name="status">The HTTP status code, or <c>0</c> if not available.</param>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="errorCode">The service specific error code.</param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (Nothing in Visual Basic) if no inner exception is specified.</param>
        </member>
        <member name="M:Azure.RequestFailedException.#ctor(Azure.Response)">
            <summary>Initializes a new instance of the <see cref="T:Azure.RequestFailedException"></see> class
            with an error message, HTTP status code, and error code obtained from the specified response.</summary>
            <param name="response">The response to obtain error details from.</param>
        </member>
        <member name="M:Azure.RequestFailedException.#ctor(Azure.Response,System.Exception)">
            <summary>Initializes a new instance of the <see cref="T:Azure.RequestFailedException"></see> class
            with an error message, HTTP status code, and error code obtained from the specified response.</summary>
            <param name="response">The response to obtain error details from.</param>
            <param name="innerException">An inner exception to associate with the new <see cref="T:Azure.RequestFailedException" />.</param>
        </member>
        <member name="M:Azure.RequestFailedException.#ctor(Azure.Response,System.Exception,Azure.Core.RequestFailedDetailsParser)">
            <summary>Initializes a new instance of the <see cref="T:Azure.RequestFailedException"></see> class
            with an error message, HTTP status code, and error code obtained from the specified response.</summary>
            <param name="response">The response to obtain error details from.</param>
            <param name="innerException">An inner exception to associate with the new <see cref="T:Azure.RequestFailedException" />.</param>
            <param name="detailsParser">The parser to use to parse the response content.</param>
        </member>
        <member name="M:Azure.RequestFailedException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>Initializes a new instance of the <see cref="T:System.Exception" /> class with serialized data.</summary><param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown. </param><param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination. </param><exception cref="T:System.ArgumentNullException">The <paramref name="info" /> parameter is null. </exception><exception cref="T:System.Runtime.Serialization.SerializationException">The class name is null or <see cref="P:System.Exception.HResult" /> is zero (0). </exception>
        </member>
        <member name="M:Azure.RequestFailedException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>When overridden in a derived class, sets the <see cref="T:System.Runtime.Serialization.SerializationInfo" /> with information about the exception.</summary><param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown. </param><param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination. </param><exception cref="T:System.ArgumentNullException">The <paramref name="info" /> parameter is a null reference (Nothing in Visual Basic). </exception><filterpriority>2</filterpriority><PermissionSet><IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Read="*AllFiles*" PathDiscovery="*AllFiles*" /><IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="SerializationFormatter" /></PermissionSet>
        </member>
        <member name="M:Azure.RequestFailedException.GetRawResponse">
            <summary>
            Gets the response, if any, that led to the exception.
            </summary>
        </member>
        <member name="T:Azure.Response">
            <summary>
            Represents the HTTP response from the service.
            </summary>
        </member>
        <member name="P:Azure.Response.Status">
            <summary>
            Gets the HTTP status code.
            </summary>
        </member>
        <member name="P:Azure.Response.ReasonPhrase">
            <summary>
            Gets the HTTP reason phrase.
            </summary>
        </member>
        <member name="P:Azure.Response.ContentStream">
            <summary>
            Gets the contents of HTTP response. Returns <c>null</c> for responses without content.
            </summary>
        </member>
        <member name="P:Azure.Response.ClientRequestId">
            <summary>
            Gets the client request id that was sent to the server as <c>x-ms-client-request-id</c> headers.
            </summary>
        </member>
        <member name="P:Azure.Response.Headers">
            <summary>
            Get the HTTP response headers.
            </summary>
        </member>
        <member name="P:Azure.Response.Content">
            <summary>
            Gets the contents of HTTP response, if it is available.
            </summary>
            <remarks>
            Throws <see cref="T:System.InvalidOperationException" /> when <see cref="P:Azure.Response.ContentStream" /> is not a <see cref="T:System.IO.MemoryStream" />.
            </remarks>
        </member>
        <member name="M:Azure.Response.Dispose">
            <summary>
            Frees resources held by this <see cref="T:Azure.Response" /> instance.
            </summary>
        </member>
        <member name="P:Azure.Response.IsError">
            <summary>
            Indicates whether the status code of the returned response is considered
            an error code.
            </summary>
        </member>
        <member name="M:Azure.Response.TryGetHeader(System.String,System.String@)">
            <summary>
            Returns header value if the header is stored in the collection. If header has multiple values they are going to be joined with a comma.
            </summary>
            <param name="name">The header name.</param>
            <param name="value">The reference to populate with value.</param>
            <returns><c>true</c> if the specified header is stored in the collection, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Azure.Response.TryGetHeaderValues(System.String,System.Collections.Generic.IEnumerable{System.String}@)">
            <summary>
            Returns header values if the header is stored in the collection.
            </summary>
            <param name="name">The header name.</param>
            <param name="values">The reference to populate with values.</param>
            <returns><c>true</c> if the specified header is stored in the collection, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Azure.Response.ContainsHeader(System.String)">
            <summary>
            Returns <c>true</c> if the header is stored in the collection.
            </summary>
            <param name="name">The header name.</param>
            <returns><c>true</c> if the specified header is stored in the collection, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Azure.Response.EnumerateHeaders">
            <summary>
            Returns an iterator for enumerating <see cref="T:Azure.Core.HttpHeader" /> in the response.
            </summary>
            <returns>The <see cref="T:System.Collections.Generic.IEnumerable`1" /> enumerating <see cref="T:Azure.Core.HttpHeader" /> in the response.</returns>
        </member>
        <member name="M:Azure.Response.FromValue``1(``0,Azure.Response)">
            <summary>
            Creates a new instance of <see cref="T:Azure.Response`1" /> with the provided value and HTTP response.
            </summary>
            <typeparam name="T">The type of the value.</typeparam>
            <param name="value">The value.</param>
            <param name="response">The HTTP response.</param>
            <returns>A new instance of <see cref="T:Azure.Response`1" /> with the provided value and HTTP response.</returns>
        </member>
        <member name="M:Azure.Response.ToString">
            <summary>
            Returns the string representation of this <see cref="T:Azure.Response" />.
            </summary>
            <returns>The string representation of this <see cref="T:Azure.Response" /></returns>
        </member>
        <member name="T:Azure.ResponseError">
            <summary>
            Represents an error returned by an Azure Service.
            </summary>
        </member>
        <member name="M:Azure.ResponseError.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Azure.ResponseError" />.
            </summary>
            <param name="code">The error code.</param>
            <param name="message">The error message.</param>
        </member>
        <member name="M:Azure.ResponseError.#ctor(System.String,System.String,System.String,System.Text.Json.JsonElement,Azure.ResponseInnerError,System.Collections.Generic.IReadOnlyList{Azure.ResponseError})">
            <summary>
            Initializes a new instance of <see cref="T:Azure.ResponseError" />.
            </summary>
            <param name="code">The error code.</param>
            <param name="message">The error message.</param>
            <param name="element">The raw JSON element the error was parsed from.</param>
            <param name="innerError">The inner error.</param>
            <param name="target">The error target.</param>
            <param name="details">The error details.</param>
        </member>
        <member name="P:Azure.ResponseError.Code">
            <summary>
            Gets the error code.
            </summary>
        </member>
        <member name="P:Azure.ResponseError.Message">
            <summary>
            Gets the error message.
            </summary>
        </member>
        <member name="P:Azure.ResponseError.InnerError">
            <summary>
            Gets the inner error.
            </summary>
        </member>
        <member name="P:Azure.ResponseError.Target">
            <summary>
            Gets the error target.
            </summary>
        </member>
        <member name="P:Azure.ResponseError.Details">
            <summary>
            Gets the list of related errors.
            </summary>
        </member>
        <member name="M:Azure.ResponseError.ToString">
            <summary>Returns a string that represents the current object.</summary><returns>A string that represents the current object.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="T:Azure.ResponseInnerError">
            <summary>
            Represents an inner error.
            </summary>
        </member>
        <member name="P:Azure.ResponseInnerError.Code">
            <summary>
            Gets the error code.
            </summary>
        </member>
        <member name="P:Azure.ResponseInnerError.InnerError">
            <summary>
            Gets the inner error.
            </summary>
        </member>
        <member name="M:Azure.ResponseInnerError.ToString">
            <summary>Returns a string that represents the current object.</summary><returns>A string that represents the current object.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="T:Azure.Response`1">
            <summary>
            Represents a result of Azure operation.
            </summary>
            <typeparam name="T">The type of returned value.</typeparam>
        </member>
        <member name="P:Azure.Response`1.HasValue">
            <summary>
            Gets a value indicating whether the current instance has a valid value of its underlying type.
            </summary>
        </member>
        <member name="M:Azure.Response`1.op_Implicit(Azure.Response{`0})~`0">
            <summary>
            Returns the value of this <see cref="T:Azure.Response`1" /> object.
            </summary>
            <param name="response">The <see cref="T:Azure.Response`1" /> instance.</param>
        </member>
        <member name="M:Azure.Response`1.Equals(System.Object)">
            <summary>Determines whether the specified object is equal to the current object.</summary><returns>true if the specified object  is equal to the current object; otherwise, false.</returns><param name="obj">The object to compare with the current object. </param><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Response`1.GetHashCode">
            <summary>Serves as the default hash function. </summary><returns>A hash code for the current object.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="T:Azure.AzureCoreExtensions">
            <summary>
            Extensions that can be used for serialization.
            </summary>
        </member>
        <member name="M:Azure.AzureCoreExtensions.ToObject``1(System.BinaryData,Azure.Core.Serialization.ObjectSerializer,System.Threading.CancellationToken)">
             <summary>
             Converts the <see cref="T:System.BinaryData" /> to the specified type using
             the provided <see cref="T:Azure.Core.Serialization.ObjectSerializer" />.
             </summary>
             <typeparam name="T">The type that the data should be
             converted to.</typeparam>
             <param name="data">The <see cref="T:System.BinaryData" /> instance to convert.</param>
             <param name="serializer">The serializer to use
             when deserializing the data.</param>
             <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> to use during deserialization.</param>
            <returns>The data converted to the specified type.</returns>
        </member>
        <member name="M:Azure.AzureCoreExtensions.ToObjectAsync``1(System.BinaryData,Azure.Core.Serialization.ObjectSerializer,System.Threading.CancellationToken)">
             <summary>
             Converts the <see cref="T:System.BinaryData" /> to the specified type using
             the provided <see cref="T:Azure.Core.Serialization.ObjectSerializer" />.
             </summary>
             <typeparam name="T">The type that the data should be
             converted to.</typeparam>
             <param name="data">The <see cref="T:System.BinaryData" /> instance to convert.</param>
             <param name="serializer">The serializer to use
             when deserializing the data.</param>
             <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> to use during deserialization.</param>
            <returns>The data converted to the specified type.</returns>
        </member>
        <member name="M:Azure.AzureCoreExtensions.ToObjectFromJson(System.BinaryData)">
            <summary>
            Converts the json value represented by <see cref="T:System.BinaryData" /> to an object of a specific type.
            </summary>
            <param name="data">The <see cref="T:System.BinaryData" /> instance to convert.</param>
            <returns> The object value of the json value.
            If the object contains a primitive type such as string, int, double, bool, or null literal, it returns that type.
            Otherwise, it returns either an object[] or Dictionary&lt;string, object&gt;.
            Each value in the key value pair or list will also be converted into a primitive or another complex type recursively.
            </returns>
        </member>
        <member name="M:Azure.AzureCoreExtensions.ToDynamicFromJson(System.BinaryData)">
            <summary>
            Return the content of the BinaryData as a dynamic type.  Please see https://aka.ms/azsdk/net/dynamiccontent for details.
            </summary>
        </member>
        <member name="M:Azure.AzureCoreExtensions.ToDynamicFromJson(System.BinaryData,Azure.Core.Serialization.JsonPropertyNames,System.String)">
            <summary>
            Return the content of the BinaryData as a dynamic type.  Please see https://aka.ms/azsdk/net/dynamiccontent for details.
            <paramref name="propertyNameFormat">The format of property names in the JSON content.
            This value indicates to the dynamic type that it can convert property names on the returned value to this format in the underlying JSON.
            Please see https://aka.ms/azsdk/net/dynamiccontent#use-c-naming-conventions for details.
            </paramref>
            <paramref name="dateTimeFormat">The standard format specifier to pass when serializing DateTime and DateTimeOffset values in the JSON content.
            To serialize to unix time, pass the value <code>"x"</code> and
            see <see href="https://learn.microsoft.com/dotnet/standard/base-types/standard-date-and-time-format-strings">https://learn.microsoft.com/dotnet/standard/base-types/standard-date-and-time-format-strings#table-of-format-specifiers</see> for other well known values.
            </paramref>
            </summary>
        </member>
        <member name="M:Azure.AzureCoreExtensions.ToDynamicFromJson(System.BinaryData,Azure.Core.Serialization.DynamicDataOptions)">
            <summary>
            Return the content of the BinaryData as a dynamic type.
            </summary>
        </member>
        <member name="T:Azure.SyncAsyncEventArgs">
            <summary>
            Provides data for <see cref="T:Azure.Core.SyncAsyncEventHandler`1" />
            events that can be invoked either synchronously or asynchronously.
            </summary>
        </member>
        <member name="P:Azure.SyncAsyncEventArgs.IsRunningSynchronously">
             <summary>
             Gets a value indicating whether the event handler was invoked
             synchronously or asynchronously.  Please see
             <see cref="T:Azure.Core.SyncAsyncEventHandler`1" /> for more details.
             </summary>
             <remarks>
             <para>
             The same <see cref="T:Azure.Core.SyncAsyncEventHandler`1" />
             event can be raised from both synchronous and asynchronous code
             paths depending on whether you're calling sync or async methods on
             a client.  If you write an async handler but raise it from a sync
             method, the handler will be doing sync-over-async and may cause
             ThreadPool starvation.  See
             <see href="https://docs.microsoft.com/archive/blogs/vancem/diagnosing-net-core-threadpool-starvation-with-perfview-why-my-service-is-not-saturating-all-cores-or-seems-to-stall">
             Diagnosing .NET Core ThreadPool Starvation with PerfView</see> for
             a detailed explanation of how that can cause ThreadPool starvation
             and serious performance problems.
             </para>
             <para>
             You can use this <see cref="P:Azure.SyncAsyncEventArgs.IsRunningSynchronously" /> property to check
             how the event is being raised and implement your handler
             accordingly.  Here's an example handler that's safe to invoke from
             both sync and async code paths.
             <code snippet="Snippet:Azure_Core_Samples_EventSamples_CombinedHandler" language="csharp">
             var client = new AlarmClient();
             client.Ring += async (SyncAsyncEventArgs e) =&gt;
             {
                 if (e.IsRunningSynchronously)
                 {
                     Console.WriteLine("Wake up!");
                 }
                 else
                 {
                     await Console.Out.WriteLineAsync("Wake up!");
                 }
             };
            
             client.Snooze(); // sync call that blocks
             await client.SnoozeAsync(); // async call that doesn't block
             </code>
             </para>
             </remarks>
        </member>
        <member name="P:Azure.SyncAsyncEventArgs.CancellationToken">
            <summary>
            Gets a cancellation token related to the original operation that
            raised the event.  It's important for your handler to pass this
            token along to any asynchronous or long-running synchronous
            operations that take a token so cancellation (via something like
            <code>
            new CancellationTokenSource(TimeSpan.FromSeconds(10)).Token
            </code>
            for example) will correctly propagate.
            </summary>
        </member>
        <member name="M:Azure.SyncAsyncEventArgs.#ctor(System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.SyncAsyncEventArgs" />
            class.
            </summary>
            <param name="isRunningSynchronously">
            A value indicating whether the event handler was invoked
            synchronously or asynchronously.  Please see
            <see cref="T:Azure.Core.SyncAsyncEventHandler`1" /> for more details.
            </param>
            <param name="cancellationToken">
            A cancellation token related to the original operation that raised
            the event.  It's important for your handler to pass this token
            along to any asynchronous or long-running synchronous operations
            that take a token so cancellation will correctly propagate.  The
            default value is <see cref="P:System.Threading.CancellationToken.None" />.
            </param>
        </member>
        <member name="T:Azure.WaitUntil">
            <summary>
            Indicates whether the invocation of a long running operation should return once it has
            started or wait for the server operation to fully complete before returning.
            </summary>
        </member>
        <member name="F:Azure.WaitUntil.Completed">
            <summary>
            Indicates the method should wait until the server operation fully completes.
            </summary>
        </member>
        <member name="F:Azure.WaitUntil.Started">
            <summary>
            Indicates the method should return once the server operation has started.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.AllowNullAttribute">
            <summary>Specifies that null is allowed as an input even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DisallowNullAttribute">
            <summary>Specifies that null is disallowed as an input even if the corresponding type allows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullAttribute">
            <summary>Specifies that an output may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullAttribute">
            <summary>Specifies that an output will not be null even if the corresponding type allows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue" />, the parameter may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter may be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue" />, the parameter will not be null even if the corresponding type allows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute">
            <summary>Specifies that the output will be non-null if the named parameter is non-null.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with the associated parameter name.</summary>
            <param name="parameterName">
            The associated parameter name.  The output will be non-null if the argument to the parameter specified is non-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.ParameterName">
            <summary>Gets the associated parameter name.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnAttribute">
            <summary>Applied to a method that will never return under any circumstance.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute">
            <summary>Specifies that the method will not return if the associated Boolean parameter is passed the specified value.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified parameter value.</summary>
            <param name="parameterValue">
            The condition parameter value. Code after the method will be considered unreachable by diagnostics if the argument to
            the associated parameter matches this value.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.ParameterValue">
            <summary>Gets the condition parameter value.</summary>
        </member>
        <member name="M:Azure.Pageable`1.System#Collections#IEnumerable#GetEnumerator">
            <summary>Returns an enumerator that iterates through a collection.</summary><returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Messaging.CloudEventExtensionAttributes`2.System#Collections#IEnumerable#GetEnumerator">
            <summary>Returns an enumerator that iterates through a collection.</summary><returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoArray`1.System#Collections#IEnumerable#GetEnumerator">
            <summary>Returns an enumerator that iterates through a collection.</summary><returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoArray`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
            <summary>Returns an enumerator that iterates through the collection.</summary><returns>An enumerator that can be used to iterate through the collection.</returns><filterpriority>1</filterpriority>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoCollection.System#Collections#IEnumerable#GetEnumerator">
            <summary>Returns an enumerator that iterates through a collection.</summary><returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.GeoJson.GeoLineStringCollection.System#Collections#IEnumerable#GetEnumerator">
            <summary>Returns an enumerator that iterates through a collection.</summary><returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.Pipeline.TaskExtensions.Enumerable`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
            <summary>Returns an enumerator that iterates through the collection.</summary><returns>An enumerator that can be used to iterate through the collection.</returns><filterpriority>1</filterpriority>
        </member>
        <member name="M:Azure.Core.Pipeline.TaskExtensions.Enumerable`1.System#Collections#IEnumerable#GetEnumerator">
            <summary>Returns an enumerator that iterates through a collection.</summary><returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="P:Azure.Core.Pipeline.TaskExtensions.Enumerator`1.System#Collections#IEnumerator#Current">
            <summary>Gets the current element in the collection.</summary><returns>The current element in the collection.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="P:Azure.Core.GeoJson.GeoArray`1.Enumerator.System#Collections#IEnumerator#Current">
            <summary>Gets the current element in the collection.</summary><returns>The current element in the collection.</returns><filterpriority>2</filterpriority>
        </member>
        <member name="M:Azure.Core.Json.MutableJsonElement.ObjectEnumerator.System#Collections#Generic#IEnumerable{(System#StringName@Azure#Core#Json#MutableJsonElementValue)}#GetEnumerator">
            <summary>Returns an enumerator that iterates through the collection.</summary><returns>An enumerator that can be used to iterate through the collection.</returns><filterpriority>1</filterpriority>
        </member>
    </members>
</doc>
