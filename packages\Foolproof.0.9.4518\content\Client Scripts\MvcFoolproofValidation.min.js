var foolproof=function(){};foolproof.is=function(a,h,b,f){if(f){var e=function(a){return a==null||a==undefined||a==""},c=e(a),d=e(b);if(c&&!d||d&&!c)return true}var g=function(a){return a-0==a&&a.length>0},j=function(b){var a=new RegExp(/(?=\d)^(?:(?!(?:10\D(?:0?[5-9]|1[0-4])\D(?:1582))|(?:0?9\D(?:0?[3-9]|1[0-3])\D(?:1752)))((?:0?[13578]|1[02])|(?:0?[469]|11)(?!\/31)(?!-31)(?!\.31)|(?:0?2(?=.?(?:(?:29.(?!000[04]|(?:(?:1[^0-6]|[2468][^048]|[3579][^26])00))(?:(?:(?:\d\d)(?:[02468][048]|[13579][26])(?!\x20BC))|(?:00(?:42|3[0369]|2[147]|1[258]|09)\x20BC))))))|(?:0?2(?=.(?:(?:\d\D)|(?:[01]\d)|(?:2[0-8])))))([-.\/])(0?[1-9]|[12]\d|3[01])\2(?!0000)((?=(?:00(?:4[0-5]|[0-3]?\d)\x20BC)|(?:\d{4}(?!\x20BC)))\d{4}(?:\x20BC)?)(?:$|(?=\x20\d)\x20))?((?:(?:0?[1-9]|1[012])(?::[0-5]\d){0,2}(?:\x20[aApP][mM]))|(?:[01]\d|2[0-3])(?::[0-5]\d){1,2})?$/);return a.test(b)},i=function(a){return a===true||a===false||a==="true"||a==="false"};if(j(a)){a=Date.parse(a);b=Date.parse(b)}else if(i(a)){if(a=="false")a=false;if(b=="false")b=false;a=!!a;b=!!b}else if(g(a)){a=parseFloat(a);b=parseFloat(b)}switch(h){case"EqualTo":if(a==b)return true;break;case"NotEqualTo":if(a!=b)return true;break;case"GreaterThan":if(a>b)return true;break;case"LessThan":if(a<b)return true;break;case"GreaterThanOrEqualTo":if(a>=b)return true;break;case"LessThanOrEqualTo":if(a<=b)return true;break;case"RegExMatch":return(new RegExp(b)).test(a);break;case"NotRegExMatch":return!(new RegExp(b)).test(a)}return false};foolproof.getId=function(a,b){var c=a.id.lastIndexOf("_")+1;return a.id.substr(0,c)+b.replace(/\./g,"_")};foolproof.getName=function(a,b){var c=a.name.lastIndexOf(".")+1;return a.name.substr(0,c)+b};Sys.Mvc.ValidatorRegistry.validators.is=function(a){return function(g,f){var e=a.ValidationParameters.operator,d=a.ValidationParameters.passonnull,b=foolproof.getId(f.fieldContext.elements[0],a.ValidationParameters.dependentproperty),c=document.getElementById(b).value;return foolproof.is(g,e,c,d)?true:a.ErrorMessage}};Sys.Mvc.ValidatorRegistry.validators.requiredif=function(a){var b=a.ValidationParameters.pattern,c=a.ValidationParameters.dependentvalue,d=a.ValidationParameters.operator;return function(h,j){var i=foolproof.getName(j.fieldContext.elements[0],a.ValidationParameters.dependentproperty),e=document.getElementsByName(i),f=null;if(e.length>1){for(var g=0;g!=e.length;g++)if(e[g].checked){f=e[g].value;break}if(f==null)f=false}else f=e[0].value;if(foolproof.is(f,d,c))if(b==null){if(h!=null&&h.toString().replace(/^\s\s*/,"").replace(/\s\s*$/,"")!="")return true}else return(new RegExp(b)).test(h);else return true;return a.ErrorMessage}};Sys.Mvc.ValidatorRegistry.validators.requiredifempty=function(a){return function(c,e){var d=foolproof.getId(e.fieldContext.elements[0],a.ValidationParameters.dependentproperty),b=document.getElementById(d).value;if(b==null||b.toString().replace(/^\s\s*/,"").replace(/\s\s*$/,"")==""){if(c!=null&&c.toString().replace(/^\s\s*/,"").replace(/\s\s*$/,"")!="")return true}else return true;return a.ErrorMessage}};Sys.Mvc.ValidatorRegistry.validators.requiredifnotempty=function(a){return function(c,e){var d=foolproof.getId(e.fieldContext.elements[0],a.ValidationParameters.dependentproperty),b=document.getElementById(d).value;if(b!=null&&b.toString().replace(/^\s\s*/,"").replace(/\s\s*$/,"")!=""){if(c!=null&&c.toString().replace(/^\s\s*/,"").replace(/\s\s*$/,"")!="")return true}else return true;return a.ErrorMessage}};