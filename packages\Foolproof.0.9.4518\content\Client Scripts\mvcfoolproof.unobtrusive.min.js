var foolproof=function(){};foolproof.is=function(a,h,b,f){if(f){var e=function(a){return a==null||a==undefined||a==""},c=e(a),d=e(b);if(c&&!d||d&&!c)return true}var g=function(a){return a-0==a&&a.length>0},j=function(b){var a=new RegExp(/(?=\d)^(?:(?!(?:10\D(?:0?[5-9]|1[0-4])\D(?:1582))|(?:0?9\D(?:0?[3-9]|1[0-3])\D(?:1752)))((?:0?[13578]|1[02])|(?:0?[469]|11)(?!\/31)(?!-31)(?!\.31)|(?:0?2(?=.?(?:(?:29.(?!000[04]|(?:(?:1[^0-6]|[2468][^048]|[3579][^26])00))(?:(?:(?:\d\d)(?:[02468][048]|[13579][26])(?!\x20BC))|(?:00(?:42|3[0369]|2[147]|1[258]|09)\x20BC))))))|(?:0?2(?=.(?:(?:\d\D)|(?:[01]\d)|(?:2[0-8])))))([-.\/])(0?[1-9]|[12]\d|3[01])\2(?!0000)((?=(?:00(?:4[0-5]|[0-3]?\d)\x20BC)|(?:\d{4}(?!\x20BC)))\d{4}(?:\x20BC)?)(?:$|(?=\x20\d)\x20))?((?:(?:0?[1-9]|1[012])(?::[0-5]\d){0,2}(?:\x20[aApP][mM]))|(?:[01]\d|2[0-3])(?::[0-5]\d){1,2})?$/);return a.test(b)},i=function(a){return a===true||a===false||a==="true"||a==="false"};if(j(a)){a=Date.parse(a);b=Date.parse(b)}else if(i(a)){if(a=="false")a=false;if(b=="false")b=false;a=!!a;b=!!b}else if(g(a)){a=parseFloat(a);b=parseFloat(b)}switch(h){case"EqualTo":if(a==b)return true;break;case"NotEqualTo":if(a!=b)return true;break;case"GreaterThan":if(a>b)return true;break;case"LessThan":if(a<b)return true;break;case"GreaterThanOrEqualTo":if(a>=b)return true;break;case"LessThanOrEqualTo":if(a<=b)return true;break;case"RegExMatch":return(new RegExp(b)).test(a);break;case"NotRegExMatch":return!(new RegExp(b)).test(a)}return false};foolproof.getId=function(a,b){var c=a.id.lastIndexOf("_")+1;return a.id.substr(0,c)+b.replace(/\./g,"_")};foolproof.getName=function(a,b){var c=a.name.lastIndexOf(".")+1;return a.name.substr(0,c)+b};(function(){jQuery.validator.addMethod("is",function(g,f,a){var b=foolproof.getId(f,a.dependentproperty),e=a.operator,d=a.passonnull,c=document.getElementById(b).value;return foolproof.is(g,e,c,d)?true:false});jQuery.validator.addMethod("requiredif",function(e,j,c){var h=foolproof.getName(j,c.dependentproperty),g=c.dependentvalue,i=c.operator,f=c.pattern,a=document.getElementsByName(h),b=null;if(a.length>1){for(var d=0;d!=a.length;d++)if(a[d].checked){b=a[d].value;break}if(b==null)b=false}else b=a[0].value;if(foolproof.is(b,i,g))if(f==null){if(e!=null&&e.toString().replace(/^\s\s*/,"").replace(/\s\s*$/,"")!="")return true}else return(new RegExp(f)).test(e);else return true;return false});jQuery.validator.addMethod("requiredifempty",function(b,d,e){var c=foolproof.getId(d,e.dependentproperty),a=document.getElementById(c).value;if(a==null||a.toString().replace(/^\s\s*/,"").replace(/\s\s*$/,"")==""){if(b!=null&&b.toString().replace(/^\s\s*/,"").replace(/\s\s*$/,"")!="")return true}else return true;return false});jQuery.validator.addMethod("requiredifnotempty",function(b,d,e){var c=foolproof.getId(d,e.dependentproperty),a=document.getElementById(c).value;if(a!=null&&a.toString().replace(/^\s\s*/,"").replace(/\s\s*$/,"")!=""){if(b!=null&&b.toString().replace(/^\s\s*/,"").replace(/\s\s*$/,"")!="")return true}else return true;return false});var a=function(a,b,c){a.rules[b]=c;if(a.message)a.messages[b]=a.message},b=$.validator.unobtrusive;b.adapters.add("requiredif",["dependentproperty","dependentvalue","operator","pattern"],function(b){var c={dependentproperty:b.params.dependentproperty,dependentvalue:b.params.dependentvalue,operator:b.params.operator,pattern:b.params.pattern};a(b,"requiredif",c)});b.adapters.add("is",["dependentproperty","operator","passonnull"],function(b){a(b,"is",{dependentproperty:b.params.dependentproperty,operator:b.params.operator,passonnull:b.params.passonnull})});b.adapters.add("requiredifempty",["dependentproperty"],function(b){a(b,"requiredifempty",{dependentproperty:b.params.dependentproperty})});b.adapters.add("requiredifnotempty",["dependentproperty"],function(b){a(b,"requiredifnotempty",{dependentproperty:b.params.dependentproperty})})})();