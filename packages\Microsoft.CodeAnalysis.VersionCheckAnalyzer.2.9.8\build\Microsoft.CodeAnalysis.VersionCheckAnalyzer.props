<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  
  <!-- 
    This item group adds any .editorconfig file present at the project root directory
    as an additional file.
  -->  
  <ItemGroup Condition="'$(SkipDefaultEditorConfigAsAdditionalFile)' != 'true' And Exists('$(MSBuildProjectDirectory)\.editorconfig')" >
    <AdditionalFiles Include="$(MSBuildProjectDirectory)\.editorconfig" />
  </ItemGroup>

  <!-- 
    This property group prevents the rule ids implemented in this package to be bumped to errors when
    the 'CodeAnalysisTreatWarningsAsErrors' = 'false'.
  -->
  <PropertyGroup Condition="'$(CodeAnalysisTreatWarningsAsErrors)' == 'false'">
    <WarningsNotAsErrors>$(WarningsNotAsErrors);CA9999</WarningsNotAsErrors>
  </PropertyGroup>

  <PropertyGroup>
    <Features>$(Features);flow-analysis</Features> 
  </PropertyGroup>
</Project>