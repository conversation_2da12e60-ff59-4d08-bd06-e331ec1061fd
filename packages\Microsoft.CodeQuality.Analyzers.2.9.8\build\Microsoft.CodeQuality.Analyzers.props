<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  
  <!-- 
    This item group adds any .editorconfig file present at the project root directory
    as an additional file.
  -->  
  <ItemGroup Condition="'$(SkipDefaultEditorConfigAsAdditionalFile)' != 'true' And Exists('$(MSBuildProjectDirectory)\.editorconfig')" >
    <AdditionalFiles Include="$(MSBuildProjectDirectory)\.editorconfig" />
  </ItemGroup>

  <!-- 
    This property group prevents the rule ids implemented in this package to be bumped to errors when
    the 'CodeAnalysisTreatWarningsAsErrors' = 'false'.
  -->
  <PropertyGroup Condition="'$(CodeAnalysisTreatWarningsAsErrors)' == 'false'">
    <WarningsNotAsErrors>$(WarningsNotAsErrors);CA1000;CA1001;CA1003;CA1008;CA1010;CA1012;CA1014;CA1016;CA1017;CA1018;CA1019;CA1024;CA1027;CA1028;CA1030;CA1031;CA1032;CA1033;CA1034;CA1036;CA1040;CA1041;CA1043;CA1044;CA1050;CA1051;CA1052;CA1054;CA1055;CA1056;CA1060;CA1061;CA1062;CA1063;CA1064;CA1065;CA1066;CA1067;CA1068;CA1200;CA1501;CA1502;CA1505;CA1506;CA1507;CA1508;CA1509;CA1707;CA1708;CA1710;CA1711;CA1712;CA1714;CA1715;CA1716;CA1717;CA1720;CA1721;CA1724;CA1725;CA1801;CA1802;CA1806;CA1812;CA1814;CA1815;CA1819;CA1821;CA1822;CA1823;CA2007;CA2119;CA2200;CA2211;CA2214;CA2217;CA2218;CA2219;CA2224;CA2225;CA2226;CA2227;CA2231;CA2234;CA2244;CA2245;CA2246</WarningsNotAsErrors>
  </PropertyGroup>

  <!-- 
    This property group contains the rules that have been implemented in this package and therefore should be disabled for the binary FxCop.
    The format is -[Category]#[ID], e.g., -Microsoft.Design#CA1001;
  -->
  <PropertyGroup>
    <CodeAnalysisRuleSetOverrides>
      $(CodeAnalysisRuleSetOverrides);
      -Microsoft.Design#CA1000;
      -Microsoft.Design#CA1001;
      -Microsoft.Design#CA1003;
      -Microsoft.Design#CA1008;
      -Microsoft.Design#CA1010;
      -Microsoft.Design#CA1012;
      -Microsoft.Design#CA1014;
      -Microsoft.Design#CA1016;
      -Microsoft.Design#CA1017;
      -Microsoft.Design#CA1018;
      -Microsoft.Design#CA1019;
      -Microsoft.Design#CA1024;
      -Microsoft.Design#CA1027;
      -Microsoft.Design#CA1028;
      -Microsoft.Design#CA1030;
      -Microsoft.Design#CA1031;
      -Microsoft.Design#CA1032;
      -Microsoft.Design#CA1033;
      -Microsoft.Design#CA1034;
      -Microsoft.Design#CA1036;
      -Microsoft.Design#CA1040;
      -Microsoft.Design#CA1041;
      -Microsoft.Design#CA1043;
      -Microsoft.Design#CA1044;
      -Microsoft.Design#CA1050;
      -Microsoft.Design#CA1051;
      -Microsoft.Design#CA1052;
      -Microsoft.Design#CA1054;
      -Microsoft.Design#CA1055;
      -Microsoft.Design#CA1056;
      -Microsoft.Design#CA1060;
      -Microsoft.Design#CA1061;
      -Microsoft.Design#CA1062;
      -Microsoft.Design#CA1063;
      -Microsoft.Design#CA1064;
      -Microsoft.Design#CA1065;

      -Microsoft.Maintainability#CA1501;
      -Microsoft.Maintainability#CA1502;
      -Microsoft.Maintainability#CA1505;
      -Microsoft.Maintainability#CA1506;

      -Microsoft.Naming#CA1707;
      -Microsoft.Naming#CA1708;
      -Microsoft.Naming#CA1710;
      -Microsoft.Naming#CA1711;
      -Microsoft.Naming#CA1712;
      -Microsoft.Naming#CA1714;
      -Microsoft.Naming#CA1715;
      -Microsoft.Naming#CA1716;
      -Microsoft.Naming#CA1717;
      -Microsoft.Naming#CA1720;
      -Microsoft.Naming#CA1721;
      -Microsoft.Naming#CA1724;
      -Microsoft.Naming#CA1725;

      -Microsoft.Performance#CA1802;
      -Microsoft.Performance#CA1806;
      -Microsoft.Performance#CA1812;
      -Microsoft.Performance#CA1814;
      -Microsoft.Performance#CA1815;
      -Microsoft.Performance#CA1819;
      -Microsoft.Performance#CA1821;
      -Microsoft.Performance#CA1822;
      -Microsoft.Performance#CA1823;

      -Microsoft.Security#CA2119;

      -Microsoft.Usage#CA1801;
      -Microsoft.Usage#CA2200;
      -Microsoft.Usage#CA2211;
      -Microsoft.Usage#CA2214;
      -Microsoft.Usage#CA2217;
      -Microsoft.Usage#CA2218;
      -Microsoft.Usage#CA2219;
      -Microsoft.Usage#CA2224;
      -Microsoft.Usage#CA2225;
      -Microsoft.Usage#CA2226;
      -Microsoft.Usage#CA2227;
      -Microsoft.Usage#CA2231;
      -Microsoft.Usage#CA2234;

    </CodeAnalysisRuleSetOverrides>
  </PropertyGroup>

  <PropertyGroup>
    <Features>$(Features);flow-analysis</Features> 
  </PropertyGroup>
</Project>