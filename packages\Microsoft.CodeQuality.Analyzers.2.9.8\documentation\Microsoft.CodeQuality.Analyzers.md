
Sr. No. | Rule ID | Title | Category | Enabled | CodeFix | Description |
--------|---------|-------|----------|---------|---------|--------------------------------------------------------------------------------------------------------------|
1 | [CA1000](https://docs.microsoft.com/visualstudio/code-quality/ca1000-do-not-declare-static-members-on-generic-types) | Do not declare static members on generic types | Design | True | False | When a static member of a generic type is called, the type argument must be specified for the type. When a generic instance member that does not support inference is called, the type argument must be specified for the member. In these two cases, the syntax for specifying the type argument is different and easily confused. |
2 | [CA1001](https://docs.microsoft.com/visualstudio/code-quality/ca1001-types-that-own-disposable-fields-should-be-disposable) | Types that own disposable fields should be disposable | Design | True | True | A class declares and implements an instance field that is a System.IDisposable type, and the class does not implement IDisposable. A class that declares an IDisposable field indirectly owns an unmanaged resource and should implement the IDisposable interface. |
3 | [CA1003](https://docs.microsoft.com/visualstudio/code-quality/ca1003-use-generic-event-handler-instances) | Use generic event handler instances | Design | False | False | A type contains an event that declares an EventHandler delegate that returns void, whose signature contains two parameters (the first an object and the second a type that is assignable to EventArgs), and the containing assembly targets Microsoft .NET Framework?2.0. |
4 | [CA1008](https://docs.microsoft.com/visualstudio/code-quality/ca1008-enums-should-have-zero-value) | Enums should have zero value | Design | False | True | The default value of an uninitialized enumeration, just as other value types, is zero. A nonflags-attributed enumeration should define a member by using the value of zero so that the default value is a valid value of the enumeration. If an enumeration that has the FlagsAttribute attribute applied defines a zero-valued member, its name should be ""None"" to indicate that no values have been set in the enumeration. |
5 | [CA1010](https://docs.microsoft.com/visualstudio/code-quality/ca1010-collections-should-implement-generic-interface) | Collections should implement generic interface | Design | True | False | To broaden the usability of a collection, implement one of the generic collection interfaces. Then the collection can be used to populate generic collection types. |
6 | [CA1012](https://docs.microsoft.com/visualstudio/code-quality/ca1012-abstract-types-should-not-have-constructors) | Abstract types should not have constructors | Design | False | True | Constructors on abstract types can be called only by derived types. Because public constructors create instances of a type, and you cannot create instances of an abstract type, an abstract type that has a public constructor is incorrectly designed. |
7 | [CA1014](https://docs.microsoft.com/visualstudio/code-quality/ca1014-mark-assemblies-with-clscompliantattribute) | Mark assemblies with CLSCompliant | Design | False | False | The Common Language Specification (CLS) defines naming restrictions, data types, and rules to which assemblies must conform if they will be used across programming languages. Good design dictates that all assemblies explicitly indicate CLS compliance by using CLSCompliantAttribute . If this attribute is not present on an assembly, the assembly is not compliant. |
8 | [CA1016](https://docs.microsoft.com/visualstudio/code-quality/ca1016-mark-assemblies-with-assemblyversionattribute) | Mark assemblies with assembly version | Design | True | False | The .NET Framework uses the version number to uniquely identify an assembly, and to bind to types in strongly named assemblies. The version number is used together with version and publisher policy. By default, applications run only with the assembly version with which they were built. |
9 | [CA1017](https://docs.microsoft.com/visualstudio/code-quality/ca1017-mark-assemblies-with-comvisibleattribute) | Mark assemblies with ComVisible | Design | False | False | ComVisibleAttribute determines how COM clients access managed code. Good design dictates that assemblies explicitly indicate COM visibility. COM visibility can be set for the whole assembly and then overridden for individual types and type members. If this attribute is not present, the contents of the assembly are visible to COM clients. |
10 | [CA1018](https://docs.microsoft.com/visualstudio/code-quality/ca1018-mark-attributes-with-attributeusageattribute) | Mark attributes with AttributeUsageAttribute | Design | True | False | Specify AttributeUsage on {0}. |
11 | [CA1019](https://docs.microsoft.com/visualstudio/code-quality/ca1019-define-accessors-for-attribute-arguments) | Define accessors for attribute arguments | Design | False | True | Remove the property setter from {0} or reduce its accessibility because it corresponds to positional argument {1}. |
12 | [CA1024](https://docs.microsoft.com/visualstudio/code-quality/ca1024-use-properties-where-appropriate) | Use properties where appropriate | Design | False | False | A public or protected method has a name that starts with ""Get"", takes no parameters, and returns a value that is not an array. The method might be a good candidate to become a property. |
13 | [CA1027](https://docs.microsoft.com/visualstudio/code-quality/ca1027-mark-enums-with-flagsattribute) | Mark enums with FlagsAttribute | Design | False | True | An enumeration is a value type that defines a set of related named constants. Apply FlagsAttribute to an enumeration when its named constants can be meaningfully combined. |
14 | [CA1028](https://docs.microsoft.com/visualstudio/code-quality/ca1028-enum-storage-should-be-int32) | Enum Storage should be Int32 | Design | True | True | An enumeration is a value type that defines a set of related named constants. By default, the System.Int32 data type is used to store the constant value. Although you can change this underlying type, it is not required or recommended for most scenarios. |
15 | [CA1030](https://docs.microsoft.com/visualstudio/code-quality/ca1030-use-events-where-appropriate) | Use events where appropriate | Design | True | False | This rule detects methods that have names that ordinarily would be used for events. If a method is called in response to a clearly defined state change, the method should be invoked by an event handler. Objects that call the method should raise events instead of calling the method directly. |
16 | [CA1031](https://docs.microsoft.com/en-us/visualstudio/code-quality/ca1031-do-not-catch-general-exception-types) | Do not catch general exception types | Design | True | False | A general exception such as System.Exception or System.SystemException or a disallowed exception type is caught in a catch statement, or a general catch clause is used. General and disallowed exceptions should not be caught. |
17 | [CA1032](https://docs.microsoft.com/visualstudio/code-quality/ca1032-implement-standard-exception-constructors) | Implement standard exception constructors | Design | True | True | Failure to provide the full set of constructors can make it difficult to correctly handle exceptions. |
18 | [CA1033](https://docs.microsoft.com/visualstudio/code-quality/ca1033-interface-methods-should-be-callable-by-child-types) | Interface methods should be callable by child types | Design | False | True | An unsealed externally visible type provides an explicit method implementation of a public interface and does not provide an alternative externally visible method that has the same name. |
19 | [CA1034](https://docs.microsoft.com/visualstudio/code-quality/ca1034-nested-types-should-not-be-visible) | Nested types should not be visible | Design | True | False | A nested type is a type that is declared in the scope of another type. Nested types are useful to encapsulate private implementation details of the containing type. Used for this purpose, nested types should not be externally visible. |
20 | [CA1036](https://docs.microsoft.com/visualstudio/code-quality/ca1036-override-methods-on-comparable-types) | Override methods on comparable types | Design | True | True | A public or protected type implements the System.IComparable interface. It does not override Object.Equals nor does it overload the language-specific operator for equality, inequality, less than, less than or equal, greater than or greater than or equal. |
21 | [CA1040](https://docs.microsoft.com/visualstudio/code-quality/ca1040-avoid-empty-interfaces) | Avoid empty interfaces | Design | True | False | Interfaces define members that provide a behavior or usage contract. The functionality that is described by the interface can be adopted by any type, regardless of where the type appears in the inheritance hierarchy. A type implements an interface by providing implementations for the members of the interface. An empty interface does not define any members; therefore, it does not define a contract that can be implemented. |
22 | [CA1041](https://docs.microsoft.com/visualstudio/code-quality/ca1041-provide-obsoleteattribute-message) | Provide ObsoleteAttribute message | Design | True | False | A type or member is marked by using a System.ObsoleteAttribute attribute that does not have its ObsoleteAttribute.Message property specified. When a type or member that is marked by using ObsoleteAttribute is compiled, the Message property of the attribute is displayed. This gives the user information about the obsolete type or member. |
23 | [CA1043](https://docs.microsoft.com/visualstudio/code-quality/ca1043-use-integral-or-string-argument-for-indexers) | Use Integral Or String Argument For Indexers | Design | True | False | Indexers, that is, indexed properties, should use integer or string types for the index. These types are typically used for indexing data structures and increase the usability of the library. Use of the Object type should be restricted to those cases where the specific integer or string type cannot be specified at design time. If the design requires other types for the index, reconsider whether the type represents a logical data store. If it does not represent a logical data store, use a method. |
24 | [CA1044](https://docs.microsoft.com/visualstudio/code-quality/ca1044-properties-should-not-be-write-only) | Properties should not be write only | Design | True | False | Although it is acceptable and often necessary to have a read-only property, the design guidelines prohibit the use of write-only properties. This is because letting a user set a value, and then preventing the user from viewing that value, does not provide any security. Also, without read access, the state of shared objects cannot be viewed, which limits their usefulness. |
25 | [CA1050](https://docs.microsoft.com/visualstudio/code-quality/ca1050-declare-types-in-namespaces) | Declare types in namespaces | Design | False | False | Types are declared in namespaces to prevent name collisions and as a way to organize related types in an object hierarchy. |
26 | [CA1051](https://docs.microsoft.com/visualstudio/code-quality/ca1051-do-not-declare-visible-instance-fields) | Do not declare visible instance fields | Design | True | False | The primary use of a field should be as an implementation detail. Fields should be private or internal and should be exposed by using properties. |
27 | [CA1052](https://docs.microsoft.com/visualstudio/code-quality/ca1052-static-holder-types-should-be-sealed) | Static holder types should be Static or NotInheritable | Design | True | True | Type '{0}' is a static holder type but is neither static nor NotInheritable |
28 | [CA1054](https://docs.microsoft.com/visualstudio/code-quality/ca1054-uri-parameters-should-not-be-strings) | Uri parameters should not be strings | Design | True | True | If a method takes a string representation of a URI, a corresponding overload should be provided that takes an instance of the URI class, which provides these services in a safe and secure manner. |
29 | [CA1055](https://docs.microsoft.com/visualstudio/code-quality/ca1055-uri-return-values-should-not-be-strings) | Uri return values should not be strings | Design | True | False | This rule assumes that the method returns a URI. A string representation of a URI is prone to parsing and encoding errors, and can lead to security vulnerabilities. The System.Uri class provides these services in a safe and secure manner. |
30 | [CA1056](https://docs.microsoft.com/visualstudio/code-quality/ca1056-uri-properties-should-not-be-strings) | Uri properties should not be strings | Design | True | False | This rule assumes that the property represents a Uniform Resource Identifier (URI). A string representation of a URI is prone to parsing and encoding errors, and can lead to security vulnerabilities. The System.Uri class provides these services in a safe and secure manner. |
31 | [CA1060](https://docs.microsoft.com/visualstudio/code-quality/ca1060-move-p-invokes-to-nativemethods-class) | Move pinvokes to native methods class | Design | False | False | Platform Invocation methods, such as those that are marked by using the System.Runtime.InteropServices.DllImportAttribute attribute, or methods that are defined by using the Declare keyword in Visual Basic, access unmanaged code. These methods should be of the NativeMethods, SafeNativeMethods, or UnsafeNativeMethods class. |
32 | [CA1061](https://docs.microsoft.com/visualstudio/code-quality/ca1061-do-not-hide-base-class-methods) | Do not hide base class methods | Design | True | False | A method in a base type is hidden by an identically named method in a derived type when the parameter signature of the derived method differs only by types that are more weakly derived than the corresponding types in the parameter signature of the base method. |
33 | [CA1062](https://docs.microsoft.com/visualstudio/code-quality/ca1062-validate-arguments-of-public-methods) | Validate arguments of public methods | Design | True | False | An externally visible method dereferences one of its reference arguments without verifying whether that argument is null (Nothing in Visual Basic). All reference arguments that are passed to externally visible methods should be checked against null. If appropriate, throw an ArgumentNullException when the argument is null or add a Code Contract precondition asserting non-null argument. If the method is designed to be called only by known assemblies, you should make the method internal. |
34 | [CA1063](https://docs.microsoft.com/visualstudio/code-quality/ca1063-implement-idisposable-correctly) | Implement IDisposable Correctly | Design | True | False | All IDisposable types should implement the Dispose pattern correctly. |
35 | [CA1064](https://docs.microsoft.com/visualstudio/code-quality/ca1064-exceptions-should-be-public) | Exceptions should be public | Design | True | True | An internal exception is visible only inside its own internal scope. After the exception falls outside the internal scope, only the base exception can be used to catch the exception. If the internal exception is inherited from T:System.Exception, T:System.SystemException, or T:System.ApplicationException, the external code will not have sufficient information to know what to do with the exception. |
36 | [CA1065](https://docs.microsoft.com/visualstudio/code-quality/ca1065-do-not-raise-exceptions-in-unexpected-locations) | Do not raise exceptions in unexpected locations | Design | True | False | A method that is not expected to throw exceptions throws an exception. |
37 | [CA1066](http://go.microsoft.com/fwlink/?LinkId=734907) | Type {0} should implement IEquatable<T> because it overrides Equals | Design | True | True | When a type T overrides Object.Equals(object), the implementation must cast the object argument to the correct type T before performing the comparison. If the type implements IEquatable<T>, and therefore offers the method T.Equals(T), and if the argument is known at compile time to be of type T, then the compiler can call IEquatable<T>.Equals(T) instead of Object.Equals(object), and no cast is necessary, improving performance. |
38 | [CA1067](http://go.microsoft.com/fwlink/?LinkId=734909) | Override Object.Equals(object) when implementing IEquatable<T> | Design | True | True | When a type T implements the interface IEquatable<T>, it suggests to a user who sees a call to the Equals method in source code that an instance of the type can be equated with an instance of any other type. The user might be confused if their attempt to equate the type with an instance of another type fails to compile. This violates the "principle of least surprise". |
39 | CA1068 | CancellationToken parameters must come last | Design | True | False | Method '{0}' should take CancellationToken as the last parameter |
40 | CA1200 | Avoid using cref tags with a prefix | Documentation | True | False | Use of cref tags with prefixes should be avoided, since it prevents the compiler from verifying references and the IDE from updating references during refactorings. It is permissible to suppress this error at a single documentation site if the cref must use a prefix because the type being mentioned is not findable by the compiler. For example, if a cref is mentioning a special attribute in the full framework but you're in a file that compiles against the portable framework, or if you want to reference a type at higher layer of Roslyn, you should suppress the error. You should not suppress the error just because you want to take a shortcut and avoid using the full syntax. |
41 | [CA1501](https://docs.microsoft.com/visualstudio/code-quality/ca1501-avoid-excessive-inheritance) | Avoid excessive inheritance | Maintainability | False | False | Deeply nested type hierarchies can be difficult to follow, understand, and maintain. This rule limits analysis to hierarchies in the same module. To fix a violation of this rule, derive the type from a base type that is less deep in the inheritance hierarchy or eliminate some of the intermediate base types. |
42 | [CA1502](https://docs.microsoft.com/visualstudio/code-quality/ca1502-avoid-excessive-complexity) | Avoid excessive complexity | Maintainability | False | False | Cyclomatic complexity measures the number of linearly independent paths through the method, which is determined by the number and complexity of conditional branches. A low cyclomatic complexity generally indicates a method that is easy to understand, test, and maintain. The cyclomatic complexity is calculated from a control flow graph of the method and is given as follows: `cyclomatic complexity = the number of edges - the number of nodes + 1`, where a node represents a logic branch point and an edge represents a line between nodes. |
43 | [CA1505](https://docs.microsoft.com/visualstudio/code-quality/ca1505-avoid-unmaintainable-code) | Avoid unmaintainable code | Maintainability | False | False | The maintainability index is calculated by using the following metrics: lines of code, program volume, and cyclomatic complexity. Program volume is a measure of the difficulty of understanding of a symbol that is based on the number of operators and operands in the code. Cyclomatic complexity is a measure of the structural complexity of the type or method. A low maintainability index indicates that code is probably difficult to maintain and would be a good candidate to redesign. |
44 | [CA1506](https://docs.microsoft.com/visualstudio/code-quality/ca1506-avoid-excessive-class-coupling) | Avoid excessive class coupling | Maintainability | False | False | This rule measures class coupling by counting the number of unique type references that a symbol contains. Symbols that have a high degree of class coupling can be difficult to maintain. It is a good practice to have types and methods that exhibit low coupling and high cohesion. To fix this violation, try to redesign the code to reduce the number of types to which it is coupled. |
45 | [CA1507](https://docs.microsoft.com/visualstudio/code-quality/ca1507) | Use nameof to express symbol names | Maintainability | True | True | Using nameof helps keep your code valid when refactoring. |
46 | CA1508 | Avoid dead conditional code | Maintainability | False | False | '{0}' is always '{1}'. Remove or refactor the condition(s) to avoid dead code. |
47 | CA1509 | Invalid entry in code metrics rule specification file | Maintainability | False | False | Invalid entry in code metrics rule specification file |
48 | [CA1707](https://docs.microsoft.com/visualstudio/code-quality/ca1707-identifiers-should-not-contain-underscores) | Identifiers should not contain underscores | Naming | True | False | By convention, identifier names do not contain the underscore (_) character. This rule checks namespaces, types, members, and parameters. |
49 | [CA1708](https://docs.microsoft.com/visualstudio/code-quality/ca1708-identifiers-should-differ-by-more-than-case) | Identifiers should differ by more than case | Naming | False | False | Identifiers for namespaces, types, members, and parameters cannot differ only by case because languages that target the common language runtime are not required to be case-sensitive. |
50 | [CA1710](https://docs.microsoft.com/visualstudio/code-quality/ca1710-identifiers-should-have-correct-suffix) | Identifiers should have correct suffix | Naming | True | False | By convention, the names of types that extend certain base types or that implement certain interfaces, or types that are derived from these types, have a suffix that is associated with the base type or interface. |
51 | [CA1711](https://docs.microsoft.com/visualstudio/code-quality/ca1711-identifiers-should-not-have-incorrect-suffix) | Identifiers should not have incorrect suffix | Naming | False | False | By convention, only the names of types that extend certain base types or that implement certain interfaces, or types that are derived from these types, should end with specific reserved suffixes. Other type names should not use these reserved suffixes. |
52 | [CA1712](https://docs.microsoft.com/en-us/visualstudio/code-quality/ca1712-do-not-prefix-enum-values-with-type-name) | Do not prefix enum values with type name | Naming | True | False | An enumeration's values should not start with the type name of the enumeration. |
53 | [CA1714](https://docs.microsoft.com/visualstudio/code-quality/ca1714-flags-enums-should-have-plural-names) | Flags enums should have plural names | Naming | True | False | A public enumeration has the System.FlagsAttribute attribute, and its name does not end in ""s"". Types that are marked by using FlagsAttribute have names that are plural because the attribute indicates that more than one value can be specified. |
54 | [CA1715](https://docs.microsoft.com/visualstudio/code-quality/ca1715-identifiers-should-have-correct-prefix) | Identifiers should have correct prefix | Naming | True | False | Identifiers should have correct prefix |
55 | [CA1716](https://docs.microsoft.com/visualstudio/code-quality/ca1716-identifiers-should-not-match-keywords) | Identifiers should not match keywords | Naming | True | False | A namespace name or a type name matches a reserved keyword in a programming language. Identifiers for namespaces and types should not match keywords that are defined by languages that target the common language runtime. |
56 | [CA1717](https://docs.microsoft.com/visualstudio/code-quality/ca1717-only-flagsattribute-enums-should-have-plural-names) | Only FlagsAttribute enums should have plural names | Naming | True | False | Naming conventions dictate that a plural name for an enumeration indicates that more than one value of the enumeration can be specified at the same time. |
57 | [CA1720](https://docs.microsoft.com/visualstudio/code-quality/ca1720-identifiers-should-not-contain-type-names) | Identifier contains type name | Naming | True | False | Names of parameters and members are better used to communicate their meaning than to describe their type, which is expected to be provided by development tools. For names of members, if a data type name must be used, use a language-independent name instead of a language-specific one. |
58 | [CA1721](https://docs.microsoft.com/visualstudio/code-quality/ca1721-property-names-should-not-match-get-methods) | Property names should not match get methods | Naming | True | False | The name of a public or protected member starts with ""Get"" and otherwise matches the name of a public or protected property. ""Get"" methods and properties should have names that clearly distinguish their function. |
59 | [CA1724](https://docs.microsoft.com/visualstudio/code-quality/ca1724-type-names-should-not-match-namespaces) | Type names should not match namespaces | Naming | True | False | Type names should not match the names of namespaces that are defined in the .NET Framework class library. Violating this rule can reduce the usability of the library. |
60 | [CA1725](https://docs.microsoft.com/visualstudio/code-quality/ca1725-parameter-names-should-match-base-declaration) | Parameter names should match base declaration | Naming | False | True | Consistent naming of parameters in an override hierarchy increases the usability of the method overrides. A parameter name in a derived method that differs from the name in the base declaration can cause confusion about whether the method is an override of the base method or a new overload of the method. |
61 | [CA1801](https://docs.microsoft.com/visualstudio/code-quality/ca1801-review-unused-parameters) | Review unused parameters | Usage | True | True | Avoid unused paramereters in your code. If the parameter cannot be removed, then change its name so it starts with an underscore and is optionally followed by an integer, such as '_', '_1', '_2', etc. These are treated as special discard symbol names. |
62 | [CA1802](https://docs.microsoft.com/visualstudio/code-quality/ca1802-use-literals-where-appropriate) | Use literals where appropriate | Performance | True | True | A field is declared static and read-only (Shared and ReadOnly in Visual Basic), and is initialized by using a value that is computable at compile time. Because the value that is assigned to the targeted field is computable at compile time, change the declaration to a const (Const in Visual Basic) field so that the value is computed at compile time instead of at run?time. |
63 | [CA1806](https://docs.microsoft.com/visualstudio/code-quality/ca1806-do-not-ignore-method-results) | Do not ignore method results | Performance | True | False | A new object is created but never used; or a method that creates and returns a new string is called and the new string is never used; or a COM or P/Invoke method returns an HRESULT or error code that is never used. |
64 | [CA1812](https://docs.microsoft.com/visualstudio/code-quality/ca1812-avoid-uninstantiated-internal-classes) | Avoid uninstantiated internal classes | Performance | True | False | An instance of an assembly-level type is not created by code in the assembly. |
65 | [CA1814](https://docs.microsoft.com/visualstudio/code-quality/ca1814-prefer-jagged-arrays-over-multidimensional) | Prefer jagged arrays over multidimensional | Performance | True | False | A jagged array is an array whose elements are arrays. The arrays that make up the elements can be of different sizes, leading to less wasted space for some sets of data. |
66 | [CA1815](https://docs.microsoft.com/visualstudio/code-quality/ca1815-override-equals-and-operator-equals-on-value-types) | Override equals and operator equals on value types | Performance | True | True | For value types, the inherited implementation of Equals uses the Reflection library and compares the contents of all fields. Reflection is computationally expensive, and comparing every field for equality might be unnecessary. If you expect users to compare or sort instances, or to use instances as hash table keys, your value type should implement Equals. |
67 | [CA1819](https://docs.microsoft.com/visualstudio/code-quality/ca1819-properties-should-not-return-arrays) | Properties should not return arrays | Performance | True | False | Arrays that are returned by properties are not write-protected, even when the property is read-only. To keep the array tamper-proof, the property must return a copy of the array. Typically, users will not understand the adverse performance implications of calling such a property. |
68 | [CA1821](https://docs.microsoft.com/visualstudio/code-quality/ca1821-remove-empty-finalizers) | Remove empty Finalizers | Performance | True | True | Finalizers should be avoided where possible, to avoid the additional performance overhead involved in tracking object lifetime. |
69 | [CA1822](https://docs.microsoft.com/visualstudio/code-quality/ca1822-mark-members-as-static) | Mark members as static | Performance | True | True | Members that do not access instance data or call instance methods can be marked as static (Shared in Visual Basic). After you mark the methods as static, the compiler will emit nonvirtual call sites to these members. This can give you a measurable performance gain for performance-sensitive code. |
70 | [CA1823](https://docs.microsoft.com/visualstudio/code-quality/ca1823-avoid-unused-private-fields) | Avoid unused private fields | Performance | True | True | Private fields were detected that do not appear to be accessed in the assembly. |
71 | [CA2007](https://docs.microsoft.com/visualstudio/code-quality/ca2007-do-not-directly-await-task) | Consider calling ConfigureAwait on the awaited task | Reliability | True | True | When an asynchronous method awaits a Task directly, continuation occurs in the same thread that created the task. Consider calling Task.ConfigureAwait(Boolean) to signal your intention for continuation. Call ConfigureAwait(false) on the task to schedule continuations to the thread pool, thereby avoiding a deadlock on the UI thread. Passing false is a good option for app-independent libraries. Calling ConfigureAwait(true) on the task has the same behavior as not explicitly calling ConfigureAwait. By explicitly calling this method, you're letting readers know you intentionally want to perform the continuation on the original synchronization context. |
72 | [CA2119](https://docs.microsoft.com/visualstudio/code-quality/ca2119-seal-methods-that-satisfy-private-interfaces) | Seal methods that satisfy private interfaces | Security | True | True | An inheritable public type provides an overridable method implementation of an internal (Friend in Visual Basic) interface. To fix a violation of this rule, prevent the method from being overridden outside the assembly. |
73 | [CA2200](https://docs.microsoft.com/visualstudio/code-quality/ca2200-rethrow-to-preserve-stack-details) | Rethrow to preserve stack details. | Usage | True | True | Re-throwing caught exception changes stack information. |
74 | [CA2211](https://docs.microsoft.com/visualstudio/code-quality/ca2211-non-constant-fields-should-not-be-visible) | Non-constant fields should not be visible | Usage | True | False | Static fields that are neither constants nor read-only are not thread-safe. Access to such a field must be carefully controlled and requires advanced programming techniques to synchronize access to the class object. |
75 | [CA2214](https://docs.microsoft.com/visualstudio/code-quality/ca2214-do-not-call-overridable-methods-in-constructors) | Do not call overridable methods in constructors | Usage | True | False | Virtual methods defined on the class should not be called from constructors. If a derived class has overridden the method, the derived class version will be called (before the derived class constructor is called). |
76 | [CA2217](https://docs.microsoft.com/visualstudio/code-quality/ca2217-do-not-mark-enums-with-flagsattribute) | Do not mark enums with FlagsAttribute | Usage | False | True | An externally visible enumeration is marked by using FlagsAttribute, and it has one or more values that are not powers of two or a combination of the other defined values on the enumeration. |
77 | [CA2218](https://docs.microsoft.com/visualstudio/code-quality/ca2218-override-gethashcode-on-overriding-equals) | Override GetHashCode on overriding Equals | Usage | True | True | GetHashCode returns a value, based on the current instance, that is suited for hashing algorithms and data structures such as a hash table. Two objects that are the same type and are equal must return the same hash code. |
78 | [CA2219](https://docs.microsoft.com/visualstudio/code-quality/ca2219-do-not-raise-exceptions-in-exception-clauses) | Do not raise exceptions in finally clauses | Usage | True | False | When an exception is raised in a finally clause, the new exception hides the active exception. This makes the original error difficult to detect and debug. |
79 | [CA2224](https://docs.microsoft.com/visualstudio/code-quality/ca2224-override-equals-on-overloading-operator-equals) | Override Equals on overloading operator equals | Usage | True | True | A public type implements the equality operator but does not override Object.Equals. |
80 | [CA2225](https://docs.microsoft.com/visualstudio/code-quality/ca2225-operator-overloads-have-named-alternates) | Operator overloads have named alternates | Usage | True | True | An operator overload was detected, and the expected named alternative method was not found. The named alternative member provides access to the same functionality as the operator and is provided for developers who program in languages that do not support overloaded operators. |
81 | [CA2226](https://docs.microsoft.com/visualstudio/code-quality/ca2226-operators-should-have-symmetrical-overloads) | Operators should have symmetrical overloads | Usage | True | True | A type implements the equality or inequality operator and does not implement the opposite operator. |
82 | [CA2227](https://docs.microsoft.com/visualstudio/code-quality/ca2227-collection-properties-should-be-read-only) | Collection properties should be read only | Usage | True | False | A writable collection property allows a user to replace the collection with a different collection. A read-only property stops the collection from being replaced but still allows the individual members to be set. |
83 | [CA2231](https://docs.microsoft.com/visualstudio/code-quality/ca2231-overload-operator-equals-on-overriding-valuetype-equals) | Overload operator equals on overriding value type Equals | Usage | True | True | In most programming languages there is no default implementation of the equality operator (==) for value types. If your programming language supports operator overloads, you should consider implementing the equality operator. Its behavior should be identical to that of Equals |
84 | [CA2234](https://docs.microsoft.com/visualstudio/code-quality/ca2234-pass-system-uri-objects-instead-of-strings) | Pass system uri objects instead of strings | Usage | True | False | A call is made to a method that has a string parameter whose name contains "uri", "URI", "urn", "URN", "url", or "URL". The declaring type of the method contains a corresponding method overload that has a System.Uri parameter. |
85 | CA2244 | Do not duplicate indexed element initializations | Usage | True | False | Indexed elements in objects initializers must initialize unique elements. A duplicate index might overwrite a previous element initialization. |
86 | CA2245 | Do not assign a property to itself. | Usage | True | False | The property {0} should not be assigned to itself. |
87 | CA2246 | Assigning symbol and its member in the same statement. | Usage | True | False | Assigning to a symbol and its member (field/property) in the same statement is not recommended. It is not clear if the member access was intended to use symbol's old value prior to the assignment or new value from the assignment in this statement. For clarity, consider splitting the assignments into separate statements. |
