﻿{
  "$schema": "http://json.schemastore.org/sarif-1.0.0",
  "version": "1.0.0",
  "runs": [
    {
      "tool": {
        "name": "Humanizer",
        "version": "2.9.8",
        "language": "en-US"
      },
      "rules": {
      }
    },
    {
      "tool": {
        "name": "Microsoft.CodeQuality.Analyzers",
        "version": "2.9.8",
        "language": "en-US"
      },
      "rules": {
        "CA1000": {
          "id": "CA1000",
          "shortDescription": "Do not declare static members on generic types",
          "fullDescription": "When a static member of a generic type is called, the type argument must be specified for the type. When a generic instance member that does not support inference is called, the type argument must be specified for the member. In these two cases, the syntax for specifying the type argument is different and easily confused.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1000-do-not-declare-static-members-on-generic-types",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": true,
            "typeName": "DoNotDeclareStaticMembersOnGenericTypesAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1008": {
          "id": "CA1008",
          "shortDescription": "Enums should have zero value",
          "fullDescription": "The default value of an uninitialized enumeration, just as other value types, is zero. A nonflags-attributed enumeration should define a member by using the value of zero so that the default value is a valid value of the enumeration. If an enumeration that has the FlagsAttribute attribute applied defines a zero-valued member, its name should be \"\"None\"\" to indicate that no values have been set in the enumeration.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1008-enums-should-have-zero-value",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": false,
            "typeName": "EnumsShouldHaveZeroValueAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry",
              "RuleNoZero"
            ]
          }
        },
        "CA1010": {
          "id": "CA1010",
          "shortDescription": "Collections should implement generic interface",
          "fullDescription": "To broaden the usability of a collection, implement one of the generic collection interfaces. Then the collection can be used to populate generic collection types.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1010-collections-should-implement-generic-interface",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": true,
            "typeName": "CollectionsShouldImplementGenericInterfaceAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1012": {
          "id": "CA1012",
          "shortDescription": "Abstract types should not have constructors",
          "fullDescription": "Constructors on abstract types can be called only by derived types. Because public constructors create instances of a type, and you cannot create instances of an abstract type, an abstract type that has a public constructor is incorrectly designed.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1012-abstract-types-should-not-have-constructors",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": false,
            "typeName": "AbstractTypesShouldNotHaveConstructorsAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1014": {
          "id": "CA1014",
          "shortDescription": "Mark assemblies with CLSCompliant",
          "fullDescription": "The Common Language Specification (CLS) defines naming restrictions, data types, and rules to which assemblies must conform if they will be used across programming languages. Good design dictates that all assemblies explicitly indicate CLS compliance by using CLSCompliantAttribute . If this attribute is not present on an assembly, the assembly is not compliant.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1014-mark-assemblies-with-clscompliantattribute",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": false,
            "typeName": "MarkAssembliesWithAttributesDiagnosticAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1016": {
          "id": "CA1016",
          "shortDescription": "Mark assemblies with assembly version",
          "fullDescription": "The .NET Framework uses the version number to uniquely identify an assembly, and to bind to types in strongly named assemblies. The version number is used together with version and publisher policy. By default, applications run only with the assembly version with which they were built.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1016-mark-assemblies-with-assemblyversionattribute",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": true,
            "typeName": "MarkAssembliesWithAttributesDiagnosticAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1017": {
          "id": "CA1017",
          "shortDescription": "Mark assemblies with ComVisible",
          "fullDescription": "ComVisibleAttribute determines how COM clients access managed code. Good design dictates that assemblies explicitly indicate COM visibility. COM visibility can be set for the whole assembly and then overridden for individual types and type members. If this attribute is not present, the contents of the assembly are visible to COM clients.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1017-mark-assemblies-with-comvisibleattribute",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": false,
            "typeName": "MarkAssembliesWithComVisibleAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1018": {
          "id": "CA1018",
          "shortDescription": "Mark attributes with AttributeUsageAttribute",
          "fullDescription": "Specify AttributeUsage on {0}.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1018-mark-attributes-with-attributeusageattribute",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": true,
            "typeName": "MarkAttributesWithAttributeUsageAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1024": {
          "id": "CA1024",
          "shortDescription": "Use properties where appropriate",
          "fullDescription": "A public or protected method has a name that starts with \"\"Get\"\", takes no parameters, and returns a value that is not an array. The method might be a good candidate to become a property.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1024-use-properties-where-appropriate",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": false,
            "typeName": "UsePropertiesWhereAppropriateAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1027": {
          "id": "CA1027",
          "shortDescription": "Mark enums with FlagsAttribute",
          "fullDescription": "An enumeration is a value type that defines a set of related named constants. Apply FlagsAttribute to an enumeration when its named constants can be meaningfully combined.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1027-mark-enums-with-flagsattribute",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": false,
            "typeName": "EnumWithFlagsAttributeAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1028": {
          "id": "CA1028",
          "shortDescription": "Enum Storage should be Int32",
          "fullDescription": "An enumeration is a value type that defines a set of related named constants. By default, the System.Int32 data type is used to store the constant value. Although you can change this underlying type, it is not required or recommended for most scenarios.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1028-enum-storage-should-be-int32",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": true,
            "typeName": "EnumStorageShouldBeInt32Analyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1030": {
          "id": "CA1030",
          "shortDescription": "Use events where appropriate",
          "fullDescription": "This rule detects methods that have names that ordinarily would be used for events. If a method is called in response to a clearly defined state change, the method should be invoked by an event handler. Objects that call the method should raise events instead of calling the method directly.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1030-use-events-where-appropriate",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": true,
            "typeName": "UseEventsWhereAppropriateAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1031": {
          "id": "CA1031",
          "shortDescription": "Do not catch general exception types",
          "fullDescription": "A general exception such as System.Exception or System.SystemException or a disallowed exception type is caught in a catch statement, or a general catch clause is used. General and disallowed exceptions should not be caught.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/en-us/visualstudio/code-quality/ca1031-do-not-catch-general-exception-types",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": true,
            "typeName": "DoNotCatchGeneralExceptionTypesAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1033": {
          "id": "CA1033",
          "shortDescription": "Interface methods should be callable by child types",
          "fullDescription": "An unsealed externally visible type provides an explicit method implementation of a public interface and does not provide an alternative externally visible method that has the same name.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1033-interface-methods-should-be-callable-by-child-types",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": false,
            "typeName": "InterfaceMethodsShouldBeCallableByChildTypesAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1034": {
          "id": "CA1034",
          "shortDescription": "Nested types should not be visible",
          "fullDescription": "A nested type is a type that is declared in the scope of another type. Nested types are useful to encapsulate private implementation details of the containing type. Used for this purpose, nested types should not be externally visible.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1034-nested-types-should-not-be-visible",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": true,
            "typeName": "NestedTypesShouldNotBeVisibleAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1036": {
          "id": "CA1036",
          "shortDescription": "Override methods on comparable types",
          "fullDescription": "A public or protected type implements the System.IComparable interface. It does not override Object.Equals nor does it overload the language-specific operator for equality, inequality, less than, less than or equal, greater than or greater than or equal.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1036-override-methods-on-comparable-types",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": true,
            "typeName": "OverrideMethodsOnComparableTypesAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1040": {
          "id": "CA1040",
          "shortDescription": "Avoid empty interfaces",
          "fullDescription": "Interfaces define members that provide a behavior or usage contract. The functionality that is described by the interface can be adopted by any type, regardless of where the type appears in the inheritance hierarchy. A type implements an interface by providing implementations for the members of the interface. An empty interface does not define any members; therefore, it does not define a contract that can be implemented.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1040-avoid-empty-interfaces",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": true,
            "typeName": "AvoidEmptyInterfacesAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1041": {
          "id": "CA1041",
          "shortDescription": "Provide ObsoleteAttribute message",
          "fullDescription": "A type or member is marked by using a System.ObsoleteAttribute attribute that does not have its ObsoleteAttribute.Message property specified. When a type or member that is marked by using ObsoleteAttribute is compiled, the Message property of the attribute is displayed. This gives the user information about the obsolete type or member.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1041-provide-obsoleteattribute-message",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": true,
            "typeName": "ProvideObsoleteAttributeMessageAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1043": {
          "id": "CA1043",
          "shortDescription": "Use Integral Or String Argument For Indexers",
          "fullDescription": "Indexers, that is, indexed properties, should use integer or string types for the index. These types are typically used for indexing data structures and increase the usability of the library. Use of the Object type should be restricted to those cases where the specific integer or string type cannot be specified at design time. If the design requires other types for the index, reconsider whether the type represents a logical data store. If it does not represent a logical data store, use a method.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1043-use-integral-or-string-argument-for-indexers",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": true,
            "typeName": "UseIntegralOrStringArgumentForIndexersAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1044": {
          "id": "CA1044",
          "shortDescription": "Properties should not be write only",
          "fullDescription": "Although it is acceptable and often necessary to have a read-only property, the design guidelines prohibit the use of write-only properties. This is because letting a user set a value, and then preventing the user from viewing that value, does not provide any security. Also, without read access, the state of shared objects cannot be viewed, which limits their usefulness.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1044-properties-should-not-be-write-only",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": true,
            "typeName": "PropertiesShouldNotBeWriteOnlyAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1050": {
          "id": "CA1050",
          "shortDescription": "Declare types in namespaces",
          "fullDescription": "Types are declared in namespaces to prevent name collisions and as a way to organize related types in an object hierarchy.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1050-declare-types-in-namespaces",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": false,
            "typeName": "DeclareTypesInNamespacesAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1051": {
          "id": "CA1051",
          "shortDescription": "Do not declare visible instance fields",
          "fullDescription": "The primary use of a field should be as an implementation detail. Fields should be private or internal and should be exposed by using properties.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1051-do-not-declare-visible-instance-fields",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": true,
            "typeName": "DoNotDeclareVisibleInstanceFieldsAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1052": {
          "id": "CA1052",
          "shortDescription": "Static holder types should be Static or NotInheritable",
          "fullDescription": "Type '{0}' is a static holder type but is neither static nor NotInheritable",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1052-static-holder-types-should-be-sealed",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": true,
            "typeName": "StaticHolderTypesAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1054": {
          "id": "CA1054",
          "shortDescription": "Uri parameters should not be strings",
          "fullDescription": "If a method takes a string representation of a URI, a corresponding overload should be provided that takes an instance of the URI class, which provides these services in a safe and secure manner.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1054-uri-parameters-should-not-be-strings",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": true,
            "typeName": "UriParametersShouldNotBeStringsAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1055": {
          "id": "CA1055",
          "shortDescription": "Uri return values should not be strings",
          "fullDescription": "This rule assumes that the method returns a URI. A string representation of a URI is prone to parsing and encoding errors, and can lead to security vulnerabilities. The System.Uri class provides these services in a safe and secure manner.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1055-uri-return-values-should-not-be-strings",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": true,
            "typeName": "UriReturnValuesShouldNotBeStringsAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1056": {
          "id": "CA1056",
          "shortDescription": "Uri properties should not be strings",
          "fullDescription": "This rule assumes that the property represents a Uniform Resource Identifier (URI). A string representation of a URI is prone to parsing and encoding errors, and can lead to security vulnerabilities. The System.Uri class provides these services in a safe and secure manner.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1056-uri-properties-should-not-be-strings",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": true,
            "typeName": "UriPropertiesShouldNotBeStringsAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1060": {
          "id": "CA1060",
          "shortDescription": "Move pinvokes to native methods class",
          "fullDescription": "Platform Invocation methods, such as those that are marked by using the System.Runtime.InteropServices.DllImportAttribute attribute, or methods that are defined by using the Declare keyword in Visual Basic, access unmanaged code. These methods should be of the NativeMethods, SafeNativeMethods, or UnsafeNativeMethods class.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1060-move-p-invokes-to-nativemethods-class",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": false,
            "typeName": "MovePInvokesToNativeMethodsClassAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1061": {
          "id": "CA1061",
          "shortDescription": "Do not hide base class methods",
          "fullDescription": "A method in a base type is hidden by an identically named method in a derived type when the parameter signature of the derived method differs only by types that are more weakly derived than the corresponding types in the parameter signature of the base method.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1061-do-not-hide-base-class-methods",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": true,
            "typeName": "DoNotHideBaseClassMethodsAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1062": {
          "id": "CA1062",
          "shortDescription": "Validate arguments of public methods",
          "fullDescription": "An externally visible method dereferences one of its reference arguments without verifying whether that argument is null (Nothing in Visual Basic). All reference arguments that are passed to externally visible methods should be checked against null. If appropriate, throw an ArgumentNullException when the argument is null or add a Code Contract precondition asserting non-null argument. If the method is designed to be called only by known assemblies, you should make the method internal.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1062-validate-arguments-of-public-methods",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": true,
            "typeName": "ValidateArgumentsOfPublicMethods",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Dataflow",
              "Telemetry"
            ]
          }
        },
        "CA1063": {
          "id": "CA1063",
          "shortDescription": "Implement IDisposable Correctly",
          "fullDescription": "All IDisposable types should implement the Dispose pattern correctly.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1063-implement-idisposable-correctly",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": true,
            "typeName": "ImplementIDisposableCorrectlyAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1064": {
          "id": "CA1064",
          "shortDescription": "Exceptions should be public",
          "fullDescription": "An internal exception is visible only inside its own internal scope. After the exception falls outside the internal scope, only the base exception can be used to catch the exception. If the internal exception is inherited from T:System.Exception, T:System.SystemException, or T:System.ApplicationException, the external code will not have sufficient information to know what to do with the exception.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1064-exceptions-should-be-public",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": true,
            "typeName": "ExceptionsShouldBePublicAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1066": {
          "id": "CA1066",
          "shortDescription": "Type {0} should implement IEquatable<T> because it overrides Equals",
          "fullDescription": "When a type T overrides Object.Equals(object), the implementation must cast the object argument to the correct type T before performing the comparison. If the type implements IEquatable<T>, and therefore offers the method T.Equals(T), and if the argument is known at compile time to be of type T, then the compiler can call IEquatable<T>.Equals(T) instead of Object.Equals(object), and no cast is necessary, improving performance.",
          "defaultLevel": "warning",
          "helpUri": "http://go.microsoft.com/fwlink/?LinkId=734907",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": true,
            "typeName": "EquatableAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ]
          }
        },
        "CA1067": {
          "id": "CA1067",
          "shortDescription": "Override Object.Equals(object) when implementing IEquatable<T>",
          "fullDescription": "When a type T implements the interface IEquatable<T>, it suggests to a user who sees a call to the Equals method in source code that an instance of the type can be equated with an instance of any other type. The user might be confused if their attempt to equate the type with an instance of another type fails to compile. This violates the \"principle of least surprise\".",
          "defaultLevel": "warning",
          "helpUri": "http://go.microsoft.com/fwlink/?LinkId=734909",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": true,
            "typeName": "EquatableAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ]
          }
        },
        "CA1068": {
          "id": "CA1068",
          "shortDescription": "CancellationToken parameters must come last",
          "fullDescription": "Method '{0}' should take CancellationToken as the last parameter",
          "defaultLevel": "warning",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": true,
            "typeName": "CancellationTokenParametersMustComeLastAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ]
          }
        },
        "CA1501": {
          "id": "CA1501",
          "shortDescription": "Avoid excessive inheritance",
          "fullDescription": "Deeply nested type hierarchies can be difficult to follow, understand, and maintain. This rule limits analysis to hierarchies in the same module. To fix a violation of this rule, derive the type from a base type that is less deep in the inheritance hierarchy or eliminate some of the intermediate base types.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1501-avoid-excessive-inheritance",
          "properties": {
            "category": "Maintainability",
            "isEnabledByDefault": false,
            "typeName": "CodeMetricsAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1502": {
          "id": "CA1502",
          "shortDescription": "Avoid excessive complexity",
          "fullDescription": "Cyclomatic complexity measures the number of linearly independent paths through the method, which is determined by the number and complexity of conditional branches. A low cyclomatic complexity generally indicates a method that is easy to understand, test, and maintain. The cyclomatic complexity is calculated from a control flow graph of the method and is given as follows: `cyclomatic complexity = the number of edges - the number of nodes + 1`, where a node represents a logic branch point and an edge represents a line between nodes.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1502-avoid-excessive-complexity",
          "properties": {
            "category": "Maintainability",
            "isEnabledByDefault": false,
            "typeName": "CodeMetricsAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1505": {
          "id": "CA1505",
          "shortDescription": "Avoid unmaintainable code",
          "fullDescription": "The maintainability index is calculated by using the following metrics: lines of code, program volume, and cyclomatic complexity. Program volume is a measure of the difficulty of understanding of a symbol that is based on the number of operators and operands in the code. Cyclomatic complexity is a measure of the structural complexity of the type or method. A low maintainability index indicates that code is probably difficult to maintain and would be a good candidate to redesign.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1505-avoid-unmaintainable-code",
          "properties": {
            "category": "Maintainability",
            "isEnabledByDefault": false,
            "typeName": "CodeMetricsAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1506": {
          "id": "CA1506",
          "shortDescription": "Avoid excessive class coupling",
          "fullDescription": "This rule measures class coupling by counting the number of unique type references that a symbol contains. Symbols that have a high degree of class coupling can be difficult to maintain. It is a good practice to have types and methods that exhibit low coupling and high cohesion. To fix this violation, try to redesign the code to reduce the number of types to which it is coupled.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1506-avoid-excessive-class-coupling",
          "properties": {
            "category": "Maintainability",
            "isEnabledByDefault": false,
            "typeName": "CodeMetricsAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1508": {
          "id": "CA1508",
          "shortDescription": "Avoid dead conditional code",
          "fullDescription": "'{0}' is always '{1}'. Remove or refactor the condition(s) to avoid dead code.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Maintainability",
            "isEnabledByDefault": false,
            "typeName": "AvoidDeadConditionalCode",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Dataflow",
              "Telemetry"
            ]
          }
        },
        "CA1509": {
          "id": "CA1509",
          "shortDescription": "Invalid entry in code metrics rule specification file",
          "fullDescription": "Invalid entry in code metrics rule specification file",
          "defaultLevel": "warning",
          "properties": {
            "category": "Maintainability",
            "isEnabledByDefault": false,
            "typeName": "CodeMetricsAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA1707": {
          "id": "CA1707",
          "shortDescription": "Identifiers should not contain underscores",
          "fullDescription": "By convention, identifier names do not contain the underscore (_) character. This rule checks namespaces, types, members, and parameters.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1707-identifiers-should-not-contain-underscores",
          "properties": {
            "category": "Naming",
            "isEnabledByDefault": true,
            "typeName": "IdentifiersShouldNotContainUnderscoresAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1708": {
          "id": "CA1708",
          "shortDescription": "Identifiers should differ by more than case",
          "fullDescription": "Identifiers for namespaces, types, members, and parameters cannot differ only by case because languages that target the common language runtime are not required to be case-sensitive.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1708-identifiers-should-differ-by-more-than-case",
          "properties": {
            "category": "Naming",
            "isEnabledByDefault": false,
            "typeName": "IdentifiersShouldDifferByMoreThanCaseAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1710": {
          "id": "CA1710",
          "shortDescription": "Identifiers should have correct suffix",
          "fullDescription": "By convention, the names of types that extend certain base types or that implement certain interfaces, or types that are derived from these types, have a suffix that is associated with the base type or interface.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1710-identifiers-should-have-correct-suffix",
          "properties": {
            "category": "Naming",
            "isEnabledByDefault": true,
            "typeName": "IdentifiersShouldHaveCorrectSuffixAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1711": {
          "id": "CA1711",
          "shortDescription": "Identifiers should not have incorrect suffix",
          "fullDescription": "By convention, only the names of types that extend certain base types or that implement certain interfaces, or types that are derived from these types, should end with specific reserved suffixes. Other type names should not use these reserved suffixes.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1711-identifiers-should-not-have-incorrect-suffix",
          "properties": {
            "category": "Naming",
            "isEnabledByDefault": false,
            "typeName": "IdentifiersShouldNotHaveIncorrectSuffixAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1712": {
          "id": "CA1712",
          "shortDescription": "Do not prefix enum values with type name",
          "fullDescription": "An enumeration's values should not start with the type name of the enumeration.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/en-us/visualstudio/code-quality/ca1712-do-not-prefix-enum-values-with-type-name",
          "properties": {
            "category": "Naming",
            "isEnabledByDefault": true,
            "typeName": "DoNotPrefixEnumValuesWithTypeNameAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1714": {
          "id": "CA1714",
          "shortDescription": "Flags enums should have plural names",
          "fullDescription": "A public enumeration has the System.FlagsAttribute attribute, and its name does not end in \"\"s\"\". Types that are marked by using FlagsAttribute have names that are plural because the attribute indicates that more than one value can be specified.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1714-flags-enums-should-have-plural-names",
          "properties": {
            "category": "Naming",
            "isEnabledByDefault": true,
            "typeName": "EnumsShouldHavePluralNamesAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1715": {
          "id": "CA1715",
          "shortDescription": "Identifiers should have correct prefix",
          "fullDescription": "Identifiers should have correct prefix",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1715-identifiers-should-have-correct-prefix",
          "properties": {
            "category": "Naming",
            "isEnabledByDefault": true,
            "typeName": "IdentifiersShouldHaveCorrectPrefixAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1716": {
          "id": "CA1716",
          "shortDescription": "Identifiers should not match keywords",
          "fullDescription": "A namespace name or a type name matches a reserved keyword in a programming language. Identifiers for namespaces and types should not match keywords that are defined by languages that target the common language runtime.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1716-identifiers-should-not-match-keywords",
          "properties": {
            "category": "Naming",
            "isEnabledByDefault": true,
            "typeName": "IdentifiersShouldNotMatchKeywordsAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1717": {
          "id": "CA1717",
          "shortDescription": "Only FlagsAttribute enums should have plural names",
          "fullDescription": "Naming conventions dictate that a plural name for an enumeration indicates that more than one value of the enumeration can be specified at the same time.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1717-only-flagsattribute-enums-should-have-plural-names",
          "properties": {
            "category": "Naming",
            "isEnabledByDefault": true,
            "typeName": "EnumsShouldHavePluralNamesAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1720": {
          "id": "CA1720",
          "shortDescription": "Identifier contains type name",
          "fullDescription": "Names of parameters and members are better used to communicate their meaning than to describe their type, which is expected to be provided by development tools. For names of members, if a data type name must be used, use a language-independent name instead of a language-specific one.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1720-identifiers-should-not-contain-type-names",
          "properties": {
            "category": "Naming",
            "isEnabledByDefault": true,
            "typeName": "IdentifiersShouldNotContainTypeNames",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1721": {
          "id": "CA1721",
          "shortDescription": "Property names should not match get methods",
          "fullDescription": "The name of a public or protected member starts with \"\"Get\"\" and otherwise matches the name of a public or protected property. \"\"Get\"\" methods and properties should have names that clearly distinguish their function.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1721-property-names-should-not-match-get-methods",
          "properties": {
            "category": "Naming",
            "isEnabledByDefault": true,
            "typeName": "PropertyNamesShouldNotMatchGetMethodsAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1724": {
          "id": "CA1724",
          "shortDescription": "Type names should not match namespaces",
          "fullDescription": "Type names should not match the names of namespaces that are defined in the .NET Framework class library. Violating this rule can reduce the usability of the library.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1724-type-names-should-not-match-namespaces",
          "properties": {
            "category": "Naming",
            "isEnabledByDefault": true,
            "typeName": "TypeNamesShouldNotMatchNamespacesAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1725": {
          "id": "CA1725",
          "shortDescription": "Parameter names should match base declaration",
          "fullDescription": "Consistent naming of parameters in an override hierarchy increases the usability of the method overrides. A parameter name in a derived method that differs from the name in the base declaration can cause confusion about whether the method is an override of the base method or a new overload of the method.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1725-parameter-names-should-match-base-declaration",
          "properties": {
            "category": "Naming",
            "isEnabledByDefault": false,
            "typeName": "ParameterNamesShouldMatchBaseDeclarationAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1801": {
          "id": "CA1801",
          "shortDescription": "Review unused parameters",
          "fullDescription": "Avoid unused paramereters in your code. If the parameter cannot be removed, then change its name so it starts with an underscore and is optionally followed by an integer, such as '_', '_1', '_2', etc. These are treated as special discard symbol names.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1801-review-unused-parameters",
          "properties": {
            "category": "Usage",
            "isEnabledByDefault": true,
            "typeName": "ReviewUnusedParametersAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1802": {
          "id": "CA1802",
          "shortDescription": "Use literals where appropriate",
          "fullDescription": "A field is declared static and read-only (Shared and ReadOnly in Visual Basic), and is initialized by using a value that is computable at compile time. Because the value that is assigned to the targeted field is computable at compile time, change the declaration to a const (Const in Visual Basic) field so that the value is computed at compile time instead of at run?time.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1802-use-literals-where-appropriate",
          "properties": {
            "category": "Performance",
            "isEnabledByDefault": true,
            "typeName": "UseLiteralsWhereAppropriateAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1806": {
          "id": "CA1806",
          "shortDescription": "Do not ignore method results",
          "fullDescription": "A new object is created but never used; or a method that creates and returns a new string is called and the new string is never used; or a COM or P/Invoke method returns an HRESULT or error code that is never used.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1806-do-not-ignore-method-results",
          "properties": {
            "category": "Performance",
            "isEnabledByDefault": true,
            "typeName": "DoNotIgnoreMethodResultsAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1812": {
          "id": "CA1812",
          "shortDescription": "Avoid uninstantiated internal classes",
          "fullDescription": "An instance of an assembly-level type is not created by code in the assembly.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1812-avoid-uninstantiated-internal-classes",
          "properties": {
            "category": "Performance",
            "isEnabledByDefault": true,
            "typeName": "AvoidUninstantiatedInternalClassesAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1814": {
          "id": "CA1814",
          "shortDescription": "Prefer jagged arrays over multidimensional",
          "fullDescription": "A jagged array is an array whose elements are arrays. The arrays that make up the elements can be of different sizes, leading to less wasted space for some sets of data.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1814-prefer-jagged-arrays-over-multidimensional",
          "properties": {
            "category": "Performance",
            "isEnabledByDefault": true,
            "typeName": "PreferJaggedArraysOverMultidimensionalAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1815": {
          "id": "CA1815",
          "shortDescription": "Override equals and operator equals on value types",
          "fullDescription": "For value types, the inherited implementation of Equals uses the Reflection library and compares the contents of all fields. Reflection is computationally expensive, and comparing every field for equality might be unnecessary. If you expect users to compare or sort instances, or to use instances as hash table keys, your value type should implement Equals.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1815-override-equals-and-operator-equals-on-value-types",
          "properties": {
            "category": "Performance",
            "isEnabledByDefault": true,
            "typeName": "OverrideEqualsAndOperatorEqualsOnValueTypesAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1819": {
          "id": "CA1819",
          "shortDescription": "Properties should not return arrays",
          "fullDescription": "Arrays that are returned by properties are not write-protected, even when the property is read-only. To keep the array tamper-proof, the property must return a copy of the array. Typically, users will not understand the adverse performance implications of calling such a property.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1819-properties-should-not-return-arrays",
          "properties": {
            "category": "Performance",
            "isEnabledByDefault": true,
            "typeName": "PropertiesShouldNotReturnArraysAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1822": {
          "id": "CA1822",
          "shortDescription": "Mark members as static",
          "fullDescription": "Members that do not access instance data or call instance methods can be marked as static (Shared in Visual Basic). After you mark the methods as static, the compiler will emit nonvirtual call sites to these members. This can give you a measurable performance gain for performance-sensitive code.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1822-mark-members-as-static",
          "properties": {
            "category": "Performance",
            "isEnabledByDefault": true,
            "typeName": "MarkMembersAsStaticAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1823": {
          "id": "CA1823",
          "shortDescription": "Avoid unused private fields",
          "fullDescription": "Private fields were detected that do not appear to be accessed in the assembly.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1823-avoid-unused-private-fields",
          "properties": {
            "category": "Performance",
            "isEnabledByDefault": true,
            "typeName": "AvoidUnusedPrivateFieldsAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA2007": {
          "id": "CA2007",
          "shortDescription": "Consider calling ConfigureAwait on the awaited task",
          "fullDescription": "When an asynchronous method awaits a Task directly, continuation occurs in the same thread that created the task. Consider calling Task.ConfigureAwait(Boolean) to signal your intention for continuation. Call ConfigureAwait(false) on the task to schedule continuations to the thread pool, thereby avoiding a deadlock on the UI thread. Passing false is a good option for app-independent libraries. Calling ConfigureAwait(true) on the task has the same behavior as not explicitly calling ConfigureAwait. By explicitly calling this method, you're letting readers know you intentionally want to perform the continuation on the original synchronization context.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2007-do-not-directly-await-task",
          "properties": {
            "category": "Reliability",
            "isEnabledByDefault": true,
            "typeName": "DoNotDirectlyAwaitATaskAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA2119": {
          "id": "CA2119",
          "shortDescription": "Seal methods that satisfy private interfaces",
          "fullDescription": "An inheritable public type provides an overridable method implementation of an internal (Friend in Visual Basic) interface. To fix a violation of this rule, prevent the method from being overridden outside the assembly.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2119-seal-methods-that-satisfy-private-interfaces",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "SealMethodsThatSatisfyPrivateInterfacesAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA2211": {
          "id": "CA2211",
          "shortDescription": "Non-constant fields should not be visible",
          "fullDescription": "Static fields that are neither constants nor read-only are not thread-safe. Access to such a field must be carefully controlled and requires advanced programming techniques to synchronize access to the class object.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2211-non-constant-fields-should-not-be-visible",
          "properties": {
            "category": "Usage",
            "isEnabledByDefault": true,
            "typeName": "NonConstantFieldsShouldNotBeVisibleAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA2214": {
          "id": "CA2214",
          "shortDescription": "Do not call overridable methods in constructors",
          "fullDescription": "Virtual methods defined on the class should not be called from constructors. If a derived class has overridden the method, the derived class version will be called (before the derived class constructor is called).",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2214-do-not-call-overridable-methods-in-constructors",
          "properties": {
            "category": "Usage",
            "isEnabledByDefault": true,
            "typeName": "DoNotCallOverridableMethodsInConstructorsAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA2217": {
          "id": "CA2217",
          "shortDescription": "Do not mark enums with FlagsAttribute",
          "fullDescription": "An externally visible enumeration is marked by using FlagsAttribute, and it has one or more values that are not powers of two or a combination of the other defined values on the enumeration.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2217-do-not-mark-enums-with-flagsattribute",
          "properties": {
            "category": "Usage",
            "isEnabledByDefault": false,
            "typeName": "EnumWithFlagsAttributeAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA2219": {
          "id": "CA2219",
          "shortDescription": "Do not raise exceptions in finally clauses",
          "fullDescription": "When an exception is raised in a finally clause, the new exception hides the active exception. This makes the original error difficult to detect and debug.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2219-do-not-raise-exceptions-in-exception-clauses",
          "properties": {
            "category": "Usage",
            "isEnabledByDefault": true,
            "typeName": "DoNotRaiseExceptionsInExceptionClausesAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA2225": {
          "id": "CA2225",
          "shortDescription": "Operator overloads have named alternates",
          "fullDescription": "An operator overload was detected, and the expected named alternative method was not found. The named alternative member provides access to the same functionality as the operator and is provided for developers who program in languages that do not support overloaded operators.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2225-operator-overloads-have-named-alternates",
          "properties": {
            "category": "Usage",
            "isEnabledByDefault": true,
            "typeName": "OperatorOverloadsHaveNamedAlternatesAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA2226": {
          "id": "CA2226",
          "shortDescription": "Operators should have symmetrical overloads",
          "fullDescription": "A type implements the equality or inequality operator and does not implement the opposite operator.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2226-operators-should-have-symmetrical-overloads",
          "properties": {
            "category": "Usage",
            "isEnabledByDefault": true,
            "typeName": "OperatorsShouldHaveSymmetricalOverloadsAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA2227": {
          "id": "CA2227",
          "shortDescription": "Collection properties should be read only",
          "fullDescription": "A writable collection property allows a user to replace the collection with a different collection. A read-only property stops the collection from being replaced but still allows the individual members to be set.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2227-collection-properties-should-be-read-only",
          "properties": {
            "category": "Usage",
            "isEnabledByDefault": true,
            "typeName": "CollectionPropertiesShouldBeReadOnlyAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA2231": {
          "id": "CA2231",
          "shortDescription": "Overload operator equals on overriding value type Equals",
          "fullDescription": "In most programming languages there is no default implementation of the equality operator (==) for value types. If your programming language supports operator overloads, you should consider implementing the equality operator. Its behavior should be identical to that of Equals",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2231-overload-operator-equals-on-overriding-valuetype-equals",
          "properties": {
            "category": "Usage",
            "isEnabledByDefault": true,
            "typeName": "OverloadOperatorEqualsOnOverridingValueTypeEqualsAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA2244": {
          "id": "CA2244",
          "shortDescription": "Do not duplicate indexed element initializations",
          "fullDescription": "Indexed elements in objects initializers must initialize unique elements. A duplicate index might overwrite a previous element initialization.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Usage",
            "isEnabledByDefault": true,
            "typeName": "AvoidDuplicateElementInitialization",
            "languages": [
              "C#"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA2245": {
          "id": "CA2245",
          "shortDescription": "Do not assign a property to itself.",
          "fullDescription": "The property {0} should not be assigned to itself.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Usage",
            "isEnabledByDefault": true,
            "typeName": "AvoidPropertySelfAssignment",
            "languages": [
              "C#",
              "Visual Basic"
            ]
          }
        },
        "CA2246": {
          "id": "CA2246",
          "shortDescription": "Assigning symbol and its member in the same statement.",
          "fullDescription": "Assigning to a symbol and its member (field/property) in the same statement is not recommended. It is not clear if the member access was intended to use symbol's old value prior to the assignment or new value from the assignment in this statement. For clarity, consider splitting the assignments into separate statements.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Usage",
            "isEnabledByDefault": true,
            "typeName": "AssigningSymbolAndItsMemberInSameStatement",
            "languages": [
              "C#"
            ]
          }
        }
      }
    },
    {
      "tool": {
        "name": "Microsoft.CodeQuality.CSharp.Analyzers",
        "version": "2.9.8",
        "language": "en-US"
      },
      "rules": {
        "CA1001": {
          "id": "CA1001",
          "shortDescription": "Types that own disposable fields should be disposable",
          "fullDescription": "A class declares and implements an instance field that is a System.IDisposable type, and the class does not implement IDisposable. A class that declares an IDisposable field indirectly owns an unmanaged resource and should implement the IDisposable interface.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1001-types-that-own-disposable-fields-should-be-disposable",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": true,
            "typeName": "CSharpTypesThatOwnDisposableFieldsShouldBeDisposableAnalyzer",
            "languages": [
              "C#"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1003": {
          "id": "CA1003",
          "shortDescription": "Use generic event handler instances",
          "fullDescription": "A type contains an event that declares an EventHandler delegate that returns void, whose signature contains two parameters (the first an object and the second a type that is assignable to EventArgs), and the containing assembly targets Microsoft .NET Framework?2.0.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1003-use-generic-event-handler-instances",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": false,
            "typeName": "CSharpUseGenericEventHandlerInstancesAnalyzer",
            "languages": [
              "C#"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1019": {
          "id": "CA1019",
          "shortDescription": "Define accessors for attribute arguments",
          "fullDescription": "Remove the property setter from {0} or reduce its accessibility because it corresponds to positional argument {1}.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1019-define-accessors-for-attribute-arguments",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": false,
            "typeName": "CSharpDefineAccessorsForAttributeArgumentsAnalyzer",
            "languages": [
              "C#"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1032": {
          "id": "CA1032",
          "shortDescription": "Implement standard exception constructors",
          "fullDescription": "Failure to provide the full set of constructors can make it difficult to correctly handle exceptions.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1032-implement-standard-exception-constructors",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": true,
            "typeName": "CSharpImplementStandardExceptionConstructorsAnalyzer",
            "languages": [
              "C#"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1065": {
          "id": "CA1065",
          "shortDescription": "Do not raise exceptions in unexpected locations",
          "fullDescription": "A method that is not expected to throw exceptions throws an exception.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1065-do-not-raise-exceptions-in-unexpected-locations",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": true,
            "typeName": "CSharpDoNotRaiseExceptionsInUnexpectedLocationsAnalyzer",
            "languages": [
              "C#"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1200": {
          "id": "CA1200",
          "shortDescription": "Avoid using cref tags with a prefix",
          "fullDescription": "Use of cref tags with prefixes should be avoided, since it prevents the compiler from verifying references and the IDE from updating references during refactorings. It is permissible to suppress this error at a single documentation site if the cref must use a prefix because the type being mentioned is not findable by the compiler. For example, if a cref is mentioning a special attribute in the full framework but you're in a file that compiles against the portable framework, or if you want to reference a type at higher layer of Roslyn, you should suppress the error. You should not suppress the error just because you want to take a shortcut and avoid using the full syntax.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Documentation",
            "isEnabledByDefault": true,
            "typeName": "CSharpAvoidUsingCrefTagsWithAPrefixAnalyzer",
            "languages": [
              "C#"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA1507": {
          "id": "CA1507",
          "shortDescription": "Use nameof to express symbol names",
          "fullDescription": "Using nameof helps keep your code valid when refactoring.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1507",
          "properties": {
            "category": "Maintainability",
            "isEnabledByDefault": true,
            "typeName": "CSharpUseNameofInPlaceOfStringAnalyzer",
            "languages": [
              "C#"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA1821": {
          "id": "CA1821",
          "shortDescription": "Remove empty Finalizers",
          "fullDescription": "Finalizers should be avoided where possible, to avoid the additional performance overhead involved in tracking object lifetime.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1821-remove-empty-finalizers",
          "properties": {
            "category": "Performance",
            "isEnabledByDefault": true,
            "typeName": "CSharpRemoveEmptyFinalizersAnalyzer",
            "languages": [
              "C#"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA2200": {
          "id": "CA2200",
          "shortDescription": "Rethrow to preserve stack details.",
          "fullDescription": "Re-throwing caught exception changes stack information.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2200-rethrow-to-preserve-stack-details",
          "properties": {
            "category": "Usage",
            "isEnabledByDefault": true,
            "typeName": "CSharpRethrowToPreserveStackDetailsAnalyzer",
            "languages": [
              "C#"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA2234": {
          "id": "CA2234",
          "shortDescription": "Pass system uri objects instead of strings",
          "fullDescription": "A call is made to a method that has a string parameter whose name contains \"uri\", \"URI\", \"urn\", \"URN\", \"url\", or \"URL\". The declaring type of the method contains a corresponding method overload that has a System.Uri parameter.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2234-pass-system-uri-objects-instead-of-strings",
          "properties": {
            "category": "Usage",
            "isEnabledByDefault": true,
            "typeName": "CSharpPassSystemUriObjectsInsteadOfStringsAnalyzer",
            "languages": [
              "C#"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        }
      }
    },
    {
      "tool": {
        "name": "Microsoft.CodeQuality.VisualBasic.Analyzers",
        "version": "2.9.8",
        "language": "en-US"
      },
      "rules": {
        "CA1001": {
          "id": "CA1001",
          "shortDescription": "Types that own disposable fields should be disposable",
          "fullDescription": "A class declares and implements an instance field that is a System.IDisposable type, and the class does not implement IDisposable. A class that declares an IDisposable field indirectly owns an unmanaged resource and should implement the IDisposable interface.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1001-types-that-own-disposable-fields-should-be-disposable",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": true,
            "typeName": "BasicTypesThatOwnDisposableFieldsShouldBeDisposableAnalyzer",
            "languages": [
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1003": {
          "id": "CA1003",
          "shortDescription": "Use generic event handler instances",
          "fullDescription": "A type contains an event that declares an EventHandler delegate that returns void, whose signature contains two parameters (the first an object and the second a type that is assignable to EventArgs), and the containing assembly targets Microsoft .NET Framework?2.0.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1003-use-generic-event-handler-instances",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": false,
            "typeName": "BasicUseGenericEventHandlerInstancesAnalyzer",
            "languages": [
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1019": {
          "id": "CA1019",
          "shortDescription": "Define accessors for attribute arguments",
          "fullDescription": "Remove the property setter from {0} or reduce its accessibility because it corresponds to positional argument {1}.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1019-define-accessors-for-attribute-arguments",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": false,
            "typeName": "BasicDefineAccessorsForAttributeArgumentsAnalyzer",
            "languages": [
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1032": {
          "id": "CA1032",
          "shortDescription": "Implement standard exception constructors",
          "fullDescription": "Failure to provide the full set of constructors can make it difficult to correctly handle exceptions.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1032-implement-standard-exception-constructors",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": true,
            "typeName": "BasicImplementStandardExceptionConstructorsAnalyzer",
            "languages": [
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1065": {
          "id": "CA1065",
          "shortDescription": "Do not raise exceptions in unexpected locations",
          "fullDescription": "A method that is not expected to throw exceptions throws an exception.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1065-do-not-raise-exceptions-in-unexpected-locations",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": true,
            "typeName": "BasicDoNotRaiseExceptionsInUnexpectedLocationsAnalyzer",
            "languages": [
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1200": {
          "id": "CA1200",
          "shortDescription": "Avoid using cref tags with a prefix",
          "fullDescription": "Use of cref tags with prefixes should be avoided, since it prevents the compiler from verifying references and the IDE from updating references during refactorings. It is permissible to suppress this error at a single documentation site if the cref must use a prefix because the type being mentioned is not findable by the compiler. For example, if a cref is mentioning a special attribute in the full framework but you're in a file that compiles against the portable framework, or if you want to reference a type at higher layer of Roslyn, you should suppress the error. You should not suppress the error just because you want to take a shortcut and avoid using the full syntax.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Documentation",
            "isEnabledByDefault": true,
            "typeName": "BasicAvoidUsingCrefTagsWithAPrefixAnalyzer",
            "languages": [
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA1507": {
          "id": "CA1507",
          "shortDescription": "Use nameof to express symbol names",
          "fullDescription": "Using nameof helps keep your code valid when refactoring.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1507",
          "properties": {
            "category": "Maintainability",
            "isEnabledByDefault": true,
            "typeName": "BasicUseNameofInPlaceOfStringAnalyzer",
            "languages": [
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA1821": {
          "id": "CA1821",
          "shortDescription": "Remove empty Finalizers",
          "fullDescription": "Finalizers should be avoided where possible, to avoid the additional performance overhead involved in tracking object lifetime.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1821-remove-empty-finalizers",
          "properties": {
            "category": "Performance",
            "isEnabledByDefault": true,
            "typeName": "BasicRemoveEmptyFinalizersAnalyzer",
            "languages": [
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA2200": {
          "id": "CA2200",
          "shortDescription": "Rethrow to preserve stack details.",
          "fullDescription": "Re-throwing caught exception changes stack information.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2200-rethrow-to-preserve-stack-details",
          "properties": {
            "category": "Usage",
            "isEnabledByDefault": true,
            "typeName": "BasicRethrowToPreserveStackDetailsAnalyzer",
            "languages": [
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA2218": {
          "id": "CA2218",
          "shortDescription": "Override GetHashCode on overriding Equals",
          "fullDescription": "GetHashCode returns a value, based on the current instance, that is suited for hashing algorithms and data structures such as a hash table. Two objects that are the same type and are equal must return the same hash code.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2218-override-gethashcode-on-overriding-equals",
          "properties": {
            "category": "Usage",
            "isEnabledByDefault": true,
            "typeName": "BasicOverrideGetHashCodeOnOverridingEqualsAnalyzer",
            "languages": [
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA2224": {
          "id": "CA2224",
          "shortDescription": "Override Equals on overloading operator equals",
          "fullDescription": "A public type implements the equality operator but does not override Object.Equals.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2224-override-equals-on-overloading-operator-equals",
          "properties": {
            "category": "Usage",
            "isEnabledByDefault": true,
            "typeName": "BasicOverrideEqualsOnOverloadingOperatorEqualsAnalyzer",
            "languages": [
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA2234": {
          "id": "CA2234",
          "shortDescription": "Pass system uri objects instead of strings",
          "fullDescription": "A call is made to a method that has a string parameter whose name contains \"uri\", \"URI\", \"urn\", \"URN\", \"url\", or \"URL\". The declaring type of the method contains a corresponding method overload that has a System.Uri parameter.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2234-pass-system-uri-objects-instead-of-strings",
          "properties": {
            "category": "Usage",
            "isEnabledByDefault": true,
            "typeName": "BasicPassSystemUriObjectsInsteadOfStringsAnalyzer",
            "languages": [
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        }
      }
    }
  ]
}