# NOTE: Requires **VS2019 16.3** or later

# Design Rules Enabled with default severity
# Description: All Design Rules are enabled with default severity. Design Rules with IsEnabledByDefault = false are force enabled with default severity. Rules from a different category are disabled.

# Code files
[*.{cs,vb}]

# Design Rules

# CA1000: Do not declare static members on generic types
dotnet_diagnostic.CA1000.severity = warning

# CA1001: Types that own disposable fields should be disposable
dotnet_diagnostic.CA1001.severity = warning

# CA1003: Use generic event handler instances
dotnet_diagnostic.CA1003.severity = warning

# CA1008: Enums should have zero value
dotnet_diagnostic.CA1008.severity = warning

# CA1010: Collections should implement generic interface
dotnet_diagnostic.CA1010.severity = warning

# CA1012: Abstract types should not have constructors
dotnet_diagnostic.CA1012.severity = warning

# CA1014: Mark assemblies with CLSCompliant
dotnet_diagnostic.CA1014.severity = warning

# CA1016: Mark assemblies with assembly version
dotnet_diagnostic.CA1016.severity = warning

# CA1017: Mark assemblies with ComVisible
dotnet_diagnostic.CA1017.severity = warning

# CA1018: Mark attributes with AttributeUsageAttribute
dotnet_diagnostic.CA1018.severity = warning

# CA1019: Define accessors for attribute arguments
dotnet_diagnostic.CA1019.severity = warning

# CA1024: Use properties where appropriate
dotnet_diagnostic.CA1024.severity = warning

# CA1027: Mark enums with FlagsAttribute
dotnet_diagnostic.CA1027.severity = warning

# CA1028: Enum Storage should be Int32
dotnet_diagnostic.CA1028.severity = warning

# CA1030: Use events where appropriate
dotnet_diagnostic.CA1030.severity = warning

# CA1031: Do not catch general exception types
dotnet_diagnostic.CA1031.severity = warning

# CA1032: Implement standard exception constructors
dotnet_diagnostic.CA1032.severity = warning

# CA1033: Interface methods should be callable by child types
dotnet_diagnostic.CA1033.severity = warning

# CA1034: Nested types should not be visible
dotnet_diagnostic.CA1034.severity = warning

# CA1036: Override methods on comparable types
dotnet_diagnostic.CA1036.severity = warning

# CA1040: Avoid empty interfaces
dotnet_diagnostic.CA1040.severity = warning

# CA1041: Provide ObsoleteAttribute message
dotnet_diagnostic.CA1041.severity = warning

# CA1043: Use Integral Or String Argument For Indexers
dotnet_diagnostic.CA1043.severity = warning

# CA1044: Properties should not be write only
dotnet_diagnostic.CA1044.severity = warning

# CA1050: Declare types in namespaces
dotnet_diagnostic.CA1050.severity = warning

# CA1051: Do not declare visible instance fields
dotnet_diagnostic.CA1051.severity = warning

# CA1052: Static holder types should be Static or NotInheritable
dotnet_diagnostic.CA1052.severity = warning

# CA1054: Uri parameters should not be strings
dotnet_diagnostic.CA1054.severity = warning

# CA1055: Uri return values should not be strings
dotnet_diagnostic.CA1055.severity = warning

# CA1056: Uri properties should not be strings
dotnet_diagnostic.CA1056.severity = warning

# CA1060: Move pinvokes to native methods class
dotnet_diagnostic.CA1060.severity = warning

# CA1061: Do not hide base class methods
dotnet_diagnostic.CA1061.severity = warning

# CA1062: Validate arguments of public methods
dotnet_diagnostic.CA1062.severity = warning

# CA1063: Implement IDisposable Correctly
dotnet_diagnostic.CA1063.severity = warning

# CA1064: Exceptions should be public
dotnet_diagnostic.CA1064.severity = warning

# CA1065: Do not raise exceptions in unexpected locations
dotnet_diagnostic.CA1065.severity = warning

# CA1066: Type {0} should implement IEquatable<T> because it overrides Equals
dotnet_diagnostic.CA1066.severity = warning

# CA1067: Override Object.Equals(object) when implementing IEquatable<T>
dotnet_diagnostic.CA1067.severity = warning

# CA1068: CancellationToken parameters must come last
dotnet_diagnostic.CA1068.severity = warning



# Other Rules

# CA1200: Avoid using cref tags with a prefix
dotnet_diagnostic.CA1200.severity = none

# CA1501: Avoid excessive inheritance
dotnet_diagnostic.CA1501.severity = none

# CA1502: Avoid excessive complexity
dotnet_diagnostic.CA1502.severity = none

# CA1505: Avoid unmaintainable code
dotnet_diagnostic.CA1505.severity = none

# CA1506: Avoid excessive class coupling
dotnet_diagnostic.CA1506.severity = none

# CA1507: Use nameof to express symbol names
dotnet_diagnostic.CA1507.severity = none

# CA1508: Avoid dead conditional code
dotnet_diagnostic.CA1508.severity = none

# CA1509: Invalid entry in code metrics rule specification file
dotnet_diagnostic.CA1509.severity = none

# CA1707: Identifiers should not contain underscores
dotnet_diagnostic.CA1707.severity = none

# CA1708: Identifiers should differ by more than case
dotnet_diagnostic.CA1708.severity = none

# CA1710: Identifiers should have correct suffix
dotnet_diagnostic.CA1710.severity = none

# CA1711: Identifiers should not have incorrect suffix
dotnet_diagnostic.CA1711.severity = none

# CA1712: Do not prefix enum values with type name
dotnet_diagnostic.CA1712.severity = none

# CA1714: Flags enums should have plural names
dotnet_diagnostic.CA1714.severity = none

# CA1715: Identifiers should have correct prefix
dotnet_diagnostic.CA1715.severity = none

# CA1716: Identifiers should not match keywords
dotnet_diagnostic.CA1716.severity = none

# CA1717: Only FlagsAttribute enums should have plural names
dotnet_diagnostic.CA1717.severity = none

# CA1720: Identifier contains type name
dotnet_diagnostic.CA1720.severity = none

# CA1721: Property names should not match get methods
dotnet_diagnostic.CA1721.severity = none

# CA1724: Type names should not match namespaces
dotnet_diagnostic.CA1724.severity = none

# CA1725: Parameter names should match base declaration
dotnet_diagnostic.CA1725.severity = none

# CA1801: Review unused parameters
dotnet_diagnostic.CA1801.severity = none

# CA1802: Use literals where appropriate
dotnet_diagnostic.CA1802.severity = none

# CA1806: Do not ignore method results
dotnet_diagnostic.CA1806.severity = none

# CA1812: Avoid uninstantiated internal classes
dotnet_diagnostic.CA1812.severity = none

# CA1814: Prefer jagged arrays over multidimensional
dotnet_diagnostic.CA1814.severity = none

# CA1815: Override equals and operator equals on value types
dotnet_diagnostic.CA1815.severity = none

# CA1819: Properties should not return arrays
dotnet_diagnostic.CA1819.severity = none

# CA1821: Remove empty Finalizers
dotnet_diagnostic.CA1821.severity = none

# CA1822: Mark members as static
dotnet_diagnostic.CA1822.severity = none

# CA1823: Avoid unused private fields
dotnet_diagnostic.CA1823.severity = none

# CA2007: Consider calling ConfigureAwait on the awaited task
dotnet_diagnostic.CA2007.severity = none

# CA2119: Seal methods that satisfy private interfaces
dotnet_diagnostic.CA2119.severity = none

# CA2200: Rethrow to preserve stack details.
dotnet_diagnostic.CA2200.severity = none

# CA2211: Non-constant fields should not be visible
dotnet_diagnostic.CA2211.severity = none

# CA2214: Do not call overridable methods in constructors
dotnet_diagnostic.CA2214.severity = none

# CA2217: Do not mark enums with FlagsAttribute
dotnet_diagnostic.CA2217.severity = none

# CA2218: Override GetHashCode on overriding Equals
dotnet_diagnostic.CA2218.severity = none

# CA2219: Do not raise exceptions in finally clauses
dotnet_diagnostic.CA2219.severity = none

# CA2224: Override Equals on overloading operator equals
dotnet_diagnostic.CA2224.severity = none

# CA2225: Operator overloads have named alternates
dotnet_diagnostic.CA2225.severity = none

# CA2226: Operators should have symmetrical overloads
dotnet_diagnostic.CA2226.severity = none

# CA2227: Collection properties should be read only
dotnet_diagnostic.CA2227.severity = none

# CA2231: Overload operator equals on overriding value type Equals
dotnet_diagnostic.CA2231.severity = none

# CA2234: Pass system uri objects instead of strings
dotnet_diagnostic.CA2234.severity = none

# CA2244: Do not duplicate indexed element initializations
dotnet_diagnostic.CA2244.severity = none

# CA2245: Do not assign a property to itself.
dotnet_diagnostic.CA2245.severity = none

# CA2246: Assigning symbol and its member in the same statement.
dotnet_diagnostic.CA2246.severity = none
