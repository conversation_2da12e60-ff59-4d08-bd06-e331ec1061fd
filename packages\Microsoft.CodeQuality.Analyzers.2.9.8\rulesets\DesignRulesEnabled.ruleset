<?xml version="1.0"?>
<RuleSet Name="Design Rules Enabled with default severity" Description="All Design Rules are enabled with default severity. Design Rules with IsEnabledByDefault = false are force enabled with default severity. Rules from a different category are disabled." ToolsVersion="15.0">
   <!-- Design Rules -->
   <Rules AnalyzerId="Microsoft.CodeQuality.Analyzers" RuleNamespace="Microsoft.CodeQuality.Analyzers">
      <Rule Id="CA1000" Action="Warning" />          <!-- Do not declare static members on generic types -->
      <Rule Id="CA1001" Action="Warning" />          <!-- Types that own disposable fields should be disposable -->
      <Rule Id="CA1003" Action="Warning" />          <!-- Use generic event handler instances -->
      <Rule Id="CA1008" Action="Warning" />          <!-- Enums should have zero value -->
      <Rule Id="CA1010" Action="Warning" />          <!-- Collections should implement generic interface -->
      <Rule Id="CA1012" Action="Warning" />          <!-- Abstract types should not have constructors -->
      <Rule Id="CA1014" Action="Warning" />          <!-- Mark assemblies with CLSCompliant -->
      <Rule Id="CA1016" Action="Warning" />          <!-- Mark assemblies with assembly version -->
      <Rule Id="CA1017" Action="Warning" />          <!-- Mark assemblies with ComVisible -->
      <Rule Id="CA1018" Action="Warning" />          <!-- Mark attributes with AttributeUsageAttribute -->
      <Rule Id="CA1019" Action="Warning" />          <!-- Define accessors for attribute arguments -->
      <Rule Id="CA1024" Action="Warning" />          <!-- Use properties where appropriate -->
      <Rule Id="CA1027" Action="Warning" />          <!-- Mark enums with FlagsAttribute -->
      <Rule Id="CA1028" Action="Warning" />          <!-- Enum Storage should be Int32 -->
      <Rule Id="CA1030" Action="Warning" />          <!-- Use events where appropriate -->
      <Rule Id="CA1031" Action="Warning" />          <!-- Do not catch general exception types -->
      <Rule Id="CA1032" Action="Warning" />          <!-- Implement standard exception constructors -->
      <Rule Id="CA1033" Action="Warning" />          <!-- Interface methods should be callable by child types -->
      <Rule Id="CA1034" Action="Warning" />          <!-- Nested types should not be visible -->
      <Rule Id="CA1036" Action="Warning" />          <!-- Override methods on comparable types -->
      <Rule Id="CA1040" Action="Warning" />          <!-- Avoid empty interfaces -->
      <Rule Id="CA1041" Action="Warning" />          <!-- Provide ObsoleteAttribute message -->
      <Rule Id="CA1043" Action="Warning" />          <!-- Use Integral Or String Argument For Indexers -->
      <Rule Id="CA1044" Action="Warning" />          <!-- Properties should not be write only -->
      <Rule Id="CA1050" Action="Warning" />          <!-- Declare types in namespaces -->
      <Rule Id="CA1051" Action="Warning" />          <!-- Do not declare visible instance fields -->
      <Rule Id="CA1052" Action="Warning" />          <!-- Static holder types should be Static or NotInheritable -->
      <Rule Id="CA1054" Action="Warning" />          <!-- Uri parameters should not be strings -->
      <Rule Id="CA1055" Action="Warning" />          <!-- Uri return values should not be strings -->
      <Rule Id="CA1056" Action="Warning" />          <!-- Uri properties should not be strings -->
      <Rule Id="CA1060" Action="Warning" />          <!-- Move pinvokes to native methods class -->
      <Rule Id="CA1061" Action="Warning" />          <!-- Do not hide base class methods -->
      <Rule Id="CA1062" Action="Warning" />          <!-- Validate arguments of public methods -->
      <Rule Id="CA1063" Action="Warning" />          <!-- Implement IDisposable Correctly -->
      <Rule Id="CA1064" Action="Warning" />          <!-- Exceptions should be public -->
      <Rule Id="CA1065" Action="Warning" />          <!-- Do not raise exceptions in unexpected locations -->
      <Rule Id="CA1066" Action="Warning" />          <!-- Type {0} should implement IEquatable<T> because it overrides Equals -->
      <Rule Id="CA1067" Action="Warning" />          <!-- Override Object.Equals(object) when implementing IEquatable<T> -->
      <Rule Id="CA1068" Action="Warning" />          <!-- CancellationToken parameters must come last -->
   </Rules>



   <!-- Other Rules -->
   <Rules AnalyzerId="Microsoft.CodeQuality.Analyzers" RuleNamespace="Microsoft.CodeQuality.Analyzers">
      <Rule Id="CA1200" Action="None" />             <!-- Avoid using cref tags with a prefix -->
      <Rule Id="CA1501" Action="None" />             <!-- Avoid excessive inheritance -->
      <Rule Id="CA1502" Action="None" />             <!-- Avoid excessive complexity -->
      <Rule Id="CA1505" Action="None" />             <!-- Avoid unmaintainable code -->
      <Rule Id="CA1506" Action="None" />             <!-- Avoid excessive class coupling -->
      <Rule Id="CA1507" Action="None" />             <!-- Use nameof to express symbol names -->
      <Rule Id="CA1508" Action="None" />             <!-- Avoid dead conditional code -->
      <Rule Id="CA1509" Action="None" />             <!-- Invalid entry in code metrics rule specification file -->
      <Rule Id="CA1707" Action="None" />             <!-- Identifiers should not contain underscores -->
      <Rule Id="CA1708" Action="None" />             <!-- Identifiers should differ by more than case -->
      <Rule Id="CA1710" Action="None" />             <!-- Identifiers should have correct suffix -->
      <Rule Id="CA1711" Action="None" />             <!-- Identifiers should not have incorrect suffix -->
      <Rule Id="CA1712" Action="None" />             <!-- Do not prefix enum values with type name -->
      <Rule Id="CA1714" Action="None" />             <!-- Flags enums should have plural names -->
      <Rule Id="CA1715" Action="None" />             <!-- Identifiers should have correct prefix -->
      <Rule Id="CA1716" Action="None" />             <!-- Identifiers should not match keywords -->
      <Rule Id="CA1717" Action="None" />             <!-- Only FlagsAttribute enums should have plural names -->
      <Rule Id="CA1720" Action="None" />             <!-- Identifier contains type name -->
      <Rule Id="CA1721" Action="None" />             <!-- Property names should not match get methods -->
      <Rule Id="CA1724" Action="None" />             <!-- Type names should not match namespaces -->
      <Rule Id="CA1725" Action="None" />             <!-- Parameter names should match base declaration -->
      <Rule Id="CA1801" Action="None" />             <!-- Review unused parameters -->
      <Rule Id="CA1802" Action="None" />             <!-- Use literals where appropriate -->
      <Rule Id="CA1806" Action="None" />             <!-- Do not ignore method results -->
      <Rule Id="CA1812" Action="None" />             <!-- Avoid uninstantiated internal classes -->
      <Rule Id="CA1814" Action="None" />             <!-- Prefer jagged arrays over multidimensional -->
      <Rule Id="CA1815" Action="None" />             <!-- Override equals and operator equals on value types -->
      <Rule Id="CA1819" Action="None" />             <!-- Properties should not return arrays -->
      <Rule Id="CA1821" Action="None" />             <!-- Remove empty Finalizers -->
      <Rule Id="CA1822" Action="None" />             <!-- Mark members as static -->
      <Rule Id="CA1823" Action="None" />             <!-- Avoid unused private fields -->
      <Rule Id="CA2007" Action="None" />             <!-- Consider calling ConfigureAwait on the awaited task -->
      <Rule Id="CA2119" Action="None" />             <!-- Seal methods that satisfy private interfaces -->
      <Rule Id="CA2200" Action="None" />             <!-- Rethrow to preserve stack details. -->
      <Rule Id="CA2211" Action="None" />             <!-- Non-constant fields should not be visible -->
      <Rule Id="CA2214" Action="None" />             <!-- Do not call overridable methods in constructors -->
      <Rule Id="CA2217" Action="None" />             <!-- Do not mark enums with FlagsAttribute -->
      <Rule Id="CA2218" Action="None" />             <!-- Override GetHashCode on overriding Equals -->
      <Rule Id="CA2219" Action="None" />             <!-- Do not raise exceptions in finally clauses -->
      <Rule Id="CA2224" Action="None" />             <!-- Override Equals on overloading operator equals -->
      <Rule Id="CA2225" Action="None" />             <!-- Operator overloads have named alternates -->
      <Rule Id="CA2226" Action="None" />             <!-- Operators should have symmetrical overloads -->
      <Rule Id="CA2227" Action="None" />             <!-- Collection properties should be read only -->
      <Rule Id="CA2231" Action="None" />             <!-- Overload operator equals on overriding value type Equals -->
      <Rule Id="CA2234" Action="None" />             <!-- Pass system uri objects instead of strings -->
      <Rule Id="CA2244" Action="None" />             <!-- Do not duplicate indexed element initializations -->
      <Rule Id="CA2245" Action="None" />             <!-- Do not assign a property to itself. -->
      <Rule Id="CA2246" Action="None" />             <!-- Assigning symbol and its member in the same statement. -->
   </Rules>
</RuleSet>
