<?xml version="1.0" encoding="utf-8" ?>
<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">

  <configSections xdt:Transform="InsertBefore(/configuration/*[1])" />
  <configSections xdt:Locator="XPath(/configuration/configSections[last()])">
    <section name="configBuilders" xdt:Locator="Match(name)" xdt:Transform="InsertIfMissing" type="System.Configuration.ConfigurationBuildersSection, System.Configuration, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" restartOnExternalChanges="false" requirePermission="false"/>
  </configSections>
  <configSections xdt:Transform="RemoveAll" xdt:Locator="Condition(count(*)=0)" />

</configuration>