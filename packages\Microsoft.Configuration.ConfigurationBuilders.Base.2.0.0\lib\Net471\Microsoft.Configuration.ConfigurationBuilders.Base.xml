<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Configuration.ConfigurationBuilders.Base</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Configuration.ConfigurationBuilders.SectionHandler`1">
            <summary>
            A class to be used by <see cref="T:Microsoft.Configuration.ConfigurationBuilders.KeyValueConfigBuilder"/>s to apply key/value config pairs to .Net configuration sections.
            </summary>
            <typeparam name="T">The type of <see cref="T:System.Configuration.ConfigurationSection"/> that the implementing class can process.</typeparam>
        </member>
        <member name="P:Microsoft.Configuration.ConfigurationBuilders.SectionHandler`1.ConfigSection">
            <summary>
            The <see cref="T:System.Configuration.ConfigurationSection"/> instance being processed by this <see cref="T:Microsoft.Configuration.ConfigurationBuilders.SectionHandler`1"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Configuration.ConfigurationBuilders.SectionHandler`1.GetEnumerator">
            <summary>
            Gets an <see cref="T:System.Collections.Generic.IEnumerator`1"/> that iterates over the key/value pairs contained in the assigned <see cref="P:Microsoft.Configuration.ConfigurationBuilders.SectionHandler`1.ConfigSection"/>. />
            </summary>
            <returns>An enumerator over pairs consisting of the existing key for a config value in the config section, and an object reference
            for the key/value pair to be passed in to <see cref="M:Microsoft.Configuration.ConfigurationBuilders.SectionHandler`1.InsertOrUpdate(System.String,System.String,System.String,System.Object)"/> while processing the config section.</returns>
        </member>
        <member name="M:Microsoft.Configuration.ConfigurationBuilders.SectionHandler`1.InsertOrUpdate(System.String,System.String,System.String,System.Object)">
            <summary>
            Updates an existing config value in the assigned <see cref="P:Microsoft.Configuration.ConfigurationBuilders.SectionHandler`1.ConfigSection"/> with a new key and a new value. The old config value
            can be located using the <paramref name="oldKey"/> or <paramref name="oldItem"/> parameters. If an old config value is not
            found, a new config value should be inserted.
            </summary>
            <param name="newKey">The updated key name for the config item.</param>
            <param name="newValue">The updated value for the config item.</param>
            <param name="oldKey">The old key name for the config item, or null.</param>
            <param name="oldItem">A reference to the old key/value pair obtained by <see cref="M:Microsoft.Configuration.ConfigurationBuilders.SectionHandler`1.GetEnumerator"/>, or null.</param>
        </member>
        <member name="M:Microsoft.Configuration.ConfigurationBuilders.SectionHandler`1.TryGetOriginalCase(System.String)">
            <summary>
            Attempt to lookup the original key casing so it can be preserved during greedy updates which would otherwise lose
            the original casing in favor of the casing used in the config source.
            </summary>
            <param name="requestedKey">The key to find original casing for.</param>
            <returns>Unless overridden, returns the string passed in.</returns>
        </member>
        <member name="T:Microsoft.Configuration.ConfigurationBuilders.AppSettingsSectionHandler">
            <summary>
            A class that can be used by <see cref="T:Microsoft.Configuration.ConfigurationBuilders.KeyValueConfigBuilder"/>s to apply key/value config pairs to <see cref="T:System.Configuration.AppSettingsSection"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Configuration.ConfigurationBuilders.AppSettingsSectionHandler.InsertOrUpdate(System.String,System.String,System.String,System.Object)">
            <summary>
            Updates an existing app setting in the assigned <see cref="P:Microsoft.Configuration.ConfigurationBuilders.SectionHandler`1.ConfigSection"/> with a new key and a new value. The old setting
            can be located using the <paramref name="oldKey"/> parameter. If an old setting is not found, a new setting should be inserted.
            </summary>
            <param name="newKey">The updated key name for the app setting.</param>
            <param name="newValue">The updated value for the app setting.</param>
            <param name="oldKey">The old key name for the app setting, or null.</param>
            <param name="oldItem">The old key name for the app setting, or null., or null.</param>
        </member>
        <member name="M:Microsoft.Configuration.ConfigurationBuilders.AppSettingsSectionHandler.GetEnumerator">
            <summary>
            Gets an <see cref="T:System.Collections.Generic.IEnumerator`1"/> that iterates over the key/value pairs contained in the assigned <see cref="P:Microsoft.Configuration.ConfigurationBuilders.SectionHandler`1.ConfigSection"/>. />
            </summary>
            <returns>An enumerator over pairs where both values of the pair are the existing key for each setting in the appSettings section.</returns>
        </member>
        <member name="M:Microsoft.Configuration.ConfigurationBuilders.AppSettingsSectionHandler.TryGetOriginalCase(System.String)">
            <summary>
            Attempt to lookup the original key casing so it can be preserved during greedy updates which would otherwise lose
            the original casing in favor of the casing used in the config source.
            </summary>
            <param name="requestedKey">The key to find original casing for.</param>
            <returns>A string containing the key with original casing from the config section, or the key as passed in if no match
            can be found.</returns>
        </member>
        <member name="T:Microsoft.Configuration.ConfigurationBuilders.ConnectionStringsSectionHandler">
            <summary>
            A class that can be used by <see cref="T:Microsoft.Configuration.ConfigurationBuilders.KeyValueConfigBuilder"/>s to apply key/value config pairs to <see cref="T:System.Configuration.ConnectionStringsSection"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Configuration.ConfigurationBuilders.ConnectionStringsSectionHandler.InsertOrUpdate(System.String,System.String,System.String,System.Object)">
            <summary>
            Updates an existing connection string in the assigned <see cref="P:Microsoft.Configuration.ConfigurationBuilders.SectionHandler`1.ConfigSection"/> with a new name and a new value. The old
            connection string can be located using the <paramref name="oldItem"/> parameter. If an old connection string is not found, a new connection
            string should be inserted.
            </summary>
            <param name="newKey">The updated key name for the connection string.</param>
            <param name="newValue">The updated value for the connection string.</param>
            <param name="oldKey">The old key name for the connection string, or null.</param>
            <param name="oldItem">A reference to the old <see cref="T:System.Configuration.ConnectionStringSettings"/> object obtained by <see cref="M:Microsoft.Configuration.ConfigurationBuilders.ConnectionStringsSectionHandler.GetEnumerator"/>, or null.</param>
        </member>
        <member name="M:Microsoft.Configuration.ConfigurationBuilders.ConnectionStringsSectionHandler.GetEnumerator">
            <summary>
            Gets an <see cref="T:System.Collections.Generic.IEnumerator`1"/> that iterates over the key/value pairs contained in the assigned <see cref="P:Microsoft.Configuration.ConfigurationBuilders.SectionHandler`1.ConfigSection"/>. />
            </summary>
            <returns>An enumerator over pairs where the key is the existing name for each setting in the connectionStrings section, and the value
            is a reference to the <see cref="T:System.Configuration.ConnectionStringSettings"/> object referred to by that name.</returns>
        </member>
        <member name="M:Microsoft.Configuration.ConfigurationBuilders.ConnectionStringsSectionHandler.TryGetOriginalCase(System.String)">
            <summary>
            Attempt to lookup the original key casing so it can be preserved during greedy updates which would otherwise lose
            the original casing in favor of the casing used in the config source.
            </summary>
            <param name="requestedKey">The key to find original casing for.</param>
            <returns>A string containing the key with original casing from the config section, or the key as passed in if no match
            can be found.</returns>
        </member>
        <member name="T:Microsoft.Configuration.ConfigurationBuilders.SectionHandlersSection">
            <summary>
            Provides programmatic access to the 'sectionHandlers' config section. This class can't be inherited.
            </summary>
        </member>
        <member name="P:Microsoft.Configuration.ConfigurationBuilders.SectionHandlersSection.Handlers">
            <summary>
            Gets the collection of <see cref="T:Microsoft.Configuration.ConfigurationBuilders.SectionHandler`1" />s defined for processing config sections with <see cref="T:Microsoft.Configuration.ConfigurationBuilders.KeyValueConfigBuilder"/>s./>
            </summary>
        </member>
        <member name="M:Microsoft.Configuration.ConfigurationBuilders.SectionHandlersSection.InitializeDefault">
            <summary>
            Used to initialize a default set of section handlers. (For the appSettings and connectionStrings sections.)
            </summary>
        </member>
        <member name="T:Microsoft.Configuration.ConfigurationBuilders.Utils">
            <summary>
            Utility methods commonly used by KeyValueConfigBuilders. 
            </summary>
        </member>
        <member name="M:Microsoft.Configuration.ConfigurationBuilders.Utils.MapPath(System.String)">
            <summary>
            Returns the physical file path that corresponds to the specified relative path. 
            </summary>
            <param name="path"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Configuration.ConfigurationBuilders.KeyValueConfigBuilder">
            <summary>
            Base class for a set of ConfigurationBuilders that follow a basic key/value pair substitution model. This base
            class handles substitution modes and most prefix concerns, so implementing classes only need to be a basic
            source of key/value pairs through the <see cref="M:Microsoft.Configuration.ConfigurationBuilders.KeyValueConfigBuilder.GetValue(System.String)"/> and <see cref="M:Microsoft.Configuration.ConfigurationBuilders.KeyValueConfigBuilder.GetAllValues(System.String)"/> methods.
            </summary>
        </member>
        <member name="P:Microsoft.Configuration.ConfigurationBuilders.KeyValueConfigBuilder.Mode">
            <summary>
            Gets or sets the substitution pattern to be used by the KeyValueConfigBuilder.
            </summary>
        </member>
        <member name="P:Microsoft.Configuration.ConfigurationBuilders.KeyValueConfigBuilder.KeyPrefix">
            <summary>
            Gets or sets a prefix string that must be matched by keys to be considered for value substitution.
            </summary>
        </member>
        <member name="P:Microsoft.Configuration.ConfigurationBuilders.KeyValueConfigBuilder.Optional">
            <summary>
            Specifies whether the config builder should cause errors if the backing source cannot be found.
            </summary>
        </member>
        <member name="P:Microsoft.Configuration.ConfigurationBuilders.KeyValueConfigBuilder.EscapeValues">
            <summary>
            Specifies whether the config builder should cause errors if the backing source cannot be found.
            </summary>
        </member>
        <member name="P:Microsoft.Configuration.ConfigurationBuilders.KeyValueConfigBuilder.TokenPattern">
            <summary>
            Gets or sets a regular expression used for matching tokens in raw xml during Greedy substitution.
            </summary>
        </member>
        <member name="M:Microsoft.Configuration.ConfigurationBuilders.KeyValueConfigBuilder.GetValue(System.String)">
            <summary>
            Looks up a single 'value' for the given 'key.'
            </summary>
            <param name="key">The 'key' to look up in the config source. (Prefix handling is not needed here.)</param>
            <returns>The value corresponding to the given 'key' or null if no value is found.</returns>
        </member>
        <member name="M:Microsoft.Configuration.ConfigurationBuilders.KeyValueConfigBuilder.GetAllValues(System.String)">
            <summary>
            Retrieves all known key/value pairs for the configuration source where the key begins with with <paramref name="prefix"/>.
            </summary>
            <param name="prefix">A prefix string to filter the list of potential keys retrieved from the source.</param>
            <returns>A collection of key/value pairs.</returns>
        </member>
        <member name="M:Microsoft.Configuration.ConfigurationBuilders.KeyValueConfigBuilder.MapKey(System.String)">
            <summary>
            Transform the given key to an intermediate format that will be used to look up values in backing store.
            </summary>
            <param name="key">The string to be mapped.</param>
            <returns>The key string to be used while looking up config values..</returns>
        </member>
        <member name="M:Microsoft.Configuration.ConfigurationBuilders.KeyValueConfigBuilder.ValidateKey(System.String)">
            <summary>
            Makes a determination about whether the input key is valid for this builder and backing store.
            </summary>
            <param name="key">The string to be validated. May be partial.</param>
            <returns>True if the string is valid. False if the string is not a valid key.</returns>
        </member>
        <member name="M:Microsoft.Configuration.ConfigurationBuilders.KeyValueConfigBuilder.UpdateKey(System.String)">
            <summary>
            Transforms the raw key to a new string just before updating items in Strict and Greedy modes.
            </summary>
            <param name="rawKey">The key as read from the incoming config section.</param>
            <returns>The key string that will be left in the processed config section.</returns>
        </member>
        <member name="M:Microsoft.Configuration.ConfigurationBuilders.KeyValueConfigBuilder.Initialize(System.String,System.Collections.Specialized.NameValueCollection)">
            <summary>
            Initializes the configuration builder.
            </summary>
            <param name="name">The friendly name of the provider.</param>
            <param name="config">A collection of the name/value pairs representing builder-specific attributes specified in the configuration for this provider.</param>
        </member>
        <member name="M:Microsoft.Configuration.ConfigurationBuilders.KeyValueConfigBuilder.LazyInitialize(System.String,System.Collections.Specialized.NameValueCollection)">
            <summary>
            Initializes the configuration builder lazily.
            </summary>
            <param name="name">The friendly name of the provider.</param>
            <param name="config">A collection of the name/value pairs representing builder-specific attributes specified in the configuration for this provider.</param>
        </member>
        <member name="M:Microsoft.Configuration.ConfigurationBuilders.KeyValueConfigBuilder.UpdateConfigSettingWithAppSettings(System.String)">
            <summary>
            Perform token substitution on a config parameter passed through builder initialization using token values from appSettings.
            </summary>
            <param name="configName">The name of the parameter to be retrieved.</param>
            <returns>The updated parameter value if it exists. Null otherwise.</returns>
        </member>
        <member name="M:Microsoft.Configuration.ConfigurationBuilders.KeyValueConfigBuilder.EnsureGreedyInitialized">
            <summary>
            Use <see cref="M:Microsoft.Configuration.ConfigurationBuilders.KeyValueConfigBuilder.GetAllValues(System.String)" /> to populate a cache of possible key/value pairs and avoid
            querying the config source multiple times. Always called in 'Greedy' mode. May be called by
            individual builders in some other cases.
            </summary>
        </member>
        <member name="T:Microsoft.Configuration.ConfigurationBuilders.KeyValueMode">
            <summary>
            Possible modes (or behaviors) for key/value substitution.
            </summary>
        </member>
        <member name="F:Microsoft.Configuration.ConfigurationBuilders.KeyValueMode.Strict">
            <summary>
            Replaces 'value' if 'key' is matched. Only operates on known key/value config sections.
            </summary>
        </member>
        <member name="F:Microsoft.Configuration.ConfigurationBuilders.KeyValueMode.Greedy">
            <summary>
            Inserts all 'values' regardless of the previous existence of the 'key.' Only operates on known key/value config sections.
            </summary>
        </member>
        <member name="F:Microsoft.Configuration.ConfigurationBuilders.KeyValueMode.Expand">
            <summary>
            Replace 'key'-specifying tokens in raw xml with 'values.' Operates on any config section.
            </summary>
        </member>
    </members>
</doc>
