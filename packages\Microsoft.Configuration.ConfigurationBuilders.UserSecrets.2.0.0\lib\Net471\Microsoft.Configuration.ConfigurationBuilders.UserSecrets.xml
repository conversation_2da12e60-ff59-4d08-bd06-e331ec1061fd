<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Configuration.ConfigurationBuilders.UserSecrets</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Configuration.ConfigurationBuilders.UserSecretsConfigBuilder">
            <summary>
            A ConfigurationProvider that retrieves values from a local secrets file.
            </summary>
        </member>
        <member name="P:Microsoft.Configuration.ConfigurationBuilders.UserSecretsConfigBuilder.UserSecretsId">
            <summary>
            Gets or sets an identifier string used to compose an out-of-solution path to a file that contains secrets.
            (Set to ${UserSecretsId} to reference the similarly named project property in Visual Studio environments.
            </summary>
        </member>
        <member name="P:Microsoft.Configuration.ConfigurationBuilders.UserSecretsConfigBuilder.UserSecretsFile">
            <summary>
            Gets or sets a path to the secrets file to be used.
            </summary>
        </member>
        <member name="M:Microsoft.Configuration.ConfigurationBuilders.UserSecretsConfigBuilder.LazyInitialize(System.String,System.Collections.Specialized.NameValueCollection)">
            <summary>
            Initializes the configuration builder lazily.
            </summary>
            <param name="name">The friendly name of the provider.</param>
            <param name="config">A collection of the name/value pairs representing builder-specific attributes specified in the configuration for this provider.</param>
        </member>
        <member name="M:Microsoft.Configuration.ConfigurationBuilders.UserSecretsConfigBuilder.GetAllValues(System.String)">
            <summary>
            Retrieves all known key/value pairs from the secrets file where the key begins with with <paramref name="prefix"/>.
            </summary>
            <param name="prefix">A prefix string to filter the list of potential keys retrieved from the source.</param>
            <returns>A collection of key/value pairs.</returns>
        </member>
        <member name="M:Microsoft.Configuration.ConfigurationBuilders.UserSecretsConfigBuilder.GetValue(System.String)">
            <summary>
            Looks up a single 'value' for the given 'key.'
            </summary>
            <param name="key">The 'key' to look up in the secrets file. (Prefix handling is not needed here.)</param>
            <returns>The value corresponding to the given 'key' or null if no value is found.</returns>
        </member>
    </members>
</doc>
