{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1033\deflangfe1033{\fonttbl{\f0\fswiss\fprq2\fcharset0 Calibri;}{\f1\fnil\fcharset0 Calibri;}}
{\colortbl ;\red247\green247\blue247;\red0\green0\blue255;\red36\green41\blue46;\red255\green255\blue255;\red0\green0\blue0;}
{\*\generator Riched20 10.0.17134}{\*\mmathPr\mdispDef1\mwrapIndent1440 }\viewkind4\uc1 
\pard\widctlpar\highlight1\expndtw3\f0\fs22 NOTICES AND INFORMATION\par
Do Not Translate or Localize\par
\par

\pard\nowidctlpar The Roslyn Analyzers software incorporates material from third parties. Microsoft makes certain open source code available at {{\field{\*\fldinst{HYPERLINK https://3rdpartysource.microsoft.com }}{\fldrslt{https://3rdpartysource.microsoft.com\ul0\cf0}}}}\f0\fs22 , or you may send a check or money order for US $5.00, including the product name, the open source component name, and version number, to:\par

\pard\widctlpar\par
Source Code Compliance Team\par
Microsoft Corporation\par
One Microsoft Way\par
Redmond, WA 98052\par
USA\par
\par
Notwithstanding any other terms, you may reverse engineer this software to the extent required to debug changes to any libraries licensed under the GNU Lesser General Public License.\par
\par

\pard\nowidctlpar\highlight0\expndtw0 %% \cf3\highlight4 .NET Compiler Platform \cf0\highlight0 NOTICES AND INFORMATION BEGIN HERE\par
=========================================\par
Copyright (c) .NET Foundation. All rights reserved.\par
\par

\pard\widctlpar\tx916\tx1832\tx2748\tx3664\tx4580\tx5496\tx6412\tx7328\tx8244\tx9160\tx10076\tx10992\tx11908\tx12824\tx13740\tx14656\cf5                                  Apache License\par
                           Version 2.0, January 2004\par
                        {\cf0{\field{\*\fldinst{HYPERLINK http://www.apache.org/licenses/ }}{\fldrslt{http://www.apache.org/licenses/\ul0\cf0}}}}\f0\fs22\par
\par
   TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION\par
\par
   1. Definitions.\par
\par
      "License" shall mean the terms and conditions for use, reproduction,\par
      and distribution as defined by Sections 1 through 9 of this document.\par
\par
      "Licensor" shall mean the copyright owner or entity authorized by\par
      the copyright owner that is granting the License.\par
\par
      "Legal Entity" shall mean the union of the acting entity and all\par
      other entities that control, are controlled by, or are under common\par
      control with that entity. For the purposes of this definition,\par
      "control" means (i) the power, direct or indirect, to cause the\par
      direction or management of such entity, whether by contract or\par
      otherwise, or (ii) ownership of fifty percent (50%) or more of the\par
      outstanding shares, or (iii) beneficial ownership of such entity.\par
\par
      "You" (or "Your") shall mean an individual or Legal Entity\par
      exercising permissions granted by this License.\par
\par
      "Source" form shall mean the preferred form for making modifications,\par
      including but not limited to software source code, documentation\par
      source, and configuration files.\par
\par
      "Object" form shall mean any form resulting from mechanical\par
      transformation or translation of a Source form, including but\par
      not limited to compiled object code, generated documentation,\par
      and conversions to other media types.\par
\par
      "Work" shall mean the work of authorship, whether in Source or\par
      Object form, made available under the License, as indicated by a\par
      copyright notice that is included in or attached to the work\par
      (an example is provided in the Appendix below).\par
\par
      "Derivative Works" shall mean any work, whether in Source or Object\par
      form, that is based on (or derived from) the Work and for which the\par
      editorial revisions, annotations, elaborations, or other modifications\par
      represent, as a whole, an original work of authorship. For the purposes\par
      of this License, Derivative Works shall not include works that remain\par
      separable from, or merely link (or bind by name) to the interfaces of,\par
      the Work and Derivative Works thereof.\par
\par
      "Contribution" shall mean any work of authorship, including\par
      the original version of the Work and any modifications or additions\par
      to that Work or Derivative Works thereof, that is intentionally\par
      submitted to Licensor for inclusion in the Work by the copyright owner\par
      or by an individual or Legal Entity authorized to submit on behalf of\par
      the copyright owner. For the purposes of this definition, "submitted"\par
      means any form of electronic, verbal, or written communication sent\par
      to the Licensor or its representatives, including but not limited to\par
      communication on electronic mailing lists, source code control systems,\par
      and issue tracking systems that are managed by, or on behalf of, the\par
      Licensor for the purpose of discussing and improving the Work, but\par
      excluding communication that is conspicuously marked or otherwise\par
      designated in writing by the copyright owner as "Not a Contribution."\par
\par
      "Contributor" shall mean Licensor and any individual or Legal Entity\par
      on behalf of whom a Contribution has been received by Licensor and\par
      subsequently incorporated within the Work.\par
\par
   2. Grant of Copyright License. Subject to the terms and conditions of\par
      this License, each Contributor hereby grants to You a perpetual,\par
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable\par
      copyright license to reproduce, prepare Derivative Works of,\par
      publicly display, publicly perform, sublicense, and distribute the\par
      Work and such Derivative Works in Source or Object form.\par
\par
   3. Grant of Patent License. Subject to the terms and conditions of\par
      this License, each Contributor hereby grants to You a perpetual,\par
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable\par
      (except as stated in this section) patent license to make, have made,\par
      use, offer to sell, sell, import, and otherwise transfer the Work,\par
      where such license applies only to those patent claims licensable\par
      by such Contributor that are necessarily infringed by their\par
      Contribution(s) alone or by combination of their Contribution(s)\par
      with the Work to which such Contribution(s) was submitted. If You\par
      institute patent litigation against any entity (including a\par
      cross-claim or counterclaim in a lawsuit) alleging that the Work\par
      or a Contribution incorporated within the Work constitutes direct\par
      or contributory patent infringement, then any patent licenses\par
      granted to You under this License for that Work shall terminate\par
      as of the date such litigation is filed.\par
\par
   4. Redistribution. You may reproduce and distribute copies of the\par
      Work or Derivative Works thereof in any medium, with or without\par
      modifications, and in Source or Object form, provided that You\par
      meet the following conditions:\par
\par
      (a) You must give any other recipients of the Work or\par
          Derivative Works a copy of this License; and\par
\par
      (b) You must cause any modified files to carry prominent notices\par
          stating that You changed the files; and\par
\par
      (c) You must retain, in the Source form of any Derivative Works\par
          that You distribute, all copyright, patent, trademark, and\par
          attribution notices from the Source form of the Work,\par
          excluding those notices that do not pertain to any part of\par
          the Derivative Works; and\par
\par
      (d) If the Work includes a "NOTICE" text file as part of its\par
          distribution, then any Derivative Works that You distribute must\par
          include a readable copy of the attribution notices contained\par
          within such NOTICE file, excluding those notices that do not\par
          pertain to any part of the Derivative Works, in at least one\par
          of the following places: within a NOTICE text file distributed\par
          as part of the Derivative Works; within the Source form or\par
          documentation, if provided along with the Derivative Works; or,\par
          within a display generated by the Derivative Works, if and\par
          wherever such third-party notices normally appear. The contents\par
          of the NOTICE file are for informational purposes only and\par
          do not modify the License. You may add Your own attribution\par
          notices within Derivative Works that You distribute, alongside\par
          or as an addendum to the NOTICE text from the Work, provided\par
          that such additional attribution notices cannot be construed\par
          as modifying the License.\par
\par
      You may add Your own copyright statement to Your modifications and\par
      may provide additional or different license terms and conditions\par
      for use, reproduction, or distribution of Your modifications, or\par
      for any such Derivative Works as a whole, provided Your use,\par
      reproduction, and distribution of the Work otherwise complies with\par
      the conditions stated in this License.\par
\par
   5. Submission of Contributions. Unless You explicitly state otherwise,\par
      any Contribution intentionally submitted for inclusion in the Work\par
      by You to the Licensor shall be under the terms and conditions of\par
      this License, without any additional terms or conditions.\par
      Notwithstanding the above, nothing herein shall supersede or modify\par
      the terms of any separate license agreement you may have executed\par
      with Licensor regarding such Contributions.\par
\par
   6. Trademarks. This License does not grant permission to use the trade\par
      names, trademarks, service marks, or product names of the Licensor,\par
      except as required for reasonable and customary use in describing the\par
      origin of the Work and reproducing the content of the NOTICE file.\par
\par
   7. Disclaimer of Warranty. Unless required by applicable law or\par
      agreed to in writing, Licensor provides the Work (and each\par
      Contributor provides its Contributions) on an "AS IS" BASIS,\par
      WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or\par
      implied, including, without limitation, any warranties or conditions\par
      of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A\par
      PARTICULAR PURPOSE. You are solely responsible for determining the\par
      appropriateness of using or redistributing the Work and assume any\par
      risks associated with Your exercise of permissions under this License.\par
\par
   8. Limitation of Liability. In no event and under no legal theory,\par
      whether in tort (including negligence), contract, or otherwise,\par
      unless required by applicable law (such as deliberate and grossly\par
      negligent acts) or agreed to in writing, shall any Contributor be\par
      liable to You for damages, including any direct, indirect, special,\par
      incidental, or consequential damages of any character arising as a\par
      result of this License or out of the use or inability to use the\par
      Work (including but not limited to damages for loss of goodwill,\par
      work stoppage, computer failure or malfunction, or any and all\par
      other commercial damages or losses), even if such Contributor\par
      has been advised of the possibility of such damages.\par
\par
   9. Accepting Warranty or Additional Liability. While redistributing\par
      the Work or Derivative Works thereof, You may choose to offer,\par
      and charge a fee for, acceptance of support, warranty, indemnity,\par
      or other liability obligations and/or rights consistent with this\par
      License. However, in accepting such obligations, You may act only\par
      on Your own behalf and on Your sole responsibility, not on behalf\par
      of any other Contributor, and only if You agree to indemnify,\par
      defend, and hold each Contributor harmless for any liability\par
      incurred by, or claims asserted against, such Contributor by reason\par
      of your accepting any such warranty or additional liability.\par
\par
   END OF TERMS AND CONDITIONS\par
\par
   APPENDIX: How to apply the Apache License to your work.\par
\par
      To apply the Apache License to your work, attach the following\par
      boilerplate notice, with the fields enclosed by brackets "[]"\par
      replaced with your own identifying information. (Don't include\par
      the brackets!)  The text should be enclosed in the appropriate\par
      comment syntax for the file format. We also recommend that a\par
      file or class name and description of purpose be included on the\par
      same "printed page" as the copyright notice for easier\par
      identification within third-party archives.\par
\par
   Copyright [yyyy] [name of copyright owner]\par
\par
   Licensed under the Apache License, Version 2.0 (the "License");\par
   you may not use this file except in compliance with the License.\par
   You may obtain a copy of the License at\par
\par
       {\cf0{\field{\*\fldinst{HYPERLINK http://www.apache.org/licenses/LICENSE-2.0 }}{\fldrslt{http://www.apache.org/licenses/LICENSE-2.0\ul0\cf0}}}}\f0\fs22\par
\par
   Unless required by applicable law or agreed to in writing, software\par
   distributed under the License is distributed on an "AS IS" BASIS,\par
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\par
   See the License for the specific language governing permissions and\par
   limitations under the License.\par

\pard\nowidctlpar\cf0 =========================================\par
END OF \cf3\highlight4 .NET Compiler Platform \cf0\highlight0 NOTICES AND INFORMATION\par
\par
%% Humanizer NOTICES AND INFORMATION BEGIN HERE\par
=========================================\par
The MIT License (MIT)\par
\par
Copyright (c) 2012-2014 Mehdi Khalili\par
\par
Permission is hereby granted, free of charge, to any person obtaining a copy\par
of this software and associated documentation files (the "Software"), to deal\par
in the Software without restriction, including without limitation the rights\par
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\par
copies of the Software, and to permit persons to whom the Software is\par
furnished to do so, subject to the following conditions:\par
\par
The above copyright notice and this permission notice shall be included in\par
all copies or substantial portions of the Software.\par
\par
THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\par
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\par
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\par
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\par
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\par
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\par
THE SOFTWARE.\par
\par
==============================================================================\par
\par
Inflector ({{\field{\*\fldinst{HYPERLINK https://github.com/srkirkland/Inflector }}{\fldrslt{https://github.com/srkirkland/Inflector\ul0\cf0}}}}\f0\fs22 )\par
The MIT License (MIT)\par
Copyright (c) 2013 Scott Kirkland\par
\par
==============================================================================\par
\par
ByteSize ({{\field{\*\fldinst{HYPERLINK https://github.com/omar/ByteSize }}{\fldrslt{https://github.com/omar/ByteSize\ul0\cf0}}}}\f0\fs22 )\par
The MIT License (MIT)\par
Copyright (c) 2013-2014 Omar Khudeira ({{\field{\*\fldinst{HYPERLINK http://omar.io }}{\fldrslt{http://omar.io\ul0\cf0}}}}\f0\fs22 )\par
\par
==============================================================================\par
=========================================\par
END OF Humanizer NOTICES AND INFORMATION\f1\lang9\par
}
 