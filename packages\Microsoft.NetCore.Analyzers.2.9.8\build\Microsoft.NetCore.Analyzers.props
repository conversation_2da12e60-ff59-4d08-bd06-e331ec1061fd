<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  
  <!-- 
    This item group adds any .editorconfig file present at the project root directory
    as an additional file.
  -->  
  <ItemGroup Condition="'$(SkipDefaultEditorConfigAsAdditionalFile)' != 'true' And Exists('$(MSBuildProjectDirectory)\.editorconfig')" >
    <AdditionalFiles Include="$(MSBuildProjectDirectory)\.editorconfig" />
  </ItemGroup>

  <!-- 
    This property group prevents the rule ids implemented in this package to be bumped to errors when
    the 'CodeAnalysisTreatWarningsAsErrors' = 'false'.
  -->
  <PropertyGroup Condition="'$(CodeAnalysisTreatWarningsAsErrors)' == 'false'">
    <WarningsNotAsErrors>$(WarningsNotAsErrors);CA1303;CA1304;CA1305;CA1307;CA1308;CA1309;CA1401;CA1810;CA1813;CA1816;CA1820;CA1824;CA1825;CA1826;CA1827;CA1828;CA1829;CA2000;CA2002;CA2008;CA2009;CA2010;CA2100;CA2101;CA2201;CA2207;CA2208;CA2213;CA2216;CA2229;CA2235;CA2237;CA2241;CA2242;CA2243;CA2300;CA2301;CA2302;CA2305;CA2310;CA2311;CA2312;CA2315;CA2321;CA2322;CA2326;CA2327;CA2328;CA2329;CA2330;CA3001;CA3002;CA3003;CA3004;CA3005;CA3006;CA3007;CA3008;CA3009;CA3010;CA3011;CA3012;CA3061;CA5350;CA5351;CA5358;CA5359;CA5360;CA5361;CA5362;CA5363;CA5364;CA5365;CA5366;CA5367;CA5368;CA5369;CA5370;CA5371;CA5372;CA5373;CA5374;CA5375;CA5376;CA5377;CA5378;CA5379;CA5380;CA5381;CA5382;CA5383;CA5384;CA5385;CA5386;CA5387;CA5388;CA5389;CA5390;CA5391;CA5392;CA5393;CA5394;CA5395;CA5396;CA5397;CA5398;CA5399;CA5400;CA5401;CA5402;CA5403</WarningsNotAsErrors>
  </PropertyGroup>

  <!-- 
    This property group contains the rules that have been implemented in this package and therefore should be disabled for the binary FxCop.
    The format is -[Category]#[ID], e.g., -Microsoft.Design#CA1001;
  -->
  <PropertyGroup>
    <CodeAnalysisRuleSetOverrides>
      $(CodeAnalysisRuleSetOverrides);
      -Microsoft.Globalization#CA1303;
      -Microsoft.Globalization#CA1304;
      -Microsoft.Globalization#CA1305;
      -Microsoft.Globalization#CA1307;
      -Microsoft.Globalization#CA1308;
      -Microsoft.Globalization#CA1309;
      -Microsoft.Globalization#CA2101;

      -Microsoft.Interoperability#CA1401;

      -Microsoft.Performance#CA1810;
      -Microsoft.Performance#CA1813;
      -Microsoft.Performance#CA1820;
      -Microsoft.Performance#CA1824;

      -Microsoft.Reliability#CA2000;
      -Microsoft.Reliability#CA2002;

      -Microsoft.Security#CA2100;

      -Microsoft.Usage#CA1816;
      -Microsoft.Usage#CA2201;
      -Microsoft.Usage#CA2207;
      -Microsoft.Usage#CA2208;
      -Microsoft.Usage#CA2213;
      -Microsoft.Usage#CA2216;
      -Microsoft.Usage#CA2229;
      -Microsoft.Usage#CA2235;
      -Microsoft.Usage#CA2237;
      -Microsoft.Usage#CA2241;
      -Microsoft.Usage#CA2242;
      -Microsoft.Usage#CA2243;

    </CodeAnalysisRuleSetOverrides>
  </PropertyGroup>

  <PropertyGroup>
    <Features>$(Features);flow-analysis</Features> 
  </PropertyGroup>
</Project>