﻿{
  "$schema": "http://json.schemastore.org/sarif-1.0.0",
  "version": "1.0.0",
  "runs": [
    {
      "tool": {
        "name": "Microsoft.NetCore.Analyzers",
        "version": "2.9.8",
        "language": "en-US"
      },
      "rules": {
        "CA1303": {
          "id": "CA1303",
          "shortDescription": "Do not pass literals as localized parameters",
          "fullDescription": "A method passes a string literal as a parameter to a constructor or method in the .NET Framework class library and that string should be localizable. To fix a violation of this rule, replace the string literal with a string retrieved through an instance of the ResourceManager class.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1303-do-not-pass-literals-as-localized-parameters",
          "properties": {
            "category": "Globalization",
            "isEnabledByDefault": true,
            "typeName": "DoNotPassLiteralsAsLocalizedParameters",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Dataflow",
              "Telemetry"
            ]
          }
        },
        "CA1304": {
          "id": "CA1304",
          "shortDescription": "Specify CultureInfo",
          "fullDescription": "A method or constructor calls a member that has an overload that accepts a System.Globalization.CultureInfo parameter, and the method or constructor does not call the overload that takes the CultureInfo parameter. When a CultureInfo or System.IFormatProvider object is not supplied, the default value that is supplied by the overloaded member might not have the effect that you want in all locales. If the result will be displayed to the user, specify 'CultureInfo.CurrentCulture' as the 'CultureInfo' parameter. Otherwise, if the result will be stored and accessed by software, such as when it is persisted to disk or to a database, specify 'CultureInfo.InvariantCulture'.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1304-specify-cultureinfo",
          "properties": {
            "category": "Globalization",
            "isEnabledByDefault": true,
            "typeName": "SpecifyCultureInfoAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1305": {
          "id": "CA1305",
          "shortDescription": "Specify IFormatProvider",
          "fullDescription": "A method or constructor calls one or more members that have overloads that accept a System.IFormatProvider parameter, and the method or constructor does not call the overload that takes the IFormatProvider parameter. When a System.Globalization.CultureInfo or IFormatProvider object is not supplied, the default value that is supplied by the overloaded member might not have the effect that you want in all locales. If the result will be based on the input from/output displayed to the user, specify 'CultureInfo.CurrentCulture' as the 'IFormatProvider'. Otherwise, if the result will be stored and accessed by software, such as when it is loaded from disk/database and when it is persisted to disk/database, specify 'CultureInfo.InvariantCulture'",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1305-specify-iformatprovider",
          "properties": {
            "category": "Globalization",
            "isEnabledByDefault": true,
            "typeName": "SpecifyIFormatProviderAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1307": {
          "id": "CA1307",
          "shortDescription": "Specify StringComparison",
          "fullDescription": "A string comparison operation uses a method overload that does not set a StringComparison parameter. If the result will be displayed to the user, such as when sorting a list of items for display in a list box, specify 'StringComparison.CurrentCulture' or 'StringComparison.CurrentCultureIgnoreCase' as the 'StringComparison' parameter. If comparing case-insensitive identifiers, such as file paths, environment variables, or registry keys and values, specify 'StringComparison.OrdinalIgnoreCase'. Otherwise, if comparing case-sensitive identifiers, specify 'StringComparison.Ordinal'.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1307-specify-stringcomparison",
          "properties": {
            "category": "Globalization",
            "isEnabledByDefault": true,
            "typeName": "SpecifyStringComparisonAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1308": {
          "id": "CA1308",
          "shortDescription": "Normalize strings to uppercase",
          "fullDescription": "Strings should be normalized to uppercase. A small group of characters cannot make a round trip when they are converted to lowercase. To make a round trip means to convert the characters from one locale to another locale that represents character data differently, and then to accurately retrieve the original characters from the converted characters.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1308-normalize-strings-to-uppercase",
          "properties": {
            "category": "Globalization",
            "isEnabledByDefault": true,
            "typeName": "NormalizeStringsToUppercaseAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1401": {
          "id": "CA1401",
          "shortDescription": "P/Invokes should not be visible",
          "fullDescription": "A public or protected method in a public type has the System.Runtime.InteropServices.DllImportAttribute attribute (also implemented by the Declare keyword in Visual Basic). Such methods should not be exposed.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1401-p-invokes-should-not-be-visible",
          "properties": {
            "category": "Interoperability",
            "isEnabledByDefault": true,
            "typeName": "PInvokeDiagnosticAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1813": {
          "id": "CA1813",
          "shortDescription": "Avoid unsealed attributes",
          "fullDescription": "The .NET Framework class library provides methods for retrieving custom attributes. By default, these methods search the attribute inheritance hierarchy. Sealing the attribute eliminates the search through the inheritance hierarchy and can improve performance.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1813-avoid-unsealed-attributes",
          "properties": {
            "category": "Performance",
            "isEnabledByDefault": false,
            "typeName": "AvoidUnsealedAttributesAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1816": {
          "id": "CA1816",
          "shortDescription": "Dispose methods should call SuppressFinalize",
          "fullDescription": "A method that is an implementation of Dispose does not call GC.SuppressFinalize; or a method that is not an implementation of Dispose calls GC.SuppressFinalize; or a method calls GC.SuppressFinalize and passes something other than this (Me in Visual?Basic).",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1816-call-gc-suppressfinalize-correctly",
          "properties": {
            "category": "Usage",
            "isEnabledByDefault": true,
            "typeName": "CallGCSuppressFinalizeCorrectlyAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1820": {
          "id": "CA1820",
          "shortDescription": "Test for empty strings using string length",
          "fullDescription": "Comparing strings by using the String.Length property or the String.IsNullOrEmpty method is significantly faster than using Equals.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1820-test-for-empty-strings-using-string-length",
          "properties": {
            "category": "Performance",
            "isEnabledByDefault": true,
            "typeName": "TestForEmptyStringsUsingStringLengthAnalyzer",
            "languages": [
              "C#"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1826": {
          "id": "CA1826",
          "shortDescription": "Do not use Enumerable methods on indexable collections. Instead use the collection directly",
          "fullDescription": "This collection is directly indexable. Going through LINQ here causes unnecessary allocations and CPU work.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Performance",
            "isEnabledByDefault": true,
            "typeName": "DoNotUseEnumerableMethodsOnIndexableCollectionsInsteadUseTheCollectionDirectlyAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA1827": {
          "id": "CA1827",
          "shortDescription": "Do not use Count() or LongCount() when Any() can be used",
          "fullDescription": "For non-empty collections, Count() and LongCount() enumerate the entire sequence, while Any() stops at the first item or the first item that satisfies a condition.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1827",
          "properties": {
            "category": "Performance",
            "isEnabledByDefault": true,
            "typeName": "DoNotUseCountWhenAnyCanBeUsedAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ]
          }
        },
        "CA1828": {
          "id": "CA1828",
          "shortDescription": "Do not use CountAsync() or LongCountAsync() when AnyAsync() can be used",
          "fullDescription": "For non-empty collections, CountAsync() and LongCountAsync() enumerate the entire sequence, while AnyAsync() stops at the first item or the first item that satisfies a condition.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1828",
          "properties": {
            "category": "Performance",
            "isEnabledByDefault": true,
            "typeName": "DoNotUseCountWhenAnyCanBeUsedAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ]
          }
        },
        "CA2000": {
          "id": "CA2000",
          "shortDescription": "Dispose objects before losing scope",
          "fullDescription": "If a disposable object is not explicitly disposed before all references to it are out of scope, the object will be disposed at some indeterminate time when the garbage collector runs the finalizer of the object. Because an exceptional event might occur that will prevent the finalizer of the object from running, the object should be explicitly disposed instead.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2000-dispose-objects-before-losing-scope",
          "properties": {
            "category": "Reliability",
            "isEnabledByDefault": true,
            "typeName": "DisposeObjectsBeforeLosingScope",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Dataflow",
              "Telemetry"
            ]
          }
        },
        "CA2002": {
          "id": "CA2002",
          "shortDescription": "Do not lock on objects with weak identity",
          "fullDescription": "An object is said to have a weak identity when it can be directly accessed across application domain boundaries. A thread that tries to acquire a lock on an object that has a weak identity can be blocked by a second thread in a different application domain that has a lock on the same object.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2002-do-not-lock-on-objects-with-weak-identity",
          "properties": {
            "category": "Reliability",
            "isEnabledByDefault": true,
            "typeName": "DoNotLockOnObjectsWithWeakIdentityAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA2008": {
          "id": "CA2008",
          "shortDescription": "Do not create tasks without passing a TaskScheduler",
          "fullDescription": "Do not create tasks unless you are using one of the overloads that takes a TaskScheduler. The default is to schedule on TaskScheduler.Current, which would lead to deadlocks. Either use TaskScheduler.Default to schedule on the thread pool, or explicitly pass TaskScheduler.Current to make your intentions clear.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Reliability",
            "isEnabledByDefault": true,
            "typeName": "DoNotCreateTasksWithoutPassingATaskSchedulerAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA2009": {
          "id": "CA2009",
          "shortDescription": "Do not call ToImmutableCollection on an ImmutableCollection value",
          "fullDescription": "Do not call {0} on an {1} value",
          "defaultLevel": "warning",
          "properties": {
            "category": "Reliability",
            "isEnabledByDefault": true,
            "typeName": "DoNotCallToImmutableCollectionOnAnImmutableCollectionValueAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA2100": {
          "id": "CA2100",
          "shortDescription": "Review SQL queries for security vulnerabilities",
          "fullDescription": "SQL queries that directly use user input can be vulnerable to SQL injection attacks. Review this SQL query for potential vulnerabilities, and consider using a parameterized SQL query.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2100-review-sql-queries-for-security-vulnerabilities",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "ReviewSqlQueriesForSecurityVulnerabilities",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Dataflow",
              "Telemetry"
            ]
          }
        },
        "CA2101": {
          "id": "CA2101",
          "shortDescription": "Specify marshaling for P/Invoke string arguments",
          "fullDescription": "A platform invoke member allows partially trusted callers, has a string parameter, and does not explicitly marshal the string. This can cause a potential security vulnerability.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2101-specify-marshaling-for-p-invoke-string-arguments",
          "properties": {
            "category": "Globalization",
            "isEnabledByDefault": true,
            "typeName": "PInvokeDiagnosticAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA2208": {
          "id": "CA2208",
          "shortDescription": "Instantiate argument exceptions correctly",
          "fullDescription": "A call is made to the default (parameterless) constructor of an exception type that is or derives from ArgumentException, or an incorrect string argument is passed to a parameterized constructor of an exception type that is or derives from ArgumentException.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2208-instantiate-argument-exceptions-correctly",
          "properties": {
            "category": "Usage",
            "isEnabledByDefault": true,
            "typeName": "InstantiateArgumentExceptionsCorrectlyAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA2213": {
          "id": "CA2213",
          "shortDescription": "Disposable fields should be disposed",
          "fullDescription": "A type that implements System.IDisposable declares fields that are of types that also implement IDisposable. The Dispose method of the field is not called by the Dispose method of the declaring type. To fix a violation of this rule, call Dispose on fields that are of types that implement IDisposable if you are responsible for allocating and releasing the unmanaged resources held by the field.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2213-disposable-fields-should-be-disposed",
          "properties": {
            "category": "Usage",
            "isEnabledByDefault": true,
            "typeName": "DisposableFieldsShouldBeDisposed",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Dataflow",
              "Telemetry"
            ]
          }
        },
        "CA2216": {
          "id": "CA2216",
          "shortDescription": "Disposable types should declare finalizer",
          "fullDescription": "A type that implements System.IDisposable and has fields that suggest the use of unmanaged resources does not implement a finalizer, as described by Object.Finalize.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2216-disposable-types-should-declare-finalizer",
          "properties": {
            "category": "Usage",
            "isEnabledByDefault": true,
            "typeName": "DisposableTypesShouldDeclareFinalizerAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA2229": {
          "id": "CA2229",
          "shortDescription": "Implement serialization constructors",
          "fullDescription": "To fix a violation of this rule, implement the serialization constructor. For a sealed class, make the constructor private; otherwise, make it protected.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2229-implement-serialization-constructors",
          "properties": {
            "category": "Usage",
            "isEnabledByDefault": true,
            "typeName": "SerializationRulesDiagnosticAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA2235": {
          "id": "CA2235",
          "shortDescription": "Mark all non-serializable fields",
          "fullDescription": "An instance field of a type that is not serializable is declared in a type that is serializable.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2235-mark-all-non-serializable-fields",
          "properties": {
            "category": "Usage",
            "isEnabledByDefault": true,
            "typeName": "SerializationRulesDiagnosticAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA2237": {
          "id": "CA2237",
          "shortDescription": "Mark ISerializable types with serializable",
          "fullDescription": "To be recognized by the common language runtime as serializable, types must be marked by using the SerializableAttribute attribute even when the type uses a custom serialization routine through implementation of the ISerializable interface.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2237-mark-iserializable-types-with-serializableattribute",
          "properties": {
            "category": "Usage",
            "isEnabledByDefault": true,
            "typeName": "SerializationRulesDiagnosticAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA2241": {
          "id": "CA2241",
          "shortDescription": "Provide correct arguments to formatting methods",
          "fullDescription": "The format argument that is passed to System.String.Format does not contain a format item that corresponds to each object argument, or vice versa.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2241-provide-correct-arguments-to-formatting-methods",
          "properties": {
            "category": "Usage",
            "isEnabledByDefault": true,
            "typeName": "ProvideCorrectArgumentsToFormattingMethodsAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA2242": {
          "id": "CA2242",
          "shortDescription": "Test for NaN correctly",
          "fullDescription": "This expression tests a value against Single.Nan or Double.Nan. Use Single.IsNan(Single) or Double.IsNan(Double) to test the value.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2242-test-for-nan-correctly",
          "properties": {
            "category": "Usage",
            "isEnabledByDefault": true,
            "typeName": "TestForNaNCorrectlyAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA2243": {
          "id": "CA2243",
          "shortDescription": "Attribute string literals should parse correctly",
          "fullDescription": "The string literal parameter of an attribute does not parse correctly for a URL, a GUID, or a version.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2243-attribute-string-literals-should-parse-correctly",
          "properties": {
            "category": "Usage",
            "isEnabledByDefault": true,
            "typeName": "AttributeStringLiteralsShouldParseCorrectlyAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA2300": {
          "id": "CA2300",
          "shortDescription": "Do not use insecure deserializer BinaryFormatter",
          "fullDescription": "The method '{0}' is insecure when deserializing untrusted data.  If you need to instead detect BinaryFormatter deserialization without a SerializationBinder set, then disable rule CA2300, and enable rules CA2301 and CA2302.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2300-do-not-use-insecure-deserializer-binaryformatter",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "DoNotUseInsecureDeserializerBinaryFormatterMethods",
            "languages": [
              "C#",
              "Visual Basic"
            ]
          }
        },
        "CA2301": {
          "id": "CA2301",
          "shortDescription": "Do not call BinaryFormatter.Deserialize without first setting BinaryFormatter.Binder",
          "fullDescription": "The method '{0}' is insecure when deserializing untrusted data without a SerializationBinder to restrict the type of objects in the deserialized object graph.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2301-do-not-call-binaryformatter-deserialize-without-first-setting-binaryformatter-binder",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "DoNotUseInsecureDeserializerBinaryFormatterWithoutBinder",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Dataflow",
              "Telemetry"
            ]
          }
        },
        "CA2302": {
          "id": "CA2302",
          "shortDescription": "Ensure BinaryFormatter.Binder is set before calling BinaryFormatter.Deserialize",
          "fullDescription": "The method '{0}' is insecure when deserializing untrusted data without a SerializationBinder to restrict the type of objects in the deserialized object graph.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2302-ensure-binaryformatter-binder-is-set-before-calling-binaryformatter-deserialize",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "DoNotUseInsecureDeserializerBinaryFormatterWithoutBinder",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Dataflow",
              "Telemetry"
            ]
          }
        },
        "CA2305": {
          "id": "CA2305",
          "shortDescription": "Do not use insecure deserializer LosFormatter",
          "fullDescription": "The method '{0}' is insecure when deserializing untrusted data.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2305-do-not-use-insecure-deserializer-losformatter",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "DoNotUseInsecureDeserializerLosFormatter",
            "languages": [
              "C#",
              "Visual Basic"
            ]
          }
        },
        "CA2310": {
          "id": "CA2310",
          "shortDescription": "Do not use insecure deserializer NetDataContractSerializer",
          "fullDescription": "The method '{0}' is insecure when deserializing untrusted data.  If you need to instead detect NetDataContractSerializer deserialization without a SerializationBinder set, then disable rule CA2310, and enable rules CA2311 and CA2312.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2310-do-not-use-insecure-deserializer-netdatacontractserializer",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "DoNotUseInsecureDeserializerNetDataContractSerializerMethods",
            "languages": [
              "C#",
              "Visual Basic"
            ]
          }
        },
        "CA2311": {
          "id": "CA2311",
          "shortDescription": "Do not deserialize without first setting NetDataContractSerializer.Binder",
          "fullDescription": "The method '{0}' is insecure when deserializing untrusted data without a SerializationBinder to restrict the type of objects in the deserialized object graph.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2311-do-not-deserialize-without-first-setting-netdatacontractserializer-binder",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "DoNotUseInsecureDeserializerNetDataContractSerializerWithoutBinder",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Dataflow",
              "Telemetry"
            ]
          }
        },
        "CA2312": {
          "id": "CA2312",
          "shortDescription": "Ensure NetDataContractSerializer.Binder is set before deserializing",
          "fullDescription": "The method '{0}' is insecure when deserializing untrusted data without a SerializationBinder to restrict the type of objects in the deserialized object graph.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2312-ensure-netdatacontractserializer-binder-is-set-before-deserializing",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "DoNotUseInsecureDeserializerNetDataContractSerializerWithoutBinder",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Dataflow",
              "Telemetry"
            ]
          }
        },
        "CA2315": {
          "id": "CA2315",
          "shortDescription": "Do not use insecure deserializer ObjectStateFormatter",
          "fullDescription": "The method '{0}' is insecure when deserializing untrusted data.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2315-do-not-use-insecure-deserializer-objectstateformatter",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "DoNotUseInsecureDeserializerObjectStateFormatter",
            "languages": [
              "C#",
              "Visual Basic"
            ]
          }
        },
        "CA2321": {
          "id": "CA2321",
          "shortDescription": "Do not deserialize with JavaScriptSerializer using a SimpleTypeResolver",
          "fullDescription": "The method '{0}' is insecure when deserializing untrusted data with a JavaScriptSerializer initialized with a SimpleTypeResolver. Initialize JavaScriptSerializer without a JavaScriptTypeResolver specified, or initialize with a JavaScriptTypeResolver that limits the types of objects in the deserialized object graph.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2321",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "DoNotUseInsecureDeserializerJavaScriptSerializerWithSimpleTypeResolver",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Dataflow",
              "Telemetry"
            ]
          }
        },
        "CA2322": {
          "id": "CA2322",
          "shortDescription": "Ensure JavaScriptSerializer is not initialized with SimpleTypeResolver before deserializing",
          "fullDescription": "The method '{0}' is insecure when deserializing untrusted data with a JavaScriptSerializer initialized with a SimpleTypeResolver. Ensure that the JavaScriptSerializer is initialized without a JavaScriptTypeResolver specified, or initialized with a JavaScriptTypeResolver that limits the types of objects in the deserialized object graph.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2322",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "DoNotUseInsecureDeserializerJavaScriptSerializerWithSimpleTypeResolver",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Dataflow",
              "Telemetry"
            ]
          }
        },
        "CA2326": {
          "id": "CA2326",
          "shortDescription": "Do not use TypeNameHandling values other than None",
          "fullDescription": "Deserializing JSON when using a TypeNameHandling value other than None can be insecure.  If you need to instead detect Json.NET deserialization when a SerializationBinder isn't specified, then disable rule CA2326, and enable rules CA2327, CA2328, CA2329, and CA2330.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2326",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "JsonNetTypeNameHandling",
            "languages": [
              "C#",
              "Visual Basic"
            ]
          }
        },
        "CA2327": {
          "id": "CA2327",
          "shortDescription": "Do not use insecure JsonSerializerSettings",
          "fullDescription": "When deserializing untrusted input, allowing arbitrary types to be deserialized is insecure.  When using JsonSerializerSettings, use TypeNameHandling.None, or for values other than None, restrict deserialized types with a SerializationBinder.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2327",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "DoNotUseInsecureSettingsForJsonNet",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Dataflow",
              "Telemetry"
            ]
          }
        },
        "CA2328": {
          "id": "CA2328",
          "shortDescription": "Ensure that JsonSerializerSettings are secure",
          "fullDescription": "When deserializing untrusted input, allowing arbitrary types to be deserialized is insecure.  When using JsonSerializerSettings, ensure TypeNameHandling.None is specified, or for values other than None, ensure a SerializationBinder is specified to restrict deserialized types.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2328",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "DoNotUseInsecureSettingsForJsonNet",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Dataflow",
              "Telemetry"
            ]
          }
        },
        "CA2329": {
          "id": "CA2329",
          "shortDescription": "Do not deserialize with JsonSerializer using an insecure configuration",
          "fullDescription": "When deserializing untrusted input, allowing arbitrary types to be deserialized is insecure. When using deserializing JsonSerializer, use TypeNameHandling.None, or for values other than None, restrict deserialized types with a SerializationBinder.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2329",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "DoNotUseInsecureDeserializerJsonNetWithoutBinder",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Dataflow",
              "Telemetry"
            ]
          }
        },
        "CA2330": {
          "id": "CA2330",
          "shortDescription": "Ensure that JsonSerializer has a secure configuration when deserializing",
          "fullDescription": "When deserializing untrusted input, allowing arbitrary types to be deserialized is insecure. When using deserializing JsonSerializer, use TypeNameHandling.None, or for values other than None, restrict deserialized types with a SerializationBinder.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2330",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "DoNotUseInsecureDeserializerJsonNetWithoutBinder",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Dataflow",
              "Telemetry"
            ]
          }
        },
        "CA3001": {
          "id": "CA3001",
          "shortDescription": "Review code for SQL injection vulnerabilities",
          "fullDescription": "Potential SQL injection vulnerability was found where '{0}' in method '{1}' may be tainted by user-controlled data from '{2}' in method '{3}'.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca3001-review-code-for-sql-injection-vulnerabilities",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "ReviewCodeForSqlInjectionVulnerabilities",
            "languages": [
              "C#",
              "Visual Basic"
            ]
          }
        },
        "CA3002": {
          "id": "CA3002",
          "shortDescription": "Review code for XSS vulnerabilities",
          "fullDescription": "Potential cross-site scripting (XSS) vulnerability was found where '{0}' in method '{1}' may be tainted by user-controlled data from '{2}' in method '{3}'.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca3002-review-code-for-xss-vulnerabilities",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "ReviewCodeForXssVulnerabilities",
            "languages": [
              "C#",
              "Visual Basic"
            ]
          }
        },
        "CA3003": {
          "id": "CA3003",
          "shortDescription": "Review code for file path injection vulnerabilities",
          "fullDescription": "Potential file path injection vulnerability was found where '{0}' in method '{1}' may be tainted by user-controlled data from '{2}' in method '{3}'.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca3003-review-code-for-file-path-injection-vulnerabilities",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "ReviewCodeForFilePathInjectionVulnerabilities",
            "languages": [
              "C#",
              "Visual Basic"
            ]
          }
        },
        "CA3004": {
          "id": "CA3004",
          "shortDescription": "Review code for information disclosure vulnerabilities",
          "fullDescription": "Potential information disclosure vulnerability was found where '{0}' in method '{1}' may contain unintended information from '{2}' in method '{3}'.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca3004-review-code-for-information-disclosure-vulnerabilities",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "ReviewCodeForInformationDisclosureVulnerabilities",
            "languages": [
              "C#",
              "Visual Basic"
            ]
          }
        },
        "CA3005": {
          "id": "CA3005",
          "shortDescription": "Review code for LDAP injection vulnerabilities",
          "fullDescription": "Potential LDAP injection vulnerability was found where '{0}' in method '{1}' may be tainted by user-controlled data from '{2}' in method '{3}'.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca3005-review-code-for-ldap-injection-vulnerabilities",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "ReviewCodeForLdapInjectionVulnerabilities",
            "languages": [
              "C#",
              "Visual Basic"
            ]
          }
        },
        "CA3006": {
          "id": "CA3006",
          "shortDescription": "Review code for process command injection vulnerabilities",
          "fullDescription": "Potential process command injection vulnerability was found where '{0}' in method '{1}' may be tainted by user-controlled data from '{2}' in method '{3}'.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca3006-review-code-for-process-command-injection-vulnerabilities",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "ReviewCodeForCommandExecutionVulnerabilities",
            "languages": [
              "C#",
              "Visual Basic"
            ]
          }
        },
        "CA3007": {
          "id": "CA3007",
          "shortDescription": "Review code for open redirect vulnerabilities",
          "fullDescription": "Potential open redirect vulnerability was found where '{0}' in method '{1}' may be tainted by user-controlled data from '{2}' in method '{3}'.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca3007-review-code-for-open-redirect-vulnerabilities",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "ReviewCodeForOpenRedirectVulnerabilities",
            "languages": [
              "C#",
              "Visual Basic"
            ]
          }
        },
        "CA3008": {
          "id": "CA3008",
          "shortDescription": "Review code for XPath injection vulnerabilities",
          "fullDescription": "Potential XPath injection vulnerability was found where '{0}' in method '{1}' may be tainted by user-controlled data from '{2}' in method '{3}'.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca3008-review-code-for-xpath-injection-vulnerabilities",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "ReviewCodeForXPathInjectionVulnerabilities",
            "languages": [
              "C#",
              "Visual Basic"
            ]
          }
        },
        "CA3009": {
          "id": "CA3009",
          "shortDescription": "Review code for XML injection vulnerabilities",
          "fullDescription": "Potential XML injection vulnerability was found where '{0}' in method '{1}' may be tainted by user-controlled data from '{2}' in method '{3}'.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca3009-review-code-for-xml-injection-vulnerabilities",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "ReviewCodeForXmlInjectionVulnerabilities",
            "languages": [
              "C#",
              "Visual Basic"
            ]
          }
        },
        "CA3010": {
          "id": "CA3010",
          "shortDescription": "Review code for XAML injection vulnerabilities",
          "fullDescription": "Potential XAML injection vulnerability was found where '{0}' in method '{1}' may be tainted by user-controlled data from '{2}' in method '{3}'.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca3010-review-code-for-xaml-injection-vulnerabilities",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "ReviewCodeForXamlInjectionVulnerabilities",
            "languages": [
              "C#",
              "Visual Basic"
            ]
          }
        },
        "CA3011": {
          "id": "CA3011",
          "shortDescription": "Review code for DLL injection vulnerabilities",
          "fullDescription": "Potential DLL injection vulnerability was found where '{0}' in method '{1}' may be tainted by user-controlled data from '{2}' in method '{3}'.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca3011-review-code-for-dll-injection-vulnerabilities",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "ReviewCodeForDllInjectionVulnerabilities",
            "languages": [
              "C#",
              "Visual Basic"
            ]
          }
        },
        "CA3012": {
          "id": "CA3012",
          "shortDescription": "Review code for regex injection vulnerabilities",
          "fullDescription": "Potential regex injection vulnerability was found where '{0}' in method '{1}' may be tainted by user-controlled data from '{2}' in method '{3}'.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca3012-review-code-for-regex-injection-vulnerabilities",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "ReviewCodeForRegexInjectionVulnerabilities",
            "languages": [
              "C#",
              "Visual Basic"
            ]
          }
        },
        "CA3061": {
          "id": "CA3061",
          "shortDescription": "Do Not Add Schema By URL",
          "fullDescription": "This overload of XmlSchemaCollection.Add method internally enables DTD processing on the XML reader instance used, and uses UrlResolver for resolving external XML entities. The outcome is information disclosure. Content from file system or network shares for the machine processing the XML can be exposed to attacker. In addition, an attacker can use this as a DoS vector.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca3061",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "DoNotAddSchemaByURL",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA5350": {
          "id": "CA5350",
          "shortDescription": "Do Not Use Weak Cryptographic Algorithms",
          "fullDescription": "Cryptographic algorithms degrade over time as attacks become for advances to attacker get access to more computation. Depending on the type and application of this cryptographic algorithm, further degradation of the cryptographic strength of it may allow attackers to read enciphered messages, tamper with enciphered  messages, forge digital signatures, tamper with hashed content, or otherwise compromise any cryptosystem based on this algorithm. Replace encryption uses with the AES algorithm (AES-256, AES-192 and AES-128 are acceptable) with a key length greater than or equal to 128 bits. Replace hashing uses with a hashing function in the SHA-2 family, such as SHA-2 512, SHA-2 384, or SHA-2 256.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca5350-do-not-use-weak-cryptographic-algorithms",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "DoNotUseInsecureCryptographicAlgorithmsAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA5351": {
          "id": "CA5351",
          "shortDescription": "Do Not Use Broken Cryptographic Algorithms",
          "fullDescription": "An attack making it computationally feasible to break this algorithm exists. This allows attackers to break the cryptographic guarantees it is designed to provide. Depending on the type and application of this cryptographic algorithm, this may allow attackers to read enciphered messages, tamper with enciphered  messages, forge digital signatures, tamper with hashed content, or otherwise compromise any cryptosystem based on this algorithm. Replace encryption uses with the AES algorithm (AES-256, AES-192 and AES-128 are acceptable) with a key length greater than or equal to 128 bits. Replace hashing uses with a hashing function in the SHA-2 family, such as SHA512, SHA384, or SHA256. Replace digital signature uses with RSA with a key length greater than or equal to 2048-bits, or ECDSA with a key length greater than or equal to 256 bits.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca5351-do-not-use-broken-cryptographic-algorithms",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "DoNotUseInsecureCryptographicAlgorithmsAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA5358": {
          "id": "CA5358",
          "shortDescription": "Do Not Use Unsafe Cipher Modes",
          "fullDescription": "These modes are vulnerable to attacks. Use only approved modes (CBC, CTS).",
          "defaultLevel": "warning",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "ApprovedCipherModeAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA5359": {
          "id": "CA5359",
          "shortDescription": "Do Not Disable Certificate Validation",
          "fullDescription": "A certificate can help authenticate the identity of the server. Clients should validate the server certificate to ensure requests are sent to the intended server. If the ServerCertificateValidationCallback always returns 'true', any certificate will pass validation.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "DoNotDisableCertificateValidation",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA5360": {
          "id": "CA5360",
          "shortDescription": "Do Not Call Dangerous Methods In Deserialization",
          "fullDescription": "Insecure Deserialization is a vulnerability which occurs when untrusted data is used to abuse the logic of an application, inflict a Denial-of-Service (DoS) attack, or even execute arbitrary code upon it being deserialized. It’s frequently possible for malicious users to abuse these deserialization features when the application is deserializing untrusted data which is under their control. Specifically, invoke dangerous methods in the process of deserialization. Successful insecure deserialization attacks could allow an attacker to carry out attacks such as DoS attacks, authentication bypasses, and remote code execution.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "DoNotCallDangerousMethodsInDeserialization",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA5361": {
          "id": "CA5361",
          "shortDescription": "Do Not Disable SChannel Use of Strong Crypto",
          "fullDescription": "Starting with the .NET Framework 4.6, the System.Net.ServicePointManager and System.Net.Security.SslStream classes are recommeded to use new protocols. The old ones have protocol weaknesses and are not supported. Setting Switch.System.Net.DontEnableSchUseStrongCrypto with true will use the old weak crypto check and opt out of the protocol migration.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca5361",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "DoNotSetSwitch",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Dataflow",
              "Telemetry"
            ]
          }
        },
        "CA5362": {
          "id": "CA5362",
          "shortDescription": "Do Not Refer Self In Serializable Class",
          "fullDescription": "This can allow an attacker to DOS or exhaust the memory of the process.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "DoNotReferSelfInSerializableClass",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA5363": {
          "id": "CA5363",
          "shortDescription": "Do Not Disable Request Validation",
          "fullDescription": "Request validation is a feature in ASP.NET that examines HTTP requests and determines whether they contain potentially dangerous content. This check adds protection from markup or code in the URL query string, cookies, or posted form values that might have been added for malicious purposes. So, it is generally desirable and should be left enabled for defense in depth.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca5363",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "DoNotDisableRequestValidation",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA5364": {
          "id": "CA5364",
          "shortDescription": "Do Not Use Deprecated Security Protocols",
          "fullDescription": "Using a deprecated security protocol rather than the system default is risky.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca5364",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "DoNotUseDeprecatedSecurityProtocols",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA5365": {
          "id": "CA5365",
          "shortDescription": "Do Not Disable HTTP Header Checking",
          "fullDescription": "HTTP header checking enables encoding of the carriage return and newline characters, \\r and \\n, that are found in response headers. This encoding can help to avoid injection attacks that exploit an application that echoes untrusted data contained by the header.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "DoNotDisableHTTPHeaderChecking",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA5366": {
          "id": "CA5366",
          "shortDescription": "Use XmlReader For DataSet Read Xml",
          "fullDescription": "Processing XML from untrusted data may load dangerous external references, which should be restricted by using an XmlReader with a secure resolver or with DTD processing disabled.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "UseXmlReaderForDataSetReadXml",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA5367": {
          "id": "CA5367",
          "shortDescription": "Do Not Serialize Types With Pointer Fields",
          "fullDescription": "Pointers are not \"type safe\" in the sense that you cannot guarantee the correctness of the memory they point at. So, serializing types with pointer fields is dangerous, as it may allow an attacker to control the pointer.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "DoNotSerializeTypeWithPointerFields",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA5368": {
          "id": "CA5368",
          "shortDescription": "Set ViewStateUserKey For Classes Derived From Page",
          "fullDescription": "Setting the ViewStateUserKey property can help you prevent attacks on your application by allowing you to assign an identifier to the view-state variable for individual users so that they cannot use the variable to generate an attack. Otherwise, there will be cross-site request forgery vulnerabilities.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "SetViewStateUserKey",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA5369": {
          "id": "CA5369",
          "shortDescription": "Use XmlReader For Deserialize",
          "fullDescription": "Processing XML from untrusted data may load dangerous external references, which should be restricted by using an XmlReader with a secure resolver or with DTD processing disabled.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca5369",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "UseXmlReaderForDeserialize",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA5370": {
          "id": "CA5370",
          "shortDescription": "Use XmlReader For Validating Reader",
          "fullDescription": "Processing XML from untrusted data may load dangerous external references, which should be restricted by using an XmlReader with a secure resolver or with DTD processing disabled.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca5370",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "UseXmlReaderForValidatingReader",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA5371": {
          "id": "CA5371",
          "shortDescription": "Use XmlReader For Schema Read",
          "fullDescription": "Processing XML from untrusted data may load dangerous external references, which should be restricted by using an XmlReader with a secure resolver or with DTD processing disabled.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca5371",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "UseXmlReaderForSchemaRead",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA5372": {
          "id": "CA5372",
          "shortDescription": "Use XmlReader For XPathDocument",
          "fullDescription": "Processing XML from untrusted data may load dangerous external references, which should be restricted by using an XmlReader with a secure resolver or with DTD processing disabled.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca5372",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "UseXmlReaderForXPathDocument",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA5373": {
          "id": "CA5373",
          "shortDescription": "Do not use obsolete key derivation function",
          "fullDescription": "Password-based key derivation should use PBKDF2 with SHA-2. Avoid using PasswordDeriveBytes since it generates a PBKDF1 key. Avoid using Rfc2898DeriveBytes.CryptDeriveKey since it doesn't use the iteration count or salt.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca5373",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "DoNotUseObsoleteKDFAlgorithm",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA5374": {
          "id": "CA5374",
          "shortDescription": "Do Not Use XslTransform",
          "fullDescription": "Do not use XslTransform. It does not restrict potentially dangerous external references.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "DoNotUseXslTransform",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA5375": {
          "id": "CA5375",
          "shortDescription": "Do Not Use Account Shared Access Signature",
          "fullDescription": "Shared Access Signatures(SAS) are a vital part of the security model for any application using Azure Storage, they should provide limited and safe permissions to your storage account to clients that don't have the account key. All of the operations available via a service SAS are also available via an account SAS, that is, account SAS is too powerful. So it is recommended to use Service SAS to delegate access more carefully.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "DoNotUseAccountSAS",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA5376": {
          "id": "CA5376",
          "shortDescription": "Use SharedAccessProtocol HttpsOnly",
          "fullDescription": "HTTPS encrypts network traffic. Use HttpsOnly, rather than HttpOrHttps, to ensure network traffic is always encrypted to help prevent disclosure of sensitive data.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "UseSharedAccessProtocolHttpsOnly",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Dataflow",
              "Telemetry"
            ]
          }
        },
        "CA5377": {
          "id": "CA5377",
          "shortDescription": "Use Container Level Access Policy",
          "fullDescription": "No access policy identifier is specified, making tokens non-revocable.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "UseContainerLevelAccessPolicy",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Dataflow",
              "Telemetry"
            ]
          }
        },
        "CA5378": {
          "id": "CA5378",
          "shortDescription": "Do not disable ServicePointManagerSecurityProtocols",
          "fullDescription": "Do not set Switch.System.ServiceModel.DisableUsingServicePointManagerSecurityProtocols to true.  Setting this switch limits Windows Communication Framework (WCF) to using Transport Layer Security (TLS) 1.0, which is insecure and obsolete.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca5378",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "DoNotSetSwitch",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Dataflow",
              "Telemetry"
            ]
          }
        },
        "CA5379": {
          "id": "CA5379",
          "shortDescription": "Do Not Use Weak Key Derivation Function Algorithm",
          "fullDescription": "Some implementations of the Rfc2898DeriveBytes class allow for a hash algorithm to be specified in a constructor parameter or overwritten in the HashAlgorithm property. If a hash algorithm is specified, then it should be SHA-256 or higher.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "DoNotUseWeakKDFAlgorithm",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA5380": {
          "id": "CA5380",
          "shortDescription": "Do Not Add Certificates To Root Store",
          "fullDescription": "By default, the Trusted Root Certification Authorities certificate store is configured with a set of public CAs that has met the requirements of the Microsoft Root Certificate Program. Since all trusted root CAs can issue certificates for any domain, an attacker can pick a weak or coercible CA that you install by yourself to target for an attack – and a single vulnerable, malicious or coercible CA undermines the security of the entire system. To make matters worse, these attacks can go unnoticed quite easily.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca5380",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "DoNotInstallRootCert",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Dataflow",
              "Telemetry"
            ]
          }
        },
        "CA5381": {
          "id": "CA5381",
          "shortDescription": "Ensure Certificates Are Not Added To Root Store",
          "fullDescription": "By default, the Trusted Root Certification Authorities certificate store is configured with a set of public CAs that has met the requirements of the Microsoft Root Certificate Program. Since all trusted root CAs can issue certificates for any domain, an attacker can pick a weak or coercible CA that you install by yourself to target for an attack – and a single vulnerable, malicious or coercible CA undermines the security of the entire system. To make matters worse, these attacks can go unnoticed quite easily.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca5381",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "DoNotInstallRootCert",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Dataflow",
              "Telemetry"
            ]
          }
        },
        "CA5382": {
          "id": "CA5382",
          "shortDescription": "Use Secure Cookies In ASP.Net Core",
          "fullDescription": "Applications available over HTTPS must use secure cookies.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "UseSecureCookiesASPNetCore",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Dataflow",
              "Telemetry"
            ]
          }
        },
        "CA5383": {
          "id": "CA5383",
          "shortDescription": "Ensure Use Secure Cookies In ASP.Net Core",
          "fullDescription": "Applications available over HTTPS must use secure cookies.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "UseSecureCookiesASPNetCore",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Dataflow",
              "Telemetry"
            ]
          }
        },
        "CA5384": {
          "id": "CA5384",
          "shortDescription": "Do Not Use Digital Signature Algorithm (DSA)",
          "fullDescription": "DSA is too weak to use.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "DoNotUseDSA",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA5385": {
          "id": "CA5385",
          "shortDescription": "Use Rivest–Shamir–Adleman (RSA) Algorithm With Sufficient Key Size",
          "fullDescription": "Encryption algorithms are vulnerable to brute force attacks when too small a key size is used.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "UseRSAWithSufficientKeySize",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA5386": {
          "id": "CA5386",
          "shortDescription": "Avoid hardcoding SecurityProtocolType value",
          "fullDescription": "Avoid hardcoding SecurityProtocolType {0}, and instead use SecurityProtocolType.SystemDefault to allow the operating system to choose the best Transport Layer Security protocol to use.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca5386",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "DoNotUseDeprecatedSecurityProtocols",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA5387": {
          "id": "CA5387",
          "shortDescription": "Do Not Use Weak Key Derivation Function With Insufficient Iteration Count",
          "fullDescription": "When deriving cryptographic keys from user-provided inputs such as password, use sufficient iteration count (at least 100k).",
          "defaultLevel": "warning",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "DoNotUseWeakKDFInsufficientIterationCount",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Dataflow",
              "Telemetry"
            ]
          }
        },
        "CA5388": {
          "id": "CA5388",
          "shortDescription": "Ensure Sufficient Iteration Count When Using Weak Key Derivation Function",
          "fullDescription": "When deriving cryptographic keys from user-provided inputs such as password, use sufficient iteration count (at least 100k).",
          "defaultLevel": "warning",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "DoNotUseWeakKDFInsufficientIterationCount",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Dataflow",
              "Telemetry"
            ]
          }
        },
        "CA5389": {
          "id": "CA5389",
          "shortDescription": "Do Not Add Archive Item's Path To The Target File System Path",
          "fullDescription": "When extracting files from an archive and using the archive item's path, check if the path is safe. Archive path can be relative and can lead to file system access outside of the expected file system target path, leading to malicious config changes and remote code execution via lay-and-wait technique.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca5389",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "DoNotAddArchiveItemPathToTheTargetFileSystemPath",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Dataflow",
              "Telemetry"
            ]
          }
        },
        "CA5390": {
          "id": "CA5390",
          "shortDescription": "Do not hard-code encryption key",
          "fullDescription": "SymmetricAlgorithm's .Key property, or a method's rgbKey parameter, should never be a hard-coded value.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "DoNotHardCodeEncryptionKey",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Dataflow",
              "Telemetry"
            ]
          }
        },
        "CA5391": {
          "id": "CA5391",
          "shortDescription": "Use antiforgery tokens in ASP.NET Core MVC controllers",
          "fullDescription": "Handling a POST, PUT, PATCH, or DELETE request without validating an antiforgery token may be vulnerable to cross-site request forgery attacks. A cross-site request forgery attack can send malicious requests from an authenticated user to your ASP.NET Core MVC controller.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "UseAutoValidateAntiforgeryToken",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA5392": {
          "id": "CA5392",
          "shortDescription": "Use DefaultDllImportSearchPaths attribute for P/Invokes",
          "fullDescription": "By default, P/Invokes using DllImportAttribute probe a number of directories, including the current working directory for the library to load. This can be a security issue for certain applications, leading to DLL hijacking.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "UseDefaultDllImportSearchPathsAttribute",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA5393": {
          "id": "CA5393",
          "shortDescription": "Do not use unsafe DllImportSearchPath value",
          "fullDescription": "There could be a malicious DLL in the default DLL search directories. Or, depending on where your application is run from, there could be a malicious DLL in the application's directory. Use a DllImportSearchPath value that specifies an explicit search path instead. The DllImportSearchPath flags that this rule looks for can be configured in .editorconfig.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "UseDefaultDllImportSearchPathsAttribute",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA5394": {
          "id": "CA5394",
          "shortDescription": "Do not use insecure randomness",
          "fullDescription": "Using a cryptographically weak pseudo-random number generator may allow an attacker to predict what security-sensitive value will be generated. Use a cryptographically strong random number generator if an unpredictable value is required, or ensure that weak pseudo-random numbers aren't used in a security-sensitive manner.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "DoNotUseInsecureRandomness",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA5395": {
          "id": "CA5395",
          "shortDescription": "Miss HttpVerb attribute for action methods",
          "fullDescription": "All the methods that create, edit, delete, or otherwise modify data do so in the [HttpPost] overload of the method, which needs to be protected with the anti forgery attribute from request forgery. Performing a GET operation should be a safe operation that has no side effects and doesn't modify your persisted data.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "UseAutoValidateAntiforgeryToken",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA5396": {
          "id": "CA5396",
          "shortDescription": "Set HttpOnly to true for HttpCookie",
          "fullDescription": "As a defense in depth measure, ensure security sensitive HTTP cookies are marked as HttpOnly. This indicates web browsers should disallow scripts from accessing the cookies. Injected malicious scripts are a common way of stealing cookies.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "SetHttpOnlyForHttpCookie",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Dataflow",
              "Telemetry"
            ]
          }
        },
        "CA5397": {
          "id": "CA5397",
          "shortDescription": "Do not use deprecated SslProtocols values",
          "fullDescription": "Older protocol versions of Transport Layer Security (TLS) are less secure than TLS 1.2 and TLS 1.3, and are more likely to have new vulnerabilities. Avoid older protocol versions to minimize risk.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca5397",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "SslProtocolsAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA5398": {
          "id": "CA5398",
          "shortDescription": "Avoid hardcoded SslProtocols values",
          "fullDescription": "Current Transport Layer Security protocol versions may become deprecated if vulnerabilities are found. Avoid hardcoding SslProtocols values to keep your application secure. Use 'None' to let the Operating System choose a version.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca5398",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "SslProtocolsAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA5399": {
          "id": "CA5399",
          "shortDescription": "HttpClients should enable certificate revocation list checks",
          "fullDescription": "Using HttpClient without providing a platform specific handler (WinHttpHandler or CurlHandler or HttpClientHandler) where the CheckCertificateRevocationList property is set to true, will allow revoked certificates to be accepted by the HttpClient as valid.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "DoNotDisableHttpClientCRLCheck",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Dataflow",
              "Telemetry"
            ]
          }
        },
        "CA5400": {
          "id": "CA5400",
          "shortDescription": "Ensure HttpClient certificate revocation list check is not disabled",
          "fullDescription": "Using HttpClient without providing a platform specific handler (WinHttpHandler or CurlHandler or HttpClientHandler) where the CheckCertificateRevocationList property is set to true, will allow revoked certificates to be accepted by the HttpClient as valid.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "DoNotDisableHttpClientCRLCheck",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Dataflow",
              "Telemetry"
            ]
          }
        },
        "CA5401": {
          "id": "CA5401",
          "shortDescription": "Do not use CreateEncryptor with non-default IV",
          "fullDescription": "Symmetric encryption should always use a non-repeatable initialization vector to prevent dictionary attacks.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "DoNotUseCreateEncryptorWithNonDefaultIV",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Dataflow",
              "Telemetry"
            ]
          }
        },
        "CA5402": {
          "id": "CA5402",
          "shortDescription": "Use CreateEncryptor with the default IV ",
          "fullDescription": "Symmetric encryption should always use a non-repeatable initialization vector to prevent dictionary attacks.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "DoNotUseCreateEncryptorWithNonDefaultIV",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Dataflow",
              "Telemetry"
            ]
          }
        },
        "CA5403": {
          "id": "CA5403",
          "shortDescription": "Do not hard-code certificate",
          "fullDescription": "Hard-coded certificates in source code are vulnerable to being exploited.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": false,
            "typeName": "DoNotHardCodeCertificate",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Dataflow",
              "Telemetry"
            ]
          }
        }
      }
    },
    {
      "tool": {
        "name": "Microsoft.NetCore.CSharp.Analyzers",
        "version": "2.9.8",
        "language": "en-US"
      },
      "rules": {
        "CA1309": {
          "id": "CA1309",
          "shortDescription": "Use ordinal stringcomparison",
          "fullDescription": "A string comparison operation that is nonlinguistic does not set the StringComparison parameter to either Ordinal or OrdinalIgnoreCase. By explicitly setting the parameter to either StringComparison.Ordinal or StringComparison.OrdinalIgnoreCase, your code often gains speed, becomes more correct, and becomes more reliable.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1309-use-ordinal-stringcomparison",
          "properties": {
            "category": "Globalization",
            "isEnabledByDefault": false,
            "typeName": "CSharpUseOrdinalStringComparisonAnalyzer",
            "languages": [
              "C#"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1810": {
          "id": "CA1810",
          "shortDescription": "Initialize reference type static fields inline",
          "fullDescription": "A reference type declares an explicit static constructor. To fix a violation of this rule, initialize all static data when it is declared and remove the static constructor.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1810-initialize-reference-type-static-fields-inline",
          "properties": {
            "category": "Performance",
            "isEnabledByDefault": true,
            "typeName": "CSharpInitializeStaticFieldsInlineAnalyzer",
            "languages": [
              "C#"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1824": {
          "id": "CA1824",
          "shortDescription": "Mark assemblies with NeutralResourcesLanguageAttribute",
          "fullDescription": "The NeutralResourcesLanguage attribute informs the ResourceManager of the language that was used to display the resources of a neutral culture for an assembly. This improves lookup performance for the first resource that you load and can reduce your working set.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1824-mark-assemblies-with-neutralresourceslanguageattribute",
          "properties": {
            "category": "Performance",
            "isEnabledByDefault": true,
            "typeName": "CSharpMarkAssembliesWithNeutralResourcesLanguageAnalyzer",
            "languages": [
              "C#"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1825": {
          "id": "CA1825",
          "shortDescription": "Avoid zero-length array allocations.",
          "fullDescription": "Avoid unnecessary zero-length array allocations.  Use {0} instead.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Performance",
            "isEnabledByDefault": true,
            "typeName": "CSharpAvoidZeroLengthArrayAllocationsAnalyzer",
            "languages": [
              "C#"
            ]
          }
        },
        "CA1829": {
          "id": "CA1829",
          "shortDescription": "Use Length/Count property instead of Count() when available",
          "fullDescription": "Enumerable.Count() potentially enumerates the sequence while a Length/Count property is a direct access.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1829",
          "properties": {
            "category": "Performance",
            "isEnabledByDefault": true,
            "typeName": "CSharpUsePropertyInsteadOfCountMethodWhenAvailableAnalyzer",
            "languages": [
              "C#"
            ]
          }
        },
        "CA2010": {
          "id": "CA2010",
          "shortDescription": "Always consume the value returned by methods marked with PreserveSigAttribute",
          "fullDescription": "PreserveSigAttribute indicates that a method will return an HRESULT, rather than throwing an exception. Therefore, it is important to consume the HRESULT returned by the method, so that errors can be detected. Generally, this is done by calling Marshal.ThrowExceptionForHR.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Reliability",
            "isEnabledByDefault": true,
            "typeName": "CSharpAlwaysConsumeTheValueReturnedByMethodsMarkedWithPreserveSigAttributeAnalyzer",
            "languages": [
              "C#"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA2201": {
          "id": "CA2201",
          "shortDescription": "Do not raise reserved exception types",
          "fullDescription": "An exception of type that is not sufficiently specific or reserved by the runtime should never be raised by user code. This makes the original error difficult to detect and debug. If this exception instance might be thrown, use a different exception type.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2201-do-not-raise-reserved-exception-types",
          "properties": {
            "category": "Usage",
            "isEnabledByDefault": false,
            "typeName": "CSharpDoNotRaiseReservedExceptionTypesAnalyzer",
            "languages": [
              "C#"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA2207": {
          "id": "CA2207",
          "shortDescription": "Initialize value type static fields inline",
          "fullDescription": "A value type declares an explicit static constructor. To fix a violation of this rule, initialize all static data when it is declared and remove the static constructor.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2207-initialize-value-type-static-fields-inline",
          "properties": {
            "category": "Usage",
            "isEnabledByDefault": true,
            "typeName": "CSharpInitializeStaticFieldsInlineAnalyzer",
            "languages": [
              "C#"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        }
      }
    },
    {
      "tool": {
        "name": "Microsoft.NetCore.VisualBasic.Analyzers",
        "version": "2.9.8",
        "language": "en-US"
      },
      "rules": {
        "CA1309": {
          "id": "CA1309",
          "shortDescription": "Use ordinal stringcomparison",
          "fullDescription": "A string comparison operation that is nonlinguistic does not set the StringComparison parameter to either Ordinal or OrdinalIgnoreCase. By explicitly setting the parameter to either StringComparison.Ordinal or StringComparison.OrdinalIgnoreCase, your code often gains speed, becomes more correct, and becomes more reliable.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1309-use-ordinal-stringcomparison",
          "properties": {
            "category": "Globalization",
            "isEnabledByDefault": false,
            "typeName": "BasicUseOrdinalStringComparisonAnalyzer",
            "languages": [
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1810": {
          "id": "CA1810",
          "shortDescription": "Initialize reference type static fields inline",
          "fullDescription": "A reference type declares an explicit static constructor. To fix a violation of this rule, initialize all static data when it is declared and remove the static constructor.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1810-initialize-reference-type-static-fields-inline",
          "properties": {
            "category": "Performance",
            "isEnabledByDefault": true,
            "typeName": "BasicInitializeStaticFieldsInlineAnalyzer",
            "languages": [
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1824": {
          "id": "CA1824",
          "shortDescription": "Mark assemblies with NeutralResourcesLanguageAttribute",
          "fullDescription": "The NeutralResourcesLanguage attribute informs the ResourceManager of the language that was used to display the resources of a neutral culture for an assembly. This improves lookup performance for the first resource that you load and can reduce your working set.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1824-mark-assemblies-with-neutralresourceslanguageattribute",
          "properties": {
            "category": "Performance",
            "isEnabledByDefault": true,
            "typeName": "BasicMarkAssembliesWithNeutralResourcesLanguageAnalyzer",
            "languages": [
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA1825": {
          "id": "CA1825",
          "shortDescription": "Avoid zero-length array allocations.",
          "fullDescription": "Avoid unnecessary zero-length array allocations.  Use {0} instead.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Performance",
            "isEnabledByDefault": true,
            "typeName": "BasicAvoidZeroLengthArrayAllocationsAnalyzer",
            "languages": [
              "Visual Basic"
            ]
          }
        },
        "CA1829": {
          "id": "CA1829",
          "shortDescription": "Use Length/Count property instead of Count() when available",
          "fullDescription": "Enumerable.Count() potentially enumerates the sequence while a Length/Count property is a direct access.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1829",
          "properties": {
            "category": "Performance",
            "isEnabledByDefault": true,
            "typeName": "BasicUsePropertyInsteadOfCountMethodWhenAvailableAnalyzer",
            "languages": [
              "Visual Basic"
            ]
          }
        },
        "CA2010": {
          "id": "CA2010",
          "shortDescription": "Always consume the value returned by methods marked with PreserveSigAttribute",
          "fullDescription": "PreserveSigAttribute indicates that a method will return an HRESULT, rather than throwing an exception. Therefore, it is important to consume the HRESULT returned by the method, so that errors can be detected. Generally, this is done by calling Marshal.ThrowExceptionForHR.",
          "defaultLevel": "warning",
          "properties": {
            "category": "Reliability",
            "isEnabledByDefault": true,
            "typeName": "BasicAlwaysConsumeTheValueReturnedByMethodsMarkedWithPreserveSigAttributeAnalyzer",
            "languages": [
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA2201": {
          "id": "CA2201",
          "shortDescription": "Do not raise reserved exception types",
          "fullDescription": "An exception of type that is not sufficiently specific or reserved by the runtime should never be raised by user code. This makes the original error difficult to detect and debug. If this exception instance might be thrown, use a different exception type.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2201-do-not-raise-reserved-exception-types",
          "properties": {
            "category": "Usage",
            "isEnabledByDefault": false,
            "typeName": "BasicDoNotRaiseReservedExceptionTypesAnalyzer",
            "languages": [
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA2207": {
          "id": "CA2207",
          "shortDescription": "Initialize value type static fields inline",
          "fullDescription": "A value type declares an explicit static constructor. To fix a violation of this rule, initialize all static data when it is declared and remove the static constructor.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2207-initialize-value-type-static-fields-inline",
          "properties": {
            "category": "Usage",
            "isEnabledByDefault": true,
            "typeName": "BasicInitializeStaticFieldsInlineAnalyzer",
            "languages": [
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        }
      }
    }
  ]
}