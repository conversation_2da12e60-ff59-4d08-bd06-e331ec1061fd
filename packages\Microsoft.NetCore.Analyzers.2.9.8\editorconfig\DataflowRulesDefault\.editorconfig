# NOTE: Requires **VS2019 16.3** or later

# Dataflow Rules with default severity
# Description: All Dataflow Rules with default severity. Rules with IsEnabledByDefault = false and non-Dataflow rules are disabled.

# Code files
[*.{cs,vb}]

# Dataflow Rules

# CA1303: Do not pass literals as localized parameters
dotnet_diagnostic.CA1303.severity = warning

# CA2000: Dispose objects before losing scope
dotnet_diagnostic.CA2000.severity = warning

# CA2100: Review SQL queries for security vulnerabilities
dotnet_diagnostic.CA2100.severity = warning

# CA2213: Disposable fields should be disposed
dotnet_diagnostic.CA2213.severity = warning

# CA2301: Do not call BinaryFormatter.Deserialize without first setting BinaryFormatter.Binder
dotnet_diagnostic.CA2301.severity = none

# CA2302: Ensure BinaryFormatter.Binder is set before calling BinaryFormatter.Deserialize
dotnet_diagnostic.CA2302.severity = none

# CA2311: Do not deserialize without first setting NetDataContractSerializer.Binder
dotnet_diagnostic.CA2311.severity = none

# CA2312: Ensure NetDataContractSerializer.Binder is set before deserializing
dotnet_diagnostic.CA2312.severity = none

# CA2321: Do not deserialize with JavaScriptSerializer using a SimpleTypeResolver
dotnet_diagnostic.CA2321.severity = none

# CA2322: Ensure JavaScriptSerializer is not initialized with SimpleTypeResolver before deserializing
dotnet_diagnostic.CA2322.severity = none

# CA2327: Do not use insecure JsonSerializerSettings
dotnet_diagnostic.CA2327.severity = none

# CA2328: Ensure that JsonSerializerSettings are secure
dotnet_diagnostic.CA2328.severity = none

# CA2329: Do not deserialize with JsonSerializer using an insecure configuration
dotnet_diagnostic.CA2329.severity = none

# CA2330: Ensure that JsonSerializer has a secure configuration when deserializing
dotnet_diagnostic.CA2330.severity = none

# CA5361: Do Not Disable SChannel Use of Strong Crypto
dotnet_diagnostic.CA5361.severity = warning

# CA5376: Use SharedAccessProtocol HttpsOnly
dotnet_diagnostic.CA5376.severity = warning

# CA5377: Use Container Level Access Policy
dotnet_diagnostic.CA5377.severity = warning

# CA5378: Do not disable ServicePointManagerSecurityProtocols
dotnet_diagnostic.CA5378.severity = warning

# CA5380: Do Not Add Certificates To Root Store
dotnet_diagnostic.CA5380.severity = warning

# CA5381: Ensure Certificates Are Not Added To Root Store
dotnet_diagnostic.CA5381.severity = warning

# CA5382: Use Secure Cookies In ASP.Net Core
dotnet_diagnostic.CA5382.severity = none

# CA5383: Ensure Use Secure Cookies In ASP.Net Core
dotnet_diagnostic.CA5383.severity = none

# CA5387: Do Not Use Weak Key Derivation Function With Insufficient Iteration Count
dotnet_diagnostic.CA5387.severity = none

# CA5388: Ensure Sufficient Iteration Count When Using Weak Key Derivation Function
dotnet_diagnostic.CA5388.severity = none

# CA5389: Do Not Add Archive Item's Path To The Target File System Path
dotnet_diagnostic.CA5389.severity = none

# CA5390: Do not hard-code encryption key
dotnet_diagnostic.CA5390.severity = none

# CA5396: Set HttpOnly to true for HttpCookie
dotnet_diagnostic.CA5396.severity = none

# CA5399: HttpClients should enable certificate revocation list checks
dotnet_diagnostic.CA5399.severity = none

# CA5400: Ensure HttpClient certificate revocation list check is not disabled
dotnet_diagnostic.CA5400.severity = none

# CA5401: Do not use CreateEncryptor with non-default IV
dotnet_diagnostic.CA5401.severity = none

# CA5402: Use CreateEncryptor with the default IV 
dotnet_diagnostic.CA5402.severity = none

# CA5403: Do not hard-code certificate
dotnet_diagnostic.CA5403.severity = none



# Other Rules

# CA1304: Specify CultureInfo
dotnet_diagnostic.CA1304.severity = none

# CA1305: Specify IFormatProvider
dotnet_diagnostic.CA1305.severity = none

# CA1307: Specify StringComparison
dotnet_diagnostic.CA1307.severity = none

# CA1308: Normalize strings to uppercase
dotnet_diagnostic.CA1308.severity = none

# CA1309: Use ordinal stringcomparison
dotnet_diagnostic.CA1309.severity = none

# CA1401: P/Invokes should not be visible
dotnet_diagnostic.CA1401.severity = none

# CA1810: Initialize reference type static fields inline
dotnet_diagnostic.CA1810.severity = none

# CA1813: Avoid unsealed attributes
dotnet_diagnostic.CA1813.severity = none

# CA1816: Dispose methods should call SuppressFinalize
dotnet_diagnostic.CA1816.severity = none

# CA1820: Test for empty strings using string length
dotnet_diagnostic.CA1820.severity = none

# CA1824: Mark assemblies with NeutralResourcesLanguageAttribute
dotnet_diagnostic.CA1824.severity = none

# CA1825: Avoid zero-length array allocations.
dotnet_diagnostic.CA1825.severity = none

# CA1826: Do not use Enumerable methods on indexable collections. Instead use the collection directly
dotnet_diagnostic.CA1826.severity = none

# CA1827: Do not use Count() or LongCount() when Any() can be used
dotnet_diagnostic.CA1827.severity = none

# CA1828: Do not use CountAsync() or LongCountAsync() when AnyAsync() can be used
dotnet_diagnostic.CA1828.severity = none

# CA1829: Use Length/Count property instead of Count() when available
dotnet_diagnostic.CA1829.severity = none

# CA2002: Do not lock on objects with weak identity
dotnet_diagnostic.CA2002.severity = none

# CA2008: Do not create tasks without passing a TaskScheduler
dotnet_diagnostic.CA2008.severity = none

# CA2009: Do not call ToImmutableCollection on an ImmutableCollection value
dotnet_diagnostic.CA2009.severity = none

# CA2010: Always consume the value returned by methods marked with PreserveSigAttribute
dotnet_diagnostic.CA2010.severity = none

# CA2101: Specify marshaling for P/Invoke string arguments
dotnet_diagnostic.CA2101.severity = none

# CA2201: Do not raise reserved exception types
dotnet_diagnostic.CA2201.severity = none

# CA2207: Initialize value type static fields inline
dotnet_diagnostic.CA2207.severity = none

# CA2208: Instantiate argument exceptions correctly
dotnet_diagnostic.CA2208.severity = none

# CA2216: Disposable types should declare finalizer
dotnet_diagnostic.CA2216.severity = none

# CA2229: Implement serialization constructors
dotnet_diagnostic.CA2229.severity = none

# CA2235: Mark all non-serializable fields
dotnet_diagnostic.CA2235.severity = none

# CA2237: Mark ISerializable types with serializable
dotnet_diagnostic.CA2237.severity = none

# CA2241: Provide correct arguments to formatting methods
dotnet_diagnostic.CA2241.severity = none

# CA2242: Test for NaN correctly
dotnet_diagnostic.CA2242.severity = none

# CA2243: Attribute string literals should parse correctly
dotnet_diagnostic.CA2243.severity = none

# CA2300: Do not use insecure deserializer BinaryFormatter
dotnet_diagnostic.CA2300.severity = none

# CA2305: Do not use insecure deserializer LosFormatter
dotnet_diagnostic.CA2305.severity = none

# CA2310: Do not use insecure deserializer NetDataContractSerializer
dotnet_diagnostic.CA2310.severity = none

# CA2315: Do not use insecure deserializer ObjectStateFormatter
dotnet_diagnostic.CA2315.severity = none

# CA2326: Do not use TypeNameHandling values other than None
dotnet_diagnostic.CA2326.severity = none

# CA3001: Review code for SQL injection vulnerabilities
dotnet_diagnostic.CA3001.severity = none

# CA3002: Review code for XSS vulnerabilities
dotnet_diagnostic.CA3002.severity = none

# CA3003: Review code for file path injection vulnerabilities
dotnet_diagnostic.CA3003.severity = none

# CA3004: Review code for information disclosure vulnerabilities
dotnet_diagnostic.CA3004.severity = none

# CA3005: Review code for LDAP injection vulnerabilities
dotnet_diagnostic.CA3005.severity = none

# CA3006: Review code for process command injection vulnerabilities
dotnet_diagnostic.CA3006.severity = none

# CA3007: Review code for open redirect vulnerabilities
dotnet_diagnostic.CA3007.severity = none

# CA3008: Review code for XPath injection vulnerabilities
dotnet_diagnostic.CA3008.severity = none

# CA3009: Review code for XML injection vulnerabilities
dotnet_diagnostic.CA3009.severity = none

# CA3010: Review code for XAML injection vulnerabilities
dotnet_diagnostic.CA3010.severity = none

# CA3011: Review code for DLL injection vulnerabilities
dotnet_diagnostic.CA3011.severity = none

# CA3012: Review code for regex injection vulnerabilities
dotnet_diagnostic.CA3012.severity = none

# CA3061: Do Not Add Schema By URL
dotnet_diagnostic.CA3061.severity = none

# CA5350: Do Not Use Weak Cryptographic Algorithms
dotnet_diagnostic.CA5350.severity = none

# CA5351: Do Not Use Broken Cryptographic Algorithms
dotnet_diagnostic.CA5351.severity = none

# CA5358: Do Not Use Unsafe Cipher Modes
dotnet_diagnostic.CA5358.severity = none

# CA5359: Do Not Disable Certificate Validation
dotnet_diagnostic.CA5359.severity = none

# CA5360: Do Not Call Dangerous Methods In Deserialization
dotnet_diagnostic.CA5360.severity = none

# CA5362: Do Not Refer Self In Serializable Class
dotnet_diagnostic.CA5362.severity = none

# CA5363: Do Not Disable Request Validation
dotnet_diagnostic.CA5363.severity = none

# CA5364: Do Not Use Deprecated Security Protocols
dotnet_diagnostic.CA5364.severity = none

# CA5365: Do Not Disable HTTP Header Checking
dotnet_diagnostic.CA5365.severity = none

# CA5366: Use XmlReader For DataSet Read Xml
dotnet_diagnostic.CA5366.severity = none

# CA5367: Do Not Serialize Types With Pointer Fields
dotnet_diagnostic.CA5367.severity = none

# CA5368: Set ViewStateUserKey For Classes Derived From Page
dotnet_diagnostic.CA5368.severity = none

# CA5369: Use XmlReader For Deserialize
dotnet_diagnostic.CA5369.severity = none

# CA5370: Use XmlReader For Validating Reader
dotnet_diagnostic.CA5370.severity = none

# CA5371: Use XmlReader For Schema Read
dotnet_diagnostic.CA5371.severity = none

# CA5372: Use XmlReader For XPathDocument
dotnet_diagnostic.CA5372.severity = none

# CA5373: Do not use obsolete key derivation function
dotnet_diagnostic.CA5373.severity = none

# CA5374: Do Not Use XslTransform
dotnet_diagnostic.CA5374.severity = none

# CA5375: Do Not Use Account Shared Access Signature
dotnet_diagnostic.CA5375.severity = none

# CA5379: Do Not Use Weak Key Derivation Function Algorithm
dotnet_diagnostic.CA5379.severity = none

# CA5384: Do Not Use Digital Signature Algorithm (DSA)
dotnet_diagnostic.CA5384.severity = none

# CA5385: Use Rivest–Shamir–Adleman (RSA) Algorithm With Sufficient Key Size
dotnet_diagnostic.CA5385.severity = none

# CA5386: Avoid hardcoding SecurityProtocolType value
dotnet_diagnostic.CA5386.severity = none

# CA5391: Use antiforgery tokens in ASP.NET Core MVC controllers
dotnet_diagnostic.CA5391.severity = none

# CA5392: Use DefaultDllImportSearchPaths attribute for P/Invokes
dotnet_diagnostic.CA5392.severity = none

# CA5393: Do not use unsafe DllImportSearchPath value
dotnet_diagnostic.CA5393.severity = none

# CA5394: Do not use insecure randomness
dotnet_diagnostic.CA5394.severity = none

# CA5395: Miss HttpVerb attribute for action methods
dotnet_diagnostic.CA5395.severity = none

# CA5397: Do not use deprecated SslProtocols values
dotnet_diagnostic.CA5397.severity = none

# CA5398: Avoid hardcoded SslProtocols values
dotnet_diagnostic.CA5398.severity = none
