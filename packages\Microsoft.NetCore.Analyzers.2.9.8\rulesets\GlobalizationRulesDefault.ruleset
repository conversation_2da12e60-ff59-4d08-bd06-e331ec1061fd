<?xml version="1.0"?>
<RuleSet Name="Globalization Rules with default severity" Description="All Globalization Rules with default severity. Rules with IsEnabledByDefault = false or from a different category are disabled." ToolsVersion="15.0">
   <!-- Globalization Rules -->
   <Rules AnalyzerId="Microsoft.NetCore.Analyzers" RuleNamespace="Microsoft.NetCore.Analyzers">
      <Rule Id="CA1303" Action="Warning" />          <!-- Do not pass literals as localized parameters -->
      <Rule Id="CA1304" Action="Warning" />          <!-- Specify CultureInfo -->
      <Rule Id="CA1305" Action="Warning" />          <!-- Specify IFormatProvider -->
      <Rule Id="CA1307" Action="Warning" />          <!-- Specify StringComparison -->
      <Rule Id="CA1308" Action="Warning" />          <!-- Normalize strings to uppercase -->
      <Rule Id="CA1309" Action="None" />             <!-- Use ordinal stringcomparison -->
      <Rule Id="CA2101" Action="Warning" />          <!-- Specify marshaling for P/Invoke string arguments -->
   </Rules>



   <!-- Other Rules -->
   <Rules AnalyzerId="Microsoft.NetCore.Analyzers" RuleNamespace="Microsoft.NetCore.Analyzers">
      <Rule Id="CA1401" Action="None" />             <!-- P/Invokes should not be visible -->
      <Rule Id="CA1810" Action="None" />             <!-- Initialize reference type static fields inline -->
      <Rule Id="CA1813" Action="None" />             <!-- Avoid unsealed attributes -->
      <Rule Id="CA1816" Action="None" />             <!-- Dispose methods should call SuppressFinalize -->
      <Rule Id="CA1820" Action="None" />             <!-- Test for empty strings using string length -->
      <Rule Id="CA1824" Action="None" />             <!-- Mark assemblies with NeutralResourcesLanguageAttribute -->
      <Rule Id="CA1825" Action="None" />             <!-- Avoid zero-length array allocations. -->
      <Rule Id="CA1826" Action="None" />             <!-- Do not use Enumerable methods on indexable collections. Instead use the collection directly -->
      <Rule Id="CA1827" Action="None" />             <!-- Do not use Count() or LongCount() when Any() can be used -->
      <Rule Id="CA1828" Action="None" />             <!-- Do not use CountAsync() or LongCountAsync() when AnyAsync() can be used -->
      <Rule Id="CA1829" Action="None" />             <!-- Use Length/Count property instead of Count() when available -->
      <Rule Id="CA2000" Action="None" />             <!-- Dispose objects before losing scope -->
      <Rule Id="CA2002" Action="None" />             <!-- Do not lock on objects with weak identity -->
      <Rule Id="CA2008" Action="None" />             <!-- Do not create tasks without passing a TaskScheduler -->
      <Rule Id="CA2009" Action="None" />             <!-- Do not call ToImmutableCollection on an ImmutableCollection value -->
      <Rule Id="CA2010" Action="None" />             <!-- Always consume the value returned by methods marked with PreserveSigAttribute -->
      <Rule Id="CA2100" Action="None" />             <!-- Review SQL queries for security vulnerabilities -->
      <Rule Id="CA2201" Action="None" />             <!-- Do not raise reserved exception types -->
      <Rule Id="CA2207" Action="None" />             <!-- Initialize value type static fields inline -->
      <Rule Id="CA2208" Action="None" />             <!-- Instantiate argument exceptions correctly -->
      <Rule Id="CA2213" Action="None" />             <!-- Disposable fields should be disposed -->
      <Rule Id="CA2216" Action="None" />             <!-- Disposable types should declare finalizer -->
      <Rule Id="CA2229" Action="None" />             <!-- Implement serialization constructors -->
      <Rule Id="CA2235" Action="None" />             <!-- Mark all non-serializable fields -->
      <Rule Id="CA2237" Action="None" />             <!-- Mark ISerializable types with serializable -->
      <Rule Id="CA2241" Action="None" />             <!-- Provide correct arguments to formatting methods -->
      <Rule Id="CA2242" Action="None" />             <!-- Test for NaN correctly -->
      <Rule Id="CA2243" Action="None" />             <!-- Attribute string literals should parse correctly -->
      <Rule Id="CA2300" Action="None" />             <!-- Do not use insecure deserializer BinaryFormatter -->
      <Rule Id="CA2301" Action="None" />             <!-- Do not call BinaryFormatter.Deserialize without first setting BinaryFormatter.Binder -->
      <Rule Id="CA2302" Action="None" />             <!-- Ensure BinaryFormatter.Binder is set before calling BinaryFormatter.Deserialize -->
      <Rule Id="CA2305" Action="None" />             <!-- Do not use insecure deserializer LosFormatter -->
      <Rule Id="CA2310" Action="None" />             <!-- Do not use insecure deserializer NetDataContractSerializer -->
      <Rule Id="CA2311" Action="None" />             <!-- Do not deserialize without first setting NetDataContractSerializer.Binder -->
      <Rule Id="CA2312" Action="None" />             <!-- Ensure NetDataContractSerializer.Binder is set before deserializing -->
      <Rule Id="CA2315" Action="None" />             <!-- Do not use insecure deserializer ObjectStateFormatter -->
      <Rule Id="CA2321" Action="None" />             <!-- Do not deserialize with JavaScriptSerializer using a SimpleTypeResolver -->
      <Rule Id="CA2322" Action="None" />             <!-- Ensure JavaScriptSerializer is not initialized with SimpleTypeResolver before deserializing -->
      <Rule Id="CA2326" Action="None" />             <!-- Do not use TypeNameHandling values other than None -->
      <Rule Id="CA2327" Action="None" />             <!-- Do not use insecure JsonSerializerSettings -->
      <Rule Id="CA2328" Action="None" />             <!-- Ensure that JsonSerializerSettings are secure -->
      <Rule Id="CA2329" Action="None" />             <!-- Do not deserialize with JsonSerializer using an insecure configuration -->
      <Rule Id="CA2330" Action="None" />             <!-- Ensure that JsonSerializer has a secure configuration when deserializing -->
      <Rule Id="CA3001" Action="None" />             <!-- Review code for SQL injection vulnerabilities -->
      <Rule Id="CA3002" Action="None" />             <!-- Review code for XSS vulnerabilities -->
      <Rule Id="CA3003" Action="None" />             <!-- Review code for file path injection vulnerabilities -->
      <Rule Id="CA3004" Action="None" />             <!-- Review code for information disclosure vulnerabilities -->
      <Rule Id="CA3005" Action="None" />             <!-- Review code for LDAP injection vulnerabilities -->
      <Rule Id="CA3006" Action="None" />             <!-- Review code for process command injection vulnerabilities -->
      <Rule Id="CA3007" Action="None" />             <!-- Review code for open redirect vulnerabilities -->
      <Rule Id="CA3008" Action="None" />             <!-- Review code for XPath injection vulnerabilities -->
      <Rule Id="CA3009" Action="None" />             <!-- Review code for XML injection vulnerabilities -->
      <Rule Id="CA3010" Action="None" />             <!-- Review code for XAML injection vulnerabilities -->
      <Rule Id="CA3011" Action="None" />             <!-- Review code for DLL injection vulnerabilities -->
      <Rule Id="CA3012" Action="None" />             <!-- Review code for regex injection vulnerabilities -->
      <Rule Id="CA3061" Action="None" />             <!-- Do Not Add Schema By URL -->
      <Rule Id="CA5350" Action="None" />             <!-- Do Not Use Weak Cryptographic Algorithms -->
      <Rule Id="CA5351" Action="None" />             <!-- Do Not Use Broken Cryptographic Algorithms -->
      <Rule Id="CA5358" Action="None" />             <!-- Do Not Use Unsafe Cipher Modes -->
      <Rule Id="CA5359" Action="None" />             <!-- Do Not Disable Certificate Validation -->
      <Rule Id="CA5360" Action="None" />             <!-- Do Not Call Dangerous Methods In Deserialization -->
      <Rule Id="CA5361" Action="None" />             <!-- Do Not Disable SChannel Use of Strong Crypto -->
      <Rule Id="CA5362" Action="None" />             <!-- Do Not Refer Self In Serializable Class -->
      <Rule Id="CA5363" Action="None" />             <!-- Do Not Disable Request Validation -->
      <Rule Id="CA5364" Action="None" />             <!-- Do Not Use Deprecated Security Protocols -->
      <Rule Id="CA5365" Action="None" />             <!-- Do Not Disable HTTP Header Checking -->
      <Rule Id="CA5366" Action="None" />             <!-- Use XmlReader For DataSet Read Xml -->
      <Rule Id="CA5367" Action="None" />             <!-- Do Not Serialize Types With Pointer Fields -->
      <Rule Id="CA5368" Action="None" />             <!-- Set ViewStateUserKey For Classes Derived From Page -->
      <Rule Id="CA5369" Action="None" />             <!-- Use XmlReader For Deserialize -->
      <Rule Id="CA5370" Action="None" />             <!-- Use XmlReader For Validating Reader -->
      <Rule Id="CA5371" Action="None" />             <!-- Use XmlReader For Schema Read -->
      <Rule Id="CA5372" Action="None" />             <!-- Use XmlReader For XPathDocument -->
      <Rule Id="CA5373" Action="None" />             <!-- Do not use obsolete key derivation function -->
      <Rule Id="CA5374" Action="None" />             <!-- Do Not Use XslTransform -->
      <Rule Id="CA5375" Action="None" />             <!-- Do Not Use Account Shared Access Signature -->
      <Rule Id="CA5376" Action="None" />             <!-- Use SharedAccessProtocol HttpsOnly -->
      <Rule Id="CA5377" Action="None" />             <!-- Use Container Level Access Policy -->
      <Rule Id="CA5378" Action="None" />             <!-- Do not disable ServicePointManagerSecurityProtocols -->
      <Rule Id="CA5379" Action="None" />             <!-- Do Not Use Weak Key Derivation Function Algorithm -->
      <Rule Id="CA5380" Action="None" />             <!-- Do Not Add Certificates To Root Store -->
      <Rule Id="CA5381" Action="None" />             <!-- Ensure Certificates Are Not Added To Root Store -->
      <Rule Id="CA5382" Action="None" />             <!-- Use Secure Cookies In ASP.Net Core -->
      <Rule Id="CA5383" Action="None" />             <!-- Ensure Use Secure Cookies In ASP.Net Core -->
      <Rule Id="CA5384" Action="None" />             <!-- Do Not Use Digital Signature Algorithm (DSA) -->
      <Rule Id="CA5385" Action="None" />             <!-- Use Rivest–Shamir–Adleman (RSA) Algorithm With Sufficient Key Size -->
      <Rule Id="CA5386" Action="None" />             <!-- Avoid hardcoding SecurityProtocolType value -->
      <Rule Id="CA5387" Action="None" />             <!-- Do Not Use Weak Key Derivation Function With Insufficient Iteration Count -->
      <Rule Id="CA5388" Action="None" />             <!-- Ensure Sufficient Iteration Count When Using Weak Key Derivation Function -->
      <Rule Id="CA5389" Action="None" />             <!-- Do Not Add Archive Item's Path To The Target File System Path -->
      <Rule Id="CA5390" Action="None" />             <!-- Do not hard-code encryption key -->
      <Rule Id="CA5391" Action="None" />             <!-- Use antiforgery tokens in ASP.NET Core MVC controllers -->
      <Rule Id="CA5392" Action="None" />             <!-- Use DefaultDllImportSearchPaths attribute for P/Invokes -->
      <Rule Id="CA5393" Action="None" />             <!-- Do not use unsafe DllImportSearchPath value -->
      <Rule Id="CA5394" Action="None" />             <!-- Do not use insecure randomness -->
      <Rule Id="CA5395" Action="None" />             <!-- Miss HttpVerb attribute for action methods -->
      <Rule Id="CA5396" Action="None" />             <!-- Set HttpOnly to true for HttpCookie -->
      <Rule Id="CA5397" Action="None" />             <!-- Do not use deprecated SslProtocols values -->
      <Rule Id="CA5398" Action="None" />             <!-- Avoid hardcoded SslProtocols values -->
      <Rule Id="CA5399" Action="None" />             <!-- HttpClients should enable certificate revocation list checks -->
      <Rule Id="CA5400" Action="None" />             <!-- Ensure HttpClient certificate revocation list check is not disabled -->
      <Rule Id="CA5401" Action="None" />             <!-- Do not use CreateEncryptor with non-default IV -->
      <Rule Id="CA5402" Action="None" />             <!-- Use CreateEncryptor with the default IV  -->
      <Rule Id="CA5403" Action="None" />             <!-- Do not hard-code certificate -->
   </Rules>
</RuleSet>
