<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  
  <!-- 
    This item group adds any .editorconfig file present at the project root directory
    as an additional file.
  -->  
  <ItemGroup Condition="'$(SkipDefaultEditorConfigAsAdditionalFile)' != 'true' And Exists('$(MSBuildProjectDirectory)\.editorconfig')" >
    <AdditionalFiles Include="$(MSBuildProjectDirectory)\.editorconfig" />
  </ItemGroup>

  <!-- 
    This property group prevents the rule ids implemented in this package to be bumped to errors when
    the 'CodeAnalysisTreatWarningsAsErrors' = 'false'.
  -->
  <PropertyGroup Condition="'$(CodeAnalysisTreatWarningsAsErrors)' == 'false'">
    <WarningsNotAsErrors>$(WarningsNotAsErrors);CA1058;CA2153;CA3075;CA3076;CA3077;CA3147</WarningsNotAsErrors>
  </PropertyGroup>

  <!-- 
    This property group contains the rules that have been implemented in this package and therefore should be disabled for the binary FxCop.
    The format is -[Category]#[ID], e.g., -Microsoft.Design#CA1001;
  -->
  <PropertyGroup>
    <CodeAnalysisRuleSetOverrides>
      $(CodeAnalysisRuleSetOverrides);
      -Microsoft.Design#CA1058;

    </CodeAnalysisRuleSetOverrides>
  </PropertyGroup>

  <PropertyGroup>
    <Features>$(Features);flow-analysis</Features> 
  </PropertyGroup>
</Project>