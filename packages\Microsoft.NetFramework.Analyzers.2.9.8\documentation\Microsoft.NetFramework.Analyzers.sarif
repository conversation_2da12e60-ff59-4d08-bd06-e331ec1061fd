﻿{
  "$schema": "http://json.schemastore.org/sarif-1.0.0",
  "version": "1.0.0",
  "runs": [
    {
      "tool": {
        "name": "Microsoft.NetFramework.Analyzers",
        "version": "2.9.8",
        "language": "en-US"
      },
      "rules": {
        "CA1058": {
          "id": "CA1058",
          "shortDescription": "Types should not extend certain base types",
          "fullDescription": "An externally visible type extends certain base types. Use one of the alternatives.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca1058-types-should-not-extend-certain-base-types",
          "properties": {
            "category": "Design",
            "isEnabledByDefault": true,
            "typeName": "TypesShouldNotExtendCertainBaseTypesAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "PortedFromFxCop",
              "Telemetry"
            ]
          }
        },
        "CA2153": {
          "id": "CA2153",
          "shortDescription": "Do Not Catch Corrupted State Exceptions",
          "fullDescription": "Catching corrupted state exceptions could mask errors (such as access violations), resulting in inconsistent state of execution or making it easier for attackers to compromise system. Instead, catch and handle a more specific set of exception type(s) or re-throw the exception",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca2153-avoid-handling-corrupted-state-exceptions",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "DoNotCatchCorruptedStateExceptionsAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA3075": {
          "id": "CA3075",
          "shortDescription": "Insecure DTD processing in XML",
          "fullDescription": "Using XmlTextReader.Load(), creating an insecure XmlReaderSettings instance when invoking XmlReader.Create(), setting the InnerXml property of the XmlDocument and enabling DTD processing using XmlUrlResolver insecurely can lead to information disclosure. Replace it with a call to the Load() method overload that takes an XmlReader instance, use XmlReader.Create() to accept XmlReaderSettings arguments or consider explicitly setting secure values. The DataViewSettingCollectionString property of DataViewManager should always be assigned from a trusted source, the DtdProcessing property should be set to false, and the XmlResolver property should be changed to XmlSecureResolver or null. ",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca3075-insecure-dtd-processing",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "DoNotUseInsecureDtdProcessingAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA3147": {
          "id": "CA3147",
          "shortDescription": "Mark Verb Handlers With Validate Antiforgery Token",
          "fullDescription": "Missing ValidateAntiForgeryTokenAttribute on controller action {0}.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca3147-mark-verb-handlers-with-validateantiforgerytoken",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "MarkVerbHandlersWithValidateAntiforgeryTokenAnalyzer",
            "languages": [
              "C#",
              "Visual Basic"
            ]
          }
        }
      }
    },
    {
      "tool": {
        "name": "Microsoft.NetFramework.CSharp.Analyzers",
        "version": "2.9.8",
        "language": "en-US"
      },
      "rules": {
        "CA3076": {
          "id": "CA3076",
          "shortDescription": "Insecure XSLT script processing.",
          "fullDescription": "Providing an insecure XsltSettings instance and an insecure XmlResolver instance to XslCompiledTransform.Load method is potentially unsafe as it allows processing script within XSL, which on an untrusted XSL input may lead to malicious code execution. Either replace the insecure XsltSettings argument with XsltSettings.Default or an instance that has disabled document function and script execution, or replace the XmlResolver argurment with null or an XmlSecureResolver instance. This message may be suppressed if the input is known to be from a trusted source and external resource resolution from locations that are not known in advance must be supported.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca3076-insecure-xslt-script-execution",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "CSharpDoNotUseInsecureXSLTScriptExecutionAnalyzer",
            "languages": [
              "C#"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA3077": {
          "id": "CA3077",
          "shortDescription": "Insecure Processing in API Design, XmlDocument and XmlTextReader",
          "fullDescription": "Enabling DTD processing on all instances derived from XmlTextReader or  XmlDocument and using XmlUrlResolver for resolving external XML entities may lead to information disclosure. Ensure to set the XmlResolver property to null, create an instance of XmlSecureResolver when processing untrusted input, or use XmlReader.Create method with a secure XmlReaderSettings argument. Unless you need to enable it, ensure the DtdProcessing property is set to false. ",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca3077-insecure-processing-in-api-design-xml-document-and-xml-text-reader",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "CSharpDoNotUseInsecureDtdProcessingInApiDesignAnalyzer",
            "languages": [
              "C#"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        }
      }
    },
    {
      "tool": {
        "name": "Microsoft.NetFramework.VisualBasic.Analyzers",
        "version": "2.9.8",
        "language": "en-US"
      },
      "rules": {
        "CA3076": {
          "id": "CA3076",
          "shortDescription": "Insecure XSLT script processing.",
          "fullDescription": "Providing an insecure XsltSettings instance and an insecure XmlResolver instance to XslCompiledTransform.Load method is potentially unsafe as it allows processing script within XSL, which on an untrusted XSL input may lead to malicious code execution. Either replace the insecure XsltSettings argument with XsltSettings.Default or an instance that has disabled document function and script execution, or replace the XmlResolver argurment with null or an XmlSecureResolver instance. This message may be suppressed if the input is known to be from a trusted source and external resource resolution from locations that are not known in advance must be supported.",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca3076-insecure-xslt-script-execution",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "BasicDoNotUseInsecureXSLTScriptExecutionAnalyzer",
            "languages": [
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        },
        "CA3077": {
          "id": "CA3077",
          "shortDescription": "Insecure Processing in API Design, XmlDocument and XmlTextReader",
          "fullDescription": "Enabling DTD processing on all instances derived from XmlTextReader or  XmlDocument and using XmlUrlResolver for resolving external XML entities may lead to information disclosure. Ensure to set the XmlResolver property to null, create an instance of XmlSecureResolver when processing untrusted input, or use XmlReader.Create method with a secure XmlReaderSettings argument. Unless you need to enable it, ensure the DtdProcessing property is set to false. ",
          "defaultLevel": "warning",
          "helpUri": "https://docs.microsoft.com/visualstudio/code-quality/ca3077-insecure-processing-in-api-design-xml-document-and-xml-text-reader",
          "properties": {
            "category": "Security",
            "isEnabledByDefault": true,
            "typeName": "BasicDoNotUseInsecureDtdProcessingInApiDesignAnalyzer",
            "languages": [
              "Visual Basic"
            ],
            "tags": [
              "Telemetry"
            ]
          }
        }
      }
    }
  ]
}