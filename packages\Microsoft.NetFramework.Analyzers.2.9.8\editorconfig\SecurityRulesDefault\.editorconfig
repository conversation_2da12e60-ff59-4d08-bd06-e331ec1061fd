# NOTE: Requires **VS2019 16.3** or later

# Security Rules with default severity
# Description: All Security Rules with default severity. Rules with IsEnabledByDefault = false or from a different category are disabled.

# Code files
[*.{cs,vb}]

# Security Rules

# CA2153: Do Not Catch Corrupted State Exceptions
dotnet_diagnostic.CA2153.severity = warning

# CA3075: Insecure DTD processing in XML
dotnet_diagnostic.CA3075.severity = warning

# CA3076: Insecure XSLT script processing.
dotnet_diagnostic.CA3076.severity = warning

# CA3077: Insecure Processing in API Design, XmlDocument and XmlTextReader
dotnet_diagnostic.CA3077.severity = warning

# CA3147: Mark Verb Handlers With Validate Antiforgery Token
dotnet_diagnostic.CA3147.severity = warning



# Other Rules

# CA1058: Types should not extend certain base types
dotnet_diagnostic.CA1058.severity = none
