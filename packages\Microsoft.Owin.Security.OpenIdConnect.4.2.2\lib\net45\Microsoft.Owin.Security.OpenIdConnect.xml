<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Owin.Security.OpenIdConnect</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Owin.Security.Notifications.AuthorizationCodeReceivedNotification">
            <summary>
            This Notification can be used to be informed when an 'AuthorizationCode' is received over the OpenIdConnect protocol.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Notifications.AuthorizationCodeReceivedNotification.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions)">
            <summary>
            Creates a <see cref="T:Microsoft.Owin.Security.Notifications.AuthorizationCodeReceivedNotification"/>
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Notifications.AuthorizationCodeReceivedNotification.AuthenticationTicket">
            <summary>
            Gets or sets the <see cref="P:Microsoft.Owin.Security.Notifications.AuthorizationCodeReceivedNotification.AuthenticationTicket"/>
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Notifications.AuthorizationCodeReceivedNotification.Code">
            <summary>
            Gets or sets the 'code'.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Notifications.AuthorizationCodeReceivedNotification.JwtSecurityToken">
            <summary>
            Gets or sets the <see cref="P:Microsoft.Owin.Security.Notifications.AuthorizationCodeReceivedNotification.JwtSecurityToken"/> that was received in the id_token + code OpenIdConnectRequest.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Notifications.AuthorizationCodeReceivedNotification.TokenEndpointRequest">
            <summary>
            The request that will be sent to the token endpoint and is available for customization.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Notifications.AuthorizationCodeReceivedNotification.ProtocolMessage">
            <summary>
            Gets or sets the <see cref="T:Microsoft.IdentityModel.Protocols.OpenIdConnect.OpenIdConnectMessage"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Notifications.AuthorizationCodeReceivedNotification.RedirectUri">
            <summary>
            Gets or sets the 'redirect_uri'.
            </summary>
            <remarks>This is the redirect_uri that was sent in the id_token + code OpenIdConnectRequest.</remarks>
        </member>
        <member name="P:Microsoft.Owin.Security.Notifications.AuthorizationCodeReceivedNotification.TokenEndpointResponse">
            <summary>
            If the developer chooses to redeem the code themselves then they can provide the resulting tokens here. This is the
            same as calling HandleCodeRedemption. If set then the handler will not attempt to redeem the code. An IdToken
            is required if one had not been previously received in the authorization response.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Notifications.AuthorizationCodeReceivedNotification.HandledCodeRedemption">
            <summary>
            Indicates if the developer choose to handle (or skip) the code redemption. If true then the handler will not attempt
            to redeem the code. See HandleCodeRedemption and TokenEndpointResponse.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Notifications.AuthorizationCodeReceivedNotification.HandleCodeRedemption">
            <summary>
            Tells the handler to skip the code redemption process. The developer may have redeemed the code themselves, or
            decided that the redemption was not required. If tokens were retrieved that are needed for further processing then
            call one of the overloads that allows providing tokens. An IdToken is required if one had not been previously received
            in the authorization response. Calling this is the same as setting TokenEndpointResponse.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Notifications.AuthorizationCodeReceivedNotification.HandleCodeRedemption(System.String,System.String)">
            <summary>
            Tells the handler to skip the code redemption process. The developer may have redeemed the code themselves, or
            decided that the redemption was not required. If tokens were retrieved that are needed for further processing then
            call one of the overloads that allows providing tokens. An IdToken is required if one had not been previously received
            in the authorization response. Calling this is the same as setting TokenEndpointResponse.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Notifications.AuthorizationCodeReceivedNotification.HandleCodeRedemption(Microsoft.IdentityModel.Protocols.OpenIdConnect.OpenIdConnectMessage)">
            <summary>
            Tells the handler to skip the code redemption process. The developer may have redeemed the code themselves, or
            decided that the redemption was not required. If tokens were retrieved that are needed for further processing then
            call one of the overloads that allows providing tokens. An IdToken is required if one had not been previously received
            in the authorization response. Calling this is the same as setting TokenEndpointResponse.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.Notifications.TokenResponseReceivedNotification">
            <summary>
            This Notification can be used to be informed when an 'AuthorizationCode' is redeemed for tokens at the token endpoint.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Notifications.TokenResponseReceivedNotification.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions)">
            <summary>
            Creates a <see cref="T:Microsoft.Owin.Security.Notifications.TokenResponseReceivedNotification"/>
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Notifications.TokenResponseReceivedNotification.ProtocolMessage">
            <summary>
            Gets or sets the <see cref="T:Microsoft.IdentityModel.Protocols.OpenIdConnect.OpenIdConnectMessage"/> that contains the code redeemed for tokens at the token endpoint.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Notifications.TokenResponseReceivedNotification.TokenEndpointResponse">
            <summary>
            Gets or sets the <see cref="T:Microsoft.IdentityModel.Protocols.OpenIdConnect.OpenIdConnectMessage"/> that contains the tokens received after redeeming the code at the token endpoint.
            </summary>
        </member>
        <member name="F:Microsoft.Owin.Security.OpenIdConnect.OAuthConstants.CodeVerifierKey">
            <summary>
            code_verifier defined in https://tools.ietf.org/html/rfc7636
            </summary>
        </member>
        <member name="F:Microsoft.Owin.Security.OpenIdConnect.OAuthConstants.CodeChallengeKey">
            <summary>
            code_challenge defined in https://tools.ietf.org/html/rfc7636
            </summary>
        </member>
        <member name="F:Microsoft.Owin.Security.OpenIdConnect.OAuthConstants.CodeChallengeMethodKey">
            <summary>
            code_challenge_method defined in https://tools.ietf.org/html/rfc7636
            </summary>
        </member>
        <member name="F:Microsoft.Owin.Security.OpenIdConnect.OAuthConstants.CodeChallengeMethodS256">
            <summary>
            S256 defined in https://tools.ietf.org/html/rfc7636
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationDefaults">
            <summary>
            Default values related to OpenIdConnect authentication middleware
            </summary>
        </member>
        <member name="F:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationDefaults.AuthenticationType">
            <summary>
            The default value used for OpenIdConnectAuthenticationOptions.AuthenticationType
            </summary>
        </member>
        <member name="F:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationDefaults.CookiePrefix">
            <summary>
            The prefix used to provide a default OpenIdConnectAuthenticationOptions.CookieName
            </summary>
        </member>
        <member name="F:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationDefaults.Caption">
            <summary>
            The default value for OpenIdConnectAuthenticationOptions.Caption.
            </summary>
        </member>
        <member name="F:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationDefaults.Nonce">
            <summary>
            The prefix used to for the a nonce in the cookie
            </summary>
        </member>
        <member name="F:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationDefaults.RedirectUriUsedForCodeKey">
            <summary>
            The property for the RedirectUri that was used when asking for a 'authorizationCode'
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationHandler">
            <summary>
            A per-request authentication handler for the OpenIdConnectAuthenticationMiddleware.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationHandler.#ctor(Microsoft.Owin.Logging.ILogger)">
            <summary>
            Creates a new OpenIdConnectAuthenticationHandler
            </summary>
            <param name="logger"></param>
        </member>
        <member name="M:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationHandler.ApplyResponseGrantAsync">
            <summary>
            Handles Signout
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationHandler.ApplyResponseChallengeAsync">
            <summary>
            Handles SignIn
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationHandler.AuthenticateCoreAsync">
            <summary>
            Invoked to process incoming authentication messages.
            </summary>
            <returns>An <see cref="T:Microsoft.Owin.Security.AuthenticationTicket"/> if successful.</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationHandler.RedeemAuthorizationCodeAsync(Microsoft.IdentityModel.Protocols.OpenIdConnect.OpenIdConnectMessage)">
            <summary>
            Redeems the authorization code for tokens at the token endpoint.
            </summary>
            <param name="tokenEndpointRequest">The request that will be sent to the token endpoint and is available for customization.</param>
            <returns>OpenIdConnect message that has tokens inside it.</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationHandler.AddNonceToMessage(Microsoft.IdentityModel.Protocols.OpenIdConnect.OpenIdConnectMessage)">
            <summary>
            Sets <see cref="P:Microsoft.IdentityModel.Protocols.OpenIdConnect.OpenIdConnectMessage.Nonce"/> to see Options.ProtocolValidator.GenerateNonce.
            </summary>
            <param name="message">the <see cref="T:Microsoft.IdentityModel.Protocols.OpenIdConnect.OpenIdConnectMessage"/> being processed.</param>
            <remarks>Calls <see cref="M:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationHandler.RememberNonce(Microsoft.IdentityModel.Protocols.OpenIdConnect.OpenIdConnectMessage,System.String)"/> to add the nonce to a protected cookie.</remarks>
        </member>
        <member name="M:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationHandler.RememberNonce(Microsoft.IdentityModel.Protocols.OpenIdConnect.OpenIdConnectMessage,System.String)">
            <summary>
            'Remembers' the nonce associated with this message. By default the nonce added as a secure cookie.
            </summary>
            <param name="message"><see cref="T:Microsoft.IdentityModel.Protocols.OpenIdConnect.OpenIdConnectMessage"/> associated with the nonce.</param>
            <param name="nonce">the nonce to remember.</param>
            <remarks>A cookie is added with the name obtained from  <see cref="M:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationHandler.GetNonceKey(System.String)"/>.</remarks>
        </member>
        <member name="M:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationHandler.RetrieveNonce(Microsoft.IdentityModel.Protocols.OpenIdConnect.OpenIdConnectMessage)">
            <summary>
            Retrieves the 'nonce' for a message.
            </summary>
            <param name="message">the <see cref="T:Microsoft.IdentityModel.Protocols.OpenIdConnect.OpenIdConnectMessage"/> being processed.</param>
            <returns>the nonce associated with this message if found, null otherwise.</returns>
            <remarks>Looks for a cookie named: 'OpenIdConnectAuthenticationDefaults.CookiePrefix + OpenIdConnectAuthenticationDefaults.Nonce + Options.AuthenticationType' in the Resquest.</remarks>
        </member>
        <member name="M:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationHandler.GetNonceKey(System.String)">
            <summary>
            Builds a key from the nonce and constants.
            </summary>
            <param name="nonce">value generated by the runtime</param>
            <remarks>'OpenIdConnectAuthenticationDefaults.CookiePrefix + OpenIdConnectAuthenticationDefaults.Nonce + Options.AuthenticationType' is attached to the Response.</remarks>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationHandler.SaveTokens(Microsoft.Owin.Security.AuthenticationProperties,Microsoft.IdentityModel.Protocols.OpenIdConnect.OpenIdConnectMessage)">
            <summary>
            Save the tokens contained in the <see cref="T:Microsoft.IdentityModel.Protocols.OpenIdConnect.OpenIdConnectMessage"/> in the <see cref="T:System.Security.Claims.ClaimsPrincipal"/>.
            </summary>
            <param name="properties">The <see cref="T:Microsoft.Owin.Security.AuthenticationProperties"/> in which tokens are saved.</param>
            <param name="message">The OpenID Connect response.</param>
        </member>
        <member name="M:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationHandler.InvokeAsync">
            <summary>
            Calls InvokeReplyPathAsync
            </summary>
            <returns>True if the request was handled, false if the next middleware should be invoked.</returns>
        </member>
        <member name="T:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationMiddleware">
            <summary>
            OWIN middleware for obtaining identities using OpenIdConnect protocol.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationMiddleware.#ctor(Microsoft.Owin.OwinMiddleware,Owin.IAppBuilder,Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions)">
            <summary>
            Initializes a <see cref="T:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationMiddleware"/>
            </summary>
            <param name="next">The next middleware in the OWIN pipeline to invoke</param>
            <param name="app">The OWIN application</param>
            <param name="options">Configuration options for the middleware</param>
        </member>
        <member name="M:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationMiddleware.CreateHandler">
            <summary>
            Provides the <see cref="T:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler"/> object for processing authentication-related requests.
            </summary>
            <returns>An <see cref="T:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler"/> configured with the <see cref="T:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions"/> supplied to the constructor.</returns>
        </member>
        <member name="T:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationNotifications">
            <summary>
            Specifies events which the <see cref="T:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationMiddleware"></see> invokes to enable developer control over the authentication process. />
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationNotifications.#ctor">
            <summary>
            Creates a new set of notifications. Each notification has a default no-op behavior unless otherwise documented.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationNotifications.AuthenticationFailed">
            <summary>
            Invoked if exceptions are thrown during request processing. The exceptions will be re-thrown after this event unless suppressed.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationNotifications.AuthorizationCodeReceived">
            <summary>
            Invoked after security token validation if an authorization code is present in the protocol message.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationNotifications.MessageReceived">
            <summary>
            Invoked when a protocol message is first received.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationNotifications.RedirectToIdentityProvider">
            <summary>
            Invoked to manipulate redirects to the identity provider for SignIn, SignOut, or Challenge.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationNotifications.SecurityTokenReceived">
            <summary>
            Invoked with the security token that has been extracted from the protocol message.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationNotifications.SecurityTokenValidated">
            <summary>
            Invoked after the security token has passed validation and a ClaimsIdentity has been generated. Note there are additional checks after this
            event that validate other aspects of the authentication flow like the nonce.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationNotifications.TokenResponseReceived">
            <summary>
            Invoked after "authorization code" is redeemed for tokens at the token endpoint.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions">
            <summary>
            Configuration options for <see cref="T:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions"/>
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.#ctor">
            <summary>
            Initializes a new <see cref="T:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions"/>
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.#ctor(System.String)">
            <summary>
            Initializes a new <see cref="T:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions"/>
            </summary>
            <remarks>
            Defaults:
            <para>AddNonceToRequest: true.</para>
            <para>AuthenticationMode: <see cref="F:Microsoft.Owin.Security.AuthenticationMode.Active"/>.</para>
            <para>BackchannelTimeout: 1 minute.</para>
            <para>Caption: <see cref="F:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationDefaults.Caption"/>.</para>
            <para>ProtocolValidator: new <see cref="T:Microsoft.IdentityModel.Protocols.OpenIdConnect.OpenIdConnectProtocolValidator"/>.</para>
            <para>RefreshOnIssuerKeyNotFound: true</para>
            <para>ResponseMode: <see cref="F:Microsoft.IdentityModel.Protocols.OpenIdConnect.OpenIdConnectResponseMode.FormPost"/></para>
            <para>ResponseType: <see cref="F:Microsoft.IdentityModel.Protocols.OpenIdConnect.OpenIdConnectResponseType.CodeIdToken"/></para>
            <para>Scope: <see cref="F:Microsoft.IdentityModel.Protocols.OpenIdConnect.OpenIdConnectScope.OpenIdProfile"/>.</para>
            <para>TokenValidationParameters: new <see cref="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.TokenValidationParameters"/> with AuthenticationType = authenticationType.</para>
            <para>UseTokenLifetime: true.</para>
            <para>RedeemCode: false.</para>
            <para>UsePkce: true.</para>
            </remarks>
            <param name="authenticationType"> will be used to when creating the <see cref="T:System.Security.Claims.ClaimsIdentity"/> for the AuthenticationType property.</param>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.Authority">
            <summary>
            Gets or sets the Authority to use when making OpenIdConnect calls.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.CallbackPath">
            <summary>
            An optional constrained path on which to process the authentication callback.
            If not provided and RedirectUri is available, this value will be generated from RedirectUri.
            </summary>
            <remarks>If you set this value, then the <see cref="T:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationHandler"/> will only listen for posts at this address. 
            If the IdentityProvider does not post to this address, you may end up in a 401 -> IdentityProvider -> Client -> 401 -> ...</remarks>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.BackchannelCertificateValidator">
            <summary>
            Gets or sets the a pinned certificate validator to use to validate the endpoints used
            when retrieving metadata.
            </summary>
            <value>
            The pinned certificate validator.
            </value>
            <remarks>If this property is null then the default certificate checks are performed,
            validating the subject name and if the signing chain is a trusted party.</remarks>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.BackchannelHttpHandler">
            <summary>
            The HttpMessageHandler used to retrieve metadata.
            This cannot be set at the same time as BackchannelCertificateValidator unless the value
            is a WebRequestHandler.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.BackchannelTimeout">
            <summary>
            Gets or sets the timeout when using the backchannel to make an http call.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.Backchannel">
            <summary>
            Used to communicate with the remote identity provider.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.Caption">
            <summary>
            Get or sets the text that the user can display on a sign in user interface.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.ClientId">
            <summary>
            Gets or sets the 'client_id'.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.ClientSecret">
            <summary>
            Gets or sets the 'client_secret'.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.Configuration">
            <summary>
            Configuration provided directly by the developer. If provided, then MetadataAddress and the Backchannel properties
            will not be used. This information should not be updated during request processing.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.RequireHttpsMetadata">
            <summary>
            Gets or sets if HTTPS is required for the metadata address or authority.
            The default is true. This should be disabled only in development environments.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.MetadataAddress">
            <summary>
            Gets or sets the discovery endpoint for obtaining metadata
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.ConfigurationManager">
            <summary>
            Responsible for retrieving, caching, and refreshing the configuration from metadata.
            If not provided, then one will be created using the MetadataAddress and Backchannel properties.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.RefreshOnIssuerKeyNotFound">
            <summary>
            Gets or sets if a metadata refresh should be attempted after a SecurityTokenSignatureKeyNotFoundException. This allows for automatic
            recovery in the event of a signature key rollover. This is enabled by default.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.Notifications">
            <summary>
            Gets or sets the <see cref="T:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationNotifications"/> to notify when processing OpenIdConnect messages.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.ProtocolValidator">
            <summary>
            Gets or sets the <see cref="T:Microsoft.IdentityModel.Protocols.OpenIdConnect.OpenIdConnectProtocolValidator"/> that is used ensure the 'id_token' received
            is valid per: http://openid.net/specs/openid-connect-core-1_0.html#IDTokenValidation 
            </summary>
            <exception cref="T:System.ArgumentNullException">if 'value' is null.</exception>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.PostLogoutRedirectUri">
            <summary>
            Gets or sets the 'post_logout_redirect_uri'
            </summary>
            <remarks>This is sent to the OP as the redirect for the user-agent.</remarks>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.RedirectUri">
            <summary>
            Gets or sets the 'redirect_uri'.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.Resource">
            <summary>
            Gets or sets the 'resource'.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.ResponseMode">
            <summary>
            Gets or sets the 'response_mode'.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.ResponseType">
            <summary>
            Gets or sets the 'response_type'.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.Scope">
            <summary>
            Gets or sets the 'scope'.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.SignInAsAuthenticationType">
            <summary>
            Gets or sets the AuthenticationType used when creating the <see cref="T:System.Security.Claims.ClaimsIdentity"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.StateDataFormat">
            <summary>
            Gets or sets the type used to secure data handled by the middleware.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.SecurityTokenValidator">
            <summary>
            Gets or sets the <see cref="T:Microsoft.IdentityModel.Tokens.ISecurityTokenValidator"/> used to validate identity tokens.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.TokenValidationParameters">
            <summary>
            Gets or sets the TokenValidationParameters
            </summary>
            <remarks>Contains the types and definitions required for validating a token.</remarks>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.UseTokenLifetime">
            <summary>
            Indicates that the authentication session lifetime (e.g. cookies) should match that of the authentication token.
            If the token does not provide lifetime information then normal session lifetimes will be used.
            This is enabled by default.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.SaveTokens">
            <summary>
            Defines whether access and refresh tokens should be stored in the
            <see cref="T:Microsoft.Owin.Security.AuthenticationProperties"/> after a successful authorization.
            This property is set to <c>false</c> by default to reduce
            the size of the final authentication cookie.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.CookieManager">
            <summary>
            An abstraction for reading and setting cookies during the authentication process.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.RedeemCode">
            <summary>
            When set to <c>true</c> the authorization code will be redeemed for tokens at the token endpoint.
            This property is set to <c>false</c> by default.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.UsePkce">
            <summary>
            Enables or disables the use of the Proof Key for Code Exchange (PKCE) standard.
            This only applies when the <see cref="P:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions.ResponseType"/> is set to <see cref="F:Microsoft.IdentityModel.Protocols.OpenIdConnect.OpenIdConnectResponseType.Code"/>.
            See https://tools.ietf.org/html/rfc7636.
            The default value is `true`.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.OpenIdConnect.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.Resources.ArgsException_BackchallelLessThanZero">
            <summary>
              Looks up a localized string similar to BackchannelTimeout cannot be less or equal to TimeSpan.Zero..
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.Resources.Exception_OpenIdConnectMessageError">
            <summary>
              Looks up a localized string similar to &quot;OpenIdConnectMessage.Error was not null, indicating an error. Error: &apos;{0}&apos;. Error_Description (may be empty): &apos;{1}&apos;. Error_Uri (may be empty): &apos;{2}&apos;.&quot;.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.Resources.Exception_RedirectUri_LogoutQueryString_IsNotWellFormed">
            <summary>
              Looks up a localized string similar to OIDC_20001: The query string for Logout is not a well formed URI. The runtime cannot redirect. Redirect uri: &apos;{0}&apos;..
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.Resources.Exception_ValidatorHandlerMismatch">
            <summary>
              Looks up a localized string similar to An ICertificateValidator cannot be specified at the same time as an HttpMessageHandler unless it is a WebRequestHandler..
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.Resources.UnableToValidateToken">
            <summary>
              Looks up a localized string similar to Unable to validate the &apos;id_token&apos;, no suitable ISecurityTokenValidator was found for: &apos;{0}&apos;.&quot;.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OpenIdConnect.Resources.ValidatedSecurityTokenNotJwt">
            <summary>
              Looks up a localized string similar to The Validated Security Token must be of type JwtSecurityToken, but instead its type is: &apos;{0}&apos;..
            </summary>
        </member>
        <member name="T:Owin.OpenIdConnectAuthenticationExtensions">
            <summary>
            Extension methods for using <see cref="T:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationMiddleware"/>
            </summary>
        </member>
        <member name="M:Owin.OpenIdConnectAuthenticationExtensions.UseOpenIdConnectAuthentication(Owin.IAppBuilder,System.String,System.String)">
            <summary>
            Adds the <see cref="T:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationMiddleware"/> into the OWIN runtime.
            </summary>
            <param name="app">The <see cref="T:Owin.IAppBuilder"/> passed to the configuration method</param>
            <param name="clientId">The application identifier.</param>
            <param name="metadataAddress">The discovery endpoint for obtaining metadata.</param>
            <returns>The updated <see cref="T:Owin.IAppBuilder"/></returns>
        </member>
        <member name="M:Owin.OpenIdConnectAuthenticationExtensions.UseOpenIdConnectAuthentication(Owin.IAppBuilder,Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions)">
            <summary>
            Adds the <see cref="T:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationMiddleware"/> into the OWIN runtime.
            </summary>
            <param name="app">The <see cref="T:Owin.IAppBuilder"/> passed to the configuration method</param>
            <param name="openIdConnectOptions">A <see cref="T:Microsoft.Owin.Security.OpenIdConnect.OpenIdConnectAuthenticationOptions"/> contains settings for obtaining identities using the OpenIdConnect protocol.</param>
            <returns>The updated <see cref="T:Owin.IAppBuilder"/></returns>
        </member>
    </members>
</doc>
