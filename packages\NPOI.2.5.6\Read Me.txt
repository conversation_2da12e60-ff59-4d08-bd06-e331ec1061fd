================
* Introduction *
================
This project is the .NET port of POI project. With NPOI, you can read/write Excel and Word files easily. It has a wide application. 
For example, you can use it a to generate a Excel report without Microsoft Office suite installed on your 
server and more efficient than call Microsoft Excel ActiveX at background; you can also use it to extract 
text from Office documents to help you implement full-text indexing feature (most of time this feature is 
used to create search engines).

=========================
* Nissl Introduction *
=========================
NPOI was used to be maintained by Neuzilla ( <PERSON>'s first startup company).  After Neuzilla is closed in 2018, Nissl Lab maintains NPOI. 
Nissl Lab is a NGO org created by Nissl LLC. The trademake NPOI is hold by Nissl in order to avoid abuse. 

==================
*  NPOI Source Code *
==================
https://github.com/nissl-lab/npoi/ 


==================
* LICENSE COMPLIANCE *
==================
You are NOT allowed to remove NPOI logo or Nissl/Neuzilla brand (even text). This is a must of Apache license. This shows basic respects to the previous contributors of NPOI. 
You must state that you are using NPOI no matter you are creating a commercial product or an open source project. 

======================
* System Requirement *
======================
VS2015 and above
medium trust environment in ASP.NET

=================
*  Nissl in social network *
=================
Linkedin: https://www.linkedin.com/company/nissl
Github: https://github.com/nissl-lab

================
* Team Members *
================
Tony Qu - coodinator, developer
-------------------------------
From: Shanghai, China
Participated since Sep, 2008
Time Zone: GMT+8

--------------------------------------------------------------------------------
NPOI 1.2.1
--------------------------------------------------------------------------------
Huseyin Turfekcilerli - developer
--------------------
From: Istanbul, Turkey
Participated since Nov, 2008
Time Zone: GMT+2
Main Contribution: POIFS Browser 1.0

aTao Xiang - Technical writer
-----------
From: China
Participated since Aug, 2009
Time Zone: GMT+8
Main Contribution: NPOI 1.2 Chinese Tutorial

--------------------------------------------------------------------------------
NPOI 1.2.3 - NPOI 1.2.5
--------------------------------------------------------------------------------
Antony - developer
------------------------------
From: Guangzhou, China
Time Zone: GMT+8
Main Contribution: main code,bug fixing ,Excel2Html, Word2Html

Christian Leutloff - developer
------------------------------
From: Germany
Time Zone: GMT+1
Main Contribution: fix FXCop warnings, OOXML

Jeff Stedfast - developer
------------------------------
From: Cambridge, MA, USA
Time Zone: GMT
Main contribution: Mono version of NPOI, naming conversion

Leon Wang - developer
-----------------------------
From: Xi'an, China
Time Zone: GMT+8
Main contribution: NPOIFS/POIFS

NPOI 2.0
--------------------------------------------------------------------------------
Tony Qu - chief developer
-------------------------------
From: Shanghai, China
Time Zone: GMT+8
Main Contribution: OpenXml4Net, OpenXmlFormats, NPOI.XSSF, NPOI.XWPF implementation

Antony - developer
------------------------------
From: Guangzhou, China
Time Zone: GMT+8
Main Contribution: XWPF implementation, OpenXmlFormats

Christian Leutloff - developer
------------------------------
From: Germany
Time Zone: GMT+1
Main Contribution: OpenXmlFormats, Excel 2007 comment read

Jeff Stedfast - developer
------------------------------
From: Cambridge, MA, USA
Time Zone: GMT
Main contribution: Mono version of NPOI, enumeration changes and compilation optimization

Paul Kratt - developer
------------------------------
From: Greater Milwaukee Area, Wisconsin, USA
Time Zone: GMT
Main contribution: CopyTo, CopySheet functions in HSSFSheet

NPOI 2.1
--------------------------------------------------------------------------------
Tony Qu - main contributor
Antony - fix some poi bugs

Phil Rykoff - developer
------------------------------
Main contribution: XSSF bug fixing and feature implement

NPOI 2.2 ~ 2.5.2
--------------------------------------------------------------------------------
Antony
Tony Qu
