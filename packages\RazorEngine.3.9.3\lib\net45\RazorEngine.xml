<?xml version="1.0"?>
<doc>
    <assembly>
        <name>RazorEngine</name>
    </assembly>
    <members>
        <member name="T:RazorEngine.AttributeValue">
            <summary>
            Razor Html Attribute value
            </summary>
        </member>
        <member name="M:RazorEngine.AttributeValue.#ctor(RazorEngine.PositionTagged{System.String},RazorEngine.PositionTagged{System.Object},System.Boolean)">
            <summary>
            Creates a new Razor Html Attribute value.
            </summary>
            <param name="prefix"></param>
            <param name="value"></param>
            <param name="literal"></param>
        </member>
        <member name="P:RazorEngine.AttributeValue.Prefix">
            <summary>
            The prefix of the attribute.
            </summary>
        </member>
        <member name="P:RazorEngine.AttributeValue.Value">
            <summary>
            The Value of the attribute.
            </summary>
        </member>
        <member name="P:RazorEngine.AttributeValue.Literal">
            <summary>
            Indicates whether the attribute is a lital.
            </summary>
        </member>
        <member name="M:RazorEngine.AttributeValue.FromTuple(System.Tuple{System.Tuple{System.String,System.Int32},System.Tuple{System.Object,System.Int32},System.Boolean})">
            <summary>
            Convert from a tuple.
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.AttributeValue.FromTuple(System.Tuple{System.Tuple{System.String,System.Int32},System.Tuple{System.String,System.Int32},System.Boolean})">
            <summary>
            Convert from a tuple.
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.AttributeValue.op_Implicit(System.Tuple{System.Tuple{System.String,System.Int32},System.Tuple{System.Object,System.Int32},System.Boolean})~RazorEngine.AttributeValue">
            <summary>
            Convert from a tuple
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.AttributeValue.op_Implicit(System.Tuple{System.Tuple{System.String,System.Int32},System.Tuple{System.String,System.Int32},System.Boolean})~RazorEngine.AttributeValue">
            <summary>
            Convert from a tuple.
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="T:RazorEngine.Compilation.CompilerServiceBase">
            <summary>
            Provides a base implementation of a compiler service.
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.CompilerServiceBase.DynamicTemplateNamespace">
            <summary>
            The namespace for dynamic templates.
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.CompilerServiceBase.ClassNamePrefix">
            <summary>
            A prefix for all dynamically created classes.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.CompilerServiceBase.ParserBaseCreator">
            <summary>
            This class only exists because we cannot use Func&lt;ParserBase&gt; in non security-critical class.
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.CompilerServiceBase.ParserBaseCreator.creator">
            <summary>
            The parser creator.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServiceBase.ParserBaseCreator.#ctor(System.Func{System.Web.Razor.Parser.ParserBase})">
            <summary>
            Create a new ParserBaseCreator instance.
            </summary>
            <param name="creator">The parser creator.</param>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServiceBase.ParserBaseCreator.Create">
            <summary>
            Execute the given delegate.
            </summary>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServiceBase.#ctor(System.Web.Razor.RazorCodeLanguage,RazorEngine.Compilation.CompilerServiceBase.ParserBaseCreator)">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Compilation.CompilerServiceBase"/>
            </summary>
            <param name="codeLanguage">The code language.</param>
            <param name="markupParserFactory">The markup parser factory.</param>
        </member>
        <member name="P:RazorEngine.Compilation.CompilerServiceBase.CodeInspectors">
            <summary>
            Gets or sets the set of code inspectors.
            </summary>
        </member>
        <member name="P:RazorEngine.Compilation.CompilerServiceBase.ReferenceResolver">
            <summary>
            Gets or sets the assembly resolver.
            </summary>
        </member>
        <member name="P:RazorEngine.Compilation.CompilerServiceBase.CodeLanguage">
            <summary>
            Gets the code language.
            </summary>
        </member>
        <member name="P:RazorEngine.Compilation.CompilerServiceBase.Debug">
            <summary>
            Gets or sets whether the compiler service is operating in debug mode.
            </summary>
        </member>
        <member name="P:RazorEngine.Compilation.CompilerServiceBase.DisableTempFileLocking">
            <summary>
            Gets or sets whether the compiler should load assemblies with Assembly.Load(byte[])
            to prevent files from being locked.
            </summary>
        </member>
        <member name="P:RazorEngine.Compilation.CompilerServiceBase.MarkupParserFactory">
            <summary>
            Gets the markup parser.
            </summary>
        </member>
        <member name="P:RazorEngine.Compilation.CompilerServiceBase.SourceFileExtension">
            <summary>
            Extension of a source file without dot ("cs" for C# files or "vb" for VB.NET files).
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServiceBase.GetDefaultTemporaryDirectory">
            <summary>
            Tries to create and return a unique temporary directory.
            </summary>
            <returns>the (already created) temporary directory</returns>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServiceBase.GetTemporaryDirectory">
            <summary>
            Returns a new temporary directory ready to be used.
            This can be overwritten in subclases to change the created directories.
            </summary>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServiceBase.BuildTypeName(System.Type,System.Type)">
            <summary>
            Builds a type name for the specified template type.
            </summary>
            <param name="templateType">The template type.</param>
            <param name="modelType">The model type.</param>
            <returns>The string type name (including namespace).</returns>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServiceBase.CompileType(RazorEngine.Compilation.TypeContext)">
            <summary>
            Compiles the type defined in the specified type context.
            </summary>
            <param name="context">The type context which defines the type to compile.</param>
            <returns>The compiled type.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServiceBase.CreateHost(System.Type,System.Type,System.String)">
            <summary>
            Creates a <see cref="T:RazorEngine.Compilation.RazorEngineHost"/> used for class generation.
            </summary>
            <param name="templateType">The template base type.</param>
            <param name="modelType">The model type.</param>
            <param name="className">The class name.</param>
            <returns>An instance of <see cref="T:RazorEngine.Compilation.RazorEngineHost"/>.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServiceBase.GetCodeCompileUnit(System.String,RazorEngine.Templating.ITemplateSource,System.Collections.Generic.ISet{System.String},System.Type,System.Type)">
            <summary>
            Gets the source code from Razor for the given template.
            </summary>
            <param name="className">The class name.</param>
            <param name="template">The template to compile.</param>
            <param name="namespaceImports">The set of namespace imports.</param>
            <param name="templateType">The template type.</param>
            <param name="modelType">The model type.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServiceBase.GetAssemblyName(RazorEngine.Compilation.TypeContext)">
            <summary>
            Helper method to generate the prefered assembly name.
            </summary>
            <param name="context">the context of the current compilation.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServiceBase.InspectSource(System.Web.Razor.GeneratorResults,RazorEngine.Compilation.TypeContext)">
            <summary>
            Inspects the source and returns the source code.
            </summary>
            <param name="results"></param>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServiceBase.GetCodeCompileUnit(RazorEngine.Compilation.TypeContext)">
            <summary>
            Gets the code compile unit used to compile a type.
            </summary>
            <param name="context"></param>
            <returns>A <see cref="T:System.CodeDom.CodeCompileUnit"/> used to compile a type.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServiceBase.GetGeneratorResult(RazorEngine.Compilation.RazorEngineHost,RazorEngine.Compilation.TypeContext)">
            <summary>
            Gets the generator result.
            </summary>
            <param name="host">The razor engine host.</param>
            <param name="context">The compile context.</param>
            <returns>The generator result.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServiceBase.GetNamespaces(System.Type,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Gets any required namespace imports.
            </summary>
            <param name="templateType">The template type.</param>
            <param name="otherNamespaces">The requested set of namespace imports.</param>
            <returns>A set of namespace imports.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServiceBase.IncludeAssemblies">
            <summary>
            Returns a set of assemblies that must be referenced by the compiled template.
            </summary>
            <returns>The set of assemblies.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServiceBase.IncludeReferences">
            <summary>
            Returns a set of references that must be referenced by the compiled template.
            </summary>
            <returns>The set of references.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServiceBase.GetAllReferences(RazorEngine.Compilation.TypeContext)">
            <summary>
            Helper method to get all references for the given compilation.
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServiceBase.Inspect(System.CodeDom.CodeCompileUnit)">
            <summary>
            Inspects the generated code compile unit.
            </summary>
            <param name="unit">The code compile unit.</param>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServiceBase.Dispose">
            <summary>
            Disposes the current instance.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServiceBase.Dispose(System.Boolean)">
            <summary>
            Disposes the current instance via the disposable pattern.
            </summary>
            <param name="disposing">true when Dispose() was called manually.</param>
        </member>
        <member name="T:RazorEngine.Compilation.CompilerServiceBuilder">
            <summary>
            Manages creation of <see cref="T:RazorEngine.Compilation.ICompilerService"/> instances.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServiceBuilder.SetCompilerServiceFactory(RazorEngine.Compilation.ICompilerServiceFactory)">
            <summary>
            Sets the <see cref="T:RazorEngine.Compilation.ICompilerServiceFactory"/> used to create compiler service instances.
            </summary>
            <param name="factory">The compiler service factory to use.</param>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServiceBuilder.GetCompilerService(RazorEngine.Language)">
            <summary>
            Gets the <see cref="T:RazorEngine.Compilation.ICompilerService"/> for the specfied language.
            </summary>
            <param name="language">The code language.</param>
            <returns>The compiler service instance.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServiceBuilder.GetDefaultCompilerService">
            <summary>
            Gets the <see cref="T:RazorEngine.Compilation.ICompilerService"/> for the default <see cref="T:RazorEngine.Language"/>.
            </summary>
            <returns>The compiler service instance.</returns>
        </member>
        <member name="T:RazorEngine.Compilation.CompilerServicesUtility">
            <summary>
            Provides service methods for compilation.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServicesUtility.IsAnonymousType(System.Type)">
            <summary>
            Determines if the specified type is an anonymous type.
            </summary>
            <param name="type">The type to check.</param>
            <returns>True if the type is an anonymous type, otherwise false.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServicesUtility.IsAnonymousTypeRecursive(System.Type)">
            <summary>
            Checks if the given type is a anonymous type or a generic type containing a 
            reference type as generic type argument
            </summary>
            <param name="t">the type to check</param>
            <returns>true when there exists a reference to an anonymous type.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServicesUtility.IsDynamicType(System.Type)">
            <summary>
            Determines if the specified type is a dynamic type.
            </summary>
            <param name="type">The type to check.</param>
            <returns>True if the type is an anonymous type, otherwise false.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServicesUtility.IsIteratorType(System.Type)">
            <summary>
            Determines if the specified type is a compiler generated iterator type.
            </summary>
            <param name="type">The type to check.</param>
            <returns>True if the type is an iterator type, otherwise false.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServicesUtility.GenerateClassName">
            <summary>
            Generates a random class name.
            </summary>
            <returns>A new random class name.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServicesUtility.GetConstructors(System.Type)">
            <summary>
            Gets the public or protected constructors of the specified type.
            </summary>
            <param name="type">The target type.</param>
            <returns>An enumerable of constructors.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServicesUtility.ResolveCSharpTypeName(System.Type)">
            <summary>
            Resolves the C# name of the given type.
            </summary>
            <param name="type">the type to emit.</param>
            <returns>The full type name or dynamic if the type is an instance of an dynamic type.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServicesUtility.ResolveVBTypeName(System.Type)">
            <summary>
            Resolves the VB.net name of the given type.
            </summary>
            <param name="type">the type to emit.</param>
            <returns>The full type name or Object if the type is an instance of an dynamic type.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServicesUtility.GetIteratorInterface(System.Type)">
            <summary>
            Gets the Iterator type for the given compiler generated iterator.
            </summary>
            <param name="type">The target type.</param>
            <returns>Tries to return IEnumerable of T if possible.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServicesUtility.GetLoadedAssemblies">
            <summary>
            Gets an enumerable of all assemblies loaded in the current domain.
            </summary>
            <returns>An enumerable of loaded assemblies.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServicesUtility.CSharpGetRawTypeName(System.Type)">
            <summary>
            Return the raw type name (including namespace) without any generic arguments.
            Returns the typename in a way it can be used in C# code.
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServicesUtility.VBGetRawTypeName(System.Type)">
            <summary>
            Return the raw type name (including namespace) without any generic arguments.
            Returns the typename in a way it can be used in VB.net code.
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServicesUtility.CSharpCreateGenericType(System.Type,System.String,System.Boolean)">
            <summary>
            Return the raw type name (including namespace) with the given modelTypeName as generic argument (if applicable).
            Returns the typename in a way it can be used in C# code.
            </summary>
            <param name="templateType"></param>
            <param name="modelTypeName"></param>
            <param name="throwWhenNotGeneric"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.CompilerServicesUtility.VBCreateGenericType(System.Type,System.String,System.Boolean)">
            <summary>
            Return the raw type name (including namespace) with the given modelTypeName as generic argument (if applicable).
            Returns the typename in a way it can be used in VB.net code.
            </summary>
            <param name="templateType"></param>
            <param name="modelTypeName"></param>
            <param name="throwWhenNotGeneric"></param>
            <returns></returns>
        </member>
        <member name="T:RazorEngine.Compilation.CrossAppDomainCleanUp">
            <summary>
            Helper class to cleanup locked files and folders after the current AppDomain has been unloaded.
            (Because of locking they can't be deleted while the current AppDomain is up, see https://github.com/Antaris/RazorEngine/issues/244)
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.CrossAppDomainCleanUp.IPrinter">
            <summary>
            Simple helper object to print status messages across appdomains.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.CrossAppDomainCleanUp.IPrinter.Print(System.String,System.Object[])">
            <summary>
            Print a status message
            </summary>
            <param name="format"></param>
            <param name="args"></param>
        </member>
        <member name="M:RazorEngine.Compilation.CrossAppDomainCleanUp.IPrinter.PrintError(System.String,System.Object[])">
            <summary>
            Print a error message.
            </summary>
            <param name="format"></param>
            <param name="args"></param>
        </member>
        <member name="T:RazorEngine.Compilation.CrossAppDomainCleanUp.Printer">
            <summary>
            A simple Printer which wrints in stdout and stderr.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.CrossAppDomainCleanUp.Printer.#ctor">
            <summary>
            Creates a new simple printer.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.CrossAppDomainCleanUp.ErrorOnlyPrinter">
            <summary>
            A simple printer writing only in stderr.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.CrossAppDomainCleanUp.ErrorOnlyPrinter.#ctor">
            <summary>
            Create a new ErrorOnlyPrinter.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.CrossAppDomainCleanUp.EmptyPrinter">
            <summary>
            A new empty printer, which prints nothing.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.CrossAppDomainCleanUp.EmptyPrinter.#ctor">
            <summary>
            Creates a new EmptyPrinter
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.CrossAppDomainCleanUp.CleanupHelper">
            <summary>
            Helper class to communicate with the Cleanup AppDomain.
            Inits the cleanup AppDomain (AssemblyResolve) and registers items to delete.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.CrossAppDomainCleanUp.CleanupHelper.SubscribeHelper">
            <summary>
            A helper to be able to subscribe to the DomainUnload event.
            Additionally we use this object to check if the printer lives in the wrong appdomain 
            (which would lead to an application crash).
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.CrossAppDomainCleanUp.CleanupHelper.SubscribeHelper.Subscribe(RazorEngine.Compilation.CrossAppDomainCleanUp.CleanupHelper)">
            <summary>
            Subscribe to the DomainUnload event and call the helper back.
            </summary>
            <param name="helper"></param>
        </member>
        <member name="M:RazorEngine.Compilation.CrossAppDomainCleanUp.CleanupHelper.SubscribeHelper.CheckPrinter(RazorEngine.Compilation.CrossAppDomainCleanUp.IPrinter)">
            <summary>
            Check if the given printer object is valid.
            </summary>
            <param name="printer"></param>
        </member>
        <member name="M:RazorEngine.Compilation.CrossAppDomainCleanUp.CleanupHelper.IsUnloaded(System.AppDomain)">
            <summary>
            Check if the given AppDomain is unloaded.
            </summary>
            <param name="domain"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.CrossAppDomainCleanUp.CleanupHelper.Init(System.AppDomain,RazorEngine.Compilation.CrossAppDomainCleanUp.IPrinter)">
            <summary>
            Init the current helper object with the given AppDomain.
            </summary>
            <param name="domain"></param>
            <param name="printer"></param>
        </member>
        <member name="M:RazorEngine.Compilation.CrossAppDomainCleanUp.CleanupHelper.RegisterCleanupPath(System.String)">
            <summary>
            Register the given path for cleanup.
            </summary>
            <param name="path"></param>
        </member>
        <member name="M:RazorEngine.Compilation.CrossAppDomainCleanUp.CleanupHelper.SetupDomain">
            <summary>
            Setup AssemblyResolve.
            </summary>
        </member>
        <member name="P:RazorEngine.Compilation.CrossAppDomainCleanUp.CurrentPrinter">
            <summary>
            Gets or sets the printer that is used by default when creating new CrossAppDomainCleanUp objects.
            Do not use this property unless you know what you are doing.
            Settings this to a serializable object is safe, however setting this to a marshalbyrefobject
            can lead to errors if the object lives in the domain that is watched for unloading
            </summary>
        </member>
        <member name="P:RazorEngine.Compilation.CrossAppDomainCleanUp.CurrentCleanup">
            <summary>
            A cleanup instance for the current AppDomain
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.CrossAppDomainCleanUp.FromAssembly(System.Reflection.Assembly)">
            <summary>
            Get the StrongName of the given assembly.
            </summary>
            <param name="ass"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.CrossAppDomainCleanUp.RegisterCleanup(System.String,System.Boolean)">
            <summary>
            A helper method to register items to cleanup for the current AppDomain.
            </summary>
            <param name="item"></param>
            <param name="throwOnDefault">Throw an exception when we are on the default AppDomain</param>
        </member>
        <member name="M:RazorEngine.Compilation.CrossAppDomainCleanUp.#ctor(System.AppDomain,RazorEngine.Compilation.CrossAppDomainCleanUp.IPrinter)">
            <summary>
            Create a new CrossAppDomainCleanUp object for the current AppDomain.
            </summary>
            <param name="toWatch">the appDomain to watch for unload.</param>
            <param name="printer"></param>
        </member>
        <member name="T:RazorEngine.Compilation.CrossAppDomainCleanUp.InitHelper">
            <summary>
            This class only exists because we can't use a simple lambda.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.CrossAppDomainCleanUp.RegisterCleanupHelper">
            <summary>
            This class only exists because we can't use a simple lambda.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.CrossAppDomainCleanUp.RegisterCleanupPath(System.String)">
            <summary>
            Register the given path for cleanup.
            </summary>
            <param name="path"></param>
        </member>
        <member name="M:RazorEngine.Compilation.CrossAppDomainCleanUp.Dispose">
            <summary>
            Dispose the current instance.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.CSharp.CSharpCodeParser">
            <summary>
            Defines a code parser that supports the C# syntax.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.CSharp.CSharpCodeParser.#ctor">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Compilation.CSharp.CSharpCodeParser"/>.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.CSharp.CSharpCodeParser.InheritsDirective">
            <summary>
            Parses the inherits statement.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.CSharp.CSharpCodeParser.ModelDirective">
            <summary>
            Parses the model statement.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.CSharp.CSharpDirectCompilerService">
            <summary>
            Defines a direct compiler service for the C# syntax.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.CSharp.CSharpDirectCompilerService.#ctor(System.Boolean,System.Func{System.Web.Razor.Parser.ParserBase})">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Compilation.CSharp.CSharpDirectCompilerService"/>.
            </summary>
            <param name="strictMode">Specifies whether the strict mode parsing is enabled.</param>
            <param name="markupParserFactory">The markup parser factory to use.</param>
        </member>
        <member name="P:RazorEngine.Compilation.CSharp.CSharpDirectCompilerService.SourceFileExtension">
            <summary>
            Extension of a source file without dot ("cs" for C# files or "vb" for VB.NET files).
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.CSharp.CSharpDirectCompilerService.IncludeReferences">
            <summary>
            Returns a set of assemblies that must be referenced by the compiled template.
            </summary>
            <returns>The set of assemblies.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.CSharp.CSharpDirectCompilerService.BuildTypeName(System.Type,System.Type)">
            <summary>
            Builds a type name for the specified template type.
            </summary>
            <param name="templateType">The template type.</param>
            <param name="modelType">The model type.</param>
            <returns>The string type name (including namespace).</returns>
        </member>
        <member name="T:RazorEngine.Compilation.CSharp.CSharpRazorCodeGenerator">
            <summary>
            Defines a code generator that supports C# syntax.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.CSharp.CSharpRazorCodeGenerator.#ctor(System.String,System.String,System.String,System.Web.Razor.RazorEngineHost,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Compilation.CSharp.CSharpRazorCodeGenerator"/> class.
            </summary>
            <param name="className">Name of the class.</param>
            <param name="rootNamespaceName">Name of the root namespace.</param>
            <param name="sourceFileName">Name of the source file.</param>
            <param name="host">The host.</param>
            <param name="strictMode">Flag to specify that this generator is running in struct mode.</param>
        </member>
        <member name="P:RazorEngine.Compilation.CSharp.CSharpRazorCodeGenerator.StrictMode">
            <summary>
            Gets whether the code generator is running in strict mode.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.CSharp.CSharpRazorCodeGenerator.VisitError(System.Web.Razor.Parser.SyntaxTree.RazorError)">
            <summary>
            Visits an error generated through parsing.
            </summary>
            <param name="err">The error that was generated.</param>
        </member>
        <member name="T:RazorEngine.Compilation.CSharp.CSharpRazorCodeLanguage">
            <summary>
            Provides a razor code language that supports the C# language.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.CSharp.CSharpRazorCodeLanguage.#ctor(System.Boolean)">
            <summary>
            Initialises a new instance
            </summary>
            <param name="strictMode">Flag to determine whether strict mode is enabled.</param>
        </member>
        <member name="P:RazorEngine.Compilation.CSharp.CSharpRazorCodeLanguage.StrictMode">
            <summary>
            Gets whether strict mode is enabled.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.CSharp.CSharpRazorCodeLanguage.CreateCodeGenerator(System.String,System.String,System.String,System.Web.Razor.RazorEngineHost)">
            <summary>
            Creates the code generator.
            </summary>
            <param name="className">Name of the class.</param>
            <param name="rootNamespaceName">Name of the root namespace.</param>
            <param name="sourceFileName">Name of the source file.</param>
            <param name="host">The host.</param>
        </member>
        <member name="T:RazorEngine.Compilation.DefaultCompilerServiceFactory">
            <summary>
            Provides a default implementation of a <see cref="T:RazorEngine.Compilation.ICompilerServiceFactory"/>.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.DefaultCompilerServiceFactory.CreateCompilerService(RazorEngine.Language)">
            <summary>
            Creates a <see cref="T:RazorEngine.Compilation.ICompilerService"/> that supports the specified language.
            </summary>
            <param name="language">The <see cref="T:RazorEngine.Language"/>.</param>
            <returns>An instance of <see cref="T:RazorEngine.Compilation.ICompilerService"/>.</returns>
        </member>
        <member name="T:RazorEngine.Compilation.DirectCompilerServiceBase">
            <summary>
            Provides a base implementation of a direct compiler service.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.DirectCompilerServiceBase.#ctor(System.Web.Razor.RazorCodeLanguage,System.CodeDom.Compiler.CodeDomProvider,System.Func{System.Web.Razor.Parser.ParserBase})">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Compilation.DirectCompilerServiceBase"/>.
            </summary>
            <param name="codeLanguage">The razor code language.</param>
            <param name="codeDomProvider">The code dom provider used to generate code.</param>
            <param name="markupParserFactory">The markup parser factory.</param>
        </member>
        <member name="P:RazorEngine.Compilation.DirectCompilerServiceBase.CodeDomProvider">
            <summary>
            The underlaying CodeDomProvider instance.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.DirectCompilerServiceBase.Compile(RazorEngine.Compilation.TypeContext)">
            <summary>
            Creates the compile results for the specified <see cref="T:RazorEngine.Compilation.TypeContext"/>.
            </summary>
            <param name="context">The type context.</param>
            <returns>The compiler results.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.DirectCompilerServiceBase.InspectSource(System.Web.Razor.GeneratorResults,RazorEngine.Compilation.TypeContext)">
            <summary>
            Inspects the GeneratorResults and returns the source code.
            </summary>
            <param name="results"></param>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.DirectCompilerServiceBase.GenerateConstructors(System.Collections.Generic.IEnumerable{System.Reflection.ConstructorInfo},System.CodeDom.CodeTypeDeclaration)">
            <summary>
            Generates any required contructors for the specified type.
            </summary>
            <param name="constructors">The set of constructors.</param>
            <param name="codeType">The code type declaration.</param>
        </member>
        <member name="M:RazorEngine.Compilation.DirectCompilerServiceBase.CompileType(RazorEngine.Compilation.TypeContext)">
            <summary>
            Compiles the type defined in the specified type context.
            </summary>
            <param name="context">The type context which defines the type to compile.</param>
            <returns>The compiled type.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.DirectCompilerServiceBase.Dispose(System.Boolean)">
            <summary>
            Releases managed resources used by this instance.
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.ActLikeCaster">
            <summary>
            Extends the <see cref="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuForwarder"/> class to allow implicit
            and explicit conversions to any interface type.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.ActLikeCaster.TryConvert(System.Dynamic.ConvertBinder,System.Object@)">
            <summary>
            handles any conversion call.
            </summary>
            <param name="binder"></param>
            <param name="result"></param>
            <returns>true if successful.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.ActLikeCaster.#ctor(System.Object,System.Collections.Generic.IEnumerable{System.Type})">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Compilation.ImpromptuInterface.ActLikeCaster"/> type.
            </summary>
            <param name="target">the target object for call forwarding.</param>
            <param name="types">the supported interface types.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.ActLikeCaster.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Compilation.ImpromptuInterface.ActLikeCaster"/> type.
            </summary>
            <param name="info">the serialization info.</param>
            <param name="context">the streaming context.</param>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Build.IActLikeProxyInitialize">
            <summary>
            This interface can be used to define your own custom proxy if you preload it.
            </summary>
            <remarks>
            Advanced usage only! This is required as well as <see cref="T:RazorEngine.Compilation.ImpromptuInterface.Build.ActLikeProxyAttribute"></see>
            </remarks>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.IActLikeProxyInitialize.Initialize(System.Object,System.Collections.Generic.IEnumerable{System.Type},System.Collections.Generic.IDictionary{System.String,System.Type})">
            <summary>
             Method used to Initialize Proxy
            </summary>
            <param name="original"></param>
            <param name="interfaces"></param>
            <param name="informalInterface"></param>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Build.ActLikeProxy">
            <summary>
            Base class of Emited ProxiesC:\Documents and Settings\jayt\My Documents\Visual Studio 2010\Projects\RazorEngine.Compilation.ImpromptuInterface\RazorEngine.Compilation.ImpromptuInterface\Optimization\
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.ActLikeProxy.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Compilation.ImpromptuInterface.Build.ActLikeProxy"/> class.
            </summary>
        </member>
        <member name="P:RazorEngine.Compilation.ImpromptuInterface.Build.ActLikeProxy.ActLikeProxyOriginal">
            <summary>
            Returns the proxied object
            </summary>
            <value></value>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.ActLikeProxy.RazorEngine#Compilation#ImpromptuInterface#Build#IActLikeProxyInitialize#Initialize(System.Object,System.Collections.Generic.IEnumerable{System.Type},System.Collections.Generic.IDictionary{System.String,System.Type})">
            <summary>
            Method used to Initialize Proxy
            </summary>
            <param name="original"></param>
            <param name="interfaces"></param>
            <param name="informalInterface"></param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.ActLikeProxy.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object"/> is equal to this instance.
            </summary>
            <param name="obj">The <see cref="T:System.Object"/> to compare with this instance.</param>
            <returns>
            	<c>true</c> if the specified <see cref="T:System.Object"/> is equal to this instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.ActLikeProxy.Equals(RazorEngine.Compilation.ImpromptuInterface.Build.ActLikeProxy)">
            <summary>
            Actlike proxy should be equivalent to the objects they proxy
            </summary>
            <param name="other">The other.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.ActLikeProxy.GetHashCode">
            <summary>
            Returns a hash code for this instance.
            </summary>
            <returns>
            A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table. 
            </returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.ActLikeProxy.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Populates a <see cref="T:System.Runtime.Serialization.SerializationInfo"/> with the data needed to serialize the target object.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> to populate with data.</param>
            <param name="context">The destination (see <see cref="T:System.Runtime.Serialization.StreamingContext"/>) for this serialization.</param>
            <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.ActLikeProxy.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Build.ActLikeProxyAttribute">
            <summary>
            Meta info describing proxy usage. Can be used to preload proxy.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.ActLikeProxyAttribute.#ctor(System.Type[],System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Compilation.ImpromptuInterface.Build.ActLikeProxyAttribute"/> class.
            </summary>
            <param name="interfaces">The interfaces.</param>
            <param name="context">The context.</param>
        </member>
        <member name="P:RazorEngine.Compilation.ImpromptuInterface.Build.ActLikeProxyAttribute.Interfaces">
            <summary>
            Gets or sets the interfaces.
            </summary>
            <value>The interfaces.</value>
        </member>
        <member name="P:RazorEngine.Compilation.ImpromptuInterface.Build.ActLikeProxyAttribute.Context">
            <summary>
            Gets or sets the context.
            </summary>
            <value>The context.</value>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Build.ActLikeProxySerializationHelper">
            <summary>
            Support Deserializing the proxy since on separate runs of an executable
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.Build.ActLikeProxySerializationHelper.Original">
            <summary>
            Original Object
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.Build.ActLikeProxySerializationHelper.Interfaces">
            <summary>
            Intefaces
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.Build.ActLikeProxySerializationHelper.Context">
            <summary>
            Type Context
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.ActLikeProxySerializationHelper.GetRealObject(System.Runtime.Serialization.StreamingContext)">
            <summary>
            Returns the real object that should be deserialized, rather than the object that the serialized stream specifies.
            </summary>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext"/> from which the current object is deserialized.</param>
            <returns>
            Returns the actual object that is put into the graph.
            </returns>
            <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. The call will not work on a medium trusted server.</exception>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Build.BuildProxy">
            <summary>
             Does most of the work buiding and caching proxies
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.BuildProxy.RecursiveActLike``1(System.Object)">
            <summary>
            Calls ActLike on the given object.
            </summary>
            <typeparam name="TInterface">the interface to act like.</typeparam>
            <param name="target">the object "implementing" the interface (duck typing).</param>
            <returns>the wrapper.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.BuildProxy.WriteOutDll(System.String)">
            <summary>
            Writes the out DLL of types created between this call and being closed used for debugging of emitted IL code
            </summary>
            <param name="name">The name.</param>
            <returns></returns>
            <remarks>
                This may be used for generating an assembly for preloading proxies, however you must be very careful when doing so as 
                changes could make the emitted asssembly out of date very easily.
            </remarks>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.BuildProxy.BuildType(System.Type,System.Type,System.Type[])">
            <summary>
            Builds the type for the static proxy or returns from cache
            </summary>
            <param name="contextType">Type of the context.</param>
            <param name="mainInterface">The main interface.</param>
            <param name="otherInterfaces">The other interfaces.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.BuildProxy.BuildType(System.Type,System.Collections.Generic.IDictionary{System.String,System.Type})">
            <summary>
            Builds the type.
            </summary>
            <param name="contextType">Type of the context.</param>
            <param name="informalInterface">The informal interface.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.BuildProxy.PreLoadProxy(System.Type,RazorEngine.Compilation.ImpromptuInterface.Build.ActLikeProxyAttribute)">
            <summary>
            Preloads a proxy for ActLike to use.
            </summary>
            <param name="proxyType">Type of the proxy.</param>
            <param name="attribute">The ActLikeProxyAttribute, if not provide it will be looked up.</param>
            <returns>Returns false if there already is a proxy registered for the same type.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.BuildProxy.PreLoadProxiesFromAssembly(System.Reflection.Assembly)">
            <summary>
            Preloads proxies that ActLike uses from assembly.
            </summary>
            <param name="assembly">The assembly.</param>
            <returns>Returns false if there already is a proxy registered for the same type.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.BuildProxy.MakePropertyHelper(System.Reflection.Emit.ModuleBuilder,System.Reflection.Emit.TypeBuilder,RazorEngine.Compilation.ImpromptuInterface.Build.BuildProxy.PropertyEmitInfo,System.Reflection.PropertyInfo,System.Reflection.MethodInfo,System.Reflection.MethodInfo)">
            <summary>
            Makes the property helper.
            </summary>
            <param name="builder">The builder.</param>
            <param name="typeBuilder">The type builder.</param>
            <param name="info">The info.</param>
            <param name="getMethod">The get method.</param>
            <param name="setMethod">The set method.</param>
            <param name="emitInfo">The emit info.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.BuildProxy.EmitCallSiteFuncType(System.Collections.Generic.IEnumerable{System.Type},System.Type)">
            <summary>
            Emits new delegate type of the call site func.
            </summary>
            <param name="argTypes">The arg types.</param>
            <param name="returnType">Type of the return.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.BuildProxy.GenerateCallSiteFuncType(System.Collections.Generic.IEnumerable{System.Type},System.Type,System.Reflection.MethodInfo,System.Reflection.Emit.TypeBuilder)">
            <summary>
            Generates the delegate type of the call site function.
            </summary>
            <param name="argTypes">The arg types.</param>
            <param name="returnType">Type of the return.</param>
            <param name="methodInfo">The method info. Required for reference types or delegates with more than 16 arguments.</param>
            <param name="builder">The Type Builder. Required for reference types or delegates with more than 16 arguments.</param>
            <returns></returns>
        </member>
        <member name="P:RazorEngine.Compilation.ImpromptuInterface.Build.BuildProxy.Builder">
            <summary>
             Module Builder for buiding proxies
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Build.EmitExtensions">
            <summary>
             Extension Methods that make emiting code easier and easier to read
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Build.EmitExtensions.BranchTrueOverBlock">
            <summary>
             Used to automatically create label on dispose
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.EmitExtensions.BranchTrueOverBlock.#ctor(System.Reflection.Emit.ILGenerator)">
            <summary>
             Constructor
            </summary>
            <param name="generator"></param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.EmitExtensions.BranchTrueOverBlock.Dispose">
            <summary>
            Finishes block
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Build.EmitExtensions.BranchFalseOverBlock">
            <summary>
            The false block.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.EmitExtensions.BranchFalseOverBlock.#ctor(System.Reflection.Emit.ILGenerator)">
            <summary>
             Constructor
            </summary>
            <param name="generator"></param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.EmitExtensions.BranchFalseOverBlock.Dispose">
            <summary>
            Finishes block
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.EmitExtensions.GetFieldEvenIfGeneric(System.Type,System.String)">
            <summary>
            Gets the field info even if generic type parameter.
            </summary>
            <param name="type">The type.</param>
            <param name="fieldName">Name of the field.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.EmitExtensions.GetMethodEvenIfGeneric(System.Type,System.String,System.Type[])">
            <summary>
            Gets the method info even if generic type parameter.
            </summary>
            <param name="type">The type.</param>
            <param name="methodName">Name of the method.</param>
            <param name="argTypes">The arg types.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.EmitExtensions.GetMethodEvenIfGeneric(System.Type,System.String)">
            <summary>
            Gets the method info even if generic type parameter.
            </summary>
            <param name="type">The type.</param>
            <param name="methodName">Name of the method.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.EmitExtensions.EmitBranchTrue(System.Reflection.Emit.ILGenerator,System.Action{System.Reflection.Emit.ILGenerator})">
            <summary>
            Emits branch true. expects using keyword.
            </summary>
            <param name="generator">The generator.</param>
            <param name="condition">The condition.</param>
            <returns></returns>
            <example>
            Using keyword allows you to set the emit code you are branching over and then automatically emits label when disposing
            <code>
            		<![CDATA[
            using (tIlGen.EmitBranchTrue(g=>g.Emit(OpCodes.Ldsfld, tConvertField)))
            {
            tIlGen.EmitDynamicConvertBinder(CSharpBinderFlags.None, returnType, contextType);
            tIlGen.EmitCallsiteCreate(convertFuncType);
            tIlGen.Emit(OpCodes.Stsfld, tConvertField);
            }
            ]]>
            	</code>
            </example>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.EmitExtensions.EmitBranchFalse(System.Reflection.Emit.ILGenerator,System.Action{System.Reflection.Emit.ILGenerator})">
            <summary>
            Emits branch false. expects using keyword.
            </summary>
            <param name="generator">The generator.</param>
            <param name="condition">The condition.</param>
            <returns></returns>
            <example>
            Using keyword allows you to set the emit code you are branching over and then automatically emits label when disposing
            <code>
            		<![CDATA[
            using (tIlGen.EmitBranchTrue(g=>g.Emit(OpCodes.Ldsfld, tConvertField)))
            {
            tIlGen.EmitDynamicConvertBinder(CSharpBinderFlags.None, returnType, contextType);
            tIlGen.EmitCallsiteCreate(convertFuncType);
            tIlGen.Emit(OpCodes.Stsfld, tConvertField);
            }
            ]]>
            	</code>
            </example>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.EmitExtensions.EmitInvocation(System.Reflection.Emit.ILGenerator,System.Action{System.Reflection.Emit.ILGenerator},System.Action{System.Reflection.Emit.ILGenerator},System.Action{System.Reflection.Emit.ILGenerator}[])">
            <summary>
            Emits the call.
            </summary>
            <param name="generator">The generator.</param>
            <param name="target">The target.</param>
            <param name="call">The call.</param>
            <param name="parameters">The parameters.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.EmitExtensions.EmitCallsiteCreate(System.Reflection.Emit.ILGenerator,System.Type)">
            <summary>
            Emits creating the callsite.
            </summary>
            <param name="generator">The generator.</param>
            <param name="funcType">Type of the func.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.EmitExtensions.EmitCallInvokeFunc(System.Reflection.Emit.ILGenerator,System.Type)">
            <summary>
            Emits the call invoke delegate.
            </summary>
            <param name="generator">The generator.</param>
            <param name="funcType">Type of the func.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.EmitExtensions.EmitArray(System.Reflection.Emit.ILGenerator,System.Type,System.Collections.Generic.IList{System.Action{System.Reflection.Emit.ILGenerator}})">
            <summary>
            Emits an array.
            </summary>
            <param name="generator">The generator.</param>
            <param name="arrayType">Type of the array.</param>
            <param name="emitElements">The emit elements.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.EmitExtensions.EmitStoreLocation(System.Reflection.Emit.ILGenerator,System.Int32)">
            <summary>
            Emits the store location.
            </summary>
            <param name="generator">The generator.</param>
            <param name="location">The location.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.EmitExtensions.EmitLoadArgument(System.Reflection.Emit.ILGenerator,System.Int32)">
            <summary>
            Emits the load argument.
            </summary>
            <param name="generator">The generator.</param>
            <param name="location">The location.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.EmitExtensions.EmitLoadLocation(System.Reflection.Emit.ILGenerator,System.Int32)">
            <summary>
            Emits the load location.
            </summary>
            <param name="generator">The generator.</param>
            <param name="location">The location.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.EmitExtensions.EmitDynamicMethodInvokeBinder(System.Reflection.Emit.ILGenerator,Microsoft.CSharp.RuntimeBinder.CSharpBinderFlags,System.String,System.Collections.Generic.IEnumerable{System.Type},System.Type,System.Reflection.ParameterInfo[],System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Emits the dynamic method invoke binder.
            </summary>
            <param name="generator">The generator.</param>
            <param name="flag">The binding flags.</param>
            <param name="name">The name.</param>
            <param name="genericParms">The generic parameters.</param>
            <param name="context">The context.</param>
            <param name="argInfo">The arg info.</param>
            <param name="argNames">The arg names.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.EmitExtensions.EmitDynamicSetBinder(System.Reflection.Emit.ILGenerator,Microsoft.CSharp.RuntimeBinder.CSharpBinderFlags,System.String,System.Type,System.Type[])">
            <summary>
            Emits the dynamic set binder.
            </summary>
            <param name="generator">The generator.</param>
            <param name="flag">The binding flags.</param>
            <param name="name">The name.</param>
            <param name="context">The context.</param>
            <param name="argTypes">The arg types.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.EmitExtensions.EmitDynamicSetBinderDynamicParams(System.Reflection.Emit.ILGenerator,Microsoft.CSharp.RuntimeBinder.CSharpBinderFlags,System.String,System.Type,System.Type[])">
            <summary>
            Emits the dynamic set binder dynamic params.
            </summary>
            <param name="generator">The generator.</param>
            <param name="flag">The flag.</param>
            <param name="name">The name.</param>
            <param name="context">The context.</param>
            <param name="argTypes">The arg types.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.EmitExtensions.EmitDynamicBinaryOpBinder(System.Reflection.Emit.ILGenerator,Microsoft.CSharp.RuntimeBinder.CSharpBinderFlags,System.Linq.Expressions.ExpressionType,System.Type,System.Type[])">
            <summary>
            Emits the dynamic binary op binder.
            </summary>
            <param name="generator">The generator.</param>
            <param name="flag">The flag.</param>
            <param name="exprType">Type of the expr.</param>
            <param name="context">The context.</param>
            <param name="argTypes">The arg types.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.EmitExtensions.EmitDynamicGetBinder(System.Reflection.Emit.ILGenerator,Microsoft.CSharp.RuntimeBinder.CSharpBinderFlags,System.String,System.Type,System.Type[])">
            <summary>
            Emits the dynamic get binder.
            </summary>
            <param name="generator">The generator.</param>
            <param name="flag">The binding flags.</param>
            <param name="name">The name.</param>
            <param name="context">The context.</param>
            <param name="argTypes">The arg types.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.EmitExtensions.EmitCreateCSharpArgumentInfo(System.Reflection.Emit.ILGenerator,Microsoft.CSharp.RuntimeBinder.CSharpArgumentInfoFlags,System.String)">
            <summary>
            Emits creating the <see cref="T:Microsoft.CSharp.RuntimeBinder.CSharpArgumentInfo"></see>
            </summary>
            <param name="generator">The generator.</param>
            <param name="flag">The flag.</param>
            <param name="name">The name.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.EmitExtensions.EmitDynamicConvertBinder(System.Reflection.Emit.ILGenerator,Microsoft.CSharp.RuntimeBinder.CSharpBinderFlags,System.Type,System.Type)">
            <summary>
            Emits the dynamic convert binder.
            </summary>
            <param name="generator">The generator.</param>
            <param name="flag">The binding flag.</param>
            <param name="returnType">Type of the return.</param>
            <param name="context">The context.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.EmitExtensions.EmitDynamicIsEventBinder(System.Reflection.Emit.ILGenerator,Microsoft.CSharp.RuntimeBinder.CSharpBinderFlags,System.String,System.Type)">
            <summary>
            Emits the dynamic event binder.
            </summary>
            <param name="generator">The generator.</param>
            <param name="flag">The binding flag.</param>
            <param name="name">The name.</param>
            <param name="context">The context.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.EmitExtensions.EmitTypeOf(System.Reflection.Emit.ILGenerator,System.Type)">
            <summary>
            Emits the typeof(Type)
            </summary>
            <param name="generator">The generator.</param>
            <param name="type">The type.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.EmitExtensions.EmitTypeOf(System.Reflection.Emit.ILGenerator,System.Reflection.Emit.TypeToken)">
            <summary>
            Emits the typeof(Type)
            </summary>
            <param name="generator">The generator.</param>
            <param name="type">The type.</param>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Build.TypeHash">
            <summary>
            Type that Encompasses Hashing a group of Types in various ways
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.TypeHash.Equals(RazorEngine.Compilation.ImpromptuInterface.Build.TypeHash)">
            <summary>
            Equalses the specified other.
            </summary>
            <param name="other">The other.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.TypeHash.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object"/> is equal to this instance.
            </summary>
            <param name="obj">The <see cref="T:System.Object"/> to compare with this instance.</param>
            <returns>
            	<c>true</c> if the specified <see cref="T:System.Object"/> is equal to this instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.TypeHash.GetHashCode">
            <summary>
            Returns a hash code for this instance.
            </summary>
            <returns>
            A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table. 
            </returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.TypeHash.op_Equality(RazorEngine.Compilation.ImpromptuInterface.Build.TypeHash,RazorEngine.Compilation.ImpromptuInterface.Build.TypeHash)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="left">The left.</param>
            <param name="right">The right.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.TypeHash.op_Inequality(RazorEngine.Compilation.ImpromptuInterface.Build.TypeHash,RazorEngine.Compilation.ImpromptuInterface.Build.TypeHash)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="left">The left.</param>
            <param name="right">The right.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.Build.TypeHash.Types">
            <summary>
            Types to be hashed
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.Build.TypeHash.InformalInterface">
            <summary>
            The Informal Interface to be hashed
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.TypeHash.#ctor(System.Collections.Generic.IEnumerable{System.Type})">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Compilation.ImpromptuInterface.Build.TypeHash"/> class.
            </summary>
            <param name="moreTypes">The more types.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.TypeHash.#ctor(System.Type,System.Type[])">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Compilation.ImpromptuInterface.Build.TypeHash"/> class.
            For use when you have must distinguish one type; and the rest aren't strict
            </summary>
            <param name="type1">The type1.</param>
            <param name="moreTypes">The more types.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.TypeHash.#ctor(System.Type,System.Collections.Generic.IDictionary{System.String,System.Type})">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Compilation.ImpromptuInterface.Build.TypeHash"/> class.
            </summary>
            <param name="type1">The type1.</param>
            <param name="informalInterface">The informal interface.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.TypeHash.#ctor(System.Boolean,System.Reflection.MemberInfo[])">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Compilation.ImpromptuInterface.Build.TypeHash"/> class.
            </summary>
            <param name="strictOrder">if set to <c>true</c> [strict order].</param>
            <param name="moreTypes">types.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.TypeHash.Create(System.Collections.Generic.IEnumerable{System.Type})">
            <summary>
            Creates the TypeHash
            </summary>
            <param name="moreTypes">The more types.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.TypeHash.Create(System.Type,System.Type[])">
            <summary>
            Creates the TypeHash
            </summary>
            <param name="type1">The type1.</param>
            <param name="moreTypes">The more types.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.TypeHash.Create(System.Type,System.Collections.Generic.IDictionary{System.String,System.Type})">
            <summary>
            Creates the TypeHash
            </summary>
            <param name="type1">The type1.</param>
            <param name="informalInterface">The informal interface.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Build.TypeHash.Create(System.Boolean,System.Reflection.MemberInfo[])">
            <summary>
            Creates the TypeHash
            </summary>
            <param name="strictOrder">if set to <c>true</c> [strict order].</param>
            <param name="moreTypes">The more types.</param>
            <returns></returns>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.AliasAttribute">
            <summary>
            Alias to swap method/property/event call name invoked on original
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.AliasAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Compilation.ImpromptuInterface.AliasAttribute" /> class.
            </summary>
            <param name="name">The name.</param>
        </member>
        <member name="P:RazorEngine.Compilation.ImpromptuInterface.AliasAttribute.Name">
            <summary>
            Gets or sets the name.
            </summary>
            <value>
            The name.
            </value>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Optimization.BareBonesList`1.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Compilation.ImpromptuInterface.Optimization.BareBonesList`1"/> class.
            </summary>
            <param name="length">The max length that the list cannot grow beyound</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Optimization.BareBonesList`1.GetEnumerator">
            <summary>
            Gets the enumerator. with bare bones this is good only once
            </summary>
            <returns></returns>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Optimization.InvokeHelper.LazyBinder">
            <summary>
            LazyBinderType
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Optimization.Util">
            <summary>
            Utility Class
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Optimization.Util.IsAnonymousType(System.Object)">
            <summary>
            Determines whether [is anonymous type] [the specified target].
            </summary>
            <param name="target">The target.</param>
            <returns>
            	<c>true</c> if [is anonymous type] [the specified target]; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Optimization.Util.NameArgsIfNecessary(System.Dynamic.CallInfo,System.Object[])">
            <summary>
            Names the args if necessary.
            </summary>
            <param name="callInfo">The call info.</param>
            <param name="args">The args.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Optimization.Util.GetTargetContext(System.Object,System.Type@,System.Boolean@)">
            <summary>
            Gets the target context.
            </summary>
            <param name="target">The target.</param>
            <param name="context">The context.</param>
            <param name="staticContext">if set to <c>true</c> [static context].</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Optimization.Util.FixContext(System.Type)">
            <summary>
            Fixes the context.
            </summary>
            <param name="context">The context.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Optimization.Util.GetValue``1(System.Runtime.Serialization.SerializationInfo,System.String)">
            <summary>
            Gets the value. Conveinence Ext method
            </summary>
            <typeparam name="T"></typeparam>
            <param name="info">The info.</param>
            <param name="name">The name.</param>
            <returns></returns>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.Optimization.Util.IsMono">
            <summary>
            Is Current Runtime Mono?
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.IActLike">
            <summary>
            This interface can be used on your custom dynamic objects if you want impromptu interfaces without casting to object or using the static method syntax of ActLike.
            Also if you want to change the behavior for slightly for specific types as this will take precident when using the dynamic keyword or your specific type is known staticly.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.IActLike.ActLike``1(System.Type[])">
             <summary>
             This interface can be used on your custom dynamic objects if you want impromptu interfaces without casting to object or using the static method syntax of ActLike.
             Also if you want to change the behavior for slightly for specific types as this will take precident when using the dynamic keyword or your specific type is known staticly.
             </summary>
            <param name="otherInterfaces"></param>
            <typeparam name="TInterface"></typeparam>
            <returns></returns>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.IDynamicKnowLike">
            <summary>
            This interface can be used on your custom dynamic objects if you want to know the interface you are impromptu-ly implementing.
            </summary>
        </member>
        <member name="P:RazorEngine.Compilation.ImpromptuInterface.Dynamic.IDynamicKnowLike.KnownInterfaces">
            <summary>
             Property used to pass interface information to proxied object
            </summary>
        </member>
        <member name="P:RazorEngine.Compilation.ImpromptuInterface.Dynamic.IDynamicKnowLike.KnownPropertySpec">
            <summary>
            Sets the known property spec.
            </summary>
            <value>The known property spec.</value>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.IForwarder">
            <summary>
            Get access to target of original proxy
            </summary>
        </member>
        <member name="P:RazorEngine.Compilation.ImpromptuInterface.Dynamic.IForwarder.Target">
            <summary>
            Gets the target.
            </summary>
            <value>The target.</value>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuForwarder">
            <summary>
            Proxies Calls allows subclasser to override do extra actions before or after base invocation
            </summary>
            <remarks>
            This may not be as efficient as other proxies that can work on just static objects or just dynamic objects...
            Consider this when using.
            </remarks>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuForwarder.#ctor(System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuForwarder"/> class.
            </summary>
            <param name="target">The target.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuForwarder.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuForwarder"/> class.
            </summary>
            <param name="info">The info.</param>
            <param name="context">The context.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuForwarder.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Populates a <see cref="T:System.Runtime.Serialization.SerializationInfo"/> with the data needed to serialize the target object.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> to populate with data.</param>
            <param name="context">The destination (see <see cref="T:System.Runtime.Serialization.StreamingContext"/>) for this serialization.</param>
            <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuForwarder.GetDynamicMemberNames">
            <summary>
            Returns the enumeration of all dynamic member names.
            </summary>
            <returns>
            A sequence that contains dynamic member names.
            </returns>
        </member>
        <member name="P:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuForwarder.Target">
            <summary>
            Gets or sets the target.
            </summary>
            <value>The target.</value>
        </member>
        <member name="P:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuForwarder.CallTarget">
            <summary>
            Gets the call target.
            </summary>
            <value>The call target.</value>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuForwarder.TryGetMember(System.Dynamic.GetMemberBinder,System.Object@)">
            <summary>
            Provides the implementation for operations that get member values. Classes derived from the <see cref="T:System.Dynamic.DynamicObject"/> class can override this method to specify dynamic behavior for operations such as getting a value for a property.
            </summary>
            <param name="binder">Provides information about the object that called the dynamic operation. The binder.Name property provides the name of the member on which the dynamic operation is performed. For example, for the Console.WriteLine(sampleObject.SampleProperty) statement, where sampleObject is an instance of the class derived from the <see cref="T:System.Dynamic.DynamicObject"/> class, binder.Name returns "SampleProperty". The binder.IgnoreCase property specifies whether the member name is case-sensitive.</param>
            <param name="result">The result of the get operation. For example, if the method is called for a property, you can assign the property value to <paramref name="result"/>.</param>
            <returns>
            true if the operation is successful; otherwise, false. If this method returns false, the run-time binder of the language determines the behavior. (In most cases, a run-time exception is thrown.)
            </returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuForwarder.TryInvoke(System.Dynamic.InvokeBinder,System.Object[],System.Object@)">
            <summary>
            Provides the implementation for operations that invoke an object. Classes derived from the <see cref="T:System.Dynamic.DynamicObject"/> class can override this method to specify dynamic behavior for operations such as invoking an object or a delegate.
            </summary>
            <param name="binder">Provides information about the invoke operation.</param>
            <param name="args">
            The arguments that are passed to the object during the invoke operation.
            For example, for the sampleObject(100) operation, where sampleObject is derived 
            from the <see cref="T:System.Dynamic.DynamicObject"/> class, <paramref name="args"/>[0] is equal to 100.
            </param>
            <param name="result">The result of the object invocation.</param>
            <returns>
            true if the operation is successful; otherwise, false. If this method returns false, the run-time binder of the language determines the behavior. (In most cases, a language-specific run-time exception is thrown.
            </returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuForwarder.TryInvokeMember(System.Dynamic.InvokeMemberBinder,System.Object[],System.Object@)">
            <summary>
            Forwards the invoke operation.
            </summary>
            <param name="binder">the binder</param>
            <param name="args">the arguments</param>
            <param name="result">the result</param>
            <returns>true when successfull</returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuForwarder.TrySetMember(System.Dynamic.SetMemberBinder,System.Object)">
            <summary>
            Forwards the invoke operation.
            </summary>
            <param name="binder">the binder</param>
            <param name="value">the value</param>
            <returns>true when successfull</returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuForwarder.TryGetIndex(System.Dynamic.GetIndexBinder,System.Object[],System.Object@)">
            <summary>
            Forwards the invoke operation.
            </summary>
            <param name="binder">the binder</param>
            <param name="indexes">the indexes</param>
            <param name="result">the result</param>
            <returns>true when successfull</returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuForwarder.TrySetIndex(System.Dynamic.SetIndexBinder,System.Object[],System.Object)">
            <summary>
            Forwards the invoke operation.
            </summary>
            <param name="binder">the binder</param>
            <param name="indexes">the indexes</param>
            <param name="value">the value</param>
            <returns>true when successfull</returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuForwarder.TryConvert(System.Dynamic.ConvertBinder,System.Object@)">
            <summary>
            Forwards the convert operation.
            </summary>
            <param name="binder">the binder</param>
            <param name="result">the result</param>
            <returns>true when successfull</returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuForwarder.TryBinaryOperation(System.Dynamic.BinaryOperationBinder,System.Object,System.Object@)">
            <summary>
            Forwards the Binary operation
            </summary>
            <param name="binder"></param>
            <param name="arg"></param>
            <param name="result"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuForwarder.TryUnaryOperation(System.Dynamic.UnaryOperationBinder,System.Object@)">
            <summary>
            Forwards the unary operation.
            </summary>
            <param name="binder"></param>
            <param name="result"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuForwarder.Equals(RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuForwarder)">
            <summary>
            Equals the specified other.
            </summary>
            <param name="other">The other.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuForwarder.Equals(System.Object)">
            <summary>
            Checks if the objects are equal.
            </summary>
            <param name="obj">the other object</param>
            <returns>true when the current instance is equal to the given one.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuForwarder.GetHashCode">
            <summary>
            Gets the hashcode of the current instance.
            </summary>
            <returns>the hash code.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuForwarderAddRemove.op_Addition(RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuForwarderAddRemove,System.Object)">
            <summary>
            Implements the operator +.
            </summary>
            <param name="left">The left.</param>
            <param name="right">The right.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuForwarderAddRemove.op_Subtraction(RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuForwarderAddRemove,System.Object)">
            <summary>
            Implements the operator -.
            </summary>
            <param name="left">The left.</param>
            <param name="right">The right.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="P:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuForwarderAddRemove.Delegate">
            <summary>
            Gets or sets the delegate.
            </summary>
            <value>The delegate.</value>
        </member>
        <member name="P:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuForwarderAddRemove.IsAdding">
            <summary>
            Gets or sets a value indicating whether this instance is adding.
            </summary>
            <value><c>true</c> if this instance is adding; otherwise, <c>false</c>.</value>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuLateLibraryType">
            <summary>
            Late bind types from libraries not not at compile type
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuLateLibraryType.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuLateLibraryType"/> class.
            </summary>
            <param name="type">The type.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuLateLibraryType.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuLateLibraryType"/> class.
            </summary>
            <param name="typeName">Qualified Name of the type.</param>
        </member>
        <member name="P:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuLateLibraryType.new">
            <summary>
            Returns a late bound constructor
            </summary>
            <value>The late bound constructor</value>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuLateLibraryType.ConstructorForward">
            <summary>
            Forward argument to constructor including named arguments
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuLateLibraryType.ConstructorForward.TryInvoke(System.Dynamic.InvokeBinder,System.Object[],System.Object@)">
            <summary>
            Tries to invoke.
            </summary>
            <param name="binder"></param>
            <param name="args"></param>
            <param name="result"></param>
            <returns></returns>
        </member>
        <member name="P:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuLateLibraryType.IsAvailable">
            <summary>
            Gets a value indicating whether this Type is available at runtime.
            </summary>
            <value>
            	<c>true</c> if this instance is available; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuLateLibraryType.CallTarget">
            <summary>
            The call target.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuLateLibraryType.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuForwarder"/> class.
            </summary>
            <param name="info">The info.</param>
            <param name="context">The context.</param>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuObject">
            <summary>
            Dynamic Object that knows about the Impromtu Interface return types;
            Override Typical Dynamic Object methods, and use TypeForName to get the return type of an interface member.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuObject.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuObject"/> class.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuObject.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuObject"/> class. when deserializing
            </summary>
            <param name="info">The info.</param>
            <param name="context">The context.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuObject.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Populates a <see cref="T:System.Runtime.Serialization.SerializationInfo"/> with the data needed to serialize the target object.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> to populate with data.</param>
            <param name="context">The destination (see <see cref="T:System.Runtime.Serialization.StreamingContext"/>) for this serialization.</param>
            <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuObject._returnTypHash">
            <summary>
            Cache to avoid refelection for same Interfaces.
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuObject._hash">
            <summary>
            Hash for this instance to lookup cached values from <see cref="F:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuObject._returnTypHash"/>
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuObject.PropertySpec">
            <summary>
            Keep Track of Known Property Spec
            </summary>
        </member>
        <member name="P:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuObject.KnownInterfaces">
            <summary>
            Gets or sets the known interfaces.
            Set should only be called be the factory methood
            </summary>
            <value>The known interfaces.</value>
        </member>
        <member name="P:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuObject.KnownPropertySpec">
            <summary>
            Gets or sets the known fake interface (string method name to return type mapping).
            </summary>
            <value>The known fake interface.</value>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuObject.GetDynamicMemberNames">
            <summary>
            Returns the enumeration of all dynamic member names.
            </summary>
            <returns>
            A sequence that contains dynamic member names.
            </returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuObject.TryTypeForName(System.String,System.Type@)">
            <summary>
            Tries to get the type for the property name from the interface.
            </summary>
            <param name="name">The name.</param>
            <param name="returnType">The return Type.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ImpromptuObject.ActLike``1(System.Type[])">
            <summary>
            Allows ActLike to be called via dyanmic invocation
            </summary>
            <typeparam name="TInterface">The type of the interface.</typeparam>
            <param name="otherInterfaces">The other interfaces.</param>
            <returns></returns>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.InvocationKind">
            <summary>
            Type of Invocation
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.Dynamic.InvocationKind.NotSet">
            <summary>
            NotSet
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.Dynamic.InvocationKind.Convert">
            <summary>
            Convert Implicit or Explicity
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.Dynamic.InvocationKind.Get">
            <summary>
            Get Property
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.Dynamic.InvocationKind.Set">
            <summary>
            Set Property
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.Dynamic.InvocationKind.GetIndex">
            <summary>
            Get Indexer
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.Dynamic.InvocationKind.SetIndex">
            <summary>
            Set Indexer
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.Dynamic.InvocationKind.InvokeMember">
            <summary>
            Invoke Method the has return value
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.Dynamic.InvocationKind.InvokeMemberAction">
            <summary>
            Invoke Method that returns void
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.Dynamic.InvocationKind.InvokeMemberUnknown">
            <summary>
            Invoke Method that could return a value or void
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.Dynamic.InvocationKind.Constructor">
            <summary>
            Invoke Constructor
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.Dynamic.InvocationKind.AddAssign">
            <summary>
            Invoke +=
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.Dynamic.InvocationKind.SubtractAssign">
            <summary>
            Invoke -=
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.Dynamic.InvocationKind.IsEvent">
            <summary>
            Invoke Event Property Test
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.Dynamic.InvocationKind.Invoke">
            <summary>
            Invoke Directly
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.Dynamic.InvocationKind.InvokeAction">
            <summary>
            Invoke Directly DiscardResult
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.Dynamic.InvocationKind.InvokeUnknown">
            <summary>
            Invoke Directly Return Value
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.Invocation">
            <summary>
            Storable representation of an invocation without the target
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.Dynamic.Invocation.ExplicitConvertBinderName">
            <summary>
            Defacto Binder Name for Explicit Convert Op
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.Dynamic.Invocation.ImplicitConvertBinderName">
            <summary>
            Defacto Binder Name for Implicit Convert Op
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.Dynamic.Invocation.IndexBinderName">
            <summary>
            Defacto Binder Name for Indexer
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.Dynamic.Invocation.ConstructorBinderName">
            <summary>
            Defacto Binder Name for Construvter
            </summary>
        </member>
        <member name="P:RazorEngine.Compilation.ImpromptuInterface.Dynamic.Invocation.Kind">
            <summary>
            Gets or sets the kind.
            </summary>
            <value>The kind.</value>
        </member>
        <member name="P:RazorEngine.Compilation.ImpromptuInterface.Dynamic.Invocation.Name">
            <summary>
            Gets or sets the name.
            </summary>
            <value>The name.</value>
        </member>
        <member name="P:RazorEngine.Compilation.ImpromptuInterface.Dynamic.Invocation.Args">
            <summary>
            Gets or sets the args.
            </summary>
            <value>The args.</value>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.Invocation.Create(RazorEngine.Compilation.ImpromptuInterface.Dynamic.InvocationKind,RazorEngine.Compilation.ImpromptuInterface.String_OR_InvokeMemberName,System.Object[])">
            <summary>
            Creates the invocation.
            </summary>
            <param name="kind">The kind.</param>
            <param name="name">The name.</param>
            <param name="storedArgs">The args.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.Invocation.#ctor(RazorEngine.Compilation.ImpromptuInterface.Dynamic.InvocationKind,RazorEngine.Compilation.ImpromptuInterface.String_OR_InvokeMemberName,System.Object[])">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.Invocation"/> class.
            </summary>
            <param name="kind">The kind.</param>
            <param name="name">The name.</param>
            <param name="storedArgs">The args.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.Invocation.Equals(RazorEngine.Compilation.ImpromptuInterface.Dynamic.Invocation)">
            <summary>
            Equalses the specified other.
            </summary>
            <param name="other">The other.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.Invocation.Equals(System.Object)">
            <summary>
            Equalses the specified other.
            </summary>
            <param name="obj">The other.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.Invocation.GetHashCode">
            <summary>
            Get the hash code.
            </summary>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.Invocation.Invoke(System.Object,System.Object[])">
            <summary>
            Invokes the invocation on specified target with specific args.
            </summary>
            <param name="target">The target.</param>
            <param name="args">The args.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.Invocation.InvokeWithArgs(System.Object,System.Object[])">
            <summary>
            Deprecated use <see cref="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.Invocation.Invoke(System.Object,System.Object[])"/>
            </summary>
            <param name="target">The target.</param>
            <param name="args">The args.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.Invocation.InvokeWithStoredArgs(System.Object)">
            <summary>
            Invokes the invocation on specified target.
            </summary>
            <param name="target">The target.</param>
            <returns></returns>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisAction">
            <summary>
            Special Delegate used to make impromptu object methods first parameter is this.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisAction`1">
            <summary>
            Special Delegate used to make impromptu object methods first parameter is this.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisAction`2">
            <summary>
            Special Delegate used to make impromptu object methods first parameter is this.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisAction`3">
            <summary>
            Special Delegate used to make impromptu object methods first parameter is this.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisAction`4">
            <summary>
            Special Delegate used to make impromptu object methods first parameter is this.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisAction`5">
            <summary>
            Special Delegate used to make impromptu object methods first parameter is this.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisAction`6">
            <summary>
            Special Delegate used to make impromptu object methods first parameter is this.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisAction`7">
            <summary>
            Special Delegate used to make impromptu object methods first parameter is this.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisAction`8">
            <summary>
            Special Delegate used to make impromptu object methods first parameter is this.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisAction`9">
            <summary>
            Special Delegate used to make impromptu object methods first parameter is this.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisAction`10">
            <summary>
            Special Delegate used to make impromptu object methods first parameter is this.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisAction`11">
            <summary>
            Special Delegate used to make impromptu object methods first parameter is this.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisAction`12">
            <summary>
            Special Delegate used to make impromptu object methods first parameter is this.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisAction`13">
            <summary>
            Special Delegate used to make impromptu object methods first parameter is this.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisAction`14">
            <summary>
            Special Delegate used to make impromptu object methods first parameter is this.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisAction`15">
            <summary>
            Special Delegate used to make impromptu object methods first parameter is this.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisAction`16">
            <summary>
            Special Delegate used to make impromptu object methods first parameter is this.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisFunc`1">
            <summary>
            Special Delegate used to make impromptu object methods first parameter is this.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisFunc`2">
            <summary>
            Special Delegate used to make impromptu object methods first parameter is this.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisFunc`3">
            <summary>
            Special Delegate used to make impromptu object methods first parameter is this.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisFunc`4">
            <summary>
            Special Delegate used to make impromptu object methods first parameter is this.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisFunc`5">
            <summary>
            Special Delegate used to make impromptu object methods first parameter is this.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisFunc`6">
            <summary>
            Special Delegate used to make impromptu object methods first parameter is this.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisFunc`7">
            <summary>
            Special Delegate used to make impromptu object methods first parameter is this.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisFunc`8">
            <summary>
            Special Delegate used to make impromptu object methods first parameter is this.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisFunc`9">
            <summary>
            Special Delegate used to make impromptu object methods first parameter is this.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisFunc`10">
            <summary>
            Special Delegate used to make impromptu object methods first parameter is this.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisFunc`11">
            <summary>
            Special Delegate used to make impromptu object methods first parameter is this.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisFunc`12">
            <summary>
            Special Delegate used to make impromptu object methods first parameter is this.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisFunc`13">
            <summary>
            Special Delegate used to make impromptu object methods first parameter is this.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisFunc`14">
            <summary>
            Special Delegate used to make impromptu object methods first parameter is this.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisFunc`15">
            <summary>
            Special Delegate used to make impromptu object methods first parameter is this.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisFunc`16">
            <summary>
            Special Delegate used to make impromptu object methods first parameter is this.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisFunc`17">
            <summary>
            Special Delegate used to make impromptu object methods first parameter is this.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisDelegate">
            <summary>
            Extension method for Dealing with Special Delegate Type
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Dynamic.ThisDelegate.IsSpecialThisDelegate(System.Delegate)">
            <summary>
            Determines whether [is special this delegate] [the specified del].
            </summary>
            <param name="del">The del.</param>
            <returns>
            	<c>true</c> if [is special this delegate] [the specified del]; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.IActLikeProxy">
            <summary>
            This interface can be used to access the original content of your emitted type;
            </summary>
        </member>
        <member name="P:RazorEngine.Compilation.ImpromptuInterface.IActLikeProxy.Original">
            <summary>
             Returns the proxied object
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.Impromptu">
            <summary>
            Main API
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.CreateCallSite(System.Type,System.Runtime.CompilerServices.CallSiteBinder,RazorEngine.Compilation.ImpromptuInterface.String_OR_InvokeMemberName,System.Type,System.String[],System.Boolean,System.Boolean)">
            <summary>
            Creates a cached call site at runtime.
            </summary>
            <param name="delegateType">Type of the delegate.</param>
            <param name="binder">The CallSite binder.</param>
            <param name="name">Member Name</param>
            <param name="context">Permissions Context type</param>
            <param name="argNames">The arg names.</param>
            <param name="staticContext">if set to <c>true</c> [static context].</param>
            <param name="isEvent">if set to <c>true</c> [is event].</param>
            <returns>The CallSite</returns>
            <remarks>
            Advanced usage only for serious custom dynamic invocation.
            </remarks>
            <seealso cref="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.CreateCallSite``1(System.Runtime.CompilerServices.CallSiteBinder,RazorEngine.Compilation.ImpromptuInterface.String_OR_InvokeMemberName,System.Type,System.String[],System.Boolean,System.Boolean)"/>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.CreateCallSite``1(System.Runtime.CompilerServices.CallSiteBinder,RazorEngine.Compilation.ImpromptuInterface.String_OR_InvokeMemberName,System.Type,System.String[],System.Boolean,System.Boolean)">
            <summary>
            Creates the call site.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="binder">The binder.</param>
            <param name="name">The name.</param>
            <param name="context">The context.</param>
            <param name="argNames">The arg names.</param>
            <param name="staticContext">if set to <c>true</c> [static context].</param>
            <param name="isEvent">if set to <c>true</c> [is event].</param>
            <returns></returns>
            /// 
            <example>
            Unit test that exhibits usage
            <code><![CDATA[
            string tResult = String.Empty;
            var tPoco = new MethOutPoco();
            var tBinder =
            Binder.InvokeMember(BinderFlags.None, "Func", null, GetType(),
            new[]
            {
            Info.Create(
            InfoFlags.None, null),
            Info.Create(
            InfoFlags.IsOut |
            InfoFlags.UseCompileTimeType, null)
            });
            var tSite = Impromptu.CreateCallSite<DynamicTryString>(tBinder);
            tSite.Target.Invoke(tSite, tPoco, out tResult);
            Assert.AreEqual("success", tResult);
            ]]></code>
            </example>
            <seealso cref="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.CreateCallSite(System.Type,System.Runtime.CompilerServices.CallSiteBinder,RazorEngine.Compilation.ImpromptuInterface.String_OR_InvokeMemberName,System.Type,System.String[],System.Boolean,System.Boolean)"/>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.InvokeMember(System.Object,RazorEngine.Compilation.ImpromptuInterface.String_OR_InvokeMemberName,System.Object[])">
             <summary>
             Dynamically Invokes a member method using the DLR
             </summary>
             <param name="target">The target.</param>
             <param name="name">The name. Can be a string it will be implicitly converted</param>
             <param name="args">The args.</param>
             <returns> The result</returns>
             <example>   
             Unit test that exhibits usage:
             <code>
             <![CDATA[
                dynamic tExpando = new ExpandoObject();
                tExpando.Func = new Func<int, string>(it => it.ToString());
            
                var tValue = 1;
                var tOut = Impromptu.InvokeMember(tExpando, "Func", tValue);
            
                Assert.AreEqual(tValue.ToString(), tOut);
             ]]>
             </code>
             </example>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.InvokeBinaryOperator(System.Object,System.Linq.Expressions.ExpressionType,System.Object)">
            <summary>
            Invokes the binary operator.
            </summary>
            <param name="leftArg">The left arg.</param>
            <param name="op">The op.</param>
            <param name="rightArg">The right Arg.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.InvokeUnaryOperator(System.Linq.Expressions.ExpressionType,System.Object)">
            <summary>
            Invokes the unary opartor.
            </summary>
            <param name="arg">The arg.</param>
            <param name="op">The op.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.Invoke(System.Object,System.Object[])">
            <summary>
            Invokes the specified target using the DLR;
            </summary>
            <param name="target">The target.</param>
            <param name="args">The args.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.InvokeGetIndex(System.Object,System.Object[])">
            <summary>
            Dynamically Invokes indexer using the DLR.
            </summary>
            <param name="target">The target.</param>
            <param name="indexes">The indexes.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.InvokeSetValueOnIndexes(System.Object,System.Object,System.Object[])">
            <summary>
            Convenience version of InvokeSetIndex that separates value and indexes.
            </summary>
            <param name="target">The target.</param>
            <param name="value">The value</param>
            <param name="indexes">The indexes </param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.InvokeSetIndex(System.Object,System.Object[])">
            <summary>
            Invokes setindex.
            </summary>
            <param name="target">The target.</param>
            <param name="indexesThenValue">The indexes then value.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.InvokeMemberAction(System.Object,RazorEngine.Compilation.ImpromptuInterface.String_OR_InvokeMemberName,System.Object[])">
             <summary>
             Dynamically Invokes a member method which returns void using the DLR
             </summary>
             <param name="target">The target.</param>
             <param name="name">The name.</param>
             <param name="args">The args.</param>
             <example>
             Unit test that exhibits usage:
             <code>
             <![CDATA[
                var tTest = "Wrong";
                var tValue = "Correct";
            
                dynamic tExpando = new ExpandoObject();
                tExpando.Action = new Action<string>(it => tTest = it);
            
                Impromptu.InvokeMemberAction(tExpando, "Action", tValue);
            
                Assert.AreEqual(tValue, tTest);
             ]]>
             </code>
             </example>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.InvokeAction(System.Object,System.Object[])">
            <summary>
            Invokes the action using the DLR
            </summary>
            <param name="target">The target.</param>
            <param name="args">The args.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.InvokeSet(System.Object,System.String,System.Object)">
             <summary>
             Dynamically Invokes a set member using the DLR.
             </summary>
             <param name="target">The target.</param>
             <param name="name">The name.</param>
             <param name="value">The value.</param>
             <example>
             Unit test that exhibits usage:
             <code>
             <![CDATA[
                dynamic tExpando = new ExpandoObject();
            
                var tSetValue = "1";
            
                Impromptu.InvokeSet(tExpando, "Test", tSetValue);
            
                Assert.AreEqual(tSetValue, tExpando.Test);
             ]]>
             </code>
             </example>
             <remarks>
             if you call a static property off a type with a static context the csharp dlr binder won't do it, so this method reverts to reflection
             </remarks>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.InvokeGet(System.Object,System.String)">
             <summary>
             Dynamically Invokes a get member using the DLR.
             </summary>
             <param name="target">The target.</param>
             <param name="name">The name.</param>
             <returns>The result.</returns>
             <example>
             Unit Test that describes usage
             <code>
             <![CDATA[
                var tSetValue = "1";
                var tAnon = new { Test = tSetValue };
            
                var tOut =Impromptu.InvokeGet(tAnon, "Test");
            
                Assert.AreEqual(tSetValue, tOut);
             ]]>
             </code>
             </example>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.InvokeIsEvent(System.Object,System.String)">
            <summary>
            Determines whether the specified name on target is event. This allows you to know whether to InvokeMemberAction
             add_{name} or a combo of {invokeget, +=, invokeset} and the corresponding remove_{name} 
            or a combon of {invokeget, -=, invokeset}
            </summary>
            <param name="target">The target.</param>
            <param name="name">The name.</param>
            <returns>
            	<c>true</c> if the specified target is event; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.InvokeAddAssign(System.Object,System.String,System.Object)">
            <summary>
            Invokes add assign with correct behavior for events.
            </summary>
            <param name="target">The target.</param>
            <param name="name">The name.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.InvokeAddAssignMember(System.Object,System.String,System.Object)">
            <summary>
            Invokes add assign with correct behavior for events.
            </summary>
            <param name="target">The target.</param>
            <param name="name">The name.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.InvokeSubtractAssign(System.Object,System.String,System.Object)">
            <summary>
            Invokes subtract assign with correct behavior for events.
            </summary>
            <param name="target">The target.</param>
            <param name="name">The name.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.InvokeSubtractAssignMember(System.Object,System.String,System.Object)">
            <summary>
            Invokes subtract assign with correct behavior for events.
            </summary>
            <param name="target">The target.</param>
            <param name="name">The name.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.InvokeConvert(System.Object,System.Type,System.Boolean)">
            <summary>
            Invokes  convert using the DLR.
            </summary>
            <param name="target">The target.</param>
            <param name="type">The type.</param>
            <param name="explicit">if set to <c>true</c> [explicit].</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.CoerceToDelegate(System.Object,System.Type)">
            <summary>
            Coerce to delegate.
            </summary>
            <param name="invokeableObject"></param>
            <param name="delegateType"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.InvokeConstuctor(System.Type,System.Object[])">
            <summary>
            (Obsolete)Invokes the constructor. misspelling
            </summary>
            <param name="type">The type.</param>
            <param name="args">The args.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.InvokeConstructor(System.Type,System.Object[])">
            <summary>
            Invokes the constuctor.
            </summary>
            <param name="type">The type.</param>
            <param name="args">The args.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.FastDynamicInvoke(System.Delegate,System.Object[])">
            <summary>
            FastDynamicInvoke extension method. Runs up to runs up to 20x faster than <see cref="M:System.Delegate.DynamicInvoke(System.Object[])"/> .
            </summary>
            <param name="del">The del.</param>
            <param name="args">The args.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.GenericDelegateType(System.Int32,System.Boolean)">
            <summary>
            Given a generic parameter count and whether it returns void or not gives type of Action or Func
            </summary>
            <param name="paramCount">The param count.</param>
            <param name="returnVoid">if set to <c>true</c> [return void].</param>
            <returns>Type of Action or Func</returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.GetMemberNames(System.Object,System.Boolean)">
            <summary>
            Gets the member names of properties. Not all IDynamicMetaObjectProvider have support for this.
            </summary>
            <param name="target">The target.</param>
            <param name="dynamicOnly">if set to <c>true</c> [dynamic only]. Won't add reflected properties</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.InvokeCallSite(System.Runtime.CompilerServices.CallSite,System.Object,System.Object[])">
            <summary>
            Dynamically invokes a method determined by the CallSite binder and be given an appropriate delegate type
            </summary>
            <param name="callSite">The Callsite</param>
            <param name="target">The target.</param>
            <param name="args">The args.</param>
            <returns></returns>
            <remarks>
            Advanced use only. Use this method for serious custom invocation, otherwise there are other convenience methods such as
            <see cref="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.InvokeMember(System.Object,RazorEngine.Compilation.ImpromptuInterface.String_OR_InvokeMemberName,System.Object[])"></see>, <see cref="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.InvokeGet(System.Object,System.String)"></see>, <see cref="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.InvokeSet(System.Object,System.String,System.Object)"></see> and <see cref="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.InvokeMemberAction(System.Object,RazorEngine.Compilation.ImpromptuInterface.String_OR_InvokeMemberName,System.Object[])"></see>
            </remarks>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.Invoke(System.Runtime.CompilerServices.CallSite,System.Object,System.Object[])">
            <summary>
            Dynamically invokes a method determined by the CallSite binder and be given an appropriate delegate type
            </summary>
            <param name="callSite">The Callsite</param>
            <param name="target">The target.</param>
            <param name="args">The args.</param>
            <returns></returns>
            <remarks>
            Advanced use only. Use this method for serious custom invocation, otherwise there are other convenience methods such as
            <see cref="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.InvokeMember(System.Object,RazorEngine.Compilation.ImpromptuInterface.String_OR_InvokeMemberName,System.Object[])"></see>, <see cref="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.InvokeGet(System.Object,System.String)"></see>, <see cref="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.InvokeSet(System.Object,System.String,System.Object)"></see> and <see cref="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.InvokeMemberAction(System.Object,RazorEngine.Compilation.ImpromptuInterface.String_OR_InvokeMemberName,System.Object[])"></see>
            </remarks>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.ActLike``1(System.Object,System.Type[])">
            <summary>
            Extension Method that Wraps an existing object with an Explicit interface definition
            </summary>
            <typeparam name="TInterface">The type of the interface.</typeparam>
            <param name="originalDynamic">The original object can be annoymous type, System.DynamicObject as well as any others.</param>
            <param name="otherInterfaces">Optional other interfaces.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.UndoActLike(System.Object)">
            <summary>
            Unwraps the act like proxy (if wrapped).
            </summary>
            <param name="proxiedObject">The proxied object.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.ActLike(System.Object,System.Type[])">
            <summary>
            Extension Method that Wraps an existing object with an Interface of what it is implicitly assigned to.
            </summary>
            <param name="originalDynamic">The original dynamic.</param>
            <param name="otherInterfaces">The other interfaces.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.ActLikeProperties(System.Object,System.Collections.Generic.IDictionary{System.String,System.Type})">
            <summary>
            Makes static methods for the passed in property spec, designed to be used with old api's that reflect properties.
            </summary>
            <param name="originalDynamic">The original dynamic.</param>
            <param name="propertySpec">The property spec.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.InitializeProxy(System.Type,System.Object,System.Collections.Generic.IEnumerable{System.Type},System.Collections.Generic.IDictionary{System.String,System.Type})">
            <summary>
            Private helper method that initializes the proxy.
            </summary>
            <param name="proxytype">The proxytype.</param>
            <param name="original">The original.</param>
            <param name="interfaces">The interfaces.</param>
            <param name="propertySpec">The property spec.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.CallActLike``1(System.Object,System.Object,System.Type[])">
            <summary>
            This Extension method is called off the calling context to perserve permissions with the object wrapped with an explicit interface definition.
            </summary>
            <typeparam name="TInterface">The type of the interface.</typeparam>
            <param name="caller">The caller.</param>
            <param name="originalDynamic">The original dynamic.</param>
            <param name="otherInterfaces">The other interfaces.</param>
            <returns></returns>
            <example>
            UnitTest That describes usage
            <code>
            <![CDATA[
                var tTest = new TestWithPrivateMethod();
                var tNonExposed = this.CallActLike<IExposePrivateMethod>(tTest);
                Assert.Throws<RuntimeBinderException>(() => tNonExposed.Test());
            ]]>
            </code>
            </example>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.AllActLike``1(System.Collections.Generic.IEnumerable{System.Object},System.Type[])">
            <summary>
            Chainable Linq to Objects Method, allows you to wrap a list of objects with an Explict interface defintion
            </summary>
            <typeparam name="TInterface">The type of the interface.</typeparam>
            <param name="originalDynamic">The original dynamic.</param>
            <param name="otherInterfaces">The other interfaces.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.DynamicActLike(System.Object,System.Type[])">
            <summary>
            Static Method that wraps an existing dyanmic object with a explicit interface type
            </summary>
            <param name="originalDynamic">The original dynamic.</param>
            <param name="otherInterfaces">The other interfaces.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.CallDynamicActLike(System.Object,System.Object,System.Type[])">
            <summary>
            This Extension method is called off the calling context to perserve permissions with the object wrapped with an explicit interface definition.
            </summary>
            <param name="caller">The caller.</param>
            <param name="originalDynamic">The original dynamic.</param>
            <param name="otherInterfaces">The other interfaces.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.Impromptu.AllCallActLike``1(System.Collections.Generic.IEnumerable{System.Object},System.Object,System.Type[])">
            <summary>
            Chainable Linq to Objects Method, allows you to wrap a list of objects, and preserve method permissions with a caller, with an Explict interface defintion
            </summary>
            <typeparam name="TInterface">The type of the interface.</typeparam>
            <param name="originalDynamic">The original dynamic.</param>
            <param name="caller">The caller.</param>
            <param name="otherInterfaces">The other interfaces.</param>
            <returns></returns>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.InvokeArg">
            <summary>
            Use for Named arguments passed to InvokeMethods
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.InvokeArg.op_Explicit(System.Collections.Generic.KeyValuePair{System.String,System.Object})~RazorEngine.Compilation.ImpromptuInterface.InvokeArg">
            <summary>
            Performs an explicit conversion from <see cref="T:System.Collections.Generic.KeyValuePair`2"/> to <see cref="T:RazorEngine.Compilation.ImpromptuInterface.InvokeArg"/>.
            </summary>
            <param name="pair">The pair.</param>
            <returns>The result of the conversion.</returns>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.InvokeArg.Create">
            <summary>
            Create Function can set to variable to make cleaner syntax;
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.InvokeArg.#ctor(System.String,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Compilation.ImpromptuInterface.InvokeArg"/> class.
            </summary>
            <param name="name">The name.</param>
            <param name="value">The value.</param>
        </member>
        <member name="P:RazorEngine.Compilation.ImpromptuInterface.InvokeArg.Name">
            <summary>
            Gets or sets the argument name.
            </summary>
            <value>The name.</value>
        </member>
        <member name="P:RazorEngine.Compilation.ImpromptuInterface.InvokeArg.Value">
            <summary>
            Gets or sets the argument value.
            </summary>
            <value>The value.</value>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.InvokeArg`1">
            <summary>
            InvokeArg that makes it easier to Cast from any IDictionaryValue
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.InvokeArg`1.#ctor(System.String,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Compilation.ImpromptuInterface.InvokeArg`1"/> class.
            </summary>
            <param name="name">The name.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.InvokeArg`1.op_Explicit(System.Collections.Generic.KeyValuePair{System.String,`0})~RazorEngine.Compilation.ImpromptuInterface.InvokeArg{`0}">
            <summary>
            Performs an explicit conversion from <see cref="T:System.Collections.Generic.KeyValuePair`2"/> to <see cref="T:RazorEngine.Compilation.ImpromptuInterface.InvokeArg`1"/>.
            </summary>
            <param name="pair">The pair.</param>
            <returns>The result of the conversion.</returns>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.StaticContext">
            <summary>
            Specific version of InvokeContext which declares a type to be used to invoke static methods.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.StaticContext.op_Explicit(System.Type)~RazorEngine.Compilation.ImpromptuInterface.StaticContext">
            <summary>
            Performs an explicit conversion from <see cref="T:System.Type"/> to <see cref="T:RazorEngine.Compilation.ImpromptuInterface.StaticContext"/>.
            </summary>
            <param name="type">The type.</param>
            <returns>The result of the conversion.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.StaticContext.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Compilation.ImpromptuInterface.StaticContext"/> class.
            </summary>
            <param name="target">The target.</param>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.InvokeContext">
            <summary>
            Object that stores a context with a target for dynamic invocation
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.InvokeContext.CreateContext">
            <summary>
            Create Function can set to variable to make cleaner syntax;
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.InvokeContext.CreateStatic">
            <summary>
            Create Function can set to variable to make cleaner syntax;
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.InvokeContext.CreateStaticWithContext">
            <summary>
            Create Function can set to variable to make cleaner syntax;
            </summary>
        </member>
        <member name="P:RazorEngine.Compilation.ImpromptuInterface.InvokeContext.Target">
            <summary>
            Gets or sets the target.
            </summary>
            <value>The target.</value>
        </member>
        <member name="P:RazorEngine.Compilation.ImpromptuInterface.InvokeContext.Context">
            <summary>
            Gets or sets the context.
            </summary>
            <value>The context.</value>
        </member>
        <member name="P:RazorEngine.Compilation.ImpromptuInterface.InvokeContext.StaticContext">
            <summary>
            Gets or sets a value indicating whether [static context].
            </summary>
            <value><c>true</c> if [static context]; otherwise, <c>false</c>.</value>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.InvokeContext.#ctor(System.Type,System.Boolean,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Compilation.ImpromptuInterface.InvokeContext"/> class.
            </summary>
            <param name="target">The target.</param>
            <param name="staticContext">if set to <c>true</c> [static context].</param>
            <param name="context">The context.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.InvokeContext.#ctor(System.Object,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Compilation.ImpromptuInterface.InvokeContext"/> class.
            </summary>
            <param name="target">The target.</param>
            <param name="context">The context.</param>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.InvokeExt.InvokeExt">
            <summary>
            Various extension methods for add
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.InvokeExt.InvokeExt.WithContext(System.Object,System.Type)">
            <summary>
            Combines target with context.
            </summary>
            <param name="target">The target.</param>
            <param name="context">The context.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.InvokeExt.InvokeExt.WithContext``1(System.Object)">
            <summary>
            Combines target with context.
            </summary>
            <typeparam name="TContext">The type of the context.</typeparam>
            <param name="target">The target.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.InvokeExt.InvokeExt.WithContext(System.Object,System.Object)">
            <summary>
            Combines target with context.
            </summary>
            <param name="target">The target.</param>
            <param name="context">The context.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.InvokeExt.InvokeExt.WithStaticContext(System.Type,System.Object)">
            <summary>
            Withes the static context.
            </summary>
            <param name="target">The target.</param>
            <param name="context">The context.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.InvokeExt.InvokeExt.WithGenericArgs(System.String,System.Type[])">
            <summary>
            attaches generic args to string
            </summary>
            <param name="name">The name.</param>
            <param name="genericArgs">The generic args.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.InvokeExt.InvokeExt.WithArgumentName(System.Object,System.String)">
            <summary>
            attaches name of the argument.
            </summary>
            <param name="argument">The argument.</param>
            <param name="name">The name.</param>
            <returns></returns>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.String_OR_InvokeMemberName">
            <summary>
            String or InvokeMemberName
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.String_OR_InvokeMemberName.op_Implicit(System.String)~RazorEngine.Compilation.ImpromptuInterface.String_OR_InvokeMemberName">
            <summary>
            Performs an implicit conversion from <see cref="T:System.String"/> to <see cref="T:RazorEngine.Compilation.ImpromptuInterface.String_OR_InvokeMemberName"/>.
            </summary>
            <param name="name">The name.</param>
            <returns>The result of the conversion.</returns>
        </member>
        <member name="P:RazorEngine.Compilation.ImpromptuInterface.String_OR_InvokeMemberName.Name">
            <summary>
            Gets the name.
            </summary>
            <value>The name.</value>
        </member>
        <member name="P:RazorEngine.Compilation.ImpromptuInterface.String_OR_InvokeMemberName.GenericArgs">
            <summary>
            Gets the generic args.
            </summary>
            <value>The generic args.</value>
        </member>
        <member name="P:RazorEngine.Compilation.ImpromptuInterface.String_OR_InvokeMemberName.IsSpecialName">
            <summary>
            Gets or sets a value indicating whether this member is special name.
            </summary>
            <value>
            	<c>true</c> if this instance is special name; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.InvokeMemberName">
            <summary>
            Name of Member with associated Generic parameterss
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.InvokeMemberName.Create">
            <summary>
            Create Function can set to variable to make cleaner syntax;
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ImpromptuInterface.InvokeMemberName.CreateSpecialName">
            <summary>
            Create Function can set to variable to make cleaner syntax;
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.InvokeMemberName.op_Implicit(System.String)~RazorEngine.Compilation.ImpromptuInterface.InvokeMemberName">
            <summary>
            Performs an implicit conversion from <see cref="T:System.String"/> to <see cref="T:RazorEngine.Compilation.ImpromptuInterface.InvokeMemberName"/>.
            </summary>
            <param name="name">The name.</param>
            <returns>The result of the conversion.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.InvokeMemberName.#ctor(System.String,System.Type[])">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Compilation.ImpromptuInterface.InvokeMemberName"/> class.
            </summary>
            <param name="name">The name.</param>
            <param name="genericArgs">The generic args.</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.InvokeMemberName.#ctor(System.String,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Compilation.ImpromptuInterface.InvokeMemberName"/> class.
            </summary>
            <param name="name">The name.</param>
            <param name="isSpecialName">if set to <c>true</c> [is special name].</param>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.InvokeMemberName.Equals(RazorEngine.Compilation.ImpromptuInterface.InvokeMemberName)">
            <summary>
            Equalses the specified other.
            </summary>
            <param name="other">The other.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.InvokeMemberName.EqualsHelper(RazorEngine.Compilation.ImpromptuInterface.InvokeMemberName)">
            <summary>
            Equalses the specified other.
            </summary>
            <param name="other">The other.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.InvokeMemberName.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object"/> is equal to this instance.
            </summary>
            <param name="obj">The <see cref="T:System.Object"/> to compare with this instance.</param>
            <returns>
            	<c>true</c> if the specified <see cref="T:System.Object"/> is equal to this instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:RazorEngine.Compilation.ImpromptuInterface.InvokeMemberName.GetHashCode">
            <summary>
            Returns a hash code for this instance.
            </summary>
            <returns>
            A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table. 
            </returns>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.NonRecursiveInterfaceAttribute">
            <summary>
            Attribute on Inteface to stop proxy from recursively
            proxying other interfaces
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ImpromptuInterface.UseNamedArgumentAttribute">
            <summary>
            Attribute for Methods and Parameters on Custom Interfaces designed to be used with a dynamic implementation
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ExecutionContextLessThread">
            <summary>
            The sole purpose of this class is to fix https://github.com/Antaris/RazorEngine/issues/267.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.HasDynamicModelAttribute">
            <summary>
            Defines an attribute that marks the presence of a dynamic model in a template.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.Inspectors.ICodeInspector">
            <summary>
            Defines the required contract for implementing a code inspector.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.Inspectors.ICodeInspector.Inspect(System.CodeDom.CodeCompileUnit,System.CodeDom.CodeNamespace,System.CodeDom.CodeTypeDeclaration,System.CodeDom.CodeMemberMethod)">
            <summary>
            Inspects the specified code unit.
            </summary>
            <param name="unit">The code unit.</param>
            <param name="ns">The code namespace declaration.</param>
            <param name="type">The code type declaration.</param>
            <param name="executeMethod">The code method declaration for the Execute method.</param>
        </member>
        <member name="T:RazorEngine.Compilation.ICompilerService">
            <summary>
            Defines the required contract for implementing a compiler service.
            </summary>
        </member>
        <member name="P:RazorEngine.Compilation.ICompilerService.CodeInspectors">
            <summary>
            Gets or sets the set of code inspectors.
            </summary>
        </member>
        <member name="P:RazorEngine.Compilation.ICompilerService.ReferenceResolver">
            <summary>
            Gets or sets the reference resolver.
            </summary>
        </member>
        <member name="P:RazorEngine.Compilation.ICompilerService.Debug">
            <summary>
            Gets or sets whether the compiler service is operating in debug mode.
            </summary>
        </member>
        <member name="P:RazorEngine.Compilation.ICompilerService.DisableTempFileLocking">
            <summary>
            Gets or sets whether the compiler should load assemblies with Assembly.Load(byte[])
            to prevent files from being locked.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ICompilerService.CompileType(RazorEngine.Compilation.TypeContext)">
            <summary>
            Compiles the type defined in the specified type context.
            </summary>
            <param name="context">The type context which defines the type to compile.</param>
            <returns>The compiled type.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.ICompilerService.IncludeAssemblies">
            <summary>
            Returns a set of assemblies that must be referenced by the compiled template.
            </summary>
            <returns>The set of assemblies.</returns>
        </member>
        <member name="T:RazorEngine.Compilation.ICompilerServiceFactory">
            <summary>
            Defines the required contract for implementing a compiler service factory.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ICompilerServiceFactory.CreateCompilerService(RazorEngine.Language)">
            <summary>
            Creates a <see cref="T:RazorEngine.Compilation.ICompilerService"/> that supports the specified language.
            </summary>
            <param name="language">The <see cref="T:RazorEngine.Language"/>.</param>
            <returns>An instance of <see cref="T:RazorEngine.Compilation.ICompilerService"/>.</returns>
        </member>
        <member name="T:RazorEngine.Compilation.RazorDynamicObject">
            <summary>
            Wraps a dynamic object for serialization of dynamic objects and anonymous type support.
            But this type will also make (static) types work which are not serializable.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.RazorDynamicObject.MarshalWrapper">
            <summary>
            A helper class to make sure the wrapped object does not leave its <see cref="T:System.AppDomain"/>.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.RazorDynamicObject.MarshalWrapper.#ctor(System.Object,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Compilation.RazorDynamicObject.MarshalWrapper"/> class.
            </summary>
            <param name="wrapped">the wrapped object.</param>
            <param name="allowMissingMembers">true when we allow missing properties.</param>
        </member>
        <member name="M:RazorEngine.Compilation.RazorDynamicObject.MarshalWrapper.TryFindInvokeMember(System.Type,System.String,System.Object[],System.Type[],System.Object@)">
            <summary>
            Tries to find a member with the given name, the given arguments 
            and the given parameter types and invokes that member.
            </summary>
            <param name="typeToSearch">the type we search for that member.</param>
            <param name="name">the name of the member</param>
            <param name="args">the arguments of the member</param>
            <param name="paramTypes">the type of the arguments of the member</param>
            <param name="result">the result of invoking the found member.</param>
            <returns>true if a member was found and invoked.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.RazorDynamicObject.MarshalWrapper.Unwrap">
            <summary>
            Unwrap the currently wrapped object 
            (note that this can cause crossing an app-domain boundary).
            </summary>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.RazorDynamicObject.MarshalWrapper.GetResult(RazorEngine.Compilation.ImpromptuInterface.Dynamic.Invocation)">
            <summary>
            This method is used to delegate the invocation across the <see cref="T:System.AppDomain"/>.
            </summary>
            <param name="invocation">The invocation to cross the <see cref="T:System.AppDomain"/>.</param>
            <returns>The result of the invocation on the wrapped instance.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.RazorDynamicObject.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Compilation.RazorDynamicObject"/> class.
            </summary>
            <param name="info">The info.</param>
            <param name="context">The context.</param>
        </member>
        <member name="M:RazorEngine.Compilation.RazorDynamicObject.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Populates a <see cref="T:System.Runtime.Serialization.SerializationInfo"/> with the data needed to serialize the target object.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> to populate with data.</param>
            <param name="context">The destination (see <see cref="T:System.Runtime.Serialization.StreamingContext"/>) for this serialization.</param>
            <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
        </member>
        <member name="M:RazorEngine.Compilation.RazorDynamicObject.MapType(System.Type)">
            <summary>
            Try to find a type instance which has no references to anonymous types.
            Either we use the type or create a new one which implements the same interfaces.
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.RazorDynamicObject.MapInterface(System.Type)">
            <summary>
            Convert the given interface type instance in another interface 
            type instance which is free of anonymous types.
            </summary>
            <param name="interface"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.RazorDynamicObject.IsWrapped(System.Object)">
            <summary>
            Check if an instance is already wrapped with <see cref="T:RazorEngine.Compilation.RazorDynamicObject"/>.
            </summary>
            <param name="wrapped">the object to check.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.RazorDynamicObject.Create(System.Object,System.Boolean)">
            <summary>
            Create a wrapper around an dynamic object.
            This wrapper ensures that we can cross the <see cref="T:System.AppDomain"/>, 
            call internal methods (to make Anonymous types work), 
            or call missing methods (when allowMissingMembers is true).
            </summary>
            <param name="wrapped">The object to wrap.</param>
            <param name="allowMissingMembers">true when we should not throw when missing members are invoked.</param>
            <returns>the wrapped object.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.RazorDynamicObject.Cast``1(System.Object)">
            <summary>
            A simple generic cast method. Used for the DynamicCast implementation.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.RazorDynamicObject.DynamicCast(System.Object,System.Type)">
            <summary>
            A tricky dynamic cast (Cast in the runtime with dynamic types).
            </summary>
            <param name="o"></param>
            <param name="targetType"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.RazorDynamicObject.CompatibleWith(System.Reflection.ParameterInfo[],System.Type[])">
            <summary>
            Checks if the fiven ParameterInfo array is compatible with the given type array.
            </summary>
            <param name="parameterInfo"></param>
            <param name="paramTypes"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.RazorDynamicObject.IsPrimitive(System.Object)">
            <summary>
            Returnes true when the type of the given result is primitive.
            (ie should not be wrapped in another <see cref="T:RazorEngine.Compilation.RazorDynamicObject"/> instance)
            </summary>
            <param name="target">the object to check</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.RazorDynamicObject.RemoteInvoke(RazorEngine.Compilation.ImpromptuInterface.Dynamic.Invocation,System.Object@)">
            <summary>
            Captures the invocation and invokes it on the wrapped object (possibly across the <see cref="T:System.AppDomain"/> boundary.
            </summary>
            <param name="invocation">The invocation</param>
            <param name="result">the result</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.RazorDynamicObject.TryGetMember(System.Dynamic.GetMemberBinder,System.Object@)">
            <summary>
            Tries to get the member.
            </summary>
            <param name="binder">The binder.</param>
            <param name="result">The result.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.RazorDynamicObject.DynamicUnwrap">
            <summary>
            Unwraps the current RazorDynamicObject.
            </summary>
            <returns>the unwrapped object.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.RazorDynamicObject.Unwrap(System.Object)">
            <summary>
            Unwraps the current dynamic object.
            </summary>
            <returns>the unwrapped object.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.RazorDynamicObject.TryConvert(System.Dynamic.ConvertBinder,System.Object@)">
            <summary>
            Tries to convert the current instance.
            </summary>
            <param name="binder">The binder.</param>
            <param name="result">The result.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.RazorDynamicObject.TrySetMember(System.Dynamic.SetMemberBinder,System.Object)">
            <summary>
            Tries to set the member.
            </summary>
            <param name="binder">The binder.</param>
            <param name="value">The value.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.RazorDynamicObject.TryBinaryOperation(System.Dynamic.BinaryOperationBinder,System.Object,System.Object@)">
            <summary>
            Forwards the binary operation
            </summary>
            <param name="binder"></param>
            <param name="arg"></param>
            <param name="result"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.RazorDynamicObject.TryUnaryOperation(System.Dynamic.UnaryOperationBinder,System.Object@)">
            <summary>
            Forwads the unary operation
            </summary>
            <param name="binder"></param>
            <param name="result"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.RazorDynamicObject.TryInvokeMember(System.Dynamic.InvokeMemberBinder,System.Object[],System.Object@)">
            <summary>
            Tries the invoke member.
            </summary>
            <param name="binder">The binder.</param>
            <param name="args">The args.</param>
            <param name="result">The result.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.RazorDynamicObject.TryGetIndex(System.Dynamic.GetIndexBinder,System.Object[],System.Object@)">
            <summary>
            Tries the index of the get.
            </summary>
            <param name="binder">The binder.</param>
            <param name="indexes">The indexes.</param>
            <param name="result">The result.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.RazorDynamicObject.TrySetIndex(System.Dynamic.SetIndexBinder,System.Object[],System.Object)">
            <summary>
            Tries the index of the set.
            </summary>
            <param name="binder">The binder.</param>
            <param name="indexes">The indexes.</param>
            <param name="value">The value.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.RazorDynamicObject.ToString">
            <summary>
            Override ToString and remotely invoke our wrapped instance.
            </summary>
            <returns>Whatever our wrapped instance returns.</returns>
        </member>
        <member name="M:RazorEngine.Compilation.RazorDynamicObject.Finalize">
            <summary>
            Cleans up the <see cref="T:RazorEngine.Compilation.RazorDynamicObject"/> instance.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.RazorDynamicObject.Dispose">
            <summary>
            Disposes the current instance.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.RazorDynamicObject.Dispose(System.Boolean)">
            <summary>
            Disposes the current instance via the disposable pattern.
            </summary>
            <param name="disposing">true when Dispose() was called manually.</param>
        </member>
        <member name="T:RazorEngine.Compilation.RazorEngineHost">
            <summary>
            Defines the custom razor engine host.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.RazorEngineHost.#ctor(System.Web.Razor.RazorCodeLanguage,System.Func{System.Web.Razor.Parser.ParserBase})">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Compilation.RazorEngineHost"/>.
            </summary>
            <param name="language">The code language.</param>
            <param name="markupParserFactory">The markup parser factory delegate.</param>
        </member>
        <member name="P:RazorEngine.Compilation.RazorEngineHost.DefaultBaseTemplateType">
            <summary>
            Gets or sets the default template type.
            </summary>
        </member>
        <member name="P:RazorEngine.Compilation.RazorEngineHost.DefaultModelType">
            <summary>
            Gets or sets the default model type.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.RazorEngineHost.DecorateCodeParser(System.Web.Razor.Parser.ParserBase)">
            <summary>
            Decorates the code parser.
            </summary>
            <param name="incomingCodeParser">The code parser.</param>
            <returns>The decorated parser.</returns>
        </member>
        <member name="T:RazorEngine.Compilation.ReferenceResolver.CompilerReference">
            <summary>
            Represents a reference for the compiler
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ReferenceResolver.CompilerReference.ICompilerReferenceVisitor`1">
            <summary>
            Visitor pattern for the <see cref="T:RazorEngine.Compilation.ReferenceResolver.CompilerReference"/> class.
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="M:RazorEngine.Compilation.ReferenceResolver.CompilerReference.ICompilerReferenceVisitor`1.Visit(System.Reflection.Assembly)">
            <summary>
            Handle a direct assembly reference
            </summary>
            <param name="assembly"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ReferenceResolver.CompilerReference.ICompilerReferenceVisitor`1.Visit(System.String)">
            <summary>
            Handle a file reference.
            </summary>
            <param name="file"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ReferenceResolver.CompilerReference.ICompilerReferenceVisitor`1.Visit(System.IO.Stream)">
            <summary>
            Handle a stream reference.
            </summary>
            <param name="stream"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ReferenceResolver.CompilerReference.ICompilerReferenceVisitor`1.Visit(System.Byte[])">
            <summary>
            Handle a byte array reference.
            </summary>
            <param name="byteArray"></param>
            <returns></returns>
        </member>
        <member name="T:RazorEngine.Compilation.ReferenceResolver.CompilerReference.CompilerReferenceType">
            <summary>
            The Type of the reference
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ReferenceResolver.CompilerReference.CompilerReferenceType.FileReference">
            <summary>
            Reference to a file
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ReferenceResolver.CompilerReference.CompilerReferenceType.DirectAssemblyReference">
            <summary>
            Reference to a assembly instace
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ReferenceResolver.CompilerReference.CompilerReferenceType.StreamReference">
            <summary>
            Reference to a assembly stream.
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.ReferenceResolver.CompilerReference.CompilerReferenceType.ByteArrayReference">
            <summary>
            Reference to a assembly within a byte array.
            </summary>
        </member>
        <member name="P:RazorEngine.Compilation.ReferenceResolver.CompilerReference.ReferenceType">
            <summary>
            The type of the current reference.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ReferenceResolver.CompilerReference.Visit``1(RazorEngine.Compilation.ReferenceResolver.CompilerReference.ICompilerReferenceVisitor{``0})">
            <summary>
            execute the given visitor.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="visitor"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ReferenceResolver.CompilerReference.From(System.String)">
            <summary>
            Create a compiler reference from the given file.
            </summary>
            <param name="file"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ReferenceResolver.CompilerReference.From(System.Reflection.Assembly)">
            <summary>
            Create a compiler reference from the given assembly.
            NOTE: The CodeDom compiler doesn't support assembly references where assembly.Location is null (roslyn only)!
            </summary>
            <param name="assembly"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ReferenceResolver.CompilerReference.From(System.IO.Stream)">
            <summary>
            Create a compiler reference from the given stream.
            NOTE: The CodeDom compiler doesn't support stream references (roslyn only)!
            </summary>
            <param name="stream"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ReferenceResolver.CompilerReference.From(System.Byte[])">
            <summary>
            Create a compiler reference from the given byte array.
            NOTE: The CodeDom compiler doesn't support byte array references (roslyn only)!
            </summary>
            <param name="byteArray"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ReferenceResolver.CompilerReference.Resolve(System.String,System.Collections.Generic.IEnumerable{RazorEngine.Compilation.ReferenceResolver.CompilerReference})">
            <summary>
            Default implementation for resolving an assembly name.
            </summary>
            <param name="assemblyName">name of the assembly to resolve</param>
            <param name="references">references to check</param>
            <returns>the resolved assembly or null</returns>
        </member>
        <member name="M:RazorEngine.Compilation.ReferenceResolver.CompilerReference.GetFile(System.Func{System.String,System.Exception})">
            <summary>
            Try to resolve the reference to a file (throws when this is not possible).
            </summary>
            <param name="exceptionCreator"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ReferenceResolver.CompilerReference.Equals(System.Object)">
            <summary>
            Checks if the given object is equal to the current object.
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Compilation.ReferenceResolver.CompilerReference.GetHashCode">
            <summary>
            Gets a hashcode for the current object.
            </summary>
            <returns></returns>
        </member>
        <member name="T:RazorEngine.Compilation.ReferenceResolver.CompilerReference.SelectFileVisitor">
            <summary>
            A visitor for the GetFile function.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.ReferenceResolver.CompilerReference.FileReference">
            <summary>
            A file reference.
            </summary>
        </member>
        <member name="P:RazorEngine.Compilation.ReferenceResolver.CompilerReference.FileReference.File">
            <summary>
            The referenced file.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ReferenceResolver.CompilerReference.FileReference.Visit``1(RazorEngine.Compilation.ReferenceResolver.CompilerReference.ICompilerReferenceVisitor{``0})">
            <summary>
            Visit the given visitor.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="visitor"></param>
            <returns></returns>
        </member>
        <member name="T:RazorEngine.Compilation.ReferenceResolver.CompilerReference.DirectAssemblyReference">
            <summary>
            A direct assembly reference.
            </summary>
        </member>
        <member name="P:RazorEngine.Compilation.ReferenceResolver.CompilerReference.DirectAssemblyReference.Assembly">
            <summary>
            The referenced assembly.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ReferenceResolver.CompilerReference.DirectAssemblyReference.Visit``1(RazorEngine.Compilation.ReferenceResolver.CompilerReference.ICompilerReferenceVisitor{``0})">
            <summary>
            Visit the visitor.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="visitor"></param>
            <returns></returns>
        </member>
        <member name="T:RazorEngine.Compilation.ReferenceResolver.CompilerReference.StreamReference">
            <summary>
            A stream reference.
            </summary>
        </member>
        <member name="P:RazorEngine.Compilation.ReferenceResolver.CompilerReference.StreamReference.Stream">
            <summary>
            The referenced stream.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ReferenceResolver.CompilerReference.StreamReference.Visit``1(RazorEngine.Compilation.ReferenceResolver.CompilerReference.ICompilerReferenceVisitor{``0})">
            <summary>
            Visit the given visitor.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="visitor"></param>
            <returns></returns>
        </member>
        <member name="T:RazorEngine.Compilation.ReferenceResolver.CompilerReference.ByteArrayReference">
            <summary>
            A byte array reference.
            </summary>
        </member>
        <member name="P:RazorEngine.Compilation.ReferenceResolver.CompilerReference.ByteArrayReference.ByteArray">
            <summary>
            The referenced byte array.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ReferenceResolver.CompilerReference.ByteArrayReference.Visit``1(RazorEngine.Compilation.ReferenceResolver.CompilerReference.ICompilerReferenceVisitor{``0})">
            <summary>
            Visit the given visitor.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="visitor"></param>
            <returns></returns>
        </member>
        <member name="T:RazorEngine.Compilation.ReferenceResolver.IReferenceResolver">
            <summary>
            Tries to resolve the references for a given compilation option.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ReferenceResolver.IReferenceResolver.GetReferences(RazorEngine.Compilation.TypeContext,System.Collections.Generic.IEnumerable{RazorEngine.Compilation.ReferenceResolver.CompilerReference})">
            <summary>
            Resolve the reference for a compilation process.
            </summary>
            <param name="context">gives context about the compilation process.</param>
            <param name="includeAssemblies">The references that should be included (requested by the compiler itself)</param>
            <returns>the references which will be used in the compilation process.</returns>
        </member>
        <member name="T:RazorEngine.Compilation.ReferenceResolver.UseCurrentAssembliesReferenceResolver">
            <summary>
            Resolves the assemblies by using all currently loaded assemblies. See <see cref="T:RazorEngine.Compilation.ReferenceResolver.IReferenceResolver"/>
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.ReferenceResolver.UseCurrentAssembliesReferenceResolver.GetReferences(RazorEngine.Compilation.TypeContext,System.Collections.Generic.IEnumerable{RazorEngine.Compilation.ReferenceResolver.CompilerReference})">
            <summary>
            See <see cref="M:RazorEngine.Compilation.ReferenceResolver.IReferenceResolver.GetReferences(RazorEngine.Compilation.TypeContext,System.Collections.Generic.IEnumerable{RazorEngine.Compilation.ReferenceResolver.CompilerReference})"/>
            </summary>
            <param name="context"></param>
            <param name="includeAssemblies"></param>
            <returns></returns>
        </member>
        <member name="T:RazorEngine.Compilation.CompilationData">
            <summary>
            Provides (temporary) data about an compilation process.
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.CompilationData.tmpFolder">
            <summary>
            The temporary folder for the compilation process
            </summary>
        </member>
        <member name="F:RazorEngine.Compilation.CompilationData.srcCode">
            <summary>
            The generated source code for the template.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.CompilationData.#ctor(System.String,System.String)">
            <summary>
            Creates a new CompilationData instance.
            </summary>
            <param name="sourceCode">The generated source code for the template.</param>
            <param name="tmpFolder">The temporary folder for the compilation process</param>
        </member>
        <member name="P:RazorEngine.Compilation.CompilationData.SourceCode">
            <summary>
            The generated source code of the template (can be null).
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.CompilationData.DeleteAll">
            <summary>
            Deletes all remaining files
            </summary>
        </member>
        <member name="P:RazorEngine.Compilation.CompilationData.TmpFolder">
            <summary>
            returns the temporary folder for the compilation process (can be null).
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.CompilationData.Finalize">
            <summary>
            Destructs the current instance.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.CompilationData.Dispose">
            <summary>
            Clean up the compilation (ie delete temporary files).
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.CompilationData.Dispose(System.Boolean)">
            <summary>
            Cleans up the data of the current compilation.
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="T:RazorEngine.Compilation.TypeContext">
            <summary>
            Defines a type context that describes a template to compile.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.TypeContext.#ctor(System.Action{System.Collections.Generic.IEnumerable{RazorEngine.Compilation.ReferenceResolver.CompilerReference}})">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Compilation.TypeContext"/>.
            </summary>
            <param name="addReferences"></param>
        </member>
        <member name="M:RazorEngine.Compilation.TypeContext.#ctor(System.String,System.Collections.Generic.ISet{System.String})">
            <summary>
            Creates a new TypeContext instance with the given classname and the given namespaces.
            </summary>
            <param name="className"></param>
            <param name="namespaces"></param>
        </member>
        <member name="P:RazorEngine.Compilation.TypeContext.ClassName">
            <summary>
            Gets the class name.
            </summary>
        </member>
        <member name="P:RazorEngine.Compilation.TypeContext.ModelType">
            <summary>
            Gets or sets the model type.
            </summary>
        </member>
        <member name="P:RazorEngine.Compilation.TypeContext.Namespaces">
            <summary>
            Gets the set of namespace imports.
            </summary>
        </member>
        <member name="P:RazorEngine.Compilation.TypeContext.TemplateContent">
            <summary>
            Gets or sets the template content.
            </summary>
        </member>
        <member name="P:RazorEngine.Compilation.TypeContext.TemplateType">
            <summary>
            Gets or sets the base template type.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.TypeContext.AddReferences(System.Collections.Generic.IEnumerable{RazorEngine.Compilation.ReferenceResolver.CompilerReference})">
            <summary>
            Adds compiler references to the current dynamic assembly resolve list.
            </summary>
            <param name="references">the references to add to the dynamic resolve list.</param>
        </member>
        <member name="T:RazorEngine.Compilation.VisualBasic.VBCodeParser">
            <summary>
            Defines a code parser that supports the VB syntax.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.VisualBasic.VBCodeParser.#ctor">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Compilation.VisualBasic.VBCodeParser"/>
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.VisualBasic.VBCodeParser.InheritsStatement">
            <summary>
            Parses the inherits statement.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.VisualBasic.VBCodeParser.ModelTypeDirective">
            <summary>
            Parses the modeltype statement.
            </summary>
        </member>
        <member name="T:RazorEngine.Compilation.VisualBasic.VBDirectCompilerService">
            <summary>
            Defines a direct compiler service for the VB syntax.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.VisualBasic.VBDirectCompilerService.#ctor(System.Boolean,System.Func{System.Web.Razor.Parser.ParserBase})">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Compilation.VisualBasic.VBDirectCompilerService"/>.
            </summary>
            <param name="strictMode">Specifies whether the strict mode parsing is enabled.</param>
            <param name="markupParserFactory">The markup parser to use.</param>
        </member>
        <member name="P:RazorEngine.Compilation.VisualBasic.VBDirectCompilerService.SourceFileExtension">
            <summary>
            Extension of a source file without dot ("cs" for C# files or "vb" for VB.NET files).
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.VisualBasic.VBDirectCompilerService.BuildTypeName(System.Type,System.Type)">
            <summary>
            Builds a type name for the specified template type.
            </summary>
            <param name="templateType">The template type.</param>
            <param name="modelType">The model type.</param>
            <returns>The string type name (including namespace).</returns>
        </member>
        <member name="T:RazorEngine.Compilation.VisualBasic.VBRazorCodeGenerator">
            <summary>
            Defines a code generator that supports VB syntax.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.VisualBasic.VBRazorCodeGenerator.#ctor(System.String,System.String,System.String,System.Web.Razor.RazorEngineHost,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Compilation.VisualBasic.VBRazorCodeGenerator"/> class.
            </summary>
            <param name="className">Name of the class.</param>
            <param name="rootNamespaceName">Name of the root namespace.</param>
            <param name="sourceFileName">Name of the source file.</param>
            <param name="host">The host.</param>
            <param name="strictMode">Flag to specify that this generator is running in struct mode.</param>
        </member>
        <member name="P:RazorEngine.Compilation.VisualBasic.VBRazorCodeGenerator.StrictMode">
            <summary>
            Gets whether the code generator is running in strict mode.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.VisualBasic.VBRazorCodeGenerator.VisitError(System.Web.Razor.Parser.SyntaxTree.RazorError)">
            <summary>
            Visits an error generated through parsing.
            </summary>
            <param name="err">The error that was generated.</param>
        </member>
        <member name="T:RazorEngine.Compilation.VisualBasic.VBRazorCodeLanguage">
            <summary>
            Provides a razor code language that supports the VB language.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.VisualBasic.VBRazorCodeLanguage.#ctor(System.Boolean)">
            <summary>
            Initialises a new instance
            </summary>
            <param name="strictMode">Flag to determine whether strict mode is enabled.</param>
        </member>
        <member name="P:RazorEngine.Compilation.VisualBasic.VBRazorCodeLanguage.StrictMode">
            <summary>
            Gets whether strict mode is enabled.
            </summary>
        </member>
        <member name="M:RazorEngine.Compilation.VisualBasic.VBRazorCodeLanguage.CreateCodeGenerator(System.String,System.String,System.String,System.Web.Razor.RazorEngineHost)">
            <summary>
            Creates the code generator.
            </summary>
            <param name="className">Name of the class.</param>
            <param name="rootNamespaceName">Name of the root namespace.</param>
            <param name="sourceFileName">Name of the source file.</param>
            <param name="host">The host.</param>
            <returns>An instance of <see cref="T:System.Web.Razor.Generator.RazorCodeGenerator"/>.</returns>
        </member>
        <member name="T:RazorEngine.CrossAppDomainObject">
            <summary>
            Enables access to objects across application domain boundaries.
            This type differs from <see cref="T:System.MarshalByRefObject"/> by ensuring that the
            service lifetime is managed deterministically by the consumer.
            </summary>
        </member>
        <member name="M:RazorEngine.CrossAppDomainObject.Finalize">
            <summary>
            Cleans up the <see cref="T:RazorEngine.CrossAppDomainObject"/> instance.
            </summary>
        </member>
        <member name="M:RazorEngine.CrossAppDomainObject.Disconnect">
            <summary>
            Disconnects the remoting channel(s) of this object and all nested objects.
            </summary>
        </member>
        <member name="M:RazorEngine.CrossAppDomainObject.InitializeLifetimeService">
            <summary>
            initializes the lifetime service for the current instance.
            </summary>
            <returns>null</returns>
        </member>
        <member name="M:RazorEngine.CrossAppDomainObject.Dispose">
            <summary>
            Disposes the current instance.
            </summary>
        </member>
        <member name="M:RazorEngine.CrossAppDomainObject.Dispose(System.Boolean)">
            <summary>
            Disposes the current instance via the disposable pattern.
            </summary>
            <param name="disposing">true when Dispose() was called manually.</param>
        </member>
        <member name="T:RazorEngine.Configuration.ReadOnlyTemplateServiceConfiguration">
            <summary>
            Provides a readonly view of a configuration, and safe-copies all references.
            </summary>
        </member>
        <member name="M:RazorEngine.Configuration.ReadOnlyTemplateServiceConfiguration.#ctor(RazorEngine.Configuration.ITemplateServiceConfiguration)">
            <summary>
            Create a new readonly view (and copy) of the given configuration.
            </summary>
            <param name="config">the configuration to copy.</param>
        </member>
        <member name="P:RazorEngine.Configuration.ReadOnlyTemplateServiceConfiguration.Activator">
            <summary>
            Gets the activator.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.ReadOnlyTemplateServiceConfiguration.AllowMissingPropertiesOnDynamic">
            <summary>
            Gets or sets whether to allow missing properties on dynamic models.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.ReadOnlyTemplateServiceConfiguration.BaseTemplateType">
            <summary>
            Gets the base template type.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.ReadOnlyTemplateServiceConfiguration.CachingProvider">
            <summary>
            Gets the caching provider.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.ReadOnlyTemplateServiceConfiguration.CodeInspectors">
            <summary>
            Gets the code inspectors.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.ReadOnlyTemplateServiceConfiguration.CompilerServiceFactory">
            <summary>
            Gets the compiler service factory.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.ReadOnlyTemplateServiceConfiguration.Debug">
            <summary>
            Gets whether the template service is operating in debug mode.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.ReadOnlyTemplateServiceConfiguration.DisableTempFileLocking">
            <summary>
            Loads all dynamic assemblies with Assembly.Load(byte[]).
            This prevents temp files from being locked (which makes it impossible for RazorEngine to delete them).
            At the same time this completely shuts down any sandboxing/security.
            Use this only if you have a limited amount of static templates (no modifications on rumtime), 
            which you fully trust and when a seperate AppDomain is no solution for you!.
            This option will also hurt debugging.
            
            OK, YOU HAVE BEEN WARNED.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.ReadOnlyTemplateServiceConfiguration.EncodedStringFactory">
            <summary>
            Gets the encoded string factory.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.ReadOnlyTemplateServiceConfiguration.Language">
            <summary>
            Gets the language.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.ReadOnlyTemplateServiceConfiguration.Namespaces">
            <summary>
            Gets the namespaces.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.ReadOnlyTemplateServiceConfiguration.ReferenceResolver">
            <summary>
            Gets the reference resolver.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.ReadOnlyTemplateServiceConfiguration.Resolver">
            <summary>
            Gets the template resolver.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.ReadOnlyTemplateServiceConfiguration.TemplateManager">
            <summary>
            Gets the template resolver.
            </summary>
        </member>
        <member name="T:RazorEngine.Configuration.TemplateServiceConfiguration">
            <summary>
            Provides a default implementation of a template service configuration.
            </summary>
        </member>
        <member name="M:RazorEngine.Configuration.TemplateServiceConfiguration.#ctor">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Configuration.TemplateServiceConfiguration"/>.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.TemplateServiceConfiguration.Activator">
            <summary>
            Gets or sets the activator.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.TemplateServiceConfiguration.AllowMissingPropertiesOnDynamic">
            <summary>
            Gets or sets whether to allow missing properties on dynamic models.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.TemplateServiceConfiguration.DisableTempFileLocking">
            <summary>
            Loads all dynamic assemblies with Assembly.Load(byte[]).
            This prevents temp files from being locked (which makes it impossible for RazorEngine to delete them).
            At the same time this completely shuts down any sandboxing/security.
            Use this only if you have a limited amount of static templates (no modifications on rumtime), 
            which you fully trust and when a seperate AppDomain is no solution for you!.
            This option will also hurt debugging.
            
            OK, YOU HAVE BEEN WARNED.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.TemplateServiceConfiguration.BaseTemplateType">
            <summary>
            Gets or sets the base template type.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.TemplateServiceConfiguration.RazorEngine#Configuration#ITemplateServiceConfiguration#CodeInspectors">
            <summary>
            Gets the set of code inspectors.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.TemplateServiceConfiguration.CodeInspectors">
            <summary>
            Gets the set of code inspectors.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.TemplateServiceConfiguration.ReferenceResolver">
            <summary>
            Gets or sets the reference resolver
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.TemplateServiceConfiguration.CachingProvider">
            <summary>
            Gets or sets the caching provider.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.TemplateServiceConfiguration.CompilerServiceFactory">
            <summary>
            Gets or sets the compiler service factory.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.TemplateServiceConfiguration.Debug">
            <summary>
            Gets whether the template service is operating in debug mode.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.TemplateServiceConfiguration.EncodedStringFactory">
            <summary>
            Gets or sets the encoded string factory.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.TemplateServiceConfiguration.Language">
            <summary>
            Gets or sets the language.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.TemplateServiceConfiguration.Namespaces">
            <summary>
            Gets or sets the collection of namespaces.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.TemplateServiceConfiguration.Resolver">
            <summary>
            Gets or sets the template resolver.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.TemplateServiceConfiguration.TemplateManager">
            <summary>
            Gets or sets the template resolver.
            </summary>
        </member>
        <member name="T:RazorEngine.Configuration.FluentConfigurationBuilder">
            <summary>
            Provides a default implementation of a <see cref="T:RazorEngine.Configuration.IConfigurationBuilder"/>.
            </summary>
        </member>
        <member name="M:RazorEngine.Configuration.FluentConfigurationBuilder.#ctor(RazorEngine.Configuration.TemplateServiceConfiguration)">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Configuration.FluentConfigurationBuilder"/>.
            </summary>
            <param name="config">The default configuration that we build a new configuration from.</param>
        </member>
        <member name="M:RazorEngine.Configuration.FluentConfigurationBuilder.ActivateUsing(RazorEngine.Templating.IActivator)">
            <summary>
            Sets the activator.
            </summary>
            <param name="activator">The activator instance.</param>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.FluentConfigurationBuilder.ActivateUsing``1">
            <summary>
            Sets the activator.
            </summary>
            <typeparam name="TActivator">The activator type.</typeparam>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.FluentConfigurationBuilder.ActivateUsing(System.Func{RazorEngine.Templating.InstanceContext,RazorEngine.Templating.ITemplate})">
            <summary>
            Sets the activator.
            </summary>
            <param name="activator">The activator delegate.</param>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.FluentConfigurationBuilder.AddInspector``1">
            <summary>
            Adds the specified code inspector.
            </summary>
            <typeparam name="TInspector">The code inspector type.</typeparam>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.FluentConfigurationBuilder.AddInspector(RazorEngine.Compilation.Inspectors.ICodeInspector)">
            <summary>
            Adds the specified code inspector.
            </summary>
            <param name="inspector">The code inspector.</param>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.FluentConfigurationBuilder.AllowMissingPropertiesOnDynamic">
            <summary>
            Sets that dynamic models should be fault tollerant in accepting missing properties.
            </summary>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.FluentConfigurationBuilder.CompileUsing(RazorEngine.Compilation.ICompilerServiceFactory)">
            <summary>
            Sets the compiler service factory.
            </summary>
            <param name="factory">The compiler service factory.</param>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.FluentConfigurationBuilder.CompileUsing``1">
            <summary>
            Sets the compiler service factory.
            </summary>
            <typeparam name="TCompilerServiceFactory">The compiler service factory type.</typeparam>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.FluentConfigurationBuilder.EncodeUsing(RazorEngine.Text.IEncodedStringFactory)">
            <summary>
            Sets the encoded string factory.
            </summary>
            <param name="factory">The encoded string factory.</param>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.FluentConfigurationBuilder.EncodeUsing``1">
            <summary>
            Sets the encoded string factory.
            </summary>
            <typeparam name="TEncodedStringFactory">The encoded string factory type.</typeparam>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.FluentConfigurationBuilder.IncludeNamespaces(System.String[])">
            <summary>
            Includes the specified namespaces
            </summary>
            <param name="namespaces">The set of namespaces to include.</param>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.FluentConfigurationBuilder.ResolveUsing``1">
            <summary>
            Sets the resolve used to locate unknown templates.
            </summary>
            <typeparam name="TResolver">The resolve type.</typeparam>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.FluentConfigurationBuilder.ManageUsing``1">
            <summary>
            Sets the resolve used to locate unknown templates.
            </summary>
            <typeparam name="TResolver">The resolve type.</typeparam>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.FluentConfigurationBuilder.ResolveUsing(RazorEngine.Templating.ITemplateResolver)">
            <summary>
            Sets the resolver used to locate unknown templates.
            </summary>
            <param name="resolver">The resolver instance to use.</param>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.FluentConfigurationBuilder.ManageUsing(RazorEngine.Templating.ITemplateManager)">
            <summary>
            Sets the resolver used to locate unknown templates.
            </summary>
            <param name="resolver">The resolver instance to use.</param>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.FluentConfigurationBuilder.ResolveUsing(System.Func{System.String,System.String})">
            <summary>
            Sets the resolver delegate used to locate unknown templates.
            </summary>
            <param name="resolver">The resolver delegate to use.</param>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.FluentConfigurationBuilder.DisableTempFileLocking">
            <summary>
            Loads all dynamic assemblies with Assembly.Load(byte[]).
            This prevents temp files from being locked (which makes it impossible for RazorEngine to delete them).
            At the same time this completely shuts down any sandboxing/security.
            Use this only if you have a limited amount of static templates (no modifications on rumtime), 
            which you fully trust and when a seperate AppDomain is no solution for you!.
            This option will also hurt debugging.
            
            OK, YOU HAVE BEEN WARNED.
            </summary>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.FluentConfigurationBuilder.UseDefaultActivator">
            <summary>
            Sets the default activator.
            </summary>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.FluentConfigurationBuilder.UseDefaultCompilerServiceFactory">
            <summary>
            Sets the default compiler service factory.
            </summary>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.FluentConfigurationBuilder.UseDefaultEncodedStringFactory">
            <summary>
            Sets the default encoded string factory.
            </summary>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.FluentConfigurationBuilder.WithBaseTemplateType(System.Type)">
            <summary>
            Sets the base template type.
            </summary>
            <param name="baseTemplateType">The base template type.</param>
            <returns>The current configuration builder/.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.FluentConfigurationBuilder.WithCodeLanguage(RazorEngine.Language)">
            <summary>
            Sets the code language.
            </summary>
            <param name="language">The code language.</param>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.FluentConfigurationBuilder.WithEncoding(RazorEngine.Encoding)">
            <summary>
            Sets the encoding.
            </summary>
            <param name="encoding">The encoding.</param>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="T:RazorEngine.Configuration.FluentTemplateServiceConfiguration">
            <summary>
            Defines a fluent template service configuration
            </summary>
        </member>
        <member name="M:RazorEngine.Configuration.FluentTemplateServiceConfiguration.#ctor(System.Action{RazorEngine.Configuration.IConfigurationBuilder})">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Configuration.FluentTemplateServiceConfiguration"/>.
            </summary>
            <param name="config">The delegate used to create the configuration.</param>
        </member>
        <member name="P:RazorEngine.Configuration.FluentTemplateServiceConfiguration.Activator">
            <summary>
            Gets or sets the activator.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.FluentTemplateServiceConfiguration.AllowMissingPropertiesOnDynamic">
            <summary>
            Gets or sets whether to allow missing properties on dynamic models.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.FluentTemplateServiceConfiguration.DisableTempFileLocking">
            <summary>
            Loads all dynamic assemblies with Assembly.Load(byte[]).
            This prevents temp files from being locked (which makes it impossible for RazorEngine to delete them).
            At the same time this completely shuts down any sandboxing/security.
            Use this only if you have a limited amount of static templates (no modifications on rumtime), 
            which you fully trust and when a seperate AppDomain is no solution for you!.
            This option will also hurt debugging.
            
            OK, YOU HAVE BEEN WARNED.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.FluentTemplateServiceConfiguration.BaseTemplateType">
            <summary>
            Gets the base template type.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.FluentTemplateServiceConfiguration.CodeInspectors">
            <summary>
            Gets the set of code inspectors.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.FluentTemplateServiceConfiguration.ReferenceResolver">
            <summary>
            Gets the reference resolver.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.FluentTemplateServiceConfiguration.CachingProvider">
            <summary>
            Gets the caching provider.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.FluentTemplateServiceConfiguration.CompilerServiceFactory">
            <summary>
            Gets or sets the compiler service factory.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.FluentTemplateServiceConfiguration.Debug">
            <summary>
            Gets whether the template service is operating in debug mode.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.FluentTemplateServiceConfiguration.EncodedStringFactory">
            <summary>
            Gets or sets the encoded string factory.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.FluentTemplateServiceConfiguration.Language">
            <summary>
            Gets or sets the language.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.FluentTemplateServiceConfiguration.Namespaces">
            <summary>
            Gets or sets the collection of namespaces.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.FluentTemplateServiceConfiguration.Resolver">
            <summary>
            Gets the resolver.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.FluentTemplateServiceConfiguration.TemplateManager">
            <summary>
            Gets the template manager.
            </summary>
        </member>
        <member name="T:RazorEngine.Configuration.IConfigurationBuilder">
            <summary>
            Defines the required contract for implementing a configuration builder.
            </summary>
        </member>
        <member name="M:RazorEngine.Configuration.IConfigurationBuilder.ActivateUsing(RazorEngine.Templating.IActivator)">
            <summary>
            Sets the activator.
            </summary>
            <param name="activator">The activator instance.</param>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.IConfigurationBuilder.ActivateUsing``1">
            <summary>
            Sets the activator.
            </summary>
            <typeparam name="TActivator">The activator type.</typeparam>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.IConfigurationBuilder.ActivateUsing(System.Func{RazorEngine.Templating.InstanceContext,RazorEngine.Templating.ITemplate})">
            <summary>
            Sets the activator.
            </summary>
            <param name="activator">The activator delegate.</param>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.IConfigurationBuilder.AddInspector``1">
            <summary>
            Adds the specified code inspector.
            </summary>
            <typeparam name="TInspector">The code inspector type.</typeparam>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.IConfigurationBuilder.AddInspector(RazorEngine.Compilation.Inspectors.ICodeInspector)">
            <summary>
            Adds the specified code inspector.
            </summary>
            <param name="inspector">The code inspector.</param>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.IConfigurationBuilder.CompileUsing(RazorEngine.Compilation.ICompilerServiceFactory)">
            <summary>
            Sets the compiler service factory.
            </summary>
            <param name="factory">The compiler service factory.</param>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.IConfigurationBuilder.CompileUsing``1">
            <summary>
            Sets the compiler service factory.
            </summary>
            <typeparam name="TCompilerServiceFactory">The compiler service factory type.</typeparam>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.IConfigurationBuilder.EncodeUsing(RazorEngine.Text.IEncodedStringFactory)">
            <summary>
            Sets the encoded string factory.
            </summary>
            <param name="factory">The encoded string factory.</param>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.IConfigurationBuilder.EncodeUsing``1">
            <summary>
            Sets the encoded string factory.
            </summary>
            <typeparam name="TEncodedStringFactory">The encoded string factory type.</typeparam>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.IConfigurationBuilder.ResolveUsing``1">
            <summary>
            Sets the resolve used to locate unknown templates.
            </summary>
            <typeparam name="TResolver">The resolve type.</typeparam>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.IConfigurationBuilder.ManageUsing``1">
            <summary>
            Sets the manager used to locate unknown templates.
            </summary>
            <typeparam name="TManager">The manager type.</typeparam>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.IConfigurationBuilder.ResolveUsing(RazorEngine.Templating.ITemplateResolver)">
            <summary>
            Sets the resolver used to locate unknown templates.
            </summary>
            <param name="resolver">The resolver instance to use.</param>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.IConfigurationBuilder.ManageUsing(RazorEngine.Templating.ITemplateManager)">
            <summary>
            Sets the manager used to locate unknown templates.
            </summary>
            <param name="manager">The manager instance to use.</param>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.IConfigurationBuilder.ResolveUsing(System.Func{System.String,System.String})">
            <summary>
            Sets the resolver delegate used to locate unknown templates.
            </summary>
            <param name="resolver">The resolver delegate to use.</param>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.IConfigurationBuilder.IncludeNamespaces(System.String[])">
            <summary>
            Includes the specified namespaces
            </summary>
            <param name="namespaces">The set of namespaces to include.</param>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.IConfigurationBuilder.DisableTempFileLocking">
            <summary>
            Loads all dynamic assemblies with Assembly.Load(byte[]).
            This prevents temp files from being locked (which makes it impossible for RazorEngine to delete them).
            At the same time this completely shuts down any sandboxing/security.
            Use this only if you have a limited amount of static templates (no modifications on rumtime), 
            which you fully trust and when a seperate AppDomain is no solution for you!.
            This option will also hurt debugging.
            
            OK, YOU HAVE BEEN WARNED.
            </summary>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.IConfigurationBuilder.UseDefaultActivator">
            <summary>
            Sets the default activator.
            </summary>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.IConfigurationBuilder.UseDefaultCompilerServiceFactory">
            <summary>
            Sets the default compiler service factory.
            </summary>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.IConfigurationBuilder.UseDefaultEncodedStringFactory">
            <summary>
            Sets the default encoded string factory.
            </summary>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.IConfigurationBuilder.WithBaseTemplateType(System.Type)">
            <summary>
            Sets the base template type.
            </summary>
            <param name="baseTemplateType">The base template type.</param>
            <returns>The current configuration builder/.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.IConfigurationBuilder.WithCodeLanguage(RazorEngine.Language)">
            <summary>
            Sets the code language.
            </summary>
            <param name="language">The code language.</param>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.IConfigurationBuilder.WithEncoding(RazorEngine.Encoding)">
            <summary>
            Sets the encoding.
            </summary>
            <param name="encoding">The encoding.</param>
            <returns>The current configuration builder.</returns>
        </member>
        <member name="T:RazorEngine.Configuration.ITemplateServiceConfiguration">
            <summary>
            Defines the required contract for implementing template service configuration.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.ITemplateServiceConfiguration.Activator">
            <summary>
            Gets the activator.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.ITemplateServiceConfiguration.AllowMissingPropertiesOnDynamic">
            <summary>
            Gets or sets whether to allow missing properties on dynamic models.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.ITemplateServiceConfiguration.BaseTemplateType">
            <summary>
            Gets the base template type.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.ITemplateServiceConfiguration.CodeInspectors">
            <summary>
            Gets the code inspectors.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.ITemplateServiceConfiguration.ReferenceResolver">
            <summary>
            Gets the reference resolver.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.ITemplateServiceConfiguration.CachingProvider">
            <summary>
            Gets the caching provider.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.ITemplateServiceConfiguration.CompilerServiceFactory">
            <summary>
            Gets the compiler service factory.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.ITemplateServiceConfiguration.Debug">
            <summary>
            Gets whether the template service is operating in debug mode.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.ITemplateServiceConfiguration.DisableTempFileLocking">
            <summary>
            Loads all dynamic assemblies with Assembly.Load(byte[]).
            This prevents temp files from being locked (which makes it impossible for RazorEngine to delete them).
            At the same time this completely shuts down any sandboxing/security.
            Use this only if you have a limited amount of static templates (no modifications on rumtime), 
            which you fully trust and when a seperate AppDomain is no solution for you!.
            This option will also hurt debugging.
            
            OK, YOU HAVE BEEN WARNED.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.ITemplateServiceConfiguration.EncodedStringFactory">
            <summary>
            Gets the encoded string factory.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.ITemplateServiceConfiguration.Language">
            <summary>
            Gets the language.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.ITemplateServiceConfiguration.Namespaces">
            <summary>
            Gets the namespaces.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.ITemplateServiceConfiguration.Resolver">
            <summary>
            Gets the template resolver.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.ITemplateServiceConfiguration.TemplateManager">
            <summary>
            Gets the template resolver.
            </summary>
        </member>
        <member name="T:RazorEngine.Configuration.RazorEngineConfigurationSection">
            <summary>
            Defines the main configuration section for the RazorEngine.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.RazorEngineConfigurationSection.ActivatorType">
            <summary>
            Gets the activator type.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.RazorEngineConfigurationSection.AllowMissingPropertiesOnDynamic">
            <summary>
            Gets or sets whether to allow missing properties on dynamic models.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.RazorEngineConfigurationSection.DisableTempFileLocking">
            <summary>
            Gets or sets whether to allow missing properties on dynamic models.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.RazorEngineConfigurationSection.CompilerServiceFactoryType">
            <summary>
            Gets the compiler service factory type.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.RazorEngineConfigurationSection.ReferenceResolverType">
            <summary>
            Gets the compiler service factory type.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.RazorEngineConfigurationSection.DefaultLanguage">
            <summary>
            Gets or sets the default language.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.RazorEngineConfigurationSection.Namespaces">
            <summary>
            Gets the collection of namespaces.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.RazorEngineConfigurationSection.TemplateResolverType">
            <summary>
            Gets the template resolver type.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.RazorEngineConfigurationSection.TemplateManagerType">
            <summary>
            Gets the template resolver type.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.RazorEngineConfigurationSection.TemplateServices">
            <summary>
            Gets the collection of template service configurations.
            </summary>
        </member>
        <member name="M:RazorEngine.Configuration.RazorEngineConfigurationSection.GetConfiguration">
            <summary>
            Gets an instance of <see cref="T:RazorEngine.Configuration.RazorEngineConfigurationSection"/> that represents the current configuration.
            </summary>
            <returns>An instance of <see cref="T:RazorEngine.Configuration.RazorEngineConfigurationSection"/>, or null if no configuration is specified.</returns>
        </member>
        <member name="T:RazorEngine.Configuration.Xml.NamespaceConfigurationElement">
            <summary>
            Defines a configuration of a namespace.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.Xml.NamespaceConfigurationElement.Namespace">
            <summary>
            Gets the namespace.
            </summary>
        </member>
        <member name="T:RazorEngine.Configuration.Xml.NamespaceConfigurationElementCollection">
            <summary>
            Defines a collection of <see cref="T:RazorEngine.Configuration.Xml.NamespaceConfigurationElement"/> instances.
            </summary>
        </member>
        <member name="M:RazorEngine.Configuration.Xml.NamespaceConfigurationElementCollection.CreateNewElement">
            <summary>
            Creates a new <see cref="T:System.Configuration.ConfigurationElement"/> for use with the collection.
            </summary>
            <returns>The <see cref="T:System.Configuration.ConfigurationElement"/> instance.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.Xml.NamespaceConfigurationElementCollection.GetElementKey(System.Configuration.ConfigurationElement)">
            <summary>
            Gets a unique key for the specified element.
            </summary>
            <param name="element">The configuration element.</param>
            <returns>The key for the element.</returns>
        </member>
        <member name="T:RazorEngine.Configuration.Xml.TemplateServiceConfigurationElement">
            <summary>
            Defines a configuration of a template service.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.Xml.TemplateServiceConfigurationElement.BaseTemplateType">
            <summary>
            Gets the base template type.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.Xml.TemplateServiceConfigurationElement.Debug">
            <summary>
            Gets whether the template service is in debug mode.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.Xml.TemplateServiceConfigurationElement.EncodedStringFactoryType">
            <summary>
            Gets the encoded string factory type.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.Xml.TemplateServiceConfigurationElement.Language">
            <summary>
            Gets the language.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.Xml.TemplateServiceConfigurationElement.Name">
            <summary>
            Gets the name of the template service.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.Xml.TemplateServiceConfigurationElement.Namespaces">
            <summary>
            Gets the collection of namespaces.
            </summary>
        </member>
        <member name="T:RazorEngine.Configuration.Xml.TemplateServiceConfigurationElementCollection">
            <summary>
            Defines a collection of <see cref="T:RazorEngine.Configuration.Xml.TemplateServiceConfigurationElement"/> instances.
            </summary>
        </member>
        <member name="M:RazorEngine.Configuration.Xml.TemplateServiceConfigurationElementCollection.CreateNewElement">
            <summary>
            Creates a new <see cref="T:System.Configuration.ConfigurationElement"/> for use with the collection.
            </summary>
            <returns>The <see cref="T:System.Configuration.ConfigurationElement"/> instance.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.Xml.TemplateServiceConfigurationElementCollection.GetElementKey(System.Configuration.ConfigurationElement)">
            <summary>
            Gets a unique key for the specified element.
            </summary>
            <param name="element">The configuration element.</param>
            <returns>The key for the element.</returns>
        </member>
        <member name="T:RazorEngine.Configuration.Xml.WrapperTemplateManager">
            <summary>
            This is a simple wrapper around an <see cref="T:RazorEngine.Templating.ITemplateResolver"/> to provide
            an <see cref="T:RazorEngine.Templating.ITemplateManager"/> service.
            </summary>
        </member>
        <member name="M:RazorEngine.Configuration.Xml.WrapperTemplateManager.#ctor(RazorEngine.Templating.ITemplateResolver)">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Templating.DelegateTemplateResolver"/>.
            </summary>
            <param name="resolver">The resolver delegate.</param>
        </member>
        <member name="M:RazorEngine.Configuration.Xml.WrapperTemplateManager.Resolve(RazorEngine.Templating.ITemplateKey)">
            <summary>
            Resolves the template content with the specified key.
            </summary>
            <param name="key">The key of the template to resolve.</param>
            <returns>The template content.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.Xml.WrapperTemplateManager.AddDynamic(RazorEngine.Templating.ITemplateKey,RazorEngine.Templating.ITemplateSource)">
            <summary>
            Adds a template dynamically.
            </summary>
            <param name="key">the key of the template</param>
            <param name="source">the source of the template</param>
        </member>
        <member name="M:RazorEngine.Configuration.Xml.WrapperTemplateManager.GetKey(System.String,RazorEngine.Templating.ResolveType,RazorEngine.Templating.ITemplateKey)">
            <summary>
            Gets the key for a template.
            See <see cref="M:RazorEngine.Templating.ITemplateManager.GetKey(System.String,RazorEngine.Templating.ResolveType,RazorEngine.Templating.ITemplateKey)"/>.
            </summary>
            <param name="name">name of the template</param>
            <param name="templateType">the type of the resolve-context</param>
            <param name="context">the context (ie. parent template).</param>
            <returns></returns>
        </member>
        <member name="T:RazorEngine.Configuration.Xml.XmlTemplateServiceConfiguration">
            <summary>
            Represents a template service configuration that supports the xml configuration mechanism.
            </summary>
        </member>
        <member name="M:RazorEngine.Configuration.Xml.XmlTemplateServiceConfiguration.#ctor(System.String)">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Configuration.Xml.XmlTemplateServiceConfiguration"/>.
            </summary>
            <param name="name">The name of the template service configuration.</param>
        </member>
        <member name="P:RazorEngine.Configuration.Xml.XmlTemplateServiceConfiguration.Activator">
            <summary>
            Gets the activator.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.Xml.XmlTemplateServiceConfiguration.AllowMissingPropertiesOnDynamic">
            <summary>
            Gets or sets whether to allow missing properties on dynamic models.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.Xml.XmlTemplateServiceConfiguration.DisableTempFileLocking">
            <summary>
            Loads all dynamic assemblies with Assembly.Load(byte[]).
            This prevents temp files from being locked (which makes it impossible for RazorEngine to delete them).
            At the same time this completely shuts down any sandboxing/security.
            Use this only if you have a limited amount of static templates (no modifications on rumtime), 
            which you fully trust and when a seperate AppDomain is no solution for you!.
            This option will also hurt debugging.
            
            OK, YOU HAVE BEEN WARNED.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.Xml.XmlTemplateServiceConfiguration.BaseTemplateType">
            <summary>
            Gets the base template type.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.Xml.XmlTemplateServiceConfiguration.CodeInspectors">
            <summary>
            Gets the code inspectors.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.Xml.XmlTemplateServiceConfiguration.ReferenceResolver">
            <summary>
            Gets the reference resolver.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.Xml.XmlTemplateServiceConfiguration.CachingProvider">
            <summary>
            Gets the caching provider.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.Xml.XmlTemplateServiceConfiguration.CompilerServiceFactory">
            <summary>
            Gets the compiler service factory.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.Xml.XmlTemplateServiceConfiguration.Debug">
            <summary>
            Gets whether the template service is operating in debug mode.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.Xml.XmlTemplateServiceConfiguration.EncodedStringFactory">
            <summary>
            Gets the encoded string factory.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.Xml.XmlTemplateServiceConfiguration.Language">
            <summary>
            Gets the language.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.Xml.XmlTemplateServiceConfiguration.Namespaces">
            <summary>
            Gets the namespaces.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.Xml.XmlTemplateServiceConfiguration.Resolver">
            <summary>
            Gets the template resolver.
            </summary>
        </member>
        <member name="P:RazorEngine.Configuration.Xml.XmlTemplateServiceConfiguration.TemplateManager">
            <summary>
            Gets the template resolver.
            </summary>
        </member>
        <member name="M:RazorEngine.Configuration.Xml.XmlTemplateServiceConfiguration.AddNamespaces(RazorEngine.Configuration.Xml.NamespaceConfigurationElementCollection)">
            <summary>
            Adds the namespaces from the specified collection.
            </summary>
            <param name="namespaces">The set of namespace configurations.</param>
        </member>
        <member name="M:RazorEngine.Configuration.Xml.XmlTemplateServiceConfiguration.GetInstance``1(System.Type)">
            <summary>
            Gets an instance of the specified type.
            </summary>
            <typeparam name="T">The expected instance type.</typeparam>
            <param name="type">The type.</param>
            <returns>The instance.</returns>
        </member>
        <member name="M:RazorEngine.Configuration.Xml.XmlTemplateServiceConfiguration.GetType(System.String)">
            <summary>
            Gets the type with the specified name.
            </summary>
            <param name="typeName">The type name.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Configuration.Xml.XmlTemplateServiceConfiguration.InitialiseConfiguration(System.String)">
            <summary>
            Initialises the configuration.
            </summary>
            <param name="name">The name of the template service configuration.</param>
        </member>
        <member name="M:RazorEngine.Configuration.Xml.XmlTemplateServiceConfiguration.InitialiseConfiguration(RazorEngine.Configuration.RazorEngineConfigurationSection,RazorEngine.Configuration.Xml.TemplateServiceConfigurationElement)">
            <summary>
            Initialises the configuration.
            </summary>
            <param name="config">The core configuration.</param>
            <param name="serviceConfig">The service configuration.</param>
        </member>
        <member name="M:RazorEngine.Configuration.Xml.XmlTemplateServiceConfiguration.SetActivator(System.String)">
            <summary>
            Sets the activator.
            </summary>
            <param name="activatorType">The activator type.</param>
        </member>
        <member name="M:RazorEngine.Configuration.Xml.XmlTemplateServiceConfiguration.SetBaseTemplateType(System.String)">
            <summary>
            Sets the base template type.
            </summary>
            <param name="baseTemplateType">The base template type.</param>
        </member>
        <member name="M:RazorEngine.Configuration.Xml.XmlTemplateServiceConfiguration.SetCompilerServiceFactory(System.String)">
            <summary>
            Sets the compiler service factory.
            </summary>
            <param name="compilerServiceFactoryType">The compiler service factory type.</param>
        </member>
        <member name="M:RazorEngine.Configuration.Xml.XmlTemplateServiceConfiguration.SetReferenceResolver(System.String)">
            <summary>
            Sets the reference resolver.
            </summary>
            <param name="referenceResolverType">The reference resolver type.</param>
        </member>
        <member name="M:RazorEngine.Configuration.Xml.XmlTemplateServiceConfiguration.SetEncodedStringFactory(System.String)">
            <summary>
            Sets the encoded string factory.
            </summary>
            <param name="encodedStringFactoryType"></param>
        </member>
        <member name="M:RazorEngine.Configuration.Xml.XmlTemplateServiceConfiguration.SetTemplateResolver(System.String)">
            <summary>
            Sets the template resolver.
            </summary>
            <param name="templateResolverType">The template resolver type.</param>
        </member>
        <member name="M:RazorEngine.Configuration.Xml.XmlTemplateServiceConfiguration.SetTemplateManager(System.String)">
            <summary>
            Sets the template manager.
            </summary>
            <param name="templateManagerType">The template manager type.</param>
        </member>
        <member name="T:RazorEngine.Engine">
            <summary>
            Provides quick access to the functionality of the <see cref="T:RazorEngine.Templating.RazorEngineService"/> class.
            </summary>
        </member>
        <member name="P:RazorEngine.Engine.Razor">
            <summary>
            Quick access to RazorEngine. See <see cref="T:RazorEngine.Templating.IRazorEngineService"/>.
            </summary>
        </member>
        <member name="P:RazorEngine.Engine.IsolatedRazor">
            <summary>
            Quick access to an isolated RazorEngine. See <see cref="T:RazorEngine.Templating.IRazorEngineService"/>.
            </summary>
        </member>
        <member name="T:RazorEngine.TaskRunner">
            <summary>
            Helper for missing net40 methods, REMOVE me when we are net45 only.
            </summary>
        </member>
        <member name="M:RazorEngine.TaskRunner.Run``1(System.Func{``0})">
            <summary>
            Runs the given delegate in a new task (like Task.Run but works on net40).
            </summary>
            <typeparam name="T"></typeparam>
            <param name="t"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.TaskRunner.Run(System.Action)">
            <summary>
            Runs the given delegate in a new task (like Task.Run but works on net40).
            </summary>
            <param name="t"></param>
            <returns></returns>
        </member>
        <member name="T:RazorEngine.Templating.FullPathWithModifiedTimeTemplateKey">
            <summary>
            This implementation adds ModifiedTime property to <see cref="T:RazorEngine.Templating.FullPathTemplateKey" />
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.FullPathWithModifiedTimeTemplateKey.#ctor(System.String,System.String,System.DateTime,RazorEngine.Templating.ResolveType,RazorEngine.Templating.ITemplateKey)">
            <summary>
            Initializes a new instance
            </summary>
            <param name="name"></param>
            <param name="fullPath"></param>
            <param name="modifiedTime"></param>
            <param name="resolveType"></param>
            <param name="context"></param>
        </member>
        <member name="P:RazorEngine.Templating.FullPathWithModifiedTimeTemplateKey.ModifiedTime">
            <summary>
            This value is used to check if cache is valid
            </summary>
        </member>
        <member name="T:RazorEngine.Templating.InvalidatingByModifiedTimeCachingProvider">
            <summary>
            An memory leaking invalidating caching provider (See <see cref="T:RazorEngine.Templating.ICachingProvider"/>).
            This implementation does a very simple in-memory caching and allows you to release templates
            by trading with memory. File modification time is used to check if cached template is valid.
            WARNING:
            Use this caching provider only on AppDomains you recycle regularly, or to
            improve the debugging experience.
            Never use this in production without any recycle strategy.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.InvalidatingByModifiedTimeCachingProvider.#ctor">
            <summary>
            Initializes a new instance
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.InvalidatingByModifiedTimeCachingProvider.#ctor(System.Action{System.String})">
            <summary>
            Initializes a new instance
            </summary>
            <param name="registerForCleanup"></param>
        </member>
        <member name="M:RazorEngine.Templating.InvalidatingByModifiedTimeCachingProvider.TryRetrieveTemplate(RazorEngine.Templating.ITemplateKey,System.Type,RazorEngine.Templating.ICompiledTemplate@)">
            <summary>
            Try to retrieve a template from the cache. See <see cref="M:RazorEngine.Templating.ICachingProvider.TryRetrieveTemplate(RazorEngine.Templating.ITemplateKey,System.Type,RazorEngine.Templating.ICompiledTemplate@)"/>.
            If cached template has different modification time, then the cache is invalidated.
            </summary>
            <param name="templateKey"></param>
            <param name="modelType"></param>
            <param name="compiledTemplate"></param>
            <returns></returns>
        </member>
        <member name="T:RazorEngine.Templating.InvalidatingCachingProvider">
            <summary>
            An memory leaking invalidating caching provider (See <see cref="T:RazorEngine.Templating.ICachingProvider"/>).
            This implementation does a very simple in-memory caching and allows you to release templates
            by trading with memory.
            WARNING: 
            Use this caching provider only on AppDomains you recycle regularly, or to
            improve the debugging experience. 
            Never use this in production without any recycle strategy.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.InvalidatingCachingProvider.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Templating.DefaultCachingProvider"/> class.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.InvalidatingCachingProvider.#ctor(System.Action{System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Templating.DefaultCachingProvider"/> class.
            </summary>
            <param name="registerForCleanup">callback for files which need to be cleaned up.</param>
        </member>
        <member name="P:RazorEngine.Templating.InvalidatingCachingProvider.TypeLoader">
            <summary>
            The manages <see cref="P:RazorEngine.Templating.InvalidatingCachingProvider.TypeLoader"/>. See <see cref="P:RazorEngine.Templating.ICachingProvider.TypeLoader"/>
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.InvalidatingCachingProvider.GetModelTypeKey(System.Type)">
            <summary>
            Get the key used within a dictionary for a modelType.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.InvalidatingCachingProvider.CacheTemplate(RazorEngine.Templating.ICompiledTemplate,RazorEngine.Templating.ITemplateKey)">
            <summary>
            Caches a template. See <see cref="M:RazorEngine.Templating.ICachingProvider.CacheTemplate(RazorEngine.Templating.ICompiledTemplate,RazorEngine.Templating.ITemplateKey)"/>.
            </summary>
            <param name="template"></param>
            <param name="templateKey"></param>
        </member>
        <member name="M:RazorEngine.Templating.InvalidatingCachingProvider.InvalidateCacheOfType(RazorEngine.Templating.ITemplateKey,System.Type)">
            <summary>
            Invalidates the compilation of the given template with the given model-type.
            WARNING: leads to memory leaks
            </summary>
            <param name="templateKey"></param>
            <param name="modelType"></param>
        </member>
        <member name="M:RazorEngine.Templating.InvalidatingCachingProvider.InvalidateCache(RazorEngine.Templating.ITemplateKey)">
            <summary>
            Invalidates all compilations of the given template.
            WARNING: leads to memory leaks
            </summary>
            <param name="templateKey"></param>
        </member>
        <member name="M:RazorEngine.Templating.InvalidatingCachingProvider.InvalidateAll">
            <summary>
            Invalidates all compilations.
            WARNING: leads to memory leaks
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.InvalidatingCachingProvider.TryRetrieveTemplate(RazorEngine.Templating.ITemplateKey,System.Type,RazorEngine.Templating.ICompiledTemplate@)">
            <summary>
            Try to retrieve a template from the cache. See <see cref="M:RazorEngine.Templating.ICachingProvider.TryRetrieveTemplate(RazorEngine.Templating.ITemplateKey,System.Type,RazorEngine.Templating.ICompiledTemplate@)"/>.
            </summary>
            <param name="templateKey"></param>
            <param name="modelType"></param>
            <param name="compiledTemplate"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.InvalidatingCachingProvider.Dispose">
            <summary>
            Dispose the instance.
            </summary>
        </member>
        <member name="T:RazorEngine.Templating.DynamicWrapperService">
            <summary>
            A wrapper around an <see cref="T:RazorEngine.Templating.IRazorEngineService"/> instance to provide support for anonymous classes.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.DynamicWrapperService.CheckModelType(System.Type)">
            <summary>
            Checks if the given model-type has a reference to an anonymous type and throws.
            </summary>
            <param name="modelType">the type to check</param>
        </member>
        <member name="M:RazorEngine.Templating.DynamicWrapperService.GetDynamicModel(System.Type,System.Object,System.Boolean)">
            <summary>
            Checks if we need to wrap the given model in
            an <see cref="T:RazorEngine.Compilation.RazorDynamicObject"/> instance and wraps it.
            </summary>
            <param name="modelType">the model-type</param>
            <param name="original">the original model</param>
            <param name="allowMissing">true when we should allow missing properties on dynamic models.</param>
            <returns>the original model or an wrapper object.</returns>
        </member>
        <member name="T:RazorEngine.Templating.IsolatedRazorEngineService">
            <summary>
            Provides template parsing and compilation in an isolated application domain.
            </summary>
        </member>
        <member name="T:RazorEngine.Templating.IsolatedRazorEngineService.IConfigCreator">
            <summary>
            A helper interface to get a custom configuration into a new AppDomain.
            Classes inheriting this interface should be Serializable 
            (and not inherit from MarshalByRefObject).
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedRazorEngineService.IConfigCreator.CreateConfiguration">
            <summary>
            Create a new configuration instance.
            This method should be executed in the new AppDomain.
            </summary>
            <returns></returns>
        </member>
        <member name="T:RazorEngine.Templating.IsolatedRazorEngineService.LanguageEncodingConfigCreator">
            <summary>
            A simple <see cref="T:RazorEngine.Templating.IsolatedRazorEngineService.IConfigCreator"/> implementation to configure the <see cref="T:RazorEngine.Language"/> and the <see cref="T:RazorEngine.Encoding"/>.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedRazorEngineService.LanguageEncodingConfigCreator.#ctor(RazorEngine.Language,RazorEngine.Encoding)">
            <summary>
            Initializes a new <see cref="T:RazorEngine.Templating.IsolatedRazorEngineService.LanguageEncodingConfigCreator"/> instance
            </summary>
            <param name="language"></param>
            <param name="encoding"></param>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedRazorEngineService.LanguageEncodingConfigCreator.CreateConfiguration">
            <summary>
            Create the configuration.
            </summary>
            <returns></returns>
        </member>
        <member name="T:RazorEngine.Templating.IsolatedRazorEngineService.DefaultConfigCreator">
            <summary>
            A simple <see cref="T:RazorEngine.Templating.IsolatedRazorEngineService.IConfigCreator"/> implementation to use the default configuration.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedRazorEngineService.DefaultConfigCreator.#ctor">
            <summary>
            Initializes a new <see cref="T:RazorEngine.Templating.IsolatedRazorEngineService.DefaultConfigCreator"/> instance
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedRazorEngineService.DefaultConfigCreator.CreateConfiguration">
            <summary>
            Create the configuration.
            </summary>
            <returns></returns>
        </member>
        <member name="T:RazorEngine.Templating.IsolatedRazorEngineService.SanboxHelper">
            <summary>
            A simple sandbox helper to create the <see cref="T:RazorEngine.Templating.IRazorEngineService"/>
            in the new <see cref="T:System.AppDomain"/>.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedRazorEngineService.SanboxHelper.CreateEngine(RazorEngine.Templating.IsolatedRazorEngineService.IConfigCreator)">
            <summary>
            Create the <see cref="T:RazorEngine.Templating.IRazorEngineService"/> in the new <see cref="T:System.AppDomain"/>.
            </summary>
            <param name="configCreator"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedRazorEngineService.#ctor(RazorEngine.Templating.IsolatedRazorEngineService.IConfigCreator,RazorEngine.Templating.IAppDomainFactory)">
            <summary>
            Initializes a new instance of <see cref="T:RazorEngine.Templating.IsolatedTemplateService"/>.
            </summary>
            <param name="configCreator">The instance to provide the configuration in the new <see cref="T:System.AppDomain"/>.</param>
            <param name="appDomainFactory">The application domain factory.</param>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedRazorEngineService.Create">
            <summary>
            Creates a new <see cref="T:RazorEngine.Templating.IRazorEngineService"/> instance which executes the templates in a new <see cref="T:System.AppDomain"/>.
            </summary>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedRazorEngineService.Create(RazorEngine.Templating.IsolatedRazorEngineService.IConfigCreator)">
            <summary>
            Creates a new <see cref="T:RazorEngine.Templating.IRazorEngineService"/> instance which executes the templates in a new <see cref="T:System.AppDomain"/>.
            </summary>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedRazorEngineService.Create(RazorEngine.Templating.IAppDomainFactory)">
            <summary>
            Creates a new <see cref="T:RazorEngine.Templating.IRazorEngineService"/> instance which executes the templates in a new <see cref="T:System.AppDomain"/>.
            </summary>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedRazorEngineService.Create(System.Func{System.AppDomain})">
            <summary>
            Creates a new <see cref="T:RazorEngine.Templating.IRazorEngineService"/> instance which executes the templates in a new <see cref="T:System.AppDomain"/>.
            </summary>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedRazorEngineService.Create(RazorEngine.Templating.IsolatedRazorEngineService.IConfigCreator,System.Func{System.AppDomain})">
            <summary>
            Creates a new <see cref="T:RazorEngine.Templating.IRazorEngineService"/> instance which executes the templates in a new <see cref="T:System.AppDomain"/>.
            </summary>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedRazorEngineService.Create(RazorEngine.Templating.IsolatedRazorEngineService.IConfigCreator,RazorEngine.Templating.IAppDomainFactory)">
            <summary>
            Creates a new <see cref="T:RazorEngine.Templating.IRazorEngineService"/> instance which executes the templates in a new <see cref="T:System.AppDomain"/>.
            </summary>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedRazorEngineService.CreateAppDomain(RazorEngine.Templating.IAppDomainFactory)">
            <summary>
            Creates an application domain.
            </summary>
            <param name="factory">The application domain factory.</param>
            <returns>An instance of <see cref="T:System.AppDomain"/>.</returns>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedRazorEngineService.Dispose(System.Boolean)">
            <summary>
            Releases resources used by this instance.
            </summary>
            <remarks>
            This method ensures the AppDomain is unloaded and any template assemblies are unloaded with it.
            </remarks>
            <param name="disposing">Flag to determine whether the instance is being disposed explicitly.</param>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedRazorEngineService.Dispose">
            <summary>
            Releases resources used by this instance.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedRazorEngineService.GetKey(System.String,RazorEngine.Templating.ResolveType,RazorEngine.Templating.ITemplateKey)">
            <summary>
            Gets a given key from the <see cref="T:RazorEngine.Templating.ITemplateManager"/> implementation.
            See <see cref="M:RazorEngine.Templating.ITemplateManager.GetKey(System.String,RazorEngine.Templating.ResolveType,RazorEngine.Templating.ITemplateKey)"/>.
            </summary>
            <param name="name"></param>
            <param name="resolveType"></param>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedRazorEngineService.IsTemplateCached(RazorEngine.Templating.ITemplateKey,System.Type)">
            <summary>
            Checks if a given template is already cached.
            </summary>
            <param name="key"></param>
            <param name="modelType"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedRazorEngineService.AddTemplate(RazorEngine.Templating.ITemplateKey,RazorEngine.Templating.ITemplateSource)">
            <summary>
            Adds a given template to the template manager as dynamic template.
            </summary>
            <param name="key"></param>
            <param name="templateSource"></param>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedRazorEngineService.Compile(RazorEngine.Templating.ITemplateKey,System.Type)">
            <summary>
            Compiles the specified template and caches it.
            </summary>
            <param name="key">The key of the template.</param>
            <param name="modelType">The model type.</param>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedRazorEngineService.RunCompile(RazorEngine.Templating.ITemplateKey,System.IO.TextWriter,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)">
            <summary>
            Runs the given cached template.
            When the cache does not contain the template 
            it will be compiled and cached beforehand.
            </summary>
            <param name="key"></param>
            <param name="writer"></param>
            <param name="modelType"></param>
            <param name="model"></param>
            <param name="viewBag"></param>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedRazorEngineService.Run(RazorEngine.Templating.ITemplateKey,System.IO.TextWriter,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)">
            <summary>
            Runs the given cached template.
            </summary>
            <param name="key"></param>
            <param name="writer"></param>
            <param name="modelType"></param>
            <param name="model"></param>
            <param name="viewBag"></param>
        </member>
        <member name="T:RazorEngine.Templating.FullPathTemplateKey">
            <summary>
            A simple <see cref="T:RazorEngine.Templating.ITemplateKey"/> implementation inheriting from <see cref="T:RazorEngine.Templating.BaseTemplateKey"/>.
            This implementation assumes that the template-names are unique and returns the name as unique key.
            (So this implementation is used by <see cref="T:RazorEngine.Templating.DelegateTemplateManager"/> and <see cref="T:RazorEngine.Configuration.Xml.WrapperTemplateManager"/>.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.FullPathTemplateKey.#ctor(System.String,System.String,RazorEngine.Templating.ResolveType,RazorEngine.Templating.ITemplateKey)">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Templating.NameOnlyTemplateKey"/> class.
            </summary>
            <param name="name"></param>
            <param name="fullPath"></param>
            <param name="resolveType"></param>
            <param name="context"></param>
        </member>
        <member name="P:RazorEngine.Templating.FullPathTemplateKey.FullPath">
            <summary>
            
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.FullPathTemplateKey.GetUniqueKeyString">
            <summary>
            Returns the name.
            </summary>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.FullPathTemplateKey.Equals(System.Object)">
            <summary>
            Checks if the names are equal.
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.FullPathTemplateKey.GetHashCode">
            <summary>
            Returns a hashcode for the current instance.
            </summary>
            <returns></returns>
        </member>
        <member name="T:RazorEngine.Templating.ReferencesListForDynamicAssemblyResolution">
            <summary>
            Manages the current list of assemblies, used for dynamic assembly resolution.
            When we compile assemblies we might have any <see cref="T:RazorEngine.Compilation.ReferenceResolver.CompilerReference"/>, but once we load
            the template the runtime will search for it and trigger an <see cref="E:System.AppDomain.AssemblyLoad"/> event.
            We can handle the event by searching in the already used list of references, which is managed by this class.
            </summary>
        </member>
        <member name="F:RazorEngine.Templating.ReferencesListForDynamicAssemblyResolution._references">
            <summary>
            All references we used until now.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.ReferencesListForDynamicAssemblyResolution.AddReferences(System.Collections.Generic.IEnumerable{RazorEngine.Compilation.ReferenceResolver.CompilerReference})">
            <summary>
            Add references to the current list of compiler references.
            This member is threadsafe.
            </summary>
            <param name="refs">The compiler references to add.</param>
        </member>
        <member name="M:RazorEngine.Templating.ReferencesListForDynamicAssemblyResolution.GetCurrentReferences">
            <summary>
            Get the current set of <see cref="T:RazorEngine.Compilation.ReferenceResolver.CompilerReference"/> instances.
            Note that this method returnes a copied snapshot and is therefore threadsafe.
            An other thread might add additional <see cref="T:RazorEngine.Compilation.ReferenceResolver.CompilerReference"/> objects while we enumerate the list.
            But that should not matter as the <see cref="E:System.AppDomain.AssemblyLoad"/> event was triggered earlier.
            </summary>
            <returns>the current list of compiler references.</returns>
        </member>
        <member name="M:RazorEngine.Templating.ReferencesListForDynamicAssemblyResolution.Dispose">
            <summary>
            Disposes the current instance.
            </summary>
        </member>
        <member name="T:RazorEngine.Templating.ResolvePathCheckModifiedTimeTemplateManager">
            <summary>
            A TemplateManager resolving remplates by path, given a list of folders to look into.
            Uses <see cref="T:RazorEngine.Templating.FullPathWithModifiedTimeTemplateKey" /> to save template modification time.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.ResolvePathCheckModifiedTimeTemplateManager.#ctor(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Initializes a new TemplateManager
            </summary>
            <param name="layoutRoots">the list of folders to look for templates.</param>
        </member>
        <member name="M:RazorEngine.Templating.ResolvePathCheckModifiedTimeTemplateManager.GetKey(System.String,RazorEngine.Templating.ResolveType,RazorEngine.Templating.ITemplateKey)">
            <summary>
            Get the given key.
            </summary>
            <param name="name"></param>
            <param name="resolveType"></param>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="T:RazorEngine.Templating.EmbeddedResourceTemplateManager">
            <summary>
            A TemplateManager loading templates from embedded resources.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.EmbeddedResourceTemplateManager.#ctor(System.Type)">
            <summary>
            Initializes a new TemplateManager.
            </summary>
            <param name="rootType">The type from the assembly that contains embedded resources that will act as a root type for Assembly.GetManifestResourceStream() calls.</param>
        </member>
        <member name="P:RazorEngine.Templating.EmbeddedResourceTemplateManager.RootType">
            <summary>
            The type from the assembly that contains embedded resources
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.EmbeddedResourceTemplateManager.Resolve(RazorEngine.Templating.ITemplateKey)">
            <summary>
            Resolve the given key
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.EmbeddedResourceTemplateManager.GetKey(System.String,RazorEngine.Templating.ResolveType,RazorEngine.Templating.ITemplateKey)">
            <summary>
            Get the given key.
            </summary>
            <param name="name"></param>
            <param name="resolveType"></param>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.EmbeddedResourceTemplateManager.AddDynamic(RazorEngine.Templating.ITemplateKey,RazorEngine.Templating.ITemplateSource)">
            <summary>
            Throws NotSupportedException.
            </summary>
            <param name="key"></param>
            <param name="source"></param>
        </member>
        <member name="T:RazorEngine.Templating.ResolvePathTemplateManager">
            <summary>
            A TemplateManager resolving templates by path, given a list of folders to look into.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.ResolvePathTemplateManager.#ctor(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Initializes a new TemplateManager.
            </summary>
            <param name="layoutRoots">the list of folders to look for templates.</param>
        </member>
        <member name="M:RazorEngine.Templating.ResolvePathTemplateManager.Resolve(RazorEngine.Templating.ITemplateKey)">
            <summary>
            Resolve the given key
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.ResolvePathTemplateManager.GetKey(System.String,RazorEngine.Templating.ResolveType,RazorEngine.Templating.ITemplateKey)">
            <summary>
            Get the given key.
            </summary>
            <param name="name"></param>
            <param name="resolveType"></param>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.ResolvePathTemplateManager.ResolveFilePath(System.String)">
            <summary>
            Resolve full file path using layout roots.
            </summary>
            <param name="name">file name</param>
            <returns>full file path</returns>
            <exception cref="T:System.InvalidOperationException"></exception>
        </member>
        <member name="M:RazorEngine.Templating.ResolvePathTemplateManager.AddDynamic(RazorEngine.Templating.ITemplateKey,RazorEngine.Templating.ITemplateSource)">
            <summary>
            Throws NotSupportedException.
            </summary>
            <param name="key"></param>
            <param name="source"></param>
        </member>
        <member name="T:RazorEngine.Templating.TemplateLoadingException">
            <summary>
            Happens when we could compile the template,
            but are unable to load the resulting assembly!
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.TemplateLoadingException.#ctor(System.String)">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Templating.TemplateLoadingException"/>.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:RazorEngine.Templating.TemplateLoadingException.#ctor(System.String,System.Exception)">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Templating.TemplateLoadingException"/>.
            </summary>
            <param name="message">The message.</param>
            <param name="inner">The root cause.</param>
        </member>
        <member name="T:RazorEngine.Templating.WatchingResolvePathTemplateManager">
            <summary>
            A ResolvePathTemplateManager which watches for changes in the 
            filesytem and invalides the corresponding cache entries.
            WARNING:
            Use this only on AppDomains you recycle regularly, or to
            improve the debugging experience.
            Never use this in production without any recycle strategy.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.WatchingResolvePathTemplateManager.#ctor(System.Collections.Generic.IEnumerable{System.String},RazorEngine.Templating.InvalidatingCachingProvider)">
            <summary>
            Creates a new WatchingResolvePathTemplateManager.
            </summary>
            <param name="layoutRoot">the folders to watch and look for templates.</param>
            <param name="cache">the cache to invalidate</param>
        </member>
        <member name="M:RazorEngine.Templating.WatchingResolvePathTemplateManager.Resolve(RazorEngine.Templating.ITemplateKey)">
            <summary>
            Resolve a template.
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.WatchingResolvePathTemplateManager.GetKey(System.String,RazorEngine.Templating.ResolveType,RazorEngine.Templating.ITemplateKey)">
            <summary>
            Gets a key for the given template.
            </summary>
            <param name="name"></param>
            <param name="resolveType"></param>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.WatchingResolvePathTemplateManager.AddDynamic(RazorEngine.Templating.ITemplateKey,RazorEngine.Templating.ITemplateSource)">
            <summary>
            Add a dynamic template (throws an exception)
            </summary>
            <param name="key"></param>
            <param name="source"></param>
        </member>
        <member name="M:RazorEngine.Templating.WatchingResolvePathTemplateManager.Dispose">
            <summary>
            Dispose the current manager.
            </summary>
        </member>
        <member name="T:RazorEngine.Templating.DelegateTemplateManager">
            <summary>
            Provides an <see cref="T:RazorEngine.Templating.ITemplateManager"/> that supports delegated template resolution.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.DelegateTemplateManager.#ctor">
            <summary>
            Creates a new DelegateTemplateManager which throws an exception when 
            we try to resolve something (supports dynamically adding templates).
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.DelegateTemplateManager.#ctor(System.Func{System.String,System.String})">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Templating.DelegateTemplateResolver"/>.
            </summary>
            <param name="resolver">The resolver delegate.</param>
        </member>
        <member name="M:RazorEngine.Templating.DelegateTemplateManager.Resolve(RazorEngine.Templating.ITemplateKey)">
            <summary>
            Resolves the template content with the specified name.
            </summary>
            <param name="key">The key of the template to resolve.</param>
            <returns>The template content.</returns>
        </member>
        <member name="M:RazorEngine.Templating.DelegateTemplateManager.AddDynamic(RazorEngine.Templating.ITemplateKey,RazorEngine.Templating.ITemplateSource)">
            <summary>
            Dynamically add a new template.
            </summary>
            <param name="key">the key of the template</param>
            <param name="source">the source-code of the template</param>
        </member>
        <member name="M:RazorEngine.Templating.DelegateTemplateManager.RemoveDynamic(RazorEngine.Templating.ITemplateKey)">
            <summary>
            Use this API to remove a dynamic template.
            WARNING: using this API doesn't really help you if the 
            template is already cached. 
            So will need to invalidate the cache as well.
            </summary>
            <param name="key"></param>
        </member>
        <member name="M:RazorEngine.Templating.DelegateTemplateManager.GetKey(System.String,RazorEngine.Templating.ResolveType,RazorEngine.Templating.ITemplateKey)">
            <summary>
            Creates a template-key instance (see also <see cref="M:RazorEngine.Templating.ITemplateManager.GetKey(System.String,RazorEngine.Templating.ResolveType,RazorEngine.Templating.ITemplateKey)"/>).
            </summary>
            <param name="name">The name of the template.</param>
            <param name="templateType">the type of the resolve context.</param>
            <param name="context">The context of the template (ie parent template).</param>
            <returns>The template-key.</returns>
        </member>
        <member name="T:RazorEngine.Templating.ITemplateKey">
            <summary>
            With a template key a template can be resolved and loaded.
            Implementations of this interface are provided along with the ITemplateManager implementation.
            See <see cref="T:RazorEngine.Templating.BaseTemplateKey"/> for a base implementation.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.ITemplateKey.Name">
            <summary>
            The name of the template (ie. when used in a @Include)
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.ITemplateKey.TemplateType">
            <summary>
            The layer where the template is to be resolved.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.ITemplateKey.Context">
            <summary>
            The context where the template is to be resolved (ie the parent template).
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.ITemplateKey.GetUniqueKeyString">
            <summary>
            Gets a unique string which can be used as key by the caching layer.
            </summary>
            <returns></returns>
        </member>
        <member name="T:RazorEngine.Templating.CachedTemplateItem">
            <summary>
            Defines a cached template item.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.CachedTemplateItem.#ctor(System.Int32,System.Type)">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Templating.CachedTemplateItem"/>.
            </summary>
            <param name="cachedHashCode">The cached hash code.</param>
            <param name="templateType">The template type.</param>
        </member>
        <member name="P:RazorEngine.Templating.CachedTemplateItem.CachedHashCode">
            <summary>
            Gets the cached hash code of the template.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.CachedTemplateItem.TemplateType">
            <summary>
            Gets the template type.
            </summary>
        </member>
        <member name="T:RazorEngine.Templating.RazorEngineService">
            <summary>
            Defines a template service and the main API for running templates.
            Implements the <see cref="T:RazorEngine.Templating.IRazorEngineService"/> interface.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineService.#ctor(RazorEngine.Configuration.ITemplateServiceConfiguration)">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Templating.TemplateService"/>
            </summary>
            <param name="config">The template service configuration.</param>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineService.#ctor">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Templating.TemplateService"/>.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineService.#ctor(RazorEngine.Language,RazorEngine.Encoding)">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Templating.TemplateService"/>
            </summary>
            <param name="language">The code language.</param>
            <param name="encoding">the encoding.</param>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineService.Create">
            <summary>
            Creates a new <see cref="T:RazorEngine.Templating.IRazorEngineService"/> instance.
            </summary>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineService.Create(RazorEngine.Configuration.ITemplateServiceConfiguration)">
            <summary>
            Creates a new <see cref="T:RazorEngine.Templating.IRazorEngineService"/> instance with the given configuration.
            </summary>
            <returns></returns>
        </member>
        <member name="P:RazorEngine.Templating.RazorEngineService.Core">
            <summary>
            The internal core instance.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.RazorEngineService.Configuration">
            <summary>
            Gets the template service configuration.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineService.IsTemplateCached(RazorEngine.Templating.ITemplateKey,System.Type)">
            <summary>
            Checks if a given template is already cached in the <see cref="T:RazorEngine.Templating.ICachingProvider"/>.
            </summary>
            <param name="key"></param>
            <param name="modelType"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineService.AddTemplate(RazorEngine.Templating.ITemplateKey,RazorEngine.Templating.ITemplateSource)">
            <summary>
            Adds a given template to the <see cref="T:RazorEngine.Templating.ITemplateManager"/> as dynamic template.
            </summary>
            <param name="key"></param>
            <param name="templateSource"></param>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineService.Dispose(System.Boolean)">
            <summary>
            Releases managed resources used by this instance.
            </summary>
            <param name="disposing">Are we explicitly disposing of this instance?</param>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineService.GetEncodedStringFactory(RazorEngine.Encoding)">
            <summary>
            Gets an instance of a <see cref="T:RazorEngine.Text.IEncodedStringFactory"/> for a known encoding.
            </summary>
            <param name="encoding">The encoding to get a factory for.</param>
            <returns>An instance of <see cref="T:RazorEngine.Text.IEncodedStringFactory"/></returns>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineService.CompileAndCacheInternal(RazorEngine.Templating.ITemplateKey,System.Type)">
            <summary>
            Compiles the given template, caches it and returns the result.
            </summary>
            <param name="key"></param>
            <param name="modelType"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineService.Compile(RazorEngine.Templating.ITemplateKey,System.Type)">
            <summary>
            Compiles the specified template and caches it.
            </summary>
            <param name="key">The key of the template.</param>
            <param name="modelType">The model type.</param>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineService.GetCompiledTemplate(RazorEngine.Templating.ITemplateKey,System.Type,System.Boolean)">
            <summary>
            Tries to resolve the template.
            When the cache misses we either throw an exception or compile the template.
            Otherwise the result is returned.
            </summary>
            <param name="key"></param>
            <param name="modelType"></param>
            <param name="compileOnCacheMiss"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineService.RunCompile(RazorEngine.Templating.ITemplateKey,System.IO.TextWriter,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)">
            <summary>
            Runs the given cached template.
            When the cache does not contain the template 
            it will be compiled and cached beforehand.
            </summary>
            <param name="key"></param>
            <param name="writer"></param>
            <param name="modelType"></param>
            <param name="model"></param>
            <param name="viewBag"></param>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineService.Run(RazorEngine.Templating.ITemplateKey,System.IO.TextWriter,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)">
            <summary>
            Runs the given cached template.
            </summary>
            <param name="key"></param>
            <param name="writer"></param>
            <param name="modelType"></param>
            <param name="model"></param>
            <param name="viewBag"></param>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineService.GetTemplate(RazorEngine.Templating.ITemplateKey,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)">
            <summary>
            Helper method for the legacy <see cref="T:RazorEngine.Templating.TemplateService"/> class.
            </summary>
            <param name="key"></param>
            <param name="modelType"></param>
            <param name="model"></param>
            <param name="viewbag"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineService.GetKey(System.String,RazorEngine.Templating.ResolveType,RazorEngine.Templating.ITemplateKey)">
            <summary>
            Gets a given key from the <see cref="T:RazorEngine.Templating.ITemplateManager"/> implementation.
            See <see cref="M:RazorEngine.Templating.ITemplateManager.GetKey(System.String,RazorEngine.Templating.ResolveType,RazorEngine.Templating.ITemplateKey)"/>.
            </summary>
            <param name="cacheName"></param>
            <param name="resolveType"></param>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="T:RazorEngine.Templating.CompiledTemplate">
            <summary>
            A simple readonly implementation of <see cref="T:RazorEngine.Templating.ICompiledTemplate"/>.
            </summary>
        </member>
        <member name="T:RazorEngine.Templating.DefaultCachingProvider">
            <summary>
            The default caching provider (See <see cref="T:RazorEngine.Templating.ICachingProvider"/>).
            This implementation does a very simple in-memory caching.
            It can handle when the same template is used with multiple model-types.
            </summary>
        </member>
        <member name="F:RazorEngine.Templating.DefaultCachingProvider.inner">
            <summary>
            We wrap it without calling any memory leaking API.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.DefaultCachingProvider.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Templating.DefaultCachingProvider"/> class.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.DefaultCachingProvider.#ctor(System.Action{System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Templating.DefaultCachingProvider"/> class.
            </summary>
            <param name="registerForCleanup">callback for files which need to be cleaned up.</param>
        </member>
        <member name="P:RazorEngine.Templating.DefaultCachingProvider.TypeLoader">
            <summary>
            The manages <see cref="P:RazorEngine.Templating.DefaultCachingProvider.TypeLoader"/>. See <see cref="P:RazorEngine.Templating.ICachingProvider.TypeLoader"/>
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.DefaultCachingProvider.GetModelTypeKey(System.Type)">
            <summary>
            Get the key used within a dictionary for a modelType.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.DefaultCachingProvider.CacheTemplate(RazorEngine.Templating.ICompiledTemplate,RazorEngine.Templating.ITemplateKey)">
            <summary>
            Caches a template. See <see cref="M:RazorEngine.Templating.ICachingProvider.CacheTemplate(RazorEngine.Templating.ICompiledTemplate,RazorEngine.Templating.ITemplateKey)"/>.
            </summary>
            <param name="template"></param>
            <param name="templateKey"></param>
        </member>
        <member name="M:RazorEngine.Templating.DefaultCachingProvider.TryRetrieveTemplate(RazorEngine.Templating.ITemplateKey,System.Type,RazorEngine.Templating.ICompiledTemplate@)">
            <summary>
            Try to retrieve a template from the cache. See <see cref="M:RazorEngine.Templating.ICachingProvider.TryRetrieveTemplate(RazorEngine.Templating.ITemplateKey,System.Type,RazorEngine.Templating.ICompiledTemplate@)"/>.
            </summary>
            <param name="templateKey"></param>
            <param name="modelType"></param>
            <param name="compiledTemplate"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.DefaultCachingProvider.Dispose">
            <summary>
            Dispose the instance.
            </summary>
        </member>
        <member name="T:RazorEngine.Templating.HtmlTemplateBase`1">
            <summary>
            Provides a base implementation of an html template with a model.
            </summary>
            <remarks>
            This type does not currently serve a purpose, and the WriteAttribute* API has been migrated to the TemplateBase type. This type is not deprecated, as it
            may form the basis for a future template that supports MVC like @Html syntax.
            </remarks>
            <typeparam name="T">The model type.</typeparam>
        </member>
        <member name="T:RazorEngine.Templating.DefaultActivator">
            <summary>
            Provides a default implementation of an <see cref="T:RazorEngine.Templating.IActivator"/>.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.DefaultActivator.CreateInstance(RazorEngine.Templating.InstanceContext)">
            <summary>
            Creates an instance of the specifed template.
            </summary>
            <param name="context">The instance context.</param>
            <returns>An instance of <see cref="T:RazorEngine.Templating.ITemplate"/>.</returns>
        </member>
        <member name="T:RazorEngine.Templating.DefaultAppDomainFactory">
            <summary>
            Provides a default implementation of an <see cref="T:System.AppDomain"/> factory.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.DefaultAppDomainFactory.CreateAppDomain">
            <summary>
            Creates the <see cref="T:System.AppDomain"/>.
            </summary>
            <returns>The <see cref="T:System.AppDomain"/> instance.</returns>
        </member>
        <member name="T:RazorEngine.Templating.DelegateActivator">
            <summary>
            Defines an activator that supports delegated activation.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.DelegateActivator.#ctor(System.Func{RazorEngine.Templating.InstanceContext,RazorEngine.Templating.ITemplate})">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Templating.DelegateActivator"/>.
            </summary>
            <param name="activator">The delegated used to create an instance of the template.</param>
        </member>
        <member name="P:RazorEngine.Templating.DelegateActivator.Activator">
            <summary>
            Gets the activator.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.DelegateActivator.CreateInstance(RazorEngine.Templating.InstanceContext)">
            <summary>
            Creates an instance of the specifed template.
            </summary>
            <param name="context">The instance context.</param>
            <returns>An instance of <see cref="T:RazorEngine.Templating.ITemplate"/>.</returns>
        </member>
        <member name="T:RazorEngine.Templating.DelegateAppDomainFactory">
            <summary>
            Provides an <see cref="T:System.AppDomain"/> factory that supports delegated <see cref="T:System.AppDomain"/> creation.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.DelegateAppDomainFactory.#ctor(System.Func{System.AppDomain})">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Templating.DelegateAppDomainFactory"/>.
            </summary>
            <param name="factory">The factory delegate.</param>
        </member>
        <member name="M:RazorEngine.Templating.DelegateAppDomainFactory.CreateAppDomain">
            <summary>
            Creates the <see cref="T:System.AppDomain"/>.
            </summary>
            <returns>The <see cref="T:System.AppDomain"/> instance.</returns>
        </member>
        <member name="T:RazorEngine.Templating.DelegateTemplateResolver">
            <summary>
            Provides an <see cref="T:RazorEngine.Templating.ITemplateResolver"/> that supports delegated template resolution.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.DelegateTemplateResolver.#ctor(System.Func{System.String,System.String})">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Templating.DelegateTemplateResolver"/>.
            </summary>
            <param name="resolver">The resolver delegate.</param>
        </member>
        <member name="M:RazorEngine.Templating.DelegateTemplateResolver.Resolve(System.String)">
            <summary>
            Resolves a template.
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="T:RazorEngine.Templating.DynamicViewBag">
            <summary>
            Defines a dynamic view bag.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.DynamicViewBag.#ctor">
            <summary>
            Create a new DynamicViewBag.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.DynamicViewBag.#ctor(System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Create a new DynamicViewBag by copying the given dictionary.
            </summary>
            <param name="dictionary"></param>
        </member>
        <member name="M:RazorEngine.Templating.DynamicViewBag.#ctor(RazorEngine.Templating.DynamicViewBag)">
            <summary>
            Create a copy of the given DynamicViewBag.
            </summary>
            <param name="viewbag"></param>
        </member>
        <member name="M:RazorEngine.Templating.DynamicViewBag.AddDictionaryValues(System.Collections.IDictionary)">
            <summary>
            Add the given dictionary to the current DynamicViewBag
            </summary>
            <param name="valueDictionary"></param>
        </member>
        <member name="M:RazorEngine.Templating.DynamicViewBag.AddDictionary(System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Adds the given dictionary to the current DynamicViewBag instance.
            </summary>
            <param name="dictionary"></param>
        </member>
        <member name="M:RazorEngine.Templating.DynamicViewBag.AddDictionaryValuesEx(System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Add the given dictionary to the current DynamicViewBag
            </summary>
            <param name="valueDictionary"></param>
        </member>
        <member name="M:RazorEngine.Templating.DynamicViewBag.AddListValues(System.Collections.IList,System.String)">
            <summary>
            Adds the given list by evaluating the given property name.
            </summary>
            <param name="valueList"></param>
            <param name="keyPropertyName"></param>
        </member>
        <member name="M:RazorEngine.Templating.DynamicViewBag.AddValue(System.String,System.Object)">
            <summary>
            Adds a single value.
            </summary>
            <param name="propertyName"></param>
            <param name="value"></param>
        </member>
        <member name="M:RazorEngine.Templating.DynamicViewBag.GetDynamicMemberNames">
            <summary>
            Gets the set of dynamic member names.
            </summary>
            <returns>An instance of <see cref="T:System.Collections.Generic.IEnumerable`1"/>.</returns>
        </member>
        <member name="M:RazorEngine.Templating.DynamicViewBag.TryGetMember(System.Dynamic.GetMemberBinder,System.Object@)">
            <summary>
            Attempts to read a dynamic member from the object.
            </summary>
            <param name="binder">The binder.</param>
            <param name="result">The result instance.</param>
            <returns>True, always.</returns>
        </member>
        <member name="M:RazorEngine.Templating.DynamicViewBag.TrySetMember(System.Dynamic.SetMemberBinder,System.Object)">
            <summary>
            Attempts to set a value on the object.
            </summary>
            <param name="binder">The binder.</param>
            <param name="value">The value to set.</param>
            <returns>True, always.</returns>
        </member>
        <member name="T:RazorEngine.Templating.ExecuteContext">
            <summary>
            Defines a context for tracking template execution.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.ExecuteContext.#ctor">
            <summary>
            Creates a new instance of ExecuteContext.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.ExecuteContext.#ctor(RazorEngine.Templating.DynamicViewBag)">
            <summary>
            DO NOT USE, throws NotSupportedException.
            </summary>
            <param name="viewbag">DO NOT USE, throws NotSupportedException.</param>
        </member>
        <member name="P:RazorEngine.Templating.ExecuteContext.CurrentWriter">
            <summary>
            Gets the current writer.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.ExecuteContext.DefineSection(System.String,System.Action)">
            <summary>
            Defines a section used in layouts.
            </summary>
            <param name="name">The name of the section.</param>
            <param name="action">The delegate action used to write the section at a later stage in the template execution.</param>
        </member>
        <member name="M:RazorEngine.Templating.ExecuteContext.GetSectionDelegate(System.String)">
            <summary>
            Gets the section delegate.
            </summary>
            <param name="name">The name of the section.</param>
            <returns>The section delegate.</returns>
        </member>
        <member name="M:RazorEngine.Templating.ExecuteContext.PopSections(System.Action,System.IO.TextWriter)">
            <summary>
            Allows to pop all the section delegates for the executing action.
            This is required for nesting sections.
            </summary>
            <param name="inner">the executing section delegate.</param>
            <param name="innerArg">the parameter for the delegate.</param>
        </member>
        <member name="M:RazorEngine.Templating.ExecuteContext.PushSections">
            <summary>
            Push the set of current sections to the stack.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.ExecuteContext.PopBody">
            <summary>
            Pops the template writer helper off the stack.
            </summary>
            <returns>The template writer helper.</returns>
        </member>
        <member name="M:RazorEngine.Templating.ExecuteContext.PushBody(RazorEngine.Templating.TemplateWriter)">
            <summary>
            Pushes the specified template writer helper onto the stack.
            </summary>
            <param name="bodyWriter">The template writer helper.</param>
        </member>
        <member name="T:RazorEngine.Templating.IActivator">
            <summary>
            Defines the required contract for implementing an activator.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.IActivator.CreateInstance(RazorEngine.Templating.InstanceContext)">
            <summary>
            Creates an instance of the specifed template.
            </summary>
            <param name="context">The instance context.</param>
            <returns>An instance of <see cref="T:RazorEngine.Templating.ITemplate"/>.</returns>
        </member>
        <member name="T:RazorEngine.Templating.IAppDomainFactory">
            <summary>
            Defines the required contract for implementing an <see cref="T:System.AppDomain"/> factory.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.IAppDomainFactory.CreateAppDomain">
            <summary>
            Creates the <see cref="T:System.AppDomain"/>.
            </summary>
            <returns>The <see cref="T:System.AppDomain"/> instance.</returns>
        </member>
        <member name="T:RazorEngine.Templating.ICachingProvider">
            <summary>
            This interface represents the caching layer.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.ICachingProvider.CacheTemplate(RazorEngine.Templating.ICompiledTemplate,RazorEngine.Templating.ITemplateKey)">
            <summary>
            Request that a given template should be cached.
            </summary>
            <param name="template">The template to be cached.</param>
            <param name="key">The key of the template.</param>
        </member>
        <member name="M:RazorEngine.Templating.ICachingProvider.TryRetrieveTemplate(RazorEngine.Templating.ITemplateKey,System.Type,RazorEngine.Templating.ICompiledTemplate@)">
            <summary>
            Try to resolve a template within the cache.
            </summary>
            <param name="key">the key of the template.</param>
            <param name="modelType">the model-type of the template.</param>
            <param name="template">the resolved template</param>
            <returns>true if a template was found.</returns>
            <remarks>
            Implementations MUST decide if they allow multiple model-types for the 
            same template key and SHOULD throw a exception when a template is requested with the wrong type!
            </remarks>
        </member>
        <member name="P:RazorEngine.Templating.ICachingProvider.TypeLoader">
            <summary>
            Every caching provider must manage a <see cref="P:RazorEngine.Templating.ICachingProvider.TypeLoader"/> instance.
            This instance makes sure that all assemblies can be resolved properly.
            </summary>
        </member>
        <member name="T:RazorEngine.Templating.ICompiledTemplate">
            <summary>
            Represents a compiled template.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.ICompiledTemplate.Key">
            <summary>
            The key for the template (used for resolving the source code).
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.ICompiledTemplate.Template">
            <summary>
            The source of the template (ie the source code).
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.ICompiledTemplate.CompilationData">
            <summary>
            All temporary information about the compilation.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.ICompiledTemplate.TemplateType">
            <summary>
            The actual Type object of the generated template.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.ICompiledTemplate.TemplateAssembly">
            <summary>
            The generated assembly of the template.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.ICompiledTemplate.ModelType">
            <summary>
            The type of the model (null = dynamic).
            </summary>
        </member>
        <member name="T:RazorEngine.Templating.IInternalTemplateService">
            <summary>
            A internal contract for the <see cref="T:RazorEngine.Templating.TemplateBase"/> class.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.IInternalTemplateService.Configuration">
            <summary>
            Gets the template service configuration.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.IInternalTemplateService.EncodedStringFactory">
            <summary>
            Gets the encoded string factory.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.IInternalTemplateService.Resolve(System.String,System.Object,System.Type,RazorEngine.Templating.DynamicViewBag,RazorEngine.Templating.ResolveType)">
            <summary>
            Resolves the template, this is for internal use only
            </summary>
            <param name="name"></param>
            <param name="model"></param>
            <param name="modelType"></param>
            <param name="viewbag"></param>
            <param name="resolveType"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.IInternalTemplateService.AddNamespace(System.String)">
            <summary>
            Adds a namespace that will be imported into the template.
            </summary>
            <param name="ns">The namespace to be imported.</param>
        </member>
        <member name="M:RazorEngine.Templating.IInternalTemplateService.CreateExecuteContext">
            <summary>
            Creates a new <see cref="T:RazorEngine.Templating.ExecuteContext"/> used to tracking templates.
            </summary> 
            <returns>The instance of <see cref="T:RazorEngine.Templating.ExecuteContext"/></returns>
        </member>
        <member name="T:RazorEngine.Templating.InstanceContext">
            <summary>
            Defines contextual information for a template instance.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.InstanceContext.#ctor(RazorEngine.Templating.TypeLoader,System.Type)">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Templating.InstanceContext"/>.
            </summary>
            <param name="loader">The type loader.</param>
            <param name="templateType">The template type.</param>
        </member>
        <member name="P:RazorEngine.Templating.InstanceContext.Loader">
            <summary>
            Gets the type loader.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.InstanceContext.TemplateType">
            <summary>
            Gets the template type.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.InternalTemplateService.EncodedStringFactory">
            <summary>
            Gets the encoded string factory.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.InternalTemplateService.Resolve(System.String,System.Object,System.Type,RazorEngine.Templating.DynamicViewBag,RazorEngine.Templating.ResolveType)">
            <summary>
            Resolves the template with the specified name.
            </summary>
            <param name="name">The name of the template type in cache.</param>
            <param name="model">The model or NULL if there is no model for the template.</param>
            <param name="modelType"></param>
            <param name="viewbag"></param>
            <param name="resolveType"></param>
            <returns>The resolved template.</returns>
        </member>
        <member name="M:RazorEngine.Templating.InternalTemplateService.AddNamespace(System.String)">
            <summary>
            Adds a namespace that will be imported into the template.
            </summary>
            <param name="ns">The namespace to be imported.</param>
        </member>
        <member name="M:RazorEngine.Templating.InternalTemplateService.CreateExecuteContext">
            <summary>
            Creates a new <see cref="T:RazorEngine.Templating.ExecuteContext"/> for tracking templates.
            </summary>
            <returns>The execute context.</returns>
        </member>
        <member name="T:RazorEngine.Templating.ITemplateManager">
            <summary>
            Defines the required contract for implementing a template-manager.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.ITemplateManager.Resolve(RazorEngine.Templating.ITemplateKey)">
            <summary>
            Resolves the template with the specified key.
            </summary>
            <param name="key">The key which should be resolved to a template source.</param>
            <returns>The template content.</returns>
        </member>
        <member name="M:RazorEngine.Templating.ITemplateManager.GetKey(System.String,RazorEngine.Templating.ResolveType,RazorEngine.Templating.ITemplateKey)">
            <summary>
            Get the key of a template.
            This method has to be implemented so that the manager can control the <see cref="T:RazorEngine.Templating.ITemplateKey"/> implementation.
            This way the cache api can rely on the unique string given by <see cref="M:RazorEngine.Templating.ITemplateKey.GetUniqueKeyString"/>.
            </summary>
            <remarks>
            For example one template manager reads all template from a single folder, then the <see cref="M:RazorEngine.Templating.ITemplateKey.GetUniqueKeyString"/> can simply return the template name.
            Another template manager can read from different folders depending whether we include a layout or including a template.
            In that situation the <see cref="M:RazorEngine.Templating.ITemplateKey.GetUniqueKeyString"/> has to take that into account so that templates with the same name can not be confused.
            </remarks>
            <param name="name">The name of the template</param>
            <param name="resolveType">how the template is resolved</param>
            <param name="context">gets the context for the current resolve operation. 
            Which template is resolving another template? (null = we search a global template)
            </param>
            <returns>the key for the template</returns>
        </member>
        <member name="M:RazorEngine.Templating.ITemplateManager.AddDynamic(RazorEngine.Templating.ITemplateKey,RazorEngine.Templating.ITemplateSource)">
            <summary>
            Adds a template dynamically to the current manager.
            </summary>
            <param name="key"></param>
            <param name="source"></param>
        </member>
        <member name="T:RazorEngine.Templating.ITemplateSource">
            <summary>
            Represents a template source (ie the source code of a template).
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.ITemplateSource.TemplateFile">
            <summary>
            When not null this file is used for debugging the template.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.ITemplateSource.Template">
            <summary>
            The source code of the template.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.ITemplateSource.GetTemplateReader">
            <summary>
            Get a reader to read the template.
            </summary>
            <returns></returns>
        </member>
        <member name="T:RazorEngine.Templating.ITemplateResolver">
            <summary>
            Defines the required contract for implementing a template resolver.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.ITemplateResolver.Resolve(System.String)">
            <summary>
            Resolves the template content with the specified name.
            </summary>
            <param name="name">The name of the template to resolve.</param>
            <returns>The template content.</returns>
        </member>
        <member name="T:RazorEngine.Templating.ITemplateService">
            <summary>
            Defines the required contract for implementing a template service.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.ITemplateService.Configuration">
            <summary>
            Gets the template service configuration.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.ITemplateService.EncodedStringFactory">
            <summary>
            Gets the encoded string factory.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.ITemplateService.AddNamespace(System.String)">
            <summary>
            Adds a namespace that will be imported into the template.
            </summary>
            <param name="ns">The namespace to be imported.</param>
        </member>
        <member name="M:RazorEngine.Templating.ITemplateService.CreateExecuteContext(RazorEngine.Templating.DynamicViewBag)">
            <summary>
            Creates a new <see cref="T:RazorEngine.Templating.ExecuteContext"/> used to tracking templates.
            </summary>
            <param name="viewBag">This parameter is ignored, set the Viewbag with template.SetData(null, viewBag)</param>
            <returns>The instance of <see cref="T:RazorEngine.Templating.ExecuteContext"/></returns>
        </member>
        <member name="M:RazorEngine.Templating.ITemplateService.Compile(System.String,System.Type,System.String)">
            <summary>
            Compiles the specified template.
            </summary>
            <param name="razorTemplate">The string template.</param>
            <param name="modelType">The model type.</param>
            <param name="name">The name of the template.</param>
        </member>
        <member name="M:RazorEngine.Templating.ITemplateService.CreateTemplate(System.String,System.Type,System.Object)">
            <summary>
            Create a template from the given razor code.
            </summary>
            <param name="razorTemplate">the string template</param>
            <param name="templateType">the type of the template</param>
            <param name="model">the model.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.ITemplateService.CreateTemplates(System.Collections.Generic.IEnumerable{System.String},System.Collections.Generic.IEnumerable{System.Type},System.Collections.Generic.IEnumerable{System.Object},System.Boolean)">
            <summary>
            Create a sequence of templates
            </summary>
            <param name="razorTemplates">the templates</param>
            <param name="templateTypes">the types</param>
            <param name="models">the models</param>
            <param name="parallel">run in parallel?</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.ITemplateService.CreateTemplateType(System.String,System.Type)">
            <summary>
            Backwards Compat
            </summary>
            <param name="razorTemplate"></param>
            <param name="modelType"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.ITemplateService.CreateTemplateTypes(System.Collections.Generic.IEnumerable{System.String},System.Collections.Generic.IEnumerable{System.Type},System.Boolean)">
            <summary>
            Backwards Compat
            </summary>
            <param name="razorTemplates"></param>
            <param name="modelTypes"></param>
            <param name="parallel"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.ITemplateService.GetTemplate(System.String,System.Object,System.String)">
            <summary>
            Get a given template (compiles the templates if not cached already)
            </summary>
            <param name="razorTemplate"></param>
            <param name="model"></param>
            <param name="cacheName"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.ITemplateService.GetTemplates(System.Collections.Generic.IEnumerable{System.String},System.Collections.Generic.IEnumerable{System.Object},System.Collections.Generic.IEnumerable{System.String},System.Boolean)">
            <summary>
            See GetTemplate.
            </summary>
            <param name="razorTemplates"></param>
            <param name="models"></param>
            <param name="cacheNames"></param>
            <param name="parallel"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.ITemplateService.HasTemplate(System.String)">
            <summary>
            Returns whether or not a template by the specified name has been created already.
            </summary>
            <param name="name">The name of the template.</param>
            <returns>Whether or not the template has been created.</returns>
        </member>
        <member name="M:RazorEngine.Templating.ITemplateService.RemoveTemplate(System.String)">
            <summary>
            Remove a template by the specified name from the cache.
            </summary>
            <param name="cacheName">The name of the template type in cache.</param>
            <returns>Whether or not the template has been removed.</returns>
        </member>
        <member name="M:RazorEngine.Templating.ITemplateService.Parse(System.String,System.Object,RazorEngine.Templating.DynamicViewBag,System.String)">
            <summary>
            Parses and returns the result of the specified string template.
            </summary>
            <param name="razorTemplate">The string template.</param>
            <param name="model">The model instance or NULL if no model exists.</param>
            <param name="viewBag">The ViewBag initial contents or NULL for an initially empty ViewBag.</param>
            <param name="cacheName">The name of the template type in the cache or NULL if no caching is desired.</param>
            <returns>The string result of the template.</returns>
        </member>
        <member name="M:RazorEngine.Templating.ITemplateService.Parse``1(System.String,System.Object,RazorEngine.Templating.DynamicViewBag,System.String)">
            <summary>
            Parses and returns the result of the specified string template.
            </summary>
            <typeparam name="T">Type of the model. Used to find out the type of the model, if model is NULL</typeparam>
            <param name="razorTemplate">The string template.</param>
            <param name="model">The model instance or NULL if no model exists.</param>
            <param name="viewBag">The ViewBag initial contents or NULL for an initially empty ViewBag.</param>
            <param name="cacheName">The name of the template type in the cache or NULL if no caching is desired.</param>
            <returns>The string result of the template.</returns>
        </member>
        <member name="M:RazorEngine.Templating.ITemplateService.ParseMany(System.Collections.Generic.IEnumerable{System.String},System.Collections.Generic.IEnumerable{System.Object},System.Collections.Generic.IEnumerable{RazorEngine.Templating.DynamicViewBag},System.Collections.Generic.IEnumerable{System.String},System.Boolean)">
            <summary>
            Parses the specified set of templates.
            </summary>
            <param name="razorTemplates">The set of string templates to partse.</param>
            <param name="models">The set of models.</param>
            <param name="viewBags"></param>
            <param name="cacheNames">The set of cache names.</param>
            <param name="parallel">Flag to determine whether parsing in templates.</param>
            <returns>The set of parsed template results.</returns>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.ITemplateService.Resolve(System.String,System.Object)">
            <summary>
            Resolves the template with the specified name.
            </summary>
            <param name="name">The name of the template.</param>
            <param name="model">The model for the template.</param>
            <returns>The resolved template.</returns>
        </member>
        <member name="M:RazorEngine.Templating.ITemplateService.Run(System.String,System.Object,RazorEngine.Templating.DynamicViewBag)">
            <summary>
            Runs the template with the specified name.
            </summary>
            <param name="name">The name of the template.</param>
            <param name="model">The model.</param>
            <param name="viewBag">the viewbag</param>
            <returns>The string result of the template.</returns>
        </member>
        <member name="M:RazorEngine.Templating.ITemplateService.Run(RazorEngine.Templating.ITemplate,RazorEngine.Templating.DynamicViewBag)">
            <summary>
            Runs the specified name.
            </summary>
            <param name="template">The template.</param>
            <param name="viewBag">The viewbag.</param>
            <returns>The string result of the template.</returns>
        </member>
        <member name="T:RazorEngine.Templating.TemplateService">
            <summary>
            Defines a template service.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.TemplateService.#ctor(RazorEngine.Configuration.ITemplateServiceConfiguration)">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Templating.TemplateService"/>
            </summary>
            <param name="config">The template service configuration.</param>
        </member>
        <member name="M:RazorEngine.Templating.TemplateService.#ctor">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Templating.TemplateService"/>.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.TemplateService.#ctor(RazorEngine.Language,RazorEngine.Encoding)">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Templating.TemplateService"/>
            </summary>
            <param name="language">The code language.</param>
            <param name="encoding">the encoding.</param>
        </member>
        <member name="P:RazorEngine.Templating.TemplateService.Configuration">
            <summary>
            Gets the template service configuration.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.TemplateService.EncodedStringFactory">
            <summary>
            Gets the encoded string factory.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.TemplateService.CreateInstanceContext(System.Type)">
            <summary>
            Creates a new <see cref="T:RazorEngine.Templating.InstanceContext"/> for creating template instances.
            </summary>
            <param name="templateType">The template type.</param>
            <returns>An instance of <see cref="T:RazorEngine.Templating.InstanceContext"/>.</returns>
        </member>
        <member name="M:RazorEngine.Templating.TemplateService.AddNamespace(System.String)">
            <summary>
            Adds a namespace that will be imported into the template.
            </summary>
            <param name="ns">The namespace to be imported.</param>
        </member>
        <member name="M:RazorEngine.Templating.TemplateService.Compile(System.String,System.Type,System.String)">
            <summary>
            Compiles the specified template.
            </summary>
            <param name="razorTemplate">The string template.</param>
            <param name="modelType">The model type.</param>
            <param name="name">The name of the template.</param>
        </member>
        <member name="M:RazorEngine.Templating.TemplateService.CreateExecuteContext(RazorEngine.Templating.DynamicViewBag)">
            <summary>
            Creates a ExecuteContext
            </summary>
            <param name="viewBag"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.TemplateService.CreateTemplate(System.String,System.Type,System.Object)">
            <summary>
            Creates an instance of <see cref="T:RazorEngine.Templating.ITemplate"/> from the specified string template.
            </summary>
            <param name="razorTemplate">The string template.</param>
            <param name="staticType">type used in the compilation.</param>
            <param name="model">The model instance.</param>
            <returns>An instance of <see cref="T:RazorEngine.Templating.ITemplate"/>.</returns>
        </member>
        <member name="M:RazorEngine.Templating.TemplateService.CreateTemplates(System.Collections.Generic.IEnumerable{System.String},System.Collections.Generic.IEnumerable{System.Type},System.Collections.Generic.IEnumerable{System.Object},System.Boolean)">
            <summary>
            Creates a set of templates from the specified string templates and models.
            </summary>
            <param name="razorTemplates">The set of templates to create <see cref="T:RazorEngine.Templating.ITemplate"/> instances for.</param>
            <param name="models">The set of models used to assign to templates.</param>
            <param name="parallel">Flag to determine whether to create templates in parallel.</param>
            <param name="types">the mode types.</param>
            <returns>The enumerable set of template instances.</returns>
        </member>
        <member name="M:RazorEngine.Templating.TemplateService.CreateTemplateType(System.String,System.Type)">
            <summary>
            Creates a <see cref="T:System.Type"/> that can be used to instantiate an instance of a template.
            </summary>
            <param name="razorTemplate">The string template.</param>
            <param name="modelType">The model type.</param>
            <returns>An instance of <see cref="T:System.Type"/>.</returns>
        </member>
        <member name="M:RazorEngine.Templating.TemplateService.CreateTemplateTypes(System.Collections.Generic.IEnumerable{System.String},System.Collections.Generic.IEnumerable{System.Type},System.Boolean)">
            <summary>
            Creates a set of template types from the specfied string templates.
            </summary>
            <param name="razorTemplates">The set of templates to create <see cref="T:System.Type"/> instances for.</param>
            <param name="types">The modeltypes</param>
            <param name="parallel">Flag to determine whether to create template types in parallel.</param>
            <returns>The set of <see cref="T:System.Type"/> instances.</returns>
        </member>
        <member name="M:RazorEngine.Templating.TemplateService.Dispose(System.Boolean)">
            <summary>
            Releases managed resources used by this instance.
            </summary>
            <param name="disposing">Are we explicitly disposing of this instance?</param>
        </member>
        <member name="M:RazorEngine.Templating.TemplateService.GetEncodedStringFactory(RazorEngine.Encoding)">
            <summary>
            Gets an instance of a <see cref="T:RazorEngine.Text.IEncodedStringFactory"/> for a known encoding.
            </summary>
            <param name="encoding">The encoding to get a factory for.</param>
            <returns>An instance of <see cref="T:RazorEngine.Text.IEncodedStringFactory"/></returns>
        </member>
        <member name="M:RazorEngine.Templating.TemplateService.GetParallelQueryPlan``1">
            <summary>
            Gets a parellel query plan used to configure a parallel query.
            </summary>
            <typeparam name="T">The query item type.</typeparam>
            <returns>An instance of <see cref="T:RazorEngine.Templating.Parallel.IParallelQueryPlan`1"/>.</returns>
        </member>
        <member name="M:RazorEngine.Templating.TemplateService.GetTemplate(System.String,System.Object,System.String)">
            <summary>
            Gets an instance of the template using the cached compiled type, or compiles the template type
            if it does not exist in the cache.
            </summary>
            <param name="razorTemplate">The string template.</param>
            <param name="model">The model instance.</param>
            <param name="name">The name of the template type in the cache.</param>
            <returns>An instance of <see cref="T:RazorEngine.Templating.ITemplate`1"/>.</returns>
        </member>
        <member name="M:RazorEngine.Templating.TemplateService.GetTemplate``1(System.String,System.Object,System.String)">
            <summary>
            Gets an instance of the template using the cached compiled type, or compiles the template type
            if it does not exist in the cache.
            </summary>
            <typeparam name="T">The model type.</typeparam>
            <param name="razorTemplate">The string template.</param>
            <param name="model">The model instance.</param>
            <param name="name">The name of the template type in the cache.</param>
            <returns>An instance of <see cref="T:RazorEngine.Templating.ITemplate`1"/>.</returns>
        </member>
        <member name="M:RazorEngine.Templating.TemplateService.GetTemplates(System.Collections.Generic.IEnumerable{System.String},System.Collections.Generic.IEnumerable{System.Object},System.Collections.Generic.IEnumerable{System.String},System.Boolean)">
            <summary>
            Gets the set of template instances for the specified string templates. Cached templates will be considered
            and if they do not exist, new types will be created and instantiated.
            </summary>
            <param name="razorTemplates">The set of templates to create.</param>
            <param name="models">The set of models.</param>
            <param name="names">The set of cache names.</param>
            <param name="parallel">Flag to determine whether to get the templates in parallel.</param>
            <returns>The set of <see cref="T:RazorEngine.Templating.ITemplate"/> instances.</returns>
        </member>
        <member name="M:RazorEngine.Templating.TemplateService.Parse(System.String,System.Object,RazorEngine.Templating.DynamicViewBag,System.String)">
            <summary>
            Parses and returns the result of the specified string template.
            </summary>
            <param name="razorTemplate">The string template.</param>
            <param name="model">The model instance.</param>
            <param name="viewBag">The viewbag.</param>
            <param name="cacheName">The cacheName.</param>
            <returns>The string result of the template.</returns>
        </member>
        <member name="M:RazorEngine.Templating.TemplateService.Parse``1(System.String,System.Object,RazorEngine.Templating.DynamicViewBag,System.String)">
            <summary>
            Parses and returns the result of the specified string template.
            </summary>
            <param name="razorTemplate">The string template.</param>
            <param name="model">The model instance or NULL if no model exists.</param>
            <param name="viewBag">The ViewBag contents or NULL for an initially empty ViewBag.</param>
            <param name="cacheName">The name of the template type in the cache or NULL if no caching is desired.</param>
            <returns>The string result of the template.</returns>
        </member>
        <member name="M:RazorEngine.Templating.TemplateService.ParseMany(System.Collections.Generic.IEnumerable{System.String},System.Collections.Generic.IEnumerable{System.Object},System.Collections.Generic.IEnumerable{RazorEngine.Templating.DynamicViewBag},System.Collections.Generic.IEnumerable{System.String},System.Boolean)">
            <summary>
            Parses the specified set of templates.
            </summary>
            <param name="razorTemplates">The set of string templates to parse.</param>
            <param name="models">The set of models.</param>
            <param name="viewBags">The viewbags</param>
            <param name="names">The set of cache names.</param>
            <param name="parallel">Flag to determine whether parsing in parallel.</param>
            <returns>The set of parsed template results.</returns>
        </member>
        <member name="M:RazorEngine.Templating.TemplateService.HasTemplate(System.String)">
            <summary>
            Returns whether or not a template by the specified name has been created already.
            </summary>
            <param name="name">The name of the template.</param>
            <returns>Whether or not the template has been created.</returns>
        </member>
        <member name="M:RazorEngine.Templating.TemplateService.RemoveTemplate(System.String)">
            <summary>
            NOT SUPPORTED.
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.TemplateService.Resolve(System.String,System.Object)">
            <summary>
            Resolves the template with the specified name.
            </summary>
            <param name="name">The name of the template.</param>
            <param name="model">The model for the template.</param>
            <returns>The resolved template.</returns>
        </member>
        <member name="M:RazorEngine.Templating.TemplateService.Run(System.String,System.Object,RazorEngine.Templating.DynamicViewBag)">
            <summary>
            Runs the template with the specified name.
            </summary>
            <param name="name">The name of the template.</param>
            <param name="model">The model.</param>
            <param name="viewBag">The viewBag.</param>
            <returns>The string result of the template.</returns>
        </member>
        <member name="M:RazorEngine.Templating.TemplateService.Run(RazorEngine.Templating.ITemplate,RazorEngine.Templating.DynamicViewBag)">
            <summary>
            Runs the template with the specified name.
            </summary>
            <param name="template">The template.</param>
            <param name="viewBag">The viewbag.</param>
            <returns>The string result of the template.</returns>
        </member>
        <member name="T:RazorEngine.Templating.NameOnlyTemplateKey">
            <summary>
            A simple <see cref="T:RazorEngine.Templating.ITemplateKey"/> implementation inheriting from <see cref="T:RazorEngine.Templating.BaseTemplateKey"/>.
            This implementation assumes that the template-names are unique and returns the name as unique key.
            (So this implementation is used by <see cref="T:RazorEngine.Templating.DelegateTemplateManager"/> and <see cref="T:RazorEngine.Configuration.Xml.WrapperTemplateManager"/>.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.NameOnlyTemplateKey.#ctor(System.String,RazorEngine.Templating.ResolveType,RazorEngine.Templating.ITemplateKey)">
            <summary>
            Initializes a new instance of the <see cref="T:RazorEngine.Templating.NameOnlyTemplateKey"/> class.
            </summary>
            <param name="name"></param>
            <param name="resolveType"></param>
            <param name="context"></param>
        </member>
        <member name="M:RazorEngine.Templating.NameOnlyTemplateKey.GetUniqueKeyString">
            <summary>
            Returns the name.
            </summary>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.NameOnlyTemplateKey.Equals(System.Object)">
            <summary>
            Checks if the names are equal.
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.NameOnlyTemplateKey.GetHashCode">
            <summary>
            Returns a hashcode for the current instance.
            </summary>
            <returns></returns>
        </member>
        <member name="T:RazorEngine.Templating.Parallel.DefaultParallelQueryPlan`1">
            <summary>
            Represents a default parallel query plan.
            </summary>
            <remarks>
            The <see cref="T:RazorEngine.Templating.Parallel.DefaultParallelQueryPlan`1"/> uses the default <see cref="T:System.Linq.ParallelQuery`1" />
            result. The degree of parallelism by default is <code>Math.Min(ProcessorCount, 64)</code>.
            </remarks>
            <typeparam name="T">The item type.</typeparam>
        </member>
        <member name="M:RazorEngine.Templating.Parallel.DefaultParallelQueryPlan`1.CreateQuery(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Creates a parallel query for the specified source.
            </summary>
            <param name="source">The source enumerable.</param>
            <returns>The parallel query.</returns>
        </member>
        <member name="T:RazorEngine.Templating.Parallel.IParallelQueryPlan`1">
            <summary>
            Defines the required contract for implementing a parallel query plan.
            </summary>
            <typeparam name="T">The item type.</typeparam>
        </member>
        <member name="M:RazorEngine.Templating.Parallel.IParallelQueryPlan`1.CreateQuery(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Creates a parallel query for the specified source.
            </summary>
            <param name="source">The source enumerable.</param>
            <returns>The parallel query.</returns>
        </member>
        <member name="T:RazorEngine.Templating.IsolatedTemplateService">
            <summary>
            Provides template parsing and compilation in an isolated application domain.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedTemplateService.#ctor">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Templating.IsolatedTemplateService"/>
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedTemplateService.#ctor(RazorEngine.Language)">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Templating.IsolatedTemplateService"/>
            </summary>
            <param name="language">The code language.</param>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedTemplateService.#ctor(RazorEngine.Encoding)">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Templating.IsolatedTemplateService"/>
            </summary>
            <param name="encoding">The encoding.</param>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedTemplateService.#ctor(RazorEngine.Templating.IAppDomainFactory)">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Templating.IsolatedTemplateService"/>
            </summary>
            <param name="appDomainFactory">The application domain factory.</param>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedTemplateService.#ctor(System.Func{System.AppDomain})">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Templating.IsolatedTemplateService"/>.
            </summary>
            <param name="appDomainFactory">The delegate used to create an application domain.</param>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedTemplateService.#ctor(RazorEngine.Language,RazorEngine.Encoding,RazorEngine.Templating.IAppDomainFactory)">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Templating.IsolatedTemplateService"/>
            </summary>
            <param name="language">The code language.</param>
            <param name="encoding">The encoding.</param>
            <param name="appDomainFactory">The application domain factory.</param>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedTemplateService.#ctor(RazorEngine.Language,System.Func{System.AppDomain})">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Templating.IsolatedTemplateService"/>.
            </summary>
            <param name="language">The code language.</param>
            <param name="appDomainFactory">The delegate used to create an application domain.</param>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedTemplateService.#ctor(RazorEngine.Language,RazorEngine.Encoding,System.Func{System.AppDomain})">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Templating.IsolatedTemplateService"/>.
            </summary>
            <param name="language">The code language.</param>
            <param name="encoding">The encoding.</param>
            <param name="appDomainFactory">The delegate used to create an application domain.</param>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedTemplateService.#ctor(RazorEngine.Encoding,System.Func{System.AppDomain})">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Templating.IsolatedTemplateService"/>.
            </summary>
            <param name="encoding">The encoding.</param>
            <param name="appDomainFactory">The delegate used to create an application domain.</param>
        </member>
        <member name="P:RazorEngine.Templating.IsolatedTemplateService.RazorEngine#Templating#ITemplateService#Configuration">
            <summary>
            Gets the template service configuration.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.IsolatedTemplateService.RazorEngine#Templating#ITemplateService#EncodedStringFactory">
            <summary>
            Gets the encoded string factory.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedTemplateService.AddNamespace(System.String)">
            <summary>
            Adds a namespace that will be imported into the template.
            </summary>
            <param name="ns">The namespace to be imported.</param>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedTemplateService.RazorEngine#Templating#ITemplateService#CreateExecuteContext(RazorEngine.Templating.DynamicViewBag)">
            <summary>
            Creates a new <see cref="T:RazorEngine.Templating.ExecuteContext"/> used to tracking templates.
            </summary>
            <param name="viewBag">This parameter is ignored, set the Viewbag with template.SetData(null, viewBag)</param>
            <returns>The instance of <see cref="T:RazorEngine.Templating.ExecuteContext"/></returns>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedTemplateService.Compile(System.String,System.Type,System.String)">
            <summary>
            Compiles the specified template.
            </summary>
            <param name="razorTemplate">The string template.</param>
            <param name="modelType">The model type.</param>
            <param name="name">The name of the template.</param>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedTemplateService.CreateAppDomain(RazorEngine.Templating.IAppDomainFactory)">
            <summary>
            Creates an application domain.
            </summary>
            <param name="factory">The application domain factory.</param>
            <returns>An instance of <see cref="T:System.AppDomain"/>.</returns>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedTemplateService.Dispose(System.Boolean)">
            <summary>
            Releases resources used by this instance.
            </summary>
            <remarks>
            This method ensures the AppDomain is unloaded and any template assemblies are unloaded with it.
            </remarks>
            <param name="disposing">Flag to determine whether the instance is being disposed explicitly.</param>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedTemplateService.Dispose">
            <summary>
            Releases resources used by this instance.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedTemplateService.GetTemplate``1(System.String,``0,System.String)">
            <summary>
            Gets an instance of the template using the cached compiled type, or compiles the template type
            if it does not exist in the cache.
            </summary>
            <typeparam name="T">The model type.</typeparam>
            <param name="razorTemplate">The string template.</param>
            <param name="model">The model instance.</param>
            <param name="name">The name of the template type in the cache.</param>
            <returns>An instance of <see cref="T:RazorEngine.Templating.ITemplate`1"/>.</returns>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedTemplateService.HasTemplate(System.String)">
            <summary>
            Returns whether or not a template by the specified name has been created already.
            </summary>
            <param name="name">The name of the template.</param>
            <returns>Whether or not the template has been created.</returns>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedTemplateService.RemoveTemplate(System.String)">
            <summary>
            Remove a template by the specified name from the cache.
            </summary>
            <param name="cacheName">The name of the template type in cache.</param>
            <returns>Whether or not the template has been removed.</returns>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedTemplateService.Parse``1(System.String,System.Object,RazorEngine.Templating.DynamicViewBag,System.String)">
            <summary>
            Parses and returns the result of the specified string template.
            </summary>
            <param name="razorTemplate">The string template.</param>
            <param name="model">The model instance or NULL if no model exists.</param>
            <param name="viewBag">The ViewBag contents or NULL for an initially empty ViewBag.</param>
            <param name="cacheName">The name of the template type in the cache or NULL if no caching is desired.</param>
            <returns>The string result of the template.</returns>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedTemplateService.RazorEngine#Templating#ITemplateService#Resolve(System.String,System.Object)">
            <summary>
            Resolves the template with the specified name.
            </summary>
            <param name="name">The name of the template.</param>
            <param name="model">The model for the template.</param>
            <returns>The resolved template.</returns>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedTemplateService.CreateTemplate(System.String,System.Type,System.Object)">
            <summary>
            Backwards Compat
            </summary>
            <param name="razorTemplate"></param>
            <param name="templateType"></param>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedTemplateService.CreateTemplates(System.Collections.Generic.IEnumerable{System.String},System.Collections.Generic.IEnumerable{System.Type},System.Collections.Generic.IEnumerable{System.Object},System.Boolean)">
            <summary>
            Backwards Compat
            </summary>
            <param name="razorTemplates"></param>
            <param name="templateTypes"></param>
            <param name="models"></param>
            <param name="parallel"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedTemplateService.GetTemplate(System.String,System.Object,System.String)">
            <summary>
            Backwards Compat
            </summary>
            <param name="razorTemplate"></param>
            <param name="model"></param>
            <param name="cacheName"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedTemplateService.GetTemplates(System.Collections.Generic.IEnumerable{System.String},System.Collections.Generic.IEnumerable{System.Object},System.Collections.Generic.IEnumerable{System.String},System.Boolean)">
            <summary>
            Backwards Compat
            </summary>
            <param name="razorTemplates"></param>
            <param name="models"></param>
            <param name="cacheNames"></param>
            <param name="parallel"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedTemplateService.Parse(System.String,System.Object,RazorEngine.Templating.DynamicViewBag,System.String)">
            <summary>
            Backwards Compat
            </summary>
            <param name="razorTemplate"></param>
            <param name="model"></param>
            <param name="viewBag"></param>
            <param name="cacheName"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedTemplateService.ParseMany(System.Collections.Generic.IEnumerable{System.String},System.Collections.Generic.IEnumerable{System.Object},System.Collections.Generic.IEnumerable{RazorEngine.Templating.DynamicViewBag},System.Collections.Generic.IEnumerable{System.String},System.Boolean)">
            <summary>
            Backwards compat
            </summary>
            <param name="razorTemplates"></param>
            <param name="models"></param>
            <param name="viewBags"></param>
            <param name="cacheNames"></param>
            <param name="parallel"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedTemplateService.Run(System.String,System.Object,RazorEngine.Templating.DynamicViewBag)">
            <summary>
            Backwards compat
            </summary>
            <param name="name"></param>
            <param name="model"></param>
            <param name="viewBag"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedTemplateService.Run(RazorEngine.Templating.ITemplate,RazorEngine.Templating.DynamicViewBag)">
            <summary>
            Backwards compat.
            </summary>
            <param name="template"></param>
            <param name="viewBag"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedTemplateService.CreateTemplateType(System.String,System.Type)">
            <summary>
            Backwards compat
            </summary>
            <param name="razorTemplate"></param>
            <param name="modelType"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.IsolatedTemplateService.CreateTemplateTypes(System.Collections.Generic.IEnumerable{System.String},System.Collections.Generic.IEnumerable{System.Type},System.Boolean)">
            <summary>
            Backwards compat.
            </summary>
            <param name="razorTemplates"></param>
            <param name="modelTypes"></param>
            <param name="parallel"></param>
            <returns></returns>
        </member>
        <member name="T:RazorEngine.Templating.ITemplate">
            <summary>
            Defines the required contract for implementing a template.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.ITemplate.InternalTemplateService">
            <summary>
            Sets the internal template service.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.ITemplate.TemplateService">
            <summary>
            OBSOLETE: Sets the template service.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.ITemplate.RazorEngine">
            <summary>
            Sets the cached template service.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.ITemplate.Razor">
            <summary>
            Sets the cached template service.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.ITemplate.SetData(System.Object,RazorEngine.Templating.DynamicViewBag)">
            <summary>
            Set the model of the template (if applicable).
            </summary>
            <param name="model"></param>
            <param name="viewbag"></param>
        </member>
        <member name="M:RazorEngine.Templating.ITemplate.Execute">
            <summary>
            Executes the compiled template.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.ITemplate.Run(RazorEngine.Templating.ExecuteContext,System.IO.TextWriter)">
            <summary>
            Runs the template and returns the result.
            </summary>
            <param name="context">The current execution context.</param>
            <param name="writer"></param>
            <returns>The merged result of the template.</returns>
        </member>
        <member name="M:RazorEngine.Templating.ITemplate.Write(System.Object)">
            <summary>
            Writes the specified object to the result.
            </summary>
            <param name="value">The value to write.</param>
        </member>
        <member name="M:RazorEngine.Templating.ITemplate.WriteLiteral(System.String)">
            <summary>
            Writes the specified string to the result.
            </summary>
            <param name="literal">The literal to write.</param>
        </member>
        <member name="T:RazorEngine.Templating.ITemplate`1">
            <summary>
            Defines the required contract for implementing a template with a model.
            </summary>
            <typeparam name="T">The model type.</typeparam>
        </member>
        <member name="P:RazorEngine.Templating.ITemplate`1.Model">
            <summary>
            Gets the or sets the model.
            </summary>
        </member>
        <member name="T:RazorEngine.Templating.IRazorEngineService">
            <summary>
            Defines the required contract for implementing a template service.
            The main API for running templates.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.IRazorEngineService.GetKey(System.String,RazorEngine.Templating.ResolveType,RazorEngine.Templating.ITemplateKey)">
            <summary>
            Gets a given key from the <see cref="T:RazorEngine.Templating.ITemplateManager"/> implementation.
            See <see cref="M:RazorEngine.Templating.ITemplateManager.GetKey(System.String,RazorEngine.Templating.ResolveType,RazorEngine.Templating.ITemplateKey)"/>.
            </summary>
            <param name="name"></param>
            <param name="resolveType"></param>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.IRazorEngineService.IsTemplateCached(RazorEngine.Templating.ITemplateKey,System.Type)">
            <summary>
            Checks if a given template is already cached.
            </summary>
            <param name="key"></param>
            <param name="modelType"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.IRazorEngineService.AddTemplate(RazorEngine.Templating.ITemplateKey,RazorEngine.Templating.ITemplateSource)">
            <summary>
            Adds a given template to the template manager as dynamic template.
            </summary>
            <param name="key"></param>
            <param name="templateSource"></param>
        </member>
        <member name="M:RazorEngine.Templating.IRazorEngineService.Compile(RazorEngine.Templating.ITemplateKey,System.Type)">
            <summary>
            Compiles the specified template and caches it.
            </summary>
            <param name="key">The key of the template.</param>
            <param name="modelType">The model type.</param>
        </member>
        <member name="M:RazorEngine.Templating.IRazorEngineService.RunCompile(RazorEngine.Templating.ITemplateKey,System.IO.TextWriter,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)">
            <summary>
            Runs the given cached template.
            When the cache does not contain the template 
            it will be compiled and cached beforehand.
            </summary>
            <param name="key"></param>
            <param name="writer"></param>
            <param name="modelType"></param>
            <param name="model"></param>
            <param name="viewBag"></param>
        </member>
        <member name="M:RazorEngine.Templating.IRazorEngineService.Run(RazorEngine.Templating.ITemplateKey,System.IO.TextWriter,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)">
            <summary>
            Runs the given cached template.
            </summary>
            <param name="key"></param>
            <param name="writer"></param>
            <param name="modelType"></param>
            <param name="model"></param>
            <param name="viewBag"></param>
        </member>
        <member name="T:RazorEngine.Templating.RequireNamespacesAttribute">
            <summary>
            Allows base templates to define require template imports when
            generating templates.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.RequireNamespacesAttribute.#ctor(System.String[])">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Templating.RequireNamespacesAttribute"/>.
            </summary>
            <param name="namespaces">The set of required namespace imports.</param>
        </member>
        <member name="P:RazorEngine.Templating.RequireNamespacesAttribute.Namespaces">
            <summary>
            Gets the set of required namespace imports.
            </summary>
        </member>
        <member name="T:RazorEngine.Templating.ResolveType">
            <summary>
            The type of a resolve action.
            </summary>
        </member>
        <member name="F:RazorEngine.Templating.ResolveType.Global">
            <summary>
            When we search for a template in as part of TemplateService.
            </summary>
        </member>
        <member name="F:RazorEngine.Templating.ResolveType.Include">
            <summary>
            When we search for a template which is included.
            </summary>
        </member>
        <member name="F:RazorEngine.Templating.ResolveType.Layout">
            <summary>
            When we search for a layout template.
            </summary>
        </member>
        <member name="T:RazorEngine.Templating.TemplateBase">
            <summary>
            Provides a base implementation of a template.
            NOTE: This class is not serializable to prevent subtle errors 
            in user IActivator implementations which would break the sandbox.
            (because executed in the wrong <see cref="T:System.AppDomain"/>)
            </summary>
        </member>
        <member name="F:RazorEngine.Templating.TemplateBase.modelInit">
            <summary>
            Because the old API (TemplateService) is designed in a way to make it impossible to init
            the model and the Viewbag at the same time (and because of backwards compatibility),
            we need to call the SetData method twice (only from within TemplateService so we can remove this bool once that has been removed).
            
            But the secound call we only need to set the Viewbag, therefore we save the state in this bool.
            </summary>
        </member>
        <member name="F:RazorEngine.Templating.TemplateBase._context">
            <summary>
            The current context, filled when we are currently writing a template instance.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.TemplateBase.#ctor">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Templating.TemplateBase"/>.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.TemplateBase.Layout">
            <summary>
            Gets or sets the layout template name.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.TemplateBase.InternalTemplateService">
            <summary>
            Gets or sets the template service.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.TemplateBase.TemplateService">
            <summary>
            Gets or sets the template service.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.TemplateBase.RazorEngine">
            <summary>
            Gets or sets the current <see cref="T:RazorEngine.Templating.IRazorEngineService"/> instance.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.TemplateBase.Razor">
            <summary>
            Gets or sets the current <see cref="T:RazorEngine.Templating.IRazorEngineService"/> instance.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.TemplateBase.ViewBag">
            <summary>
            Gets the viewbag that allows sharing state between layout and child templates.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.TemplateBase.CurrentWriter">
            <summary>
            Gets the current writer.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.TemplateBase.SetData(System.Object,RazorEngine.Templating.DynamicViewBag)">
            <summary>
            Set the data for this template.
            </summary>
            <param name="model">the model object for the current run.</param>
            <param name="viewbag">the viewbag for the current run.</param>
        </member>
        <member name="M:RazorEngine.Templating.TemplateBase.SetModel(System.Object)">
            <summary>
            Set the current model.
            </summary>
            <param name="model"></param>
        </member>
        <member name="M:RazorEngine.Templating.TemplateBase.DefineSection(System.String,System.Action)">
            <summary>
            Defines a section that can written out to a layout.
            </summary>
            <param name="name">The name of the section.</param>
            <param name="action">The delegate used to write the section.</param>
        </member>
        <member name="M:RazorEngine.Templating.TemplateBase.Include(System.String,System.Object,System.Type)">
            <summary>
            Includes the template with the specified name.
            </summary>
            <param name="name">The name of the template type in cache.</param>
            <param name="model">The model or NULL if there is no model for the template.</param>
            <param name="modelType"></param>
            <returns>The template writer helper.</returns>
        </member>
        <member name="M:RazorEngine.Templating.TemplateBase.IsSectionDefined(System.String)">
            <summary>
            Determines if the section with the specified name has been defined.
            </summary>
            <param name="name">The section name.</param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.TemplateBase.Execute">
            <summary>
            Executes the compiled template.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.TemplateBase.Raw(System.String)">
            <summary>
            Returns the specified string as a raw string. This will ensure it is not encoded.
            </summary>
            <param name="rawString">The raw string to write.</param>
            <returns>An instance of <see cref="T:RazorEngine.Text.IEncodedString"/>.</returns>
        </member>
        <member name="M:RazorEngine.Templating.TemplateBase.ResolveLayout(System.String)">
            <summary>
            Resolves the layout template.
            </summary>
            <param name="name">The name of the layout template.</param>
            <returns>An instance of <see cref="T:RazorEngine.Templating.ITemplate"/>.</returns>
        </member>
        <member name="M:RazorEngine.Templating.TemplateBase.RazorEngine#Templating#ITemplate#Run(RazorEngine.Templating.ExecuteContext,System.IO.TextWriter)">
            <summary>
            Runs the template and returns the result.
            </summary>
            <param name="context">The current execution context.</param>
            <param name="reader"></param>
            <returns>The merged result of the template.</returns>
        </member>
        <member name="M:RazorEngine.Templating.TemplateBase.RenderSection(System.String,System.Boolean)">
            <summary>
            Renders the section with the specified name.
            </summary>
            <param name="name">The name of the section.</param>
            <param name="required">Flag to specify whether the section is required.</param>
            <returns>The template writer helper.</returns>
        </member>
        <member name="M:RazorEngine.Templating.TemplateBase.RenderBody">
            <summary>
            Renders the body of the template.
            </summary>
            <returns>The template writer helper.</returns>
        </member>
        <member name="M:RazorEngine.Templating.TemplateBase.Write(System.Object)">
            <summary>
            Writes the specified object to the result.
            </summary>
            <param name="value">The value to write.</param>
        </member>
        <member name="M:RazorEngine.Templating.TemplateBase.Write(RazorEngine.Templating.TemplateWriter)">
            <summary>
            Writes the specified template helper result.
            </summary>
            <param name="helper">The template writer helper.</param>
        </member>
        <member name="M:RazorEngine.Templating.TemplateBase.WriteAttribute(System.String,RazorEngine.PositionTagged{System.String},RazorEngine.PositionTagged{System.String},RazorEngine.AttributeValue[])">
            <summary>
            Writes an attribute to the result.
            </summary>
            <param name="name">The name of the attribute.</param>
            <param name="prefix"></param>
            <param name="suffix"></param>
            <param name="values"></param>
        </member>
        <member name="M:RazorEngine.Templating.TemplateBase.WriteAttributeTo(System.IO.TextWriter,System.String,RazorEngine.PositionTagged{System.String},RazorEngine.PositionTagged{System.String},RazorEngine.AttributeValue[])">
            <summary>
            Writes an attribute to the specified <see cref="T:System.IO.TextWriter"/>.
            </summary>
            <param name="writer">The writer.</param>
            <param name="name">The name of the attribute to be written.</param>
            <param name="prefix"></param>
            <param name="suffix"></param>
            <param name="values"></param>
        </member>
        <member name="M:RazorEngine.Templating.TemplateBase.WriteLiteral(System.String)">
            <summary>
            Writes the specified string to the result.
            </summary>
            <param name="literal">The literal to write.</param>
        </member>
        <member name="M:RazorEngine.Templating.TemplateBase.WriteLiteralTo(System.IO.TextWriter,System.String)">
            <summary>
            Writes a string literal to the specified <see cref="T:System.IO.TextWriter"/>.
            </summary>
            <param name="writer">The writer.</param>
            <param name="literal">The literal to be written.</param>
        </member>
        <member name="M:RazorEngine.Templating.TemplateBase.WritePositionTaggedLiteral(System.IO.TextWriter,RazorEngine.PositionTagged{System.String})">
            <summary>
            Writes a <see cref="T:RazorEngine.PositionTagged`1" /> literal to the result.
            </summary>
            <param name="writer">The writer.</param>
            <param name="value">The literal to be written.</param>
        </member>
        <member name="M:RazorEngine.Templating.TemplateBase.WriteTo(System.IO.TextWriter,System.Object)">
            <summary>
            Writes the specified object to the specified <see cref="T:System.IO.TextWriter"/>.
            </summary>
            <param name="writer">The writer.</param>
            <param name="value">The value to be written.</param>
        </member>
        <member name="M:RazorEngine.Templating.TemplateBase.WriteTo(System.IO.TextWriter,RazorEngine.Templating.TemplateWriter)">
            <summary>
            Writes the specfied template helper result to the specified writer.
            </summary>
            <param name="writer">The writer.</param>
            <param name="helper">The template writer helper.</param>
        </member>
        <member name="M:RazorEngine.Templating.TemplateBase.ResolveUrl(System.String)">
            <summary>
            Resolves the specified path
            </summary>
            <param name="path">The path.</param>
            <returns>The resolved path.</returns>
        </member>
        <member name="T:RazorEngine.Templating.TemplateBase`1">
            <summary>
            Provides a base implementation of a template with a model.
            </summary>
            <typeparam name="T">The model type.</typeparam>
        </member>
        <member name="M:RazorEngine.Templating.TemplateBase`1.#ctor">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Templating.TemplateBase`1"/>.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.TemplateBase`1.HasDynamicModel">
            <summary>
            Determines whether this template has a dynamic model.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.TemplateBase`1.Model">
            <summary>
            Gets or sets the model.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.TemplateBase`1.SetModel(System.Object)">
            <summary>
            Set the model.
            </summary>
            <param name="model"></param>
        </member>
        <member name="M:RazorEngine.Templating.TemplateBase`1.Include(System.String,System.Object,System.Type)">
            <summary>
            Includes the template with the specified name.
            </summary>
            <param name="name">The name of the template type in cache.</param>
            <param name="model">The model or NULL if there is no model for the template.</param>
            <returns>The template writer helper.</returns>
        </member>
        <member name="M:RazorEngine.Templating.TemplateBase`1.Include(System.String,System.Object)">
            <summary>
            Includes the template with the specified name.
            </summary>
            <param name="name">The name of the template type in cache.</param>
            <param name="model">The model or NULL if there is no model for the template.</param>
            <returns>The template writer helper.</returns>
        </member>
        <member name="M:RazorEngine.Templating.TemplateBase`1.Include(System.String)">
            <summary>
            Includes the template with the specified name, uses the current model and model-type.
            </summary>
            <param name="name">The name of the template type in cache.</param>
            <returns>The template writer helper.</returns>
        </member>
        <member name="M:RazorEngine.Templating.TemplateBase`1.ResolveLayout(System.String)">
            <summary>
            Resolves the layout template.
            </summary>
            <param name="name">The name of the layout template.</param>
            <returns>An instance of <see cref="T:RazorEngine.Templating.ITemplate"/>.</returns>
        </member>
        <member name="T:RazorEngine.Templating.RazorEngineCompilerError">
            <summary>
            Defines a compiler error.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.RazorEngineCompilerError.ErrorText">
            <summary>
            The error text of the error.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.RazorEngineCompilerError.FileName">
            <summary>
            The file name of the error source.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.RazorEngineCompilerError.Line">
            <summary>
            The line number of the error location
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.RazorEngineCompilerError.Column">
            <summary>
            The column number of the error location.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.RazorEngineCompilerError.ErrorNumber">
            <summary>
            The number of the error.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.RazorEngineCompilerError.IsWarning">
            <summary>
            Indicates whether the error is a warning.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineCompilerError.#ctor(System.String,System.String,System.Int32,System.Int32,System.String,System.Boolean)">
            <summary>
            Creates a new Compiler error instance.
            </summary>
            <param name="errorText"></param>
            <param name="fileName"></param>
            <param name="line"></param>
            <param name="column"></param>
            <param name="errorNumber"></param>
            <param name="isWarning"></param>
        </member>
        <member name="T:RazorEngine.Templating.TemplateCompilationException">
            <summary>
            Defines an exception that occurs during compilation of the template.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.TemplateCompilationException.GetMessage(System.Collections.Generic.IEnumerable{RazorEngine.Templating.RazorEngineCompilerError},RazorEngine.Compilation.CompilationData,RazorEngine.Templating.ITemplateSource)">
            <summary>
            Gets a exact error message of the given error collection
            </summary>
            <param name="errors"></param>
            <param name="files"></param>
            <param name="template"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.TemplateCompilationException.#ctor(System.Collections.Generic.IEnumerable{RazorEngine.Templating.RazorEngineCompilerError},RazorEngine.Compilation.CompilationData,RazorEngine.Templating.ITemplateSource)">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Templating.TemplateCompilationException"/>.
            </summary>
            <param name="errors">The set of compiler errors.</param>
            <param name="files">The source code that wasn't compiled.</param>
            <param name="template">The source template that wasn't compiled.</param>
        </member>
        <member name="M:RazorEngine.Templating.TemplateCompilationException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Templating.TemplateCompilationException"/> from serialised data.
            </summary>
            <param name="info">The serialisation info.</param>
            <param name="context">The streaming context.</param>
        </member>
        <member name="P:RazorEngine.Templating.TemplateCompilationException.CompilerErrors">
            <summary>
            Gets the set of compiler errors.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.TemplateCompilationException.Errors">
            <summary>
            Gets the set of compiler errors.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.TemplateCompilationException.CompilationData">
            <summary>
            Gets some copilation specific (temporary) data.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.TemplateCompilationException.SourceCode">
            <summary>
            Gets the generated source code.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.TemplateCompilationException.Template">
            <summary>
            Gets the source template that wasn't compiled.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.TemplateCompilationException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Gets the object data for serialisation.
            </summary>
            <param name="info">The serialisation info.</param>
            <param name="context">The streaming context.</param>
        </member>
        <member name="T:RazorEngine.Templating.BaseTemplateKey">
            <summary>
            A base implementation for <see cref="T:RazorEngine.Templating.ITemplateKey"/>. 
            You only need to provide the <see cref="M:RazorEngine.Templating.ITemplateKey.GetUniqueKeyString"/> 
            implementation which depends on the <see cref="T:RazorEngine.Templating.ITemplateManager"/> implementation.
            </summary>
        </member>
        <member name="F:RazorEngine.Templating.BaseTemplateKey._name">
            <summary>
            See <see cref="P:RazorEngine.Templating.ITemplateKey.Name"/>.
            </summary>
        </member>
        <member name="F:RazorEngine.Templating.BaseTemplateKey._resolveType">
            <summary>
            See <see cref="P:RazorEngine.Templating.ITemplateKey.TemplateType"/>.
            </summary>
        </member>
        <member name="F:RazorEngine.Templating.BaseTemplateKey._context">
            <summary>
            See <see cref="P:RazorEngine.Templating.ITemplateKey.Context"/>.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.BaseTemplateKey.#ctor(System.String,RazorEngine.Templating.ResolveType,RazorEngine.Templating.ITemplateKey)">
            <summary>
            Create a new <see cref="T:RazorEngine.Templating.BaseTemplateKey"/> instance. 
            </summary>
            <param name="name">See <see cref="P:RazorEngine.Templating.ITemplateKey.Name"/></param>
            <param name="resolveType">See <see cref="P:RazorEngine.Templating.ITemplateKey.TemplateType"/></param>
            <param name="context">See <see cref="P:RazorEngine.Templating.ITemplateKey.Context"/></param>
        </member>
        <member name="P:RazorEngine.Templating.BaseTemplateKey.Name">
            <summary>
            See <see cref="P:RazorEngine.Templating.ITemplateKey.Name"/>.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.BaseTemplateKey.TemplateType">
            <summary>
            See <see cref="P:RazorEngine.Templating.ITemplateKey.TemplateType"/>.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.BaseTemplateKey.Context">
            <summary>
            See <see cref="P:RazorEngine.Templating.ITemplateKey.Context"/>.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.BaseTemplateKey.GetUniqueKeyString">
            <summary>
            See <see cref="M:RazorEngine.Templating.ITemplateKey.GetUniqueKeyString"/>.
            </summary>
        </member>
        <member name="T:RazorEngine.Templating.RazorEngineServiceExtensions">
            <summary>
            Extensions for the <see cref="T:RazorEngine.Templating.IRazorEngineService"/>.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineServiceExtensions.IsTemplateCached(RazorEngine.Templating.IRazorEngineService,System.String,System.Type)">
            <summary>
            Checks if a given template is already cached.
            </summary>
            <param name="service"></param>
            <param name="name"></param>
            <param name="modelType"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineServiceExtensions.AddTemplate(RazorEngine.Templating.IRazorEngineService,System.String,RazorEngine.Templating.ITemplateSource)">
            <summary>
            Adds a given template to the template manager as dynamic template.
            </summary>
            <param name="service"></param>
            <param name="name"></param>
            <param name="templateSource"></param>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineServiceExtensions.AddTemplate(RazorEngine.Templating.IRazorEngineService,RazorEngine.Templating.ITemplateKey,System.String)">
            <summary>
            Adds a given template to the template manager as dynamic template.
            </summary>
            <param name="service"></param>
            <param name="key"></param>
            <param name="templateSource"></param>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineServiceExtensions.AddTemplate(RazorEngine.Templating.IRazorEngineService,System.String,System.String)">
            <summary>
            Adds a given template to the template manager as dynamic template.
            </summary>
            <param name="service"></param>
            <param name="name"></param>
            <param name="templateSource"></param>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineServiceExtensions.Compile(RazorEngine.Templating.IRazorEngineService,System.String,System.Type)">
            <summary>
            See <see cref="M:RazorEngine.Templating.RazorEngineService.Compile(RazorEngine.Templating.ITemplateKey,System.Type)"/>.
            </summary>
            <param name="service"></param>
            <param name="name"></param>
            <param name="modelType"></param>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineServiceExtensions.Compile(RazorEngine.Templating.IRazorEngineService,RazorEngine.Templating.ITemplateSource,RazorEngine.Templating.ITemplateKey,System.Type)">
            <summary>
            See <see cref="M:RazorEngine.Templating.RazorEngineService.Compile(RazorEngine.Templating.ITemplateKey,System.Type)"/>.
            Convenience method which calls <see cref="M:RazorEngine.Templating.RazorEngineService.AddTemplate(RazorEngine.Templating.ITemplateKey,RazorEngine.Templating.ITemplateSource)"/> before calling <see cref="M:RazorEngine.Templating.RazorEngineService.Compile(RazorEngine.Templating.ITemplateKey,System.Type)"/>.
            </summary>
            <param name="service"></param>
            <param name="templateSource"></param>
            <param name="key"></param>
            <param name="modelType"></param>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineServiceExtensions.Compile(RazorEngine.Templating.IRazorEngineService,System.String,RazorEngine.Templating.ITemplateKey,System.Type)">
            <summary>
            See <see cref="M:RazorEngine.Templating.RazorEngineService.Compile(RazorEngine.Templating.ITemplateKey,System.Type)"/>.
            Convenience method which calls <see cref="M:RazorEngine.Templating.RazorEngineService.AddTemplate(RazorEngine.Templating.ITemplateKey,RazorEngine.Templating.ITemplateSource)"/> before calling <see cref="M:RazorEngine.Templating.RazorEngineService.Compile(RazorEngine.Templating.ITemplateKey,System.Type)"/>.
            </summary>
            <param name="service"></param>
            <param name="templateSource"></param>
            <param name="key"></param>
            <param name="modelType"></param>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineServiceExtensions.Compile(RazorEngine.Templating.IRazorEngineService,RazorEngine.Templating.ITemplateSource,System.String,System.Type)">
            <summary>
            See <see cref="M:RazorEngine.Templating.RazorEngineService.Compile(RazorEngine.Templating.ITemplateKey,System.Type)"/>.
            Convenience method which calls <see cref="M:RazorEngine.Templating.RazorEngineService.AddTemplate(RazorEngine.Templating.ITemplateKey,RazorEngine.Templating.ITemplateSource)"/> before calling <see cref="M:RazorEngine.Templating.RazorEngineService.Compile(RazorEngine.Templating.ITemplateKey,System.Type)"/>.
            </summary>
            <param name="service"></param>
            <param name="templateSource"></param>
            <param name="name"></param>
            <param name="modelType"></param>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineServiceExtensions.Compile(RazorEngine.Templating.IRazorEngineService,System.String,System.String,System.Type)">
            <summary>
            See <see cref="M:RazorEngine.Templating.RazorEngineService.Compile(RazorEngine.Templating.ITemplateKey,System.Type)"/>.
            Convenience method which calls <see cref="M:RazorEngine.Templating.RazorEngineService.AddTemplate(RazorEngine.Templating.ITemplateKey,RazorEngine.Templating.ITemplateSource)"/> before calling <see cref="M:RazorEngine.Templating.RazorEngineService.Compile(RazorEngine.Templating.ITemplateKey,System.Type)"/>.
            </summary>
            <param name="service"></param>
            <param name="templateSource"></param>
            <param name="name"></param>
            <param name="modelType"></param>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineServiceExtensions.RunCompile(RazorEngine.Templating.IRazorEngineService,System.String,System.IO.TextWriter,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)">
            <summary>
            See <see cref="M:RazorEngine.Templating.RazorEngineService.RunCompile(RazorEngine.Templating.ITemplateKey,System.IO.TextWriter,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)"/>.
            </summary>
            <param name="service"></param>
            <param name="name"></param>
            <param name="writer"></param>
            <param name="modelType"></param>
            <param name="model"></param>
            <param name="viewBag"></param>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineServiceExtensions.RunCompile(RazorEngine.Templating.IRazorEngineService,RazorEngine.Templating.ITemplateSource,RazorEngine.Templating.ITemplateKey,System.IO.TextWriter,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)">
            <summary>
            See <see cref="M:RazorEngine.Templating.RazorEngineService.RunCompile(RazorEngine.Templating.ITemplateKey,System.IO.TextWriter,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)"/>.
            Convenience method which calls <see cref="M:RazorEngine.Templating.RazorEngineService.AddTemplate(RazorEngine.Templating.ITemplateKey,RazorEngine.Templating.ITemplateSource)"/> before calling <see cref="M:RazorEngine.Templating.RazorEngineService.RunCompile(RazorEngine.Templating.ITemplateKey,System.IO.TextWriter,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)"/>.
            </summary>
            <param name="service"></param>
            <param name="templateSource"></param>
            <param name="key"></param>
            <param name="writer"></param>
            <param name="modelType"></param>
            <param name="model"></param>
            <param name="viewBag"></param>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineServiceExtensions.RunCompile(RazorEngine.Templating.IRazorEngineService,System.String,RazorEngine.Templating.ITemplateKey,System.IO.TextWriter,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)">
            <summary>
            See <see cref="M:RazorEngine.Templating.RazorEngineService.RunCompile(RazorEngine.Templating.ITemplateKey,System.IO.TextWriter,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)"/>.
            Convenience method which calls <see cref="M:RazorEngine.Templating.RazorEngineService.AddTemplate(RazorEngine.Templating.ITemplateKey,RazorEngine.Templating.ITemplateSource)"/> before calling <see cref="M:RazorEngine.Templating.RazorEngineService.RunCompile(RazorEngine.Templating.ITemplateKey,System.IO.TextWriter,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)"/>.
            </summary>
            <param name="service"></param>
            <param name="templateSource"></param>
            <param name="key"></param>
            <param name="writer"></param>
            <param name="modelType"></param>
            <param name="model"></param>
            <param name="viewBag"></param>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineServiceExtensions.RunCompile(RazorEngine.Templating.IRazorEngineService,RazorEngine.Templating.ITemplateSource,System.String,System.IO.TextWriter,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)">
            <summary>
            See <see cref="M:RazorEngine.Templating.RazorEngineService.RunCompile(RazorEngine.Templating.ITemplateKey,System.IO.TextWriter,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)"/>.
            Convenience method which calls <see cref="M:RazorEngine.Templating.RazorEngineService.AddTemplate(RazorEngine.Templating.ITemplateKey,RazorEngine.Templating.ITemplateSource)"/> before calling <see cref="M:RazorEngine.Templating.RazorEngineService.RunCompile(RazorEngine.Templating.ITemplateKey,System.IO.TextWriter,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)"/>.
            </summary>
            <param name="service"></param>
            <param name="templateSource"></param>
            <param name="name"></param>
            <param name="writer"></param>
            <param name="modelType"></param>
            <param name="model"></param>
            <param name="viewBag"></param>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineServiceExtensions.RunCompile(RazorEngine.Templating.IRazorEngineService,System.String,System.String,System.IO.TextWriter,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)">
            <summary>
            See <see cref="M:RazorEngine.Templating.RazorEngineService.RunCompile(RazorEngine.Templating.ITemplateKey,System.IO.TextWriter,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)"/>.
            Convenience method which calls <see cref="M:RazorEngine.Templating.RazorEngineService.AddTemplate(RazorEngine.Templating.ITemplateKey,RazorEngine.Templating.ITemplateSource)"/> before calling <see cref="M:RazorEngine.Templating.RazorEngineService.RunCompile(RazorEngine.Templating.ITemplateKey,System.IO.TextWriter,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)"/>.
            </summary>
            <param name="service"></param>
            <param name="templateSource"></param>
            <param name="name"></param>
            <param name="writer"></param>
            <param name="modelType"></param>
            <param name="model"></param>
            <param name="viewBag"></param>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineServiceExtensions.WithWriter(System.Action{System.IO.TextWriter})">
            <summary>
            Helper method to provide a TextWriter and return the written data.
            </summary>
            <param name="withWriter"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineServiceExtensions.RunCompile(RazorEngine.Templating.IRazorEngineService,RazorEngine.Templating.ITemplateKey,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)">
            <summary>
            See <see cref="M:RazorEngine.Templating.RazorEngineService.RunCompile(RazorEngine.Templating.ITemplateKey,System.IO.TextWriter,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)"/>.
            Convenience method which creates a <see cref="T:System.IO.TextWriter"/> and returns the result as string.
            </summary>
            <param name="service"></param>
            <param name="key"></param>
            <param name="modelType"></param>
            <param name="model"></param>
            <param name="viewBag"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineServiceExtensions.RunCompile(RazorEngine.Templating.IRazorEngineService,System.String,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)">
            <summary>
            See <see cref="M:RazorEngine.Templating.RazorEngineService.RunCompile(RazorEngine.Templating.ITemplateKey,System.IO.TextWriter,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)"/>.
            Convenience method which creates a <see cref="T:System.IO.TextWriter"/> and returns the result as string.
            </summary>
            <param name="service"></param>
            <param name="name"></param>
            <param name="modelType"></param>
            <param name="model"></param>
            <param name="viewBag"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineServiceExtensions.RunCompile(RazorEngine.Templating.IRazorEngineService,RazorEngine.Templating.ITemplateSource,RazorEngine.Templating.ITemplateKey,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)">
            <summary>
            See <see cref="M:RazorEngine.Templating.RazorEngineService.RunCompile(RazorEngine.Templating.ITemplateKey,System.IO.TextWriter,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)"/>.
            Convenience method which calls <see cref="M:RazorEngine.Templating.RazorEngineService.AddTemplate(RazorEngine.Templating.ITemplateKey,RazorEngine.Templating.ITemplateSource)"/> before calling <see cref="M:RazorEngine.Templating.RazorEngineService.RunCompile(RazorEngine.Templating.ITemplateKey,System.IO.TextWriter,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)"/>.
            Convenience method which creates a <see cref="T:System.IO.TextWriter"/> and returns the result as string.
            </summary>
            <param name="service"></param>
            <param name="templateSource"></param>
            <param name="key"></param>
            <param name="modelType"></param>
            <param name="model"></param>
            <param name="viewBag"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineServiceExtensions.RunCompile(RazorEngine.Templating.IRazorEngineService,System.String,RazorEngine.Templating.ITemplateKey,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)">
            <summary>
            See <see cref="M:RazorEngine.Templating.RazorEngineService.RunCompile(RazorEngine.Templating.ITemplateKey,System.IO.TextWriter,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)"/>.
            Convenience method which calls <see cref="M:RazorEngine.Templating.RazorEngineService.AddTemplate(RazorEngine.Templating.ITemplateKey,RazorEngine.Templating.ITemplateSource)"/> before calling <see cref="M:RazorEngine.Templating.RazorEngineService.RunCompile(RazorEngine.Templating.ITemplateKey,System.IO.TextWriter,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)"/>.
            Convenience method which creates a <see cref="T:System.IO.TextWriter"/> and returns the result as string.
            </summary>
            <param name="service"></param>
            <param name="templateSource"></param>
            <param name="key"></param>
            <param name="modelType"></param>
            <param name="model"></param>
            <param name="viewBag"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineServiceExtensions.RunCompile(RazorEngine.Templating.IRazorEngineService,RazorEngine.Templating.ITemplateSource,System.String,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)">
            <summary>
            See <see cref="M:RazorEngine.Templating.RazorEngineService.RunCompile(RazorEngine.Templating.ITemplateKey,System.IO.TextWriter,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)"/>.
            Convenience method which calls <see cref="M:RazorEngine.Templating.RazorEngineService.AddTemplate(RazorEngine.Templating.ITemplateKey,RazorEngine.Templating.ITemplateSource)"/> before calling <see cref="M:RazorEngine.Templating.RazorEngineService.RunCompile(RazorEngine.Templating.ITemplateKey,System.IO.TextWriter,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)"/>.
            Convenience method which creates a <see cref="T:System.IO.TextWriter"/> and returns the result as string.
            </summary>
            <param name="service"></param>
            <param name="templateSource"></param>
            <param name="name"></param>
            <param name="modelType"></param>
            <param name="model"></param>
            <param name="viewBag"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineServiceExtensions.RunCompile(RazorEngine.Templating.IRazorEngineService,System.String,System.String,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)">
            <summary>
            See <see cref="M:RazorEngine.Templating.RazorEngineService.RunCompile(RazorEngine.Templating.ITemplateKey,System.IO.TextWriter,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)"/>.
            Convenience method which calls <see cref="M:RazorEngine.Templating.RazorEngineService.AddTemplate(RazorEngine.Templating.ITemplateKey,RazorEngine.Templating.ITemplateSource)"/> before calling <see cref="M:RazorEngine.Templating.RazorEngineService.RunCompile(RazorEngine.Templating.ITemplateKey,System.IO.TextWriter,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)"/>.
            Convenience method which creates a <see cref="T:System.IO.TextWriter"/> and returns the result as string.
            </summary>
            <param name="service"></param>
            <param name="templateSource"></param>
            <param name="name"></param>
            <param name="modelType"></param>
            <param name="model"></param>
            <param name="viewBag"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineServiceExtensions.Run(RazorEngine.Templating.IRazorEngineService,System.String,System.IO.TextWriter,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)">
            <summary>
            See <see cref="M:RazorEngine.Templating.RazorEngineService.Run(RazorEngine.Templating.ITemplateKey,System.IO.TextWriter,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)"/>.
            </summary>
            <param name="service"></param>
            <param name="name"></param>
            <param name="writer"></param>
            <param name="modelType"></param>
            <param name="model"></param>
            <param name="viewBag"></param>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineServiceExtensions.Run(RazorEngine.Templating.IRazorEngineService,RazorEngine.Templating.ITemplateKey,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)">
            <summary>
            See <see cref="M:RazorEngine.Templating.RazorEngineService.Run(RazorEngine.Templating.ITemplateKey,System.IO.TextWriter,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)"/>.
            Convenience method which creates a <see cref="T:System.IO.TextWriter"/> and returns the result as string.
            </summary>
            <param name="service"></param>
            <param name="key"></param>
            <param name="modelType"></param>
            <param name="model"></param>
            <param name="viewBag"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineServiceExtensions.Run(RazorEngine.Templating.IRazorEngineService,System.String,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)">
            <summary>
            See <see cref="M:RazorEngine.Templating.RazorEngineService.Run(RazorEngine.Templating.ITemplateKey,System.IO.TextWriter,System.Type,System.Object,RazorEngine.Templating.DynamicViewBag)"/>.
            Convenience method which creates a <see cref="T:System.IO.TextWriter"/> and returns the result as string.
            </summary>
            <param name="service"></param>
            <param name="name"></param>
            <param name="modelType"></param>
            <param name="model"></param>
            <param name="viewBag"></param>
            <returns></returns>
        </member>
        <member name="T:RazorEngine.Templating.LoadedTemplateSource">
            <summary>
            A simple <see cref="T:RazorEngine.Templating.ITemplateSource"/> implementation which represents an in-memory string.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.LoadedTemplateSource.#ctor(System.String,System.String)">
            <summary>
            Initializes a new <see cref="T:RazorEngine.Templating.LoadedTemplateSource"/> instance.
            </summary>
            <param name="template"></param>
            <param name="templateFile"></param>
        </member>
        <member name="P:RazorEngine.Templating.LoadedTemplateSource.Template">
            <summary>
            The in-memory template sourcecode.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.LoadedTemplateSource.TemplateFile">
            <summary>
            The template file or null if none exists.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.LoadedTemplateSource.GetTemplateReader">
            <summary>
            Creates a new <see cref="T:System.IO.StringReader"/> to read the in-memory stream.
            </summary>
            <returns></returns>
        </member>
        <member name="T:RazorEngine.Templating.TypeLoader">
            <summary>
            Defines a type loader.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.TypeLoader.#ctor(System.AppDomain,System.Collections.Generic.IEnumerable{System.Reflection.Assembly})">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Templating.TypeLoader"/>
            </summary>
            <param name="appDomain">The application domain.</param>
            <param name="assemblies">The set of assemblies.</param>
        </member>
        <member name="M:RazorEngine.Templating.TypeLoader.CreateInstance(System.Type)">
            <summary>
            Creates an instance of the specified type.
            </summary>
            <param name="type">The type to create.</param>
            <returns>An instance of the type.</returns>
        </member>
        <member name="M:RazorEngine.Templating.TypeLoader.Dispose(System.Boolean)">
            <summary>
            Releases resources used by this instance.
            </summary>
            <param name="disposing">Flag to determine whether this instance is being disposed of explicitly.</param>
        </member>
        <member name="M:RazorEngine.Templating.TypeLoader.Dispose">
            <summary>
            Releases resources used by this reference.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.TypeLoader.GetConstructor(System.Type)">
            <summary>
            Gets the delegate used to create an instance of the template type. 
            This method will consider the cached constructor delegate before creating an instance of one.
            </summary>
            <param name="type">The template type.</param>
            <returns>The delegate instance.</returns>
        </member>
        <member name="M:RazorEngine.Templating.TypeLoader.GetConstructorInternal(System.Type)">
            <summary>
            Gets the delegate used to create an instance of the template type.
            </summary>
            <param name="type">The template type.</param>
            <returns>The delegate instance.</returns>
        </member>
        <member name="M:RazorEngine.Templating.TypeLoader.ResolveAssembly(System.String)">
            <summary>
            Resolves the assembly with the specified name.
            </summary>
            <param name="name">The name of the assembly.</param>
            <returns>The assembly instance, or null.</returns>
        </member>
        <member name="T:RazorEngine.Templating.TemplateParsingException">
            <summary>
            Defines an exception that occurs during template parsing.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.TemplateParsingException.#ctor(System.String,System.Int32,System.Int32)">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Templating.TemplateParsingException"/>.
            </summary>
            <param name="errorMessage">The error message generated by the parser.</param>
            <param name="characterIndex">The character index of the error.</param>
            <param name="lineIndex">The line index of the error.</param>
        </member>
        <member name="M:RazorEngine.Templating.TemplateParsingException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Templating.TemplateParsingException"/> from serialised data.
            </summary>
            <param name="info">The serialisation info.</param>
            <param name="context">The streaming context.</param>
        </member>
        <member name="P:RazorEngine.Templating.TemplateParsingException.Column">
            <summary>
            Gets the column the parsing error occured.
            </summary>
        </member>
        <member name="P:RazorEngine.Templating.TemplateParsingException.Line">
            <summary>
            Gets the line the parsing error occured.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.TemplateParsingException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Gets the object data for serialisation.
            </summary>
            <param name="info">The serialisation info.</param>
            <param name="context">The streaming context.</param>
        </member>
        <member name="F:RazorEngine.Templating.RazorEngineCore._cached">
            <summary>
            We need this for creating the templates.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineCore.CreateExecuteContext">
            <summary>
            Creates a new <see cref="T:RazorEngine.Templating.ExecuteContext"/> for tracking templates.
            </summary>
            <returns>The execute context.</returns>
        </member>
        <member name="P:RazorEngine.Templating.RazorEngineCore.Configuration">
            <summary>
            Gets the template service configuration.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineCore.Compile(RazorEngine.Templating.ITemplateKey,System.Type)">
            <summary>
            Compiles the specified template.
            </summary>
            <param name="key">The string template.</param>
            <param name="modelType">The model type.</param>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineCore.CreateTemplate(RazorEngine.Templating.ICompiledTemplate,System.Object,RazorEngine.Templating.DynamicViewBag)">
            <summary>
            Creates an instance of <see cref="T:RazorEngine.Templating.ITemplate"/> from the specified string template.
            </summary>
            <param name="template">The compiled template.</param>
            <param name="model">The model instance or NULL if no model exists.</param>
            <param name="viewbag"></param>
            <returns>An instance of <see cref="T:RazorEngine.Templating.ITemplate"/>.</returns>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineCore.CreateTemplateType(RazorEngine.Templating.ITemplateSource,System.Type)">
            <summary>
            Creates a <see cref="T:System.Type"/> that can be used to instantiate an instance of a template.
            </summary>
            <param name="razorTemplate">The string template.</param>
            <param name="modelType">The model type or NULL if no model exists.</param>
            <returns>An instance of <see cref="T:System.Type"/>.</returns>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineCore.RunTemplate(RazorEngine.Templating.ICompiledTemplate,System.IO.TextWriter,System.Object,RazorEngine.Templating.DynamicViewBag)">
            <summary>
            Runs the specified template and returns the result.
            </summary>
            <param name="template">The template to run.</param>
            <param name="writer"></param>
            <param name="model"></param>
            <param name="viewBag">The ViewBag contents or NULL for an initially empty ViewBag.</param>
            <returns>The string result of the template.</returns>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineCore.CreateInstanceContext(System.Type)">
            <summary>
            Creates a new <see cref="T:RazorEngine.Templating.InstanceContext"/> for creating template instances.
            </summary>
            <param name="templateType">The template type.</param>
            <returns>An instance of <see cref="T:RazorEngine.Templating.InstanceContext"/>.</returns>
        </member>
        <member name="M:RazorEngine.Templating.RazorEngineCore.Dispose">
            <summary>
            Disposes the current instance.
            </summary>
        </member>
        <member name="T:RazorEngine.Templating.TemplateWriter">
            <summary>
            Defines a template writer used for helper templates.
            </summary>
        </member>
        <member name="M:RazorEngine.Templating.TemplateWriter.#ctor(System.Action{System.IO.TextWriter})">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Templating.TemplateWriter"/>.
            </summary>
            <param name="writer">The writer delegate used to write using the specified <see cref="T:System.IO.TextWriter"/>.</param>
        </member>
        <member name="M:RazorEngine.Templating.TemplateWriter.ToString">
            <summary>
            Executes the write delegate and returns the result of this <see cref="T:RazorEngine.Templating.TemplateWriter"/>.
            </summary>
            <returns>The string result of the helper template.</returns>
        </member>
        <member name="M:RazorEngine.Templating.TemplateWriter.WriteTo(System.IO.TextWriter)">
            <summary>
            Writes the helper result of the specified text writer.
            </summary>
            <param name="writer">The text writer to write the helper result to.</param>
        </member>
        <member name="T:RazorEngine.Encoding">
            <summary>
            Defines the possible values for encoding.
            </summary>
        </member>
        <member name="F:RazorEngine.Encoding.Html">
            <summary>
            Use html encoding.
            </summary>
        </member>
        <member name="F:RazorEngine.Encoding.Raw">
            <summary>
            Use raw text (no encoding)
            </summary>
        </member>
        <member name="T:RazorEngine.Language">
            <summary>
            Defines the possible supported code languages.
            </summary>
        </member>
        <member name="F:RazorEngine.Language.CSharp">
            <summary>
            C# Language
            </summary>
        </member>
        <member name="F:RazorEngine.Language.VisualBasic">
            <summary>
            Visual Basic Language
            </summary>
        </member>
        <member name="T:RazorEngine.PositionTagged`1">
            <summary>
            PositionTagged
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="M:RazorEngine.PositionTagged`1.#ctor(`0,System.Int32)">
            <summary>
            Creates a new PositionTagged instance
            </summary>
            <param name="value"></param>
            <param name="offset"></param>
        </member>
        <member name="P:RazorEngine.PositionTagged`1.Position">
            <summary>
            The position.
            </summary>
        </member>
        <member name="P:RazorEngine.PositionTagged`1.Value">
            <summary>
            The value.
            </summary>
        </member>
        <member name="M:RazorEngine.PositionTagged`1.Equals(System.Object)">
            <summary>
            Checks if the given object equals the current object.
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.PositionTagged`1.GetHashCode">
            <summary>
            Calculates a hash-code for the current instance.
            </summary>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.PositionTagged`1.ToString">
            <summary>
            Returns Value.ToString().
            </summary>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.PositionTagged`1.op_Implicit(RazorEngine.PositionTagged{`0})~`0">
            <summary>
            convert implicitely to the value.
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.PositionTagged`1.op_Implicit(System.Tuple{`0,System.Int32})~RazorEngine.PositionTagged{`0}">
            <summary>
            Convert from a tuple.
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.PositionTagged`1.op_Equality(RazorEngine.PositionTagged{`0},RazorEngine.PositionTagged{`0})">
            <summary>
            Checks if the given instances are equal.
            </summary>
            <param name="left"></param>
            <param name="right"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.PositionTagged`1.op_Inequality(RazorEngine.PositionTagged{`0},RazorEngine.PositionTagged{`0})">
            <summary>
            Checks if the given instances are not equal.
            </summary>
            <param name="left"></param>
            <param name="right"></param>
            <returns></returns>
        </member>
        <member name="T:RazorEngine.Razor">
            <summary>
            Provides quick access to template functions.
            </summary>
        </member>
        <member name="P:RazorEngine.Razor.TemplateService">
            <summary>
            Gets the template service.
            </summary>
        </member>
        <member name="M:RazorEngine.Razor.Compile(System.String,System.String)">
            <summary>
            Compiles the specified template.
            </summary>
            <param name="razorTemplate">The string template.</param>
            <param name="name">The name of the template.</param>
        </member>
        <member name="M:RazorEngine.Razor.Compile(System.String,System.Type,System.String)">
            <summary>
            Compiles the specified template.
            </summary>
            <param name="razorTemplate">The string template.</param>
            <param name="modelType">The model type.</param>
            <param name="name">The name of the template.</param>
        </member>
        <member name="M:RazorEngine.Razor.Compile``1(System.String,System.String)">
            <summary>
            Compiles the specified template.
            </summary>
            <typeparam name="T">The model type.</typeparam>
            <param name="razorTemplate">The string template.</param>
            <param name="name">The name of the template.</param>
        </member>
        <member name="M:RazorEngine.Razor.CreateTemplate(System.String)">
            <summary>
            Creates an instance of <see cref="T:RazorEngine.Templating.ITemplate"/> from the specified string template.
            </summary>
            <param name="razorTemplate">The string template.</param>
            <returns>An instance of <see cref="T:RazorEngine.Templating.ITemplate"/>.</returns>
        </member>
        <member name="M:RazorEngine.Razor.CreateTemplate``1(System.String,``0)">
            <summary>
            Creates an instance of <see cref="T:RazorEngine.Templating.ITemplate`1"/> from the specified string template.
            </summary>
            <typeparam name="T">The model type.</typeparam>
            <param name="razorTemplate">The string template.</param>
            <param name="model">The model instance.</param>
            <returns>An instance of <see cref="T:RazorEngine.Templating.ITemplate`1"/>.</returns>
        </member>
        <member name="M:RazorEngine.Razor.CreateTemplates(System.Collections.Generic.IEnumerable{System.String},System.Boolean)">
            <summary>
            Creates a set of templates from the specified string templates.
            </summary>
            <param name="razorTemplates">The set of templates to create <see cref="T:RazorEngine.Templating.ITemplate"/> instances for.</param>
            <param name="parallel">Flag to determine whether to create templates in parallel.</param>
            <returns>The enumerable set of template instances.</returns>
        </member>
        <member name="M:RazorEngine.Razor.CreateTemplates``1(System.Collections.Generic.IEnumerable{System.String},System.Collections.Generic.IEnumerable{``0},System.Boolean)">
            <summary>
            Creates a set of templates from the specified string templates and models.
            </summary>
            <typeparam name="T">The model type.</typeparam>
            <param name="razorTemplates">The set of templates to create <see cref="T:RazorEngine.Templating.ITemplate"/> instances for.</param>
            <param name="models">The set of models used to assign to templates.</param>
            <param name="parallel">Flag to determine whether to create templates in parallel.</param>
            <returns>The enumerable set of template instances.</returns>
        </member>
        <member name="M:RazorEngine.Razor.CreateTemplateType(System.String)">
            <summary>
            Creates a <see cref="T:System.Type"/> that can be used to instantiate an instance of a template.
            </summary>
            <param name="razorTemplate">The string template.</param>
            <returns>An instance of <see cref="T:System.Type"/>.</returns>
        </member>
        <member name="M:RazorEngine.Razor.CreateTemplateType(System.String,System.Type)">
            <summary>
            Creates a <see cref="T:System.Type"/> that can be used to instantiate an instance of a template.
            </summary>
            <param name="razorTemplate">The string template.</param>
            <param name="modelType">The model type.</param>
            <returns>An instance of <see cref="T:System.Type"/>.</returns>
        </member>
        <member name="M:RazorEngine.Razor.CreateTemplateTypes(System.Collections.Generic.IEnumerable{System.String},System.Boolean)">
            <summary>
            Crates a set of template types from the specfied string templates.
            </summary>
            <param name="razorTemplates">The set of templates to create <see cref="T:System.Type"/> instances for.</param>
            <param name="parallel">Flag to determine whether to create template types in parallel.</param>
            <returns>The set of <see cref="T:System.Type"/> instances.</returns>
        </member>
        <member name="M:RazorEngine.Razor.CreateTemplateTypes(System.Collections.Generic.IEnumerable{System.String},System.Type,System.Boolean)">
            <summary>
            Creates a set of template types from the specfied string templates.
            </summary>
            <param name="razorTemplates">The set of templates to create <see cref="T:System.Type"/> instances for.</param>
            <param name="modelType">The model type.</param>
            <param name="parallel">Flag to determine whether to create template types in parallel.</param>
            <returns>The set of <see cref="T:System.Type"/> instances.</returns>
        </member>
        <member name="M:RazorEngine.Razor.GetTemplate(System.String,System.String)">
            <summary>
            Gets an instance of the template using the cached compiled type, or compiles the template type
            if it does not exist in the cache.
            </summary>
            <param name="razorTemplate">The string template.</param>
            <param name="name">The name of the template type in the cache.</param>
            <returns>An instance of <see cref="T:RazorEngine.Templating.ITemplate"/>.</returns>
        </member>
        <member name="M:RazorEngine.Razor.GetTemplate``1(System.String,``0,System.String)">
            <summary>
            Gets an instance of the template using the cached compiled type, or compiles the template type
            if it does not exist in the cache.
            </summary>
            <typeparam name="T">The model type.</typeparam>
            <param name="razorTemplate">The string template.</param>
            <param name="model">The model instance.</param>
            <param name="name">The name of the template type in the cache.</param>
            <returns>An instance of <see cref="T:RazorEngine.Templating.ITemplate`1"/>.</returns>
        </member>
        <member name="M:RazorEngine.Razor.GetTemplates(System.Collections.Generic.IEnumerable{System.String},System.Collections.Generic.IEnumerable{System.String},System.Boolean)">
            <summary>
            Gets the set of template instances for the specified string templates. Cached templates will be considered
            and if they do not exist, new types will be created and instantiated.
            </summary>
            <param name="razorTemplates">The set of templates to create.</param>
            <param name="names">The set of cache names.</param>
            <param name="parallel">Flag to determine whether to get the templates in parallel.</param>
            <returns>The set of <see cref="T:RazorEngine.Templating.ITemplate"/> instances.</returns>
        </member>
        <member name="M:RazorEngine.Razor.GetTemplates``1(System.Collections.Generic.IEnumerable{System.String},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{System.String},System.Boolean)">
            <summary>
            Gets the set of template instances for the specified string templates. Cached templates will be considered
            and if they do not exist, new types will be created and instantiated.
            </summary>
            <typeparam name="T">The model type.</typeparam>
            <param name="razorTemplates">The set of templates to create.</param>
            <param name="models">The set of models.</param>
            <param name="names">The set of cache names.</param>
            <param name="parallel">Flag to determine whether to get the templates in parallel.</param>
            <returns>The set of <see cref="T:RazorEngine.Templating.ITemplate"/> instances.</returns>
        </member>
        <member name="M:RazorEngine.Razor.Parse(System.String)">
            <summary>
            Parses and returns the result of the specified string template.
            </summary>
            <param name="razorTemplate">The string template.</param>
            <returns>The string result of the template.</returns>
        </member>
        <member name="M:RazorEngine.Razor.Parse(System.String,System.String)">
            <summary>
            Parses and returns the result of the specified string template. 
            This method will provide a cache check to see if the compiled template type already exists and is valid.
            </summary>
            <param name="razorTemplate">The string template.</param>
            <param name="name">The name of the cached template type.</param>
            <returns>The string result of the template.</returns>
        </member>
        <member name="M:RazorEngine.Razor.Parse(System.String,System.Object)">
            <summary>
            Parses and returns the result of the specified string template.
            </summary>
            <param name="razorTemplate">The string template.</param>
            <param name="model">The model instance.</param>
            <returns>The string result of the template.</returns>
        </member>
        <member name="M:RazorEngine.Razor.Parse``1(System.String,``0)">
            <summary>
            Parses and returns the result of the specified string template.
            </summary>
            <typeparam name="T">The model type.</typeparam>
            <param name="razorTemplate">The string template.</param>
            <param name="model">The model instance.</param>
            <returns>The string result of the template.</returns>
        </member>
        <member name="M:RazorEngine.Razor.Parse``1(System.String,``0,System.String)">
            <summary>
            Parses and returns the result of the specified string template.
            </summary>
            <typeparam name="T">The model type.</typeparam>
            <param name="razorTemplate">The string template.</param>
            <param name="model">The model instance.</param>
            <param name="name">The name of the template type in the cache.</param>
            <returns>The string result of the template.</returns>
        </member>
        <member name="M:RazorEngine.Razor.Parse``1(System.String,``0,RazorEngine.Templating.DynamicViewBag,System.String)">
            <summary>
            Backwards Compat
            </summary>
            <typeparam name="T"></typeparam>
            <param name="razorTemplate"></param>
            <param name="model"></param>
            <param name="viewBag"></param>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Razor.Parse(System.String,System.Object,System.String)">
            <summary>
            Parses and returns the result of the specified string template.
            </summary>
            <param name="razorTemplate">The string template.</param>
            <param name="model">The model instance.</param>
            <param name="name">The name of the template type in the cache.</param>
            <returns>The string result of the template.</returns>
        </member>
        <member name="M:RazorEngine.Razor.Parse(System.String,System.Object,RazorEngine.Templating.DynamicViewBag,System.String)">
            <summary>
            Backwards Compat
            </summary>
            <param name="razorTemplate"></param>
            <param name="model"></param>
            <param name="viewBag"></param>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:RazorEngine.Razor.ParseMany(System.Collections.Generic.IEnumerable{System.String},System.Boolean)">
            <summary>
            Parses the specified set of templates.
            </summary>
            <param name="razorTemplates">The set of string templates to partse.</param>
            <param name="parallel">Flag to determine whether parsing in templates.</param>
            <returns>The set of parsed template results.</returns>
        </member>
        <member name="M:RazorEngine.Razor.ParseMany(System.Collections.Generic.IEnumerable{System.String},System.Collections.Generic.IEnumerable{System.String},System.Boolean)">
            <summary>
            Parses the specified set of templates.
            </summary>
            <param name="razorTemplates">The set of string templates to partse.</param>
            <param name="cacheNames">The set of cache names.</param>
            <param name="parallel">Flag to determine whether parsing in templates.</param>
            <returns>The set of parsed template results.</returns>
        </member>
        <member name="M:RazorEngine.Razor.ParseMany``1(System.String,System.Collections.Generic.IEnumerable{``0},System.Boolean)">
            <summary>
            Parses the template and merges with the many models provided.
            </summary>
            <typeparam name="T">The model type.</typeparam>
            <param name="razorTemplate">The razor template.</param>
            <param name="models">The set of models.</param>
            <param name="parallel">Flag to determine whether parsing in parallel.</param>
            <returns>The set of parsed template results.</returns>
        </member>
        <member name="M:RazorEngine.Razor.ParseMany``1(System.Collections.Generic.IEnumerable{System.String},System.Collections.Generic.IEnumerable{``0},System.Boolean)">
            <summary>
            Parses the specified set of templates.
            </summary>
            <typeparam name="T">The model type.</typeparam>
            <param name="razorTemplates">The set of string templates to partse.</param>
            <param name="models">The set of models.</param>
            <param name="parallel">Flag to determine whether parsing in templates.</param>
            <returns>The set of parsed template results.</returns>
        </member>
        <member name="M:RazorEngine.Razor.ParseMany``1(System.Collections.Generic.IEnumerable{System.String},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{System.String},System.Boolean)">
            <summary>
            Parses the specified set of templates.
            </summary>
            <typeparam name="T">The model type.</typeparam>
            <param name="razorTemplates">The set of string templates to partse.</param>
            <param name="models">The set of models.</param>
            <param name="names">The set of cache names.</param>
            <param name="parallel">Flag to determine whether parsing in templates.</param>
            <returns>The set of parsed template results.</returns>
        </member>
        <member name="M:RazorEngine.Razor.Resolve(System.String)">
            <summary>
            Resolves the template with the specified name.
            </summary>
            <param name="name">The name of the template.</param>
            <returns>The resolved template.</returns>
        </member>
        <member name="M:RazorEngine.Razor.Resolve(System.String,System.Object)">
            <summary>
            Resolves the template with the specified name.
            </summary>
            <param name="name">The name of the template.</param>
            <param name="model">The model for the template.</param>
            <returns>The resolved template.</returns>
        </member>
        <member name="M:RazorEngine.Razor.Resolve``1(System.String,``0)">
            <summary>
            Resolves the template with the specified name.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="name">The name of the template.</param>
            <param name="model">The model for the template.</param>
            <returns>The resolved template.</returns>
        </member>
        <member name="M:RazorEngine.Razor.Run(System.String)">
            <summary>
            Runs the template with the specified name.
            </summary>
            <param name="name">The name of the template.</param>
            <returns>The string result of the template.</returns>
        </member>
        <member name="M:RazorEngine.Razor.Run(System.String,System.Object)">
            <summary>
            Runs the template with the specified name.
            </summary>
            <param name="name">The name of the template.</param>
            <param name="model">The model.</param>
            <returns>The string result of the template.</returns>
        </member>
        <member name="M:RazorEngine.Razor.Run``1(System.String,``0)">
            <summary>
            Runs the template with the specified name.
            </summary>
            <typeparam name="T">The model type.</typeparam>
            <param name="name">The name of the template.</param>
            <param name="model">The model.</param>
            <returns>The string result of the template.</returns>
        </member>
        <member name="M:RazorEngine.Razor.SetTemplateService(RazorEngine.Templating.ITemplateService)">
            <summary>
            Sets the template service.
            </summary>
            <param name="service">The template service.</param>
        </member>
        <member name="T:RazorEngine.Text.HtmlEncodedString">
            <summary>
            Represents a Html-encoded string.
            </summary>
        </member>
        <member name="M:RazorEngine.Text.HtmlEncodedString.#ctor(System.String)">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Text.HtmlEncodedString"/>
            </summary>
            <param name="value">The raw string to be encoded.</param>
        </member>
        <member name="M:RazorEngine.Text.HtmlEncodedString.ToEncodedString">
            <summary>
            Gets the encoded string.
            </summary>
            <returns>The encoded string.</returns>
        </member>
        <member name="M:RazorEngine.Text.HtmlEncodedString.ToString">
            <summary>
            Gets the string representation of this instance.
            </summary>
            <returns>The string representation of this instance.</returns>
        </member>
        <member name="T:RazorEngine.Text.HtmlEncodedStringFactory">
            <summary>
            Represents a factory that creates <see cref="T:RazorEngine.Text.HtmlEncodedString"/> instances.
            </summary>
        </member>
        <member name="M:RazorEngine.Text.HtmlEncodedStringFactory.CreateEncodedString(System.String)">
            <summary>
            Creates a <see cref="T:RazorEngine.Text.IEncodedString"/> instance for the specified raw string.
            </summary>
            <param name="rawString">The raw string.</param>
            <returns>An instance of <see cref="T:RazorEngine.Text.IEncodedString"/>.</returns>
        </member>
        <member name="M:RazorEngine.Text.HtmlEncodedStringFactory.CreateEncodedString(System.Object)">
            <summary>
            Creates a <see cref="T:RazorEngine.Text.IEncodedString"/> instance for the specified object instance.
            </summary>
            <param name="value">The object instance.</param>
            <returns>An instance of <see cref="T:RazorEngine.Text.IEncodedString"/>.</returns>
        </member>
        <member name="T:RazorEngine.Text.IEncodedString">
            <summary>
            Defines the required contract for implementing an encoded string.
            </summary>
        </member>
        <member name="M:RazorEngine.Text.IEncodedString.ToEncodedString">
            <summary>
            Gets the encoded string.
            </summary>
            <returns>The encoded string.</returns>
        </member>
        <member name="T:RazorEngine.Text.IEncodedStringFactory">
            <summary>
            Defines the required contract for implementing a factory for building encoded strings.
            </summary>
        </member>
        <member name="M:RazorEngine.Text.IEncodedStringFactory.CreateEncodedString(System.String)">
            <summary>
            Creates a <see cref="T:RazorEngine.Text.IEncodedString"/> instance for the specified raw string.
            </summary>
            <param name="value">The raw string.</param>
            <returns>An instance of <see cref="T:RazorEngine.Text.IEncodedString"/>.</returns>
        </member>
        <member name="M:RazorEngine.Text.IEncodedStringFactory.CreateEncodedString(System.Object)">
            <summary>
            Creates a <see cref="T:RazorEngine.Text.IEncodedString"/> instance for the specified object instance.
            </summary>
            <param name="value">The object instance.</param>
            <returns>An instance of <see cref="T:RazorEngine.Text.IEncodedString"/>.</returns>
        </member>
        <member name="T:RazorEngine.Text.RawString">
            <summary>
            Represents an unencoded string.
            </summary>
        </member>
        <member name="M:RazorEngine.Text.RawString.#ctor(System.String)">
            <summary>
            Initialises a new instance of <see cref="T:RazorEngine.Text.RawString"/>
            </summary>
            <param name="value">The value</param>
        </member>
        <member name="M:RazorEngine.Text.RawString.ToEncodedString">
            <summary>
            Gets the encoded string.
            </summary>
            <returns>The encoded string.</returns>
        </member>
        <member name="M:RazorEngine.Text.RawString.ToString">
            <summary>
            Gets the string representation of this instance.
            </summary>
            <returns>The string representation of this instance.</returns>
        </member>
        <member name="T:RazorEngine.Text.RawStringFactory">
            <summary>
            Represents a factory that creates <see cref="T:RazorEngine.Text.RawString"/> instances.
            </summary>
        </member>
        <member name="M:RazorEngine.Text.RawStringFactory.CreateEncodedString(System.String)">
            <summary>
            Creates a <see cref="T:RazorEngine.Text.IEncodedString"/> instance for the specified raw string.
            </summary>
            <param name="value">Thevalue.</param>
            <returns>An instance of <see cref="T:RazorEngine.Text.IEncodedString"/>.</returns>
        </member>
        <member name="M:RazorEngine.Text.RawStringFactory.CreateEncodedString(System.Object)">
            <summary>
            Creates a <see cref="T:RazorEngine.Text.IEncodedString"/> instance for the specified object instance.
            </summary>
            <param name="value">The value.</param>
            <returns>An instance of <see cref="T:RazorEngine.Text.IEncodedString"/>.</returns>
        </member>
    </members>
</doc>
