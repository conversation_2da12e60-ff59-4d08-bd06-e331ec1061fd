<?xml version="1.0"?>
<doc>
    <assembly>
        <name>SimpleInjector</name>
    </assembly>
    <members>
        <member name="T:SimpleInjector.ActivationException">
            <summary>
            The standard exception thrown when a container has an error in resolving an object.
            </summary>
        </member>
        <member name="M:SimpleInjector.ActivationException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SimpleInjector.ActivationException" /> class.
            </summary>
        </member>
        <member name="M:SimpleInjector.ActivationException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:SimpleInjector.ActivationException" /> class with a specified error 
            message.
            </summary>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:SimpleInjector.ActivationException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:SimpleInjector.ActivationException" /> class with a specified error 
            message and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="message">
            The error message that explains the reason for the exception. 
            </param>
            <param name="innerException">
            The exception that is the cause of the current exception, or a null reference (Nothing in Visual 
            Basic) if no inner exception is specified. 
            </param>
        </member>
        <member name="M:SimpleInjector.ActivationException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:SimpleInjector.ActivationException" /> class with serialized data.
            </summary>
            <param name="info">
            The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception 
            being thrown. 
            </param>
            <param name="context">
            The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or 
            destination. 
            </param>
            <exception cref="T:System.ArgumentNullException">
            The <paramref name="info" /> parameter is null. 
            </exception>
            <exception cref="T:System.Runtime.Serialization.SerializationException">
            The class name is null or hresult is zero (0). 
            </exception>
        </member>
        <member name="T:SimpleInjector.Advanced.AdvancedExtensions">
            <summary>
            Extension methods for enable advanced scenarios.
            </summary>
        </member>
        <member name="M:SimpleInjector.Advanced.AdvancedExtensions.IsLocked(SimpleInjector.Container)">
            <summary>
            Determines whether the specified container is locked making any new registrations. The container
            is automatically locked when <see cref="M:SimpleInjector.Container.GetInstance(System.Type)">GetInstance</see> is called for the
            first time.
            </summary>
            <param name="container">The container.</param>
            <returns>
              <c>true</c> if the specified container is locked; otherwise, <c>false</c>.
            </returns>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="container"/> is null.</exception>
        </member>
        <member name="M:SimpleInjector.Advanced.AdvancedExtensions.IsVerifying(SimpleInjector.Container)">
            <summary>Determines whether the specified container is currently verifying its configuration.</summary>
            <param name="container">The container.</param>
            <returns><c>true</c> if the specified container is verifying; otherwise, <c>false</c>.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="container"/> is null.</exception>
        </member>
        <member name="M:SimpleInjector.Advanced.AdvancedExtensions.GetInitializer``1(SimpleInjector.Container)">
            <summary>This method has been removed.</summary>
            <param name="container">The container.</param>
            <typeparam name="TService">The type for with an initializer must be built.</typeparam>
            <returns>An <see cref="T:System.Action`1"/> delegate or <b>null</b>.</returns>
        </member>
        <member name="M:SimpleInjector.Advanced.AdvancedExtensions.GetItem(SimpleInjector.Container,System.Object)">
            <summary>
            Retrieves an item from the container stored by the given <paramref name="key"/> or null when no
            item is stored by that key.
            </summary>
            <remarks>
            <b>Thread-safety:</b> Calls to this method are thread-safe, but users should take proper
            percussions when they call both <b>GetItem</b> and <see cref="M:SimpleInjector.Advanced.AdvancedExtensions.SetItem(SimpleInjector.Container,System.Object,System.Object)"/>.
            </remarks>
            <param name="container">The container.</param>
            <param name="key">The key of the item to retrieve.</param>
            <returns>The stored item or null (Nothing in VB).</returns>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the supplied arguments is a null
            reference (Nothing in VB).</exception>
        </member>
        <member name="M:SimpleInjector.Advanced.AdvancedExtensions.SetItem(SimpleInjector.Container,System.Object,System.Object)">
            <summary>
            Stores an item by the given <paramref name="key"/> in the container. 
            </summary>
            <remarks>
            <b>Thread-safety:</b> Calls to this method are thread-safe, but users should take proper
            percussions when they call both <see cref="M:SimpleInjector.Advanced.AdvancedExtensions.GetItem(SimpleInjector.Container,System.Object)"/> and <b>SetItem</b>.
            </remarks>
            <param name="container">The container.</param>
            <param name="key">The key of the item to insert or override.</param>
            <param name="item">The actual item. May be null.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when either <paramref name="container"/> or
            <paramref name="key"/> is a null reference (Nothing in VB).</exception>
        </member>
        <member name="M:SimpleInjector.Advanced.AdvancedExtensions.GetOrSetItem``1(SimpleInjector.Container,System.Object,System.Func{SimpleInjector.Container,System.Object,``0})">
            <summary>
            Adds an item by the given <paramref name="key"/> in the container by using the specified function,
            if the key does not already exist. This operation is atomic.
            </summary>
            <typeparam name="T">The Type of the item to create.</typeparam>
            <param name="container">The container.</param>
            <param name="key">The key of the item to insert or override.</param>
            <param name="valueFactory">The function used to generate a value for the given key. The supplied
            value of <paramref name="key"/> will be supplied to the function when called.</param>
            <returns>The stored item or the item from the <paramref name="valueFactory"/>.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown when either <paramref name="container"/>,
            <paramref name="key"/> or <paramref name="valueFactory"/> is a null reference (Nothing in VB).</exception>
        </member>
        <member name="M:SimpleInjector.Advanced.AdvancedExtensions.AppendToCollection(SimpleInjector.Container,System.Type,SimpleInjector.Registration)">
            <summary>
            Allows appending new registrations to existing registrations made using one of the
            <b>RegisterCollection</b> overloads.
            </summary>
            <param name="container">The container.</param>
            <param name="serviceType">The service type of the collection.</param>
            <param name="registration">The registration to append.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the supplied arguments is a null
            reference (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when the <paramref name="serviceType"/> is not a
            reference type, is open generic, or ambiguous.</exception>
            <exception cref="T:System.InvalidOperationException">Thrown when the container is locked.</exception>
            <exception cref="T:System.NotSupportedException">Thrown when the method is called for a registration
            that is made with one of the <b>RegisterCollection</b> overloads that accepts a dynamic collection
            (an <b>IEnumerable</b> or <b>IEnumerable&lt;TService&gt;</b>).</exception>
        </member>
        <member name="M:SimpleInjector.Advanced.AdvancedExtensions.AppendToCollection(SimpleInjector.Container,System.Type,System.Type)">
            <summary>
            Allows appending new registrations to existing registrations made using one of the
            <b>RegisterCollection</b> overloads.
            </summary>
            <param name="container">The container.</param>
            <param name="serviceType">The service type of the collection.</param>
            <param name="implementationType">The implementation type to append.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the supplied arguments is a null
            reference (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when the <paramref name="serviceType"/> is not a
            reference type, or ambiguous.</exception>
            <exception cref="T:System.InvalidOperationException">Thrown when the container is locked.</exception>
            <exception cref="T:System.NotSupportedException">Thrown when the method is called for a registration
            that is made with one of the <b>RegisterCollection</b> overloads that accepts a dynamic collection
            (an <b>IEnumerable</b> or <b>IEnumerable&lt;TService&gt;</b>).</exception>
        </member>
        <member name="T:SimpleInjector.Advanced.IConstructorInjectionBehavior">
            <summary>
            Defines the container's behavior for building an expression tree based on the supplied constructor of
            a given type.
            Set the <see cref="P:SimpleInjector.ContainerOptions.ConstructorInjectionBehavior">ConstructorInjectionBehavior</see> 
            property of the container's <see cref="P:SimpleInjector.Container.Options"/> property to change the default behavior 
            of the container.
            </summary>
        </member>
        <member name="M:SimpleInjector.Advanced.IConstructorInjectionBehavior.BuildParameterExpression(System.Reflection.ParameterInfo)">
            <summary>
            Builds an <see cref="T:System.Linq.Expressions.Expression"/> for the supplied <paramref name="parameter"/>, based on the
            container's configuration.
            </summary>
            <param name="parameter">The parameter.</param>
            <returns>An <see cref="T:System.Linq.Expressions.Expression"/> that describes the intend of creating that 
            <paramref name="parameter"/>. This method never returns null.</returns>
        </member>
        <member name="T:SimpleInjector.Advanced.IConstructorResolutionBehavior">
            <summary>
            Defines the container's behavior for finding a suitable constructor for the creation of a type.
            Set the <see cref="P:SimpleInjector.ContainerOptions.ConstructorResolutionBehavior">ConstructorResolutionBehavior</see> 
            property of the container's <see cref="P:SimpleInjector.Container.Options"/> property to change the default behavior 
            of the container.
            </summary>
        </member>
        <member name="M:SimpleInjector.Advanced.IConstructorResolutionBehavior.GetConstructor(System.Type,System.Type)">
            <summary>
            Gets the given <paramref name="implementationType"/>'s constructor that can be used by the 
            container to create that instance.
            </summary>
            <param name="serviceType">Type of the abstraction that is requested.</param>
            <param name="implementationType">Type of the implementation to find a suitable constructor for.</param>
            <returns>
            The <see cref="T:System.Reflection.ConstructorInfo"/>. This method never returns null.
            </returns>
            <exception cref="T:SimpleInjector.ActivationException">Thrown when no suitable constructor could be found.</exception>
        </member>
        <member name="T:SimpleInjector.Advanced.IConstructorVerificationBehavior">
            <summary>
            Defines the container's behavior for verifying the constructor that will be picked by the
            <see cref="T:SimpleInjector.Advanced.IConstructorResolutionBehavior"/>.
            Set the <see cref="P:SimpleInjector.ContainerOptions.ConstructorVerificationBehavior">ConstructorVerificationBehavior</see> 
            property of the container's <see cref="P:SimpleInjector.Container.Options"/> property to change the default behavior 
            of the container.
            </summary>
        </member>
        <member name="M:SimpleInjector.Advanced.IConstructorVerificationBehavior.Verify(System.Reflection.ParameterInfo)">
            <summary>Verifies the specified <paramref name="parameter"/>.</summary>
            <param name="parameter">The parameter.</param>
            <exception cref="T:SimpleInjector.ActivationException">Thrown when the <paramref name="parameter"/> cannot be 
            used for auto wiring.
            </exception>
            <exception cref="T:System.ArgumentNullException">Thrown when the <paramref name="parameter"/> is a
            null reference.</exception>
        </member>
        <member name="T:SimpleInjector.Advanced.IDependencyInjectionBehavior">
            <summary>
            Defines the container's behavior for building an expression tree for an dependency to inject, based on
            the information of the consuming type the dependency is injected into.
            Set the <see cref="P:SimpleInjector.ContainerOptions.DependencyInjectionBehavior">ConstructorInjectionBehavior</see> 
            property of the container's <see cref="P:SimpleInjector.Container.Options"/> property to change the default behavior 
            of the container.
            </summary>
        </member>
        <member name="M:SimpleInjector.Advanced.IDependencyInjectionBehavior.Verify(SimpleInjector.InjectionConsumerInfo)">
            <summary>Verifies the specified <paramref name="consumer"/>.</summary>
            <param name="consumer">Contextual information about the consumer where the built dependency is
            injected into.</param>
            <exception cref="T:SimpleInjector.ActivationException">
            Thrown when the type of the <see cref="P:SimpleInjector.InjectionConsumerInfo.Target">target</see> supplied with 
            the supplied <paramref name="consumer"/> cannot be used for auto wiring.</exception>
            <exception cref="T:System.ArgumentNullException">Thrown when the supplied argument is a null reference.</exception>
        </member>
        <member name="M:SimpleInjector.Advanced.IDependencyInjectionBehavior.BuildExpression(SimpleInjector.InjectionConsumerInfo)">
            <summary>
            Builds an <see cref="T:System.Linq.Expressions.Expression"/> for the 
            <see cref="P:SimpleInjector.InjectionConsumerInfo.Target">Target</see> of the supplied <paramref name="consumer"/>.
            </summary>
            <param name="consumer">Contextual information about the consumer where the built dependency is
            injected into.</param>
            <returns>An <see cref="T:System.Linq.Expressions.Expression"/> that describes the intend of creating that 
            <see cref="P:SimpleInjector.InjectionConsumerInfo.Target">Target</see>. This method never returns null.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown when the argument is a null reference.</exception>
        </member>
        <member name="T:SimpleInjector.Advanced.ILifestyleSelectionBehavior">
            <summary>
            Defines the container's behavior for selecting the lifestyle for a registration in case no lifestyle
            is explicitly supplied.
            Set the <see cref="P:SimpleInjector.ContainerOptions.LifestyleSelectionBehavior">LifestyleSelectionBehavior</see> 
            property of the container's <see cref="P:SimpleInjector.Container.Options"/> property to change the default behavior 
            of the container. By default, when no lifestyle is explicitly supplied, the 
            <see cref="F:SimpleInjector.Lifestyle.Transient">Transient</see> lifestyle is used.
            </summary>
        </member>
        <member name="M:SimpleInjector.Advanced.ILifestyleSelectionBehavior.SelectLifestyle(System.Type,System.Type)">
            <summary>Selects the lifestyle based on the supplied type information.</summary>
            <param name="serviceType">Type of the abstraction that is registered.</param>
            <param name="implementationType">Type of the implementation to that is registered.</param>
            <returns>The suited <see cref="T:SimpleInjector.Lifestyle"/> for the given type.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown when either one of the arguments is a null reference.</exception>
        </member>
        <member name="T:SimpleInjector.Advanced.InitializationContext">
            <summary>
            An instance of this type will be supplied to the <see cref="T:System.Predicate`1" />
            delegate that is that is supplied to the 
            <see cref="M:SimpleInjector.Container.RegisterInitializer(System.Action{SimpleInjector.Advanced.InstanceInitializationData},System.Predicate{SimpleInjector.Advanced.InitializationContext})">RegisterInitializer</see>
            overload that takes this delegate. This type contains contextual information about the creation and it 
            allows the user to examine the given instance to decide whether the instance should be initialized or 
            not.
            </summary>
        </member>
        <member name="P:SimpleInjector.Advanced.InitializationContext.Producer">
            <summary>
            Gets the <see cref="T:SimpleInjector.InstanceProducer"/> that is responsible for the initialization of the created
            instance.
            </summary>
            <value>The <see cref="T:SimpleInjector.InstanceProducer"/> or null (Nothing in VB) when the instance producer is
            unknown.</value>
        </member>
        <member name="P:SimpleInjector.Advanced.InitializationContext.Registration">
            <summary>
            Gets the <see cref="P:SimpleInjector.Advanced.InitializationContext.Registration"/> that is responsible for the initialization of the created
            instance.
            </summary>
            /// <value>The <see cref="P:SimpleInjector.Advanced.InitializationContext.Registration"/>.</value>
        </member>
        <member name="T:SimpleInjector.Advanced.InstanceInitializationData">
            <summary>
            Contains data that can be used to initialize a created instance. This data includes the actual
            created <see cref="P:SimpleInjector.Advanced.InstanceInitializationData.Instance"/> and the <see cref="P:SimpleInjector.Advanced.InstanceInitializationData.Context"/> information about the created instance.
            </summary>
        </member>
        <member name="M:SimpleInjector.Advanced.InstanceInitializationData.#ctor(SimpleInjector.Advanced.InitializationContext,System.Object)">
            <summary>Initializes a new instance of the <see cref="T:SimpleInjector.Advanced.InstanceInitializationData"/> struct.</summary>
            <param name="context">The <see cref="T:SimpleInjector.Advanced.InitializationContext"/> that contains contextual information
            about the created instance.</param>
            <param name="instance">The created instance.</param>
        </member>
        <member name="P:SimpleInjector.Advanced.InstanceInitializationData.Context">
            <summary>Gets the <see cref="T:SimpleInjector.Advanced.InitializationContext"/> with contextual information about the 
            created instance.</summary>
            <value>The <see cref="T:SimpleInjector.Advanced.InitializationContext"/>.</value>
        </member>
        <member name="P:SimpleInjector.Advanced.InstanceInitializationData.Instance">
            <summary>Gets the created instance.</summary>
            <value>The created instance.</value>
        </member>
        <member name="M:SimpleInjector.Advanced.InstanceInitializationData.GetHashCode">
            <summary>Returns the hash code for this instance.</summary>
            <returns>A 32-bit signed integer that is the hash code for this instance.</returns>
        </member>
        <member name="M:SimpleInjector.Advanced.InstanceInitializationData.Equals(System.Object)">
            <summary>Indicates whether this instance and a specified object are equal.</summary>
            <param name="obj">Another object to compare to.</param>
            <returns>True if the current object is equal to the other parameter; otherwise, false.</returns>
        </member>
        <member name="M:SimpleInjector.Advanced.InstanceInitializationData.Equals(SimpleInjector.Advanced.InstanceInitializationData)">
            <summary>
            Indicates whether the current object is equal to another object of the same type.
            </summary>
            <param name="other">An object to compare with this object.</param>
            <returns>True if the current object is equal to the other parameter; otherwise, false.</returns>
        </member>
        <member name="M:SimpleInjector.Advanced.InstanceInitializationData.op_Equality(SimpleInjector.Advanced.InstanceInitializationData,SimpleInjector.Advanced.InstanceInitializationData)">
            <summary>
            Indicates whether the values of two specified <see cref="T:SimpleInjector.Advanced.InstanceInitializationData"/> objects are equal.
            </summary>
            <param name="first">The first object to compare.</param>
            <param name="second">The second object to compare.</param>
            <returns>True if a and b are equal; otherwise, false.</returns>
        </member>
        <member name="M:SimpleInjector.Advanced.InstanceInitializationData.op_Inequality(SimpleInjector.Advanced.InstanceInitializationData,SimpleInjector.Advanced.InstanceInitializationData)">
            <summary>
            Indicates whether the values of two specified  <see cref="T:SimpleInjector.Advanced.InstanceInitializationData"/>  objects are 
            not equal.
            </summary>
            <param name="first">The first object to compare.</param>
            <param name="second">The second object to compare.</param>
            <returns>True if a and b are not equal; otherwise, false.</returns>
        </member>
        <member name="T:SimpleInjector.Advanced.Internal.LazyScope">
            <summary>
            This is an internal type. Only depend on this type when you want to be absolutely sure a future 
            version of the framework will break your code.
            </summary>
        </member>
        <member name="M:SimpleInjector.Advanced.Internal.LazyScope.#ctor(System.Func{SimpleInjector.Scope},SimpleInjector.Container)">
            <summary>Initializes a new instance of the <see cref="T:SimpleInjector.Advanced.Internal.LazyScope"/> struct.</summary>
            <param name="scopeFactory">The scope factory.</param>
            <param name="container">The container.</param>
        </member>
        <member name="P:SimpleInjector.Advanced.Internal.LazyScope.Value">
            <summary>Gets the lazily initialized Scope of the current LazyScope instance.</summary>
            <value>The current Scope or null.</value>
        </member>
        <member name="T:SimpleInjector.Advanced.Internal.LazyScopedRegistration`2">
            <summary>
            This is an internal type. Only depend on this type when you want to be absolutely sure a future 
            version of the framework will break your code.
            </summary>
            <typeparam name="TService">Service type.</typeparam>
            <typeparam name="TImplementation">Implementation type.</typeparam>
        </member>
        <member name="M:SimpleInjector.Advanced.Internal.LazyScopedRegistration`2.#ctor(SimpleInjector.Registration)">
            <summary>
            Initializes a new instance of the <see cref="T:SimpleInjector.Advanced.Internal.LazyScopedRegistration`2"/> 
            struct.</summary>
            <param name="registration">The registration.</param>
        </member>
        <member name="M:SimpleInjector.Advanced.Internal.LazyScopedRegistration`2.GetInstance(SimpleInjector.Scope)">
            <summary>Gets the lazily initialized instance for the of the current LazyScopedRegistration.</summary>
            <param name="scope">The scope that is used to retrieve the instance.</param>
            <returns>The cached instance.</returns>
        </member>
        <member name="T:SimpleInjector.Advanced.IPropertySelectionBehavior">
            <summary>
            Defines the container's behavior for selecting properties to inject during the creation of a type.
            Set the <see cref="P:SimpleInjector.ContainerOptions.PropertySelectionBehavior">PropertySelectionBehavior</see> 
            property of the container's <see cref="P:SimpleInjector.Container.Options"/> property to change the default behavior 
            of the container. By default, no properties will be injected by the container.
            </summary>
        </member>
        <member name="M:SimpleInjector.Advanced.IPropertySelectionBehavior.SelectProperty(System.Type,System.Reflection.PropertyInfo)">
            <summary>
            Determines whether a property should be injected by the container upon creation of its type.
            </summary>
            <param name="serviceType">Type of the abstraction that is requested.</param>
            <param name="propertyInfo">The property to check.</param>
            <returns>True when the property should be injected.</returns>
        </member>
        <member name="T:SimpleInjector.Advanced.KnownRelationship">
            <summary>
            A known relationship defines a relationship between two types. The Diagnostics Debug View uses this
            information to spot possible misconfigurations. 
            </summary>
        </member>
        <member name="M:SimpleInjector.Advanced.KnownRelationship.#ctor(System.Type,SimpleInjector.Lifestyle,SimpleInjector.InstanceProducer)">
            <summary>Initializes a new instance of the <see cref="T:SimpleInjector.Advanced.KnownRelationship"/> class.</summary>
            <param name="implementationType">The implementation type of the parent type.</param>
            <param name="lifestyle">The lifestyle of the parent type.</param>
            <param name="dependency">The type that the parent depends on (it is injected into the parent).</param>
        </member>
        <member name="P:SimpleInjector.Advanced.KnownRelationship.ImplementationType">
            <summary>Gets the implementation type of the parent type of the relationship.</summary>
            <value>The implementation type of the parent type of the relationship.</value>
        </member>
        <member name="P:SimpleInjector.Advanced.KnownRelationship.Lifestyle">
            <summary>Gets the lifestyle of the parent type of the relationship.</summary>
            <value>The lifestyle of the parent type of the relationship.</value>
        </member>
        <member name="P:SimpleInjector.Advanced.KnownRelationship.Dependency">
            <summary>Gets the type that the parent depends on (it is injected into the parent).</summary>
            <value>The type that the parent depends on.</value>
        </member>
        <member name="M:SimpleInjector.Advanced.KnownRelationship.GetHashCode">
            <summary>Serves as a hash function for a particular type.</summary>
            <returns>A hash code for the current <see cref="T:SimpleInjector.Advanced.KnownRelationship"/>.</returns>
        </member>
        <member name="M:SimpleInjector.Advanced.KnownRelationship.Equals(SimpleInjector.Advanced.KnownRelationship)">
            <summary>
            Determines whether the specified <see cref="T:SimpleInjector.Advanced.KnownRelationship"/> is equal to the current 
            <see cref="T:SimpleInjector.Advanced.KnownRelationship"/>.
            </summary>
            <param name="other">The object to compare with the current object.</param>
            <returns>True if the specified <see cref="T:SimpleInjector.Advanced.KnownRelationship"/> is equal to the current 
            <see cref="T:SimpleInjector.Advanced.KnownRelationship"/>; otherwise, false.</returns>
        </member>
        <member name="T:SimpleInjector.Container">
            <summary>
            The container. Create an instance of this type for registration of dependencies.
            </summary>
            <remarks>
            <para>
            <b>Thread-safety:</b>
            Resolving instances can be done safely from multiple threads concurrently, but registration needs to
            be done from one single thread.
            </para>
            <para> 
            It is therefore safe to call <see cref="M:SimpleInjector.Container.GetInstance(System.Type)"/>, <see cref="M:SimpleInjector.Container.GetAllInstances(System.Type)"/>, 
            <see cref="M:System.IServiceProvider.GetService(System.Type)">GetService</see>, <see cref="M:SimpleInjector.Container.GetRegistration(System.Type)"/> and
            <see cref="M:SimpleInjector.Container.GetCurrentRegistrations"/> and anything related to resolving instances from multiple thread 
            concurrently. It is however <b>unsafe</b> to call
            <see cref="M:SimpleInjector.Container.Register``2(SimpleInjector.Lifestyle)">RegisterXXX</see>,
            <see cref="E:SimpleInjector.Container.ExpressionBuilding"/>, <see cref="E:SimpleInjector.Container.ExpressionBuilt"/>, <see cref="E:SimpleInjector.Container.ResolveUnregisteredType"/>,
            <see cref="M:SimpleInjector.Container.AddRegistration(System.Type,SimpleInjector.Registration)"/> or anything related to registering from multiple threads concurrently.
            </para>
            </remarks>
        </member>
        <member name="M:SimpleInjector.Container.#ctor">
            <summary>Initializes a new instance of the <see cref="T:SimpleInjector.Container"/> class.</summary>
        </member>
        <member name="P:SimpleInjector.Container.Options">
            <summary>Gets the container options.</summary>
            <value>The <see cref="T:SimpleInjector.ContainerOptions"/> instance for this container.</value>
        </member>
        <member name="P:SimpleInjector.Container.IsVerifying">
            <summary>
            Gets a value indicating whether the container is currently being verified on the current thread.
            </summary>
            <value>True in case the container is currently being verified on the current thread; otherwise
            false.</value>
        </member>
        <member name="P:SimpleInjector.Container.SelectionBasedLifestyle">
            <summary>
            Gets the intermediate lifestyle that forwards CreateRegistration calls to the lifestyle that is 
            returned from the registered container.Options.LifestyleSelectionBehavior.
            </summary>
        </member>
        <member name="M:SimpleInjector.Container.GetCurrentRegistrations">
            <summary>
            Returns an array with the current registrations. This list contains all explicitly registered
            types, and all implicitly registered instances. Implicit registrations are  all concrete 
            unregistered types that have been requested, all types that have been resolved using
            unregistered type resolution (using the <see cref="E:SimpleInjector.Container.ResolveUnregisteredType"/> event), and
            requested unregistered collections. Note that the result of this method may change over time, 
            because of these implicit registrations.
            </summary>
            <remarks>
            <para>
            This method has a performance characteristic of O(n). Prevent from calling this in a performance
            critical path of the application.
            </para>
            <para>
            <b>Note:</b> This method is <i>not</i> guaranteed to always return the same 
            <see cref="T:SimpleInjector.InstanceProducer"/> instance for a given registration. It will however either 
            always return a producer that is able to return the expected instance. Because of this, do not 
            compare sets of instances returned by different calls to <see cref="M:SimpleInjector.Container.GetCurrentRegistrations"/> 
            by reference. The way of comparing lists is by the actual type. The type of each instance is 
            guaranteed to be unique in the returned list.
            </para>
            </remarks>
            <returns>An array of <see cref="T:SimpleInjector.InstanceProducer"/> instances.</returns>
        </member>
        <member name="M:SimpleInjector.Container.GetRootRegistrations">
            <summary>
            Returns an array with the current registrations for root objects. Root objects are registrations
            that are in the root of the object graph, meaning that no other registration is depending on it.
            </summary>
            <remarks>
            <para>
            This method has a performance characteristic of O(n). Prevent from calling this in a performance
            critical path of the application.
            </para>
            <para>
            This list contains the root objects of all explicitly registered types, and all implicitly 
            registered instances. Implicit registrations are all concrete unregistered types that have been 
            requested, all types that have been resolved using unregistered type resolution (using the 
            <see cref="E:SimpleInjector.Container.ResolveUnregisteredType"/> event), and requested unregistered collections. Note that 
            the result of this method may change over time, because of these implicit registrations.
            </para>
            <para>
            <b>Note:</b> This method is <i>not</i> guaranteed to always return the same 
            <see cref="T:SimpleInjector.InstanceProducer"/> instance for a given registration. It will however either 
            always return a producer that is able to return the expected instance. Because of this, do not 
            compare sets of instances returned by different calls to <see cref="M:SimpleInjector.Container.GetCurrentRegistrations"/> 
            by reference. The way of comparing lists is by the actual type. The type of each instance is 
            guaranteed to be unique in the returned list.
            </para>
            </remarks>
            <returns>An array of <see cref="T:SimpleInjector.InstanceProducer"/> instances.</returns>
            <exception cref="T:System.InvalidOperationException">Thrown when this method is called before
            <see cref="M:SimpleInjector.Container.Verify"/> has been successfully called.</exception>
        </member>
        <member name="M:SimpleInjector.Container.Equals(System.Object)">
            <summary>Determines whether the specified System.Object is equal to the current System.Object.
            </summary>
            <param name="obj">The System.Object to compare with the current System.Object.</param>
            <returns>
            True if the specified System.Object is equal to the current System.Object; otherwise, false.
            </returns>
        </member>
        <member name="M:SimpleInjector.Container.GetHashCode">
            <summary>Returns the hash code of the current instance.</summary>
            <returns>The hash code of the current instance.</returns>
        </member>
        <member name="M:SimpleInjector.Container.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents the <see cref="T:SimpleInjector.Container"/>.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents the <see cref="T:SimpleInjector.Container"/>.
            </returns>
        </member>
        <member name="M:SimpleInjector.Container.GetType">
            <summary>Gets the <see cref="T:System.Type"/> of the current instance.</summary>
            <returns>The <see cref="T:System.Type"/> instance that represents the exact runtime 
            type of the current instance.</returns>
        </member>
        <member name="M:SimpleInjector.Container.Dispose">
            <summary>Releases all instances that are cached by the <see cref="T:SimpleInjector.Container"/> object.</summary>
        </member>
        <member name="M:SimpleInjector.Container.LockContainer">
            <summary>Prevents any new registrations to be made to the container.</summary>
        </member>
        <member name="M:SimpleInjector.Container.Dispose(System.Boolean)">
            <summary>Releases all instances that are cached by the <see cref="T:SimpleInjector.Container"/> object.</summary>
            <param name="disposing">True for a normal dispose operation; false to finalize the handle.</param>
        </member>
        <member name="M:SimpleInjector.Container.#ctor(SimpleInjector.ContainerOptions)">
            <summary>Initializes a new instance of the <see cref="T:SimpleInjector.Container"/> class.</summary>
            <param name="options">The container options.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when the <paramref name="options"/> is a null
            reference.</exception>
            <exception cref="T:System.ArgumentException">Thrown when supplied <paramref name="options"/> is an instance
            that already is supplied to another <see cref="T:SimpleInjector.Container"/> instance. Every container must get
            its own <see cref="T:SimpleInjector.ContainerOptions"/> instance.</exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterSingle``1">
            <summary>
            Registers a single concrete instance that will be constructed using constructor injection and will
            be returned when this instance is requested by type <typeparamref name="TConcrete"/>. 
            This <typeparamref name="TConcrete"/> must be thread-safe when working in a multi-threaded 
            environment.
            If <typeparamref name="TConcrete"/> implements <see cref="T:System.IDisposable"/>, a created instance will
            get disposed when <see cref="M:SimpleInjector.Container.Dispose">Container.Dispose</see> gets called.
            </summary>
            <typeparam name="TConcrete">The concrete type that will be registered.</typeparam>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered, or when 
            <typeparamref name="TConcrete"/> has already been registered.
            </exception>
            <exception cref="T:System.ArgumentException">Thrown when the <typeparamref name="TConcrete"/> is a type
            that can not be created by the container.</exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterSingle``2">
            <summary>
            Registers that the same a single instance of type <typeparamref name="TImplementation"/> will be 
            returned every time an <typeparamref name="TService"/> type is requested. If 
            <typeparamref name="TService"/> and <typeparamref name="TImplementation"/>  represent the same 
            type, the type is registered by itself. <typeparamref name="TImplementation"/> must be thread-safe 
            when working in a multi-threaded environment.
            If <typeparamref name="TImplementation"/> implements <see cref="T:System.IDisposable"/>, a created instance will
            get disposed when <see cref="M:SimpleInjector.Container.Dispose">Container.Dispose</see> gets called.
            </summary>
            <typeparam name="TService">
            The interface or base type that can be used to retrieve the instances.
            </typeparam>
            <typeparam name="TImplementation">The concrete type that will be registered.</typeparam>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered, or when the 
            <typeparamref name="TService"/> has already been registered.</exception>
            <exception cref="T:System.ArgumentException">Thrown when the given <typeparamref name="TImplementation"/> 
            type is not a type that can be created by the container.
            </exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterSingle``1(System.Func{``0})">
            <summary>
            Registers the specified delegate that allows constructing a single instance of 
            <typeparamref name="TService"/>. This delegate will be called at most once during the lifetime of 
            the application. The returned instance must be thread-safe when working in a multi-threaded 
            environment.
            If the instance returned from <paramref name="instanceCreator"/> implements <see cref="T:System.IDisposable"/>, 
            the created instance will get disposed when <see cref="M:SimpleInjector.Container.Dispose">Container.Dispose</see> 
            gets called.
            </summary>
            <typeparam name="TService">The interface or base type that can be used to retrieve instances.</typeparam>
            <param name="instanceCreator">The delegate that allows building or creating this single
            instance.</param>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered, or when a 
            <paramref name="instanceCreator"/> for <typeparamref name="TService"/> has already been registered.
            </exception>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="instanceCreator"/> is a 
            null reference.</exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterSingle(System.Type,System.Type)">
            <summary>
            Registers that the same instance of type <paramref name="implementation"/> will be returned every 
            time an instance of type <paramref name="serviceType"/> type is requested. If 
            <paramref name="serviceType"/> and <paramref name="implementation"/> represent the same type, the 
            type is registered by itself. <paramref name="implementation"/> must be thread-safe when working 
            in a multi-threaded environment.
            </summary>
            <param name="serviceType">The base type or interface to register.</param>
            <param name="implementation">The actual type that will be returned when requested.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when either <paramref name="serviceType"/> or 
            <paramref name="implementation"/> are null references (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="implementation"/> is
            no sub type from <paramref name="serviceType"/>, or when one of them represents an open generic
            type.</exception>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered, or when an 
            the <paramref name="serviceType"/> has already been registered.
            </exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterSingle(System.Type,System.Func{System.Object})">
            <summary>
            Registers the specified delegate that allows constructing a single <paramref name="serviceType"/> 
            instance. The container will call this delegate at most once during the lifetime of the application.
            </summary>
            <param name="serviceType">The base type or interface to register.</param>
            <param name="instanceCreator">The delegate that will be used for creating that single instance.</param>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="serviceType"/> represents an open
            generic type.</exception>
            <exception cref="T:System.ArgumentNullException">Thrown when either <paramref name="serviceType"/> or 
            <paramref name="instanceCreator"/> are null references (Nothing in
            VB).</exception>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered, or when an 
            the <paramref name="serviceType"/> has already been registered.
            </exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterSingle``1(``0)">
            <summary>This method has been removed.</summary>
            <typeparam name="TService">The interface or base type that can be used to retrieve the instance.</typeparam>
            <param name="instance">The instance to register.</param>
        </member>
        <member name="M:SimpleInjector.Container.RegisterSingle(System.Type,System.Object)">
            <summary>This method has been removed.</summary>
            <param name="serviceType">The base type or interface to register.</param>
            <param name="instance">The instance to register.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when either <paramref name="serviceType"/> or 
            <paramref name="instance"/> are null references (Nothing in VB).</exception>
        </member>
        <member name="M:SimpleInjector.Container.InjectProperties(System.Object)">
            <summary>This method has been removed.</summary>
            <param name="instance">The instance whose properties will be injected.</param>
        </member>
        <member name="M:SimpleInjector.Container.RegisterAll``1(System.Collections.Generic.IEnumerable{``0})">
            <summary>
            This method is obsolete. Use <see cref="M:SimpleInjector.Container.RegisterCollection``1(System.Collections.Generic.IEnumerable{``0})"/> instead.
            </summary>
            <typeparam name="TService">The interface or base type that can be used to retrieve instances.</typeparam>
            <param name="collection">The collection to register.</param>
        </member>
        <member name="M:SimpleInjector.Container.RegisterAll``1(``0[])">
            <summary>
            This method is obsolete. Use <see cref="M:SimpleInjector.Container.RegisterCollection``1(``0[])"/> instead.
            </summary>
            <typeparam name="TService">The interface or base type that can be used to retrieve instances.</typeparam>
            <param name="singletons">The collection to register.</param>
        </member>
        <member name="M:SimpleInjector.Container.RegisterAll``1(System.Type[])">
            <summary>
            This method is obsolete. Use <see cref="M:SimpleInjector.Container.RegisterCollection``1(System.Collections.Generic.IEnumerable{System.Type})"/> instead.
            </summary>
            <typeparam name="TService">The base type or interface for elements in the collection.</typeparam>
            <param name="serviceTypes">The collection of <see cref="T:System.Type"/> objects whose instances
            will be requested from the container.</param>
        </member>
        <member name="M:SimpleInjector.Container.RegisterAll``1(System.Collections.Generic.IEnumerable{System.Type})">
            <summary>
            This method is obsolete. Use <see cref="M:SimpleInjector.Container.RegisterCollection``1(System.Collections.Generic.IEnumerable{System.Type})"/> instead.
            </summary>
            <typeparam name="TService">The base type or interface for elements in the collection.</typeparam>
            <param name="serviceTypes">The collection of <see cref="T:System.Type"/> objects whose instances
            will be requested from the container.</param>
        </member>
        <member name="M:SimpleInjector.Container.RegisterAll(System.Type,System.Collections.Generic.IEnumerable{System.Type})">
            <summary>
            This method is obsolete. Use <see cref="M:SimpleInjector.Container.RegisterCollection(System.Type,System.Collections.Generic.IEnumerable{System.Type})"/> instead.
            </summary>
            <param name="serviceType">The base type or interface for elements in the collection.</param>
            <param name="serviceTypes">The collection of <see cref="T:System.Type"/> objects whose instances
            will be requested from the container.</param>
        </member>
        <member name="M:SimpleInjector.Container.RegisterAll(System.Type,System.Collections.Generic.IEnumerable{SimpleInjector.Registration})">
            <summary>
            This method is obsolete. Use <see cref="M:SimpleInjector.Container.RegisterCollection(System.Type,System.Collections.Generic.IEnumerable{SimpleInjector.Registration})"/> instead.
            </summary>
            <param name="serviceType">The base type or interface for elements in the collection.</param>
            <param name="registrations">The collection of <see cref="T:SimpleInjector.Registration"/> objects whose instances
            will be requested from the container.</param>
        </member>
        <member name="M:SimpleInjector.Container.RegisterAll(System.Type,SimpleInjector.Registration[])">
            <summary>
            This method is obsolete. Use <see cref="M:SimpleInjector.Container.RegisterCollection(System.Type,System.Collections.Generic.IEnumerable{SimpleInjector.Registration})"/> instead.
            </summary>
            <param name="serviceType">The base type or interface for elements in the collection.</param>
            <param name="registrations">The collection of <see cref="T:SimpleInjector.Registration"/> objects whose instances
            will be requested from the container.</param>
        </member>
        <member name="M:SimpleInjector.Container.RegisterAll(System.Type,System.Collections.IEnumerable)">
            <summary>
            This method is obsolete. Use <see cref="M:SimpleInjector.Container.RegisterCollection(System.Type,System.Collections.IEnumerable)"/> instead.
            </summary>
            <param name="serviceType">The base type or interface for elements in the collection.</param>
            <param name="collection">The collection of items to register.</param>
        </member>
        <member name="M:SimpleInjector.Container.Register(System.Type,System.Collections.Generic.IEnumerable{System.Reflection.Assembly})">
            <summary>
            Registers all concrete, non-generic, public and internal types in the given set of
            <paramref name="assemblies"/> that implement the given <paramref name="openGenericServiceType"/> 
            with container's default lifestyle (which is transient by default).
            <see cref="P:SimpleInjector.TypesToRegisterOptions.IncludeDecorators">Decorators</see> and
            <see cref="P:SimpleInjector.TypesToRegisterOptions.IncludeGenericTypeDefinitions">generic type definitions</see>
            will be excluded from registration, while 
            <see cref="P:SimpleInjector.TypesToRegisterOptions.IncludeComposites">composites</see> are included.
            </summary>
            <param name="openGenericServiceType">The definition of the open generic type.</param>
            <param name="assemblies">A list of assemblies that will be searched.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the arguments contain a null
            reference (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="openGenericServiceType"/> is not
            an open generic type.</exception>
            <exception cref="T:System.InvalidOperationException">Thrown when the given set of 
            <paramref name="assemblies"/> contain multiple types that implement the same 
            closed generic version of the given <paramref name="openGenericServiceType"/>.</exception>
        </member>
        <member name="M:SimpleInjector.Container.Register(System.Type,System.Collections.Generic.IEnumerable{System.Reflection.Assembly},SimpleInjector.Lifestyle)">
            <summary>
            Registers all concrete, non-generic, public and internal types in the given set of
            <paramref name="assemblies"/> that implement the given <paramref name="openGenericServiceType"/> 
            with the supplied <paramref name="lifestyle"/>.
            <see cref="P:SimpleInjector.TypesToRegisterOptions.IncludeDecorators">Decorators</see> and
            <see cref="P:SimpleInjector.TypesToRegisterOptions.IncludeGenericTypeDefinitions">generic type definitions</see>
            will be excluded from registration, while 
            <see cref="P:SimpleInjector.TypesToRegisterOptions.IncludeComposites">composites</see> are included.
            </summary>
            <param name="openGenericServiceType">The definition of the open generic type.</param>
            <param name="assemblies">A list of assemblies that will be searched.</param>
            <param name="lifestyle">The lifestyle to register instances with.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the arguments contain a null
            reference (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="openGenericServiceType"/> is not
            an open generic type.</exception>
            <exception cref="T:System.InvalidOperationException">Thrown when the given set of 
            <paramref name="assemblies"/> contain multiple types that implement the same 
            closed generic version of the given <paramref name="openGenericServiceType"/>.</exception>
        </member>
        <member name="M:SimpleInjector.Container.Register(System.Type,System.Collections.Generic.IEnumerable{System.Type})">
            <summary>
            Registers all supplied <paramref name="implementationTypes"/> based on the closed-generic version
            of the given <paramref name="openGenericServiceType"/> with the transient lifestyle.
            </summary>
            <param name="openGenericServiceType">The definition of the open generic type.</param>
            <param name="implementationTypes">A list types to be registered.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the arguments contain a null
            reference (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="openGenericServiceType"/> is not
            an open generic type or when one of the supplied types from the 
            <paramref name="implementationTypes"/> collection does not derive from 
            <paramref name="openGenericServiceType"/>.</exception>
            <exception cref="T:System.InvalidOperationException">Thrown when the given set of 
            <paramref name="implementationTypes"/> contain multiple types that implement the same 
            closed generic version of the given <paramref name="openGenericServiceType"/>.</exception>
        </member>
        <member name="M:SimpleInjector.Container.Register(System.Type,System.Collections.Generic.IEnumerable{System.Type},SimpleInjector.Lifestyle)">
            <summary>
            Registers all supplied <paramref name="implementationTypes"/> based on the closed-generic version
            of the given <paramref name="openGenericServiceType"/> with the given <paramref name="lifestyle"/>.
            </summary>
            <param name="openGenericServiceType">The definition of the open generic type.</param>
            <param name="implementationTypes">A list types to be registered.</param>
            <param name="lifestyle">The lifestyle to register instances with.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the arguments contain a null
            reference (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="openGenericServiceType"/> is not
            an open generic type or when one of the supplied types from the 
            <paramref name="implementationTypes"/> collection does not derive from 
            <paramref name="openGenericServiceType"/>.</exception>
            <exception cref="T:System.InvalidOperationException">Thrown when the given set of 
            <paramref name="implementationTypes"/> contain multiple types that implement the same 
            closed generic version of the given <paramref name="openGenericServiceType"/>.</exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterCollection``1(System.Collections.Generic.IEnumerable{System.Reflection.Assembly})">
            <summary>
            Registers all concrete, non-generic types (both public and internal) that are defined in the given
            set of <paramref name="assemblies"/> and that implement the given <typeparamref name="TService"/>
            with a default lifestyle and register them as a collection of <typeparamref name="TService"/>.
            Unless overridden using a custom 
            <see cref="P:SimpleInjector.ContainerOptions.LifestyleSelectionBehavior">LifestyleSelectionBehavior</see>, the
            default lifestyle is <see cref="F:SimpleInjector.Lifestyle.Transient">Transient</see>.
            </summary>
            <typeparam name="TService">The element type of the collections to register. This can be either
            a non-generic, closed-generic or open-generic type.</typeparam>
            <param name="assemblies">A list of assemblies that will be searched.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the supplied arguments contain a null
            reference (Nothing in VB).</exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterCollection(System.Type,System.Reflection.Assembly[])">
            <summary>
            Registers all concrete, non-generic types (both public and internal) that are defined in the given
            set of <paramref name="assemblies"/> and that implement the given <paramref name="serviceType"/> 
            with a default lifestyle and register them as a collection of <paramref name="serviceType"/>.
            Unless overridden using a custom 
            <see cref="P:SimpleInjector.ContainerOptions.LifestyleSelectionBehavior">LifestyleSelectionBehavior</see>, the
            default lifestyle is <see cref="F:SimpleInjector.Lifestyle.Transient">Transient</see>. 
            <see cref="P:SimpleInjector.TypesToRegisterOptions.IncludeComposites">Composites</see>,
            <see cref="P:SimpleInjector.TypesToRegisterOptions.IncludeDecorators">decorators</see> and
            <see cref="P:SimpleInjector.TypesToRegisterOptions.IncludeGenericTypeDefinitions">generic type definitions</see>
            will be excluded from registration.
            </summary>
            <param name="serviceType">The element type of the collections to register. This can be either
            a non-generic, closed-generic or open-generic type.</param>
            <param name="assemblies">A list of assemblies that will be searched.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the supplied arguments contain a null
            reference (Nothing in VB).</exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterCollection(System.Type,System.Collections.Generic.IEnumerable{System.Reflection.Assembly})">
            <summary>
            Registers all concrete, non-generic types (both public and internal) that are defined in the given
            set of <paramref name="assemblies"/> and that implement the given <paramref name="serviceType"/> 
            with a default lifestyle and register them as a collection of <paramref name="serviceType"/>.
            Unless overridden using a custom 
            <see cref="P:SimpleInjector.ContainerOptions.LifestyleSelectionBehavior">LifestyleSelectionBehavior</see>, the
            default lifestyle is <see cref="F:SimpleInjector.Lifestyle.Transient">Transient</see>.
            <see cref="P:SimpleInjector.TypesToRegisterOptions.IncludeComposites">Composites</see>,
            <see cref="P:SimpleInjector.TypesToRegisterOptions.IncludeDecorators">decorators</see> and
            <see cref="P:SimpleInjector.TypesToRegisterOptions.IncludeGenericTypeDefinitions">generic type definitions</see>
            will be excluded from registration.
            </summary>
            <param name="serviceType">The element type of the collections to register. This can be either
            a non-generic, closed-generic or open-generic type.</param>
            <param name="assemblies">A list of assemblies that will be searched.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the supplied arguments contain a null
            reference (Nothing in VB).</exception>
        </member>
        <member name="M:SimpleInjector.Container.GetTypesToRegister(System.Type,System.Collections.Generic.IEnumerable{System.Reflection.Assembly})">
            <summary>
            Returns all concrete non-generic types that are located in the supplied <paramref name="assemblies"/> 
            and implement or inherit from the supplied <paramref name="serviceType"/>. 
            <paramref name="serviceType"/> can be an open-generic type.
            </summary>
            <remarks>
            Use this method when you need influence the types that are registered using 
            <see cref="M:SimpleInjector.Container.Register(System.Type,System.Collections.Generic.IEnumerable{System.Reflection.Assembly})">Register</see>. 
            The <b>Register</b> overloads that take a collection of <see cref="T:System.Reflection.Assembly"/> 
            objects use this method internally to get the list of types that need to be registered. Instead of
            calling  such overload, you can call an overload that takes a list of <see cref="T:System.Type"/> objects 
            and pass  in a filtered result from this <b>GetTypesToRegister</b> method.
            <code lang="cs"><![CDATA[
            var container = new Container();
            
            var assemblies = new[] { typeof(ICommandHandler<>).Assembly };
            var types = container.GetTypesToRegister(typeof(ICommandHandler<>), assemblies)
                .Where(type => !type.IsPublic);
            
            container.Register(typeof(ICommandHandler<>), types);
            ]]></code>
            This example calls the <b>GetTypesToRegister</b> method to request a list of concrete implementations
            of the <b>ICommandHandler&lt;T&gt;</b> interface from the assembly of that interface. After that
            all internal types are filtered out. This list is supplied to the
            <see cref="M:SimpleInjector.Container.Register(System.Type,System.Collections.Generic.IEnumerable{System.Type})">Register(Type, IEnumerable&lt;Type&gt;)</see>
            overload to finish the registration.
            </remarks>
            <param name="serviceType">The base type or interface to find derived types for. This can be both
            a non-generic and open-generic type.</param>
            <param name="assemblies">A list of assemblies that will be searched.</param>
            <returns>A collection of types.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the arguments contain a null reference 
            (Nothing in VB).</exception>
        </member>
        <member name="M:SimpleInjector.Container.GetTypesToRegister(System.Type,System.Collections.Generic.IEnumerable{System.Reflection.Assembly},SimpleInjector.TypesToRegisterOptions)">
            <summary>
            Returns all concrete types that are located in the supplied <paramref name="assemblies"/> 
            and implement or inherit from the supplied <paramref name="serviceType"/> and match the specified
            <paramref name="options.."/>. <paramref name="serviceType"/> can be an open-generic type.
            </summary>
            <remarks>
            Use this method when you need influence the types that are registered using 
            <see cref="M:SimpleInjector.Container.Register(System.Type,System.Collections.Generic.IEnumerable{System.Reflection.Assembly})">Register</see>. 
            The <b>Register</b> overloads that take a collection of <see cref="T:System.Reflection.Assembly"/> 
            objects use this method internally to get the list of types that need to be registered. Instead of
            calling  such overload, you can call an overload that takes a list of <see cref="T:System.Type"/> objects 
            and pass  in a filtered result from this <b>GetTypesToRegister</b> method.
            <code lang="cs"><![CDATA[
            var container = new Container();
            
            var assemblies = new[] { typeof(ICommandHandler<>).Assembly };
            var options = new TypesToRegisterOptions { IncludeGenericTypeDefinitions: true };
            var types = container.GetTypesToRegister(typeof(ICommandHandler<>), assemblies, options)
                .Where(type => !type.IsPublic);
            
            container.Register(typeof(ICommandHandler<>), types);
            ]]></code>
            This example calls the <b>GetTypesToRegister</b> method to request a list of concrete implementations
            of the <b>ICommandHandler&lt;T&gt;</b> interface from the assembly of that interface. After that
            all internal types are filtered out. This list is supplied to the
            <see cref="M:SimpleInjector.Container.Register(System.Type,System.Collections.Generic.IEnumerable{System.Type})">Register(Type, IEnumerable&lt;Type&gt;)</see>
            overload to finish the registration.
            </remarks>
            <param name="serviceType">The base type or interface to find derived types for. This can be both
            a non-generic and open-generic type.</param>
            <param name="assemblies">A list of assemblies that will be searched.</param>
            <param name="options">The options.</param>
            <returns>A collection of types.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the arguments contain a null reference 
            (Nothing in VB).</exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterCollection``1(System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Registers a dynamic (container uncontrolled) collection of elements of type 
            <typeparamref name="TService"/>. A call to <see cref="M:SimpleInjector.Container.GetAllInstances``1"/> will return the 
            <paramref name="containerUncontrolledCollection"/> itself, and updates to the collection will be 
            reflected in the result. If updates are allowed, make sure the collection can be iterated safely 
            if you're running a multi-threaded application.
            </summary>
            <typeparam name="TService">The interface or base type that can be used to retrieve instances.</typeparam>
            <param name="containerUncontrolledCollection">The container-uncontrolled collection to register.</param>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered, or when a <paramref name="containerUncontrolledCollection"/>
            for <typeparamref name="TService"/> has already been registered.
            </exception>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="containerUncontrolledCollection"/> is a null
            reference.</exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterCollection``1(``0[])">
            <summary>
            Registers a collection of singleton elements of type <typeparamref name="TService"/>.
            </summary>
            <typeparam name="TService">The interface or base type that can be used to retrieve instances.</typeparam>
            <param name="singletons">The collection to register.</param>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered, or when a <paramref name="singletons"/>
            for <typeparamref name="TService"/> has already been registered.
            </exception>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="singletons"/> is a null
            reference.</exception>
            <exception cref="T:System.ArgumentException">Thrown when one of the elements of <paramref name="singletons"/>
            is a null reference.</exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterCollection``1(System.Collections.Generic.IEnumerable{System.Type})">
            <summary>
            Registers a collection of <paramref name="serviceTypes"/>, whose instances will be resolved lazily
            each time the resolved collection of <typeparamref name="TService"/> is enumerated. 
            The underlying collection is a stream that will return individual instances based on their 
            specific registered lifestyle, for each call to <see cref="P:System.Collections.Generic.IEnumerator`1.Current"/>. 
            The order in which the types appear in the collection is the exact same order that the items were 
            registered, i.e the resolved collection is deterministic.   
            </summary>
            <typeparam name="TService">The base type or interface for elements in the collection.</typeparam>
            <param name="serviceTypes">The collection of <see cref="T:System.Type"/> objects whose instances
            will be requested from the container.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="serviceTypes"/> is a null 
            reference (Nothing in VB).
            </exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="serviceTypes"/> contains a null
            (Nothing in VB) element, a generic type definition, or the <typeparamref name="TService"/> is
            not assignable from one of the given <paramref name="serviceTypes"/> elements.
            </exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterCollection``1(System.Collections.Generic.IEnumerable{SimpleInjector.Registration})">
            <summary>
            Registers a collection of <paramref name="registrations"/>, whose instances will be resolved lazily
            each time the resolved collection of <typeparamref name="TService"/> is enumerated. 
            The underlying collection is a stream that will return individual instances based on their 
            specific registered lifestyle, for each call to <see cref="P:System.Collections.Generic.IEnumerator`1.Current"/>. 
            The order in which the types appear in the collection is the exact same order that the items were 
            registered, i.e the resolved collection is deterministic.   
            </summary>
            <typeparam name="TService">The base type or interface for elements in the collection.</typeparam>
            <param name="registrations">The collection of <see cref="T:SimpleInjector.Registration"/> objects whose instances
            will be requested from the container.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the supplied arguments is a null 
            reference (Nothing in VB).
            </exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="registrations"/> contains a null
            (Nothing in VB) element or when <typeparamref name="TService"/> is not assignable from any of the
            service types supplied by the given <paramref name="registrations"/> instances.
            </exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterCollection(System.Type,System.Collections.Generic.IEnumerable{System.Type})">
            <summary>
            Registers a collection of <paramref name="serviceTypes"/>, whose instances will be resolved lazily
            each time the resolved collection of <paramref name="serviceType"/> is enumerated. 
            The underlying collection is a stream that will return individual instances based on their 
            specific registered lifestyle, for each call to <see cref="P:System.Collections.Generic.IEnumerator`1.Current"/>. 
            The order in which the types appear in the collection is the exact same order that the items were 
            registered, i.e the resolved collection is deterministic.   
            </summary>
            <param name="serviceType">The base type or interface for elements in the collection.</param>
            <param name="serviceTypes">The collection of <see cref="T:System.Type"/> objects whose instances
            will be requested from the container.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the supplied arguments is a null 
            reference (Nothing in VB).
            </exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="serviceTypes"/> contains a null
            (Nothing in VB) element, a generic type definition, or the <paramref name="serviceType"/> is
            not assignable from one of the given <paramref name="serviceTypes"/> elements.
            </exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterCollection(System.Type,System.Collections.Generic.IEnumerable{SimpleInjector.Registration})">
            <summary>
            Registers a collection of <paramref name="registrations"/>, whose instances will be resolved lazily
            each time the resolved collection of <paramref name="serviceType"/> is enumerated. 
            The underlying collection is a stream that will return individual instances based on their 
            specific registered lifestyle, for each call to <see cref="P:System.Collections.Generic.IEnumerator`1.Current"/>. 
            The order in which the types appear in the collection is the exact same order that the items were 
            registered, i.e the resolved collection is deterministic.   
            </summary>
            <param name="serviceType">The base type or interface for elements in the collection. This can be
            an a non-generic type, closed generic type or generic type definition.</param>
            <param name="registrations">The collection of <see cref="T:SimpleInjector.Registration"/> objects whose instances
            will be requested from the container.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the supplied arguments is a null 
            reference (Nothing in VB).
            </exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="registrations"/> contains a null
            (Nothing in VB) element or when <paramref name="serviceType"/> is not assignable from any of the
            service types supplied by the given <paramref name="registrations"/> instances.
            </exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterCollection(System.Type,System.Collections.IEnumerable)">
            <summary>
            Registers a dynamic (container uncontrolled) collection of elements of type 
            <paramref name="serviceType"/>. A call to <see cref="M:SimpleInjector.Container.GetAllInstances``1"/> will return the 
            <paramref name="containerUncontrolledCollection"/> itself, and updates to the collection will be 
            reflected in the result. If updates are allowed, make sure the collection can be iterated safely 
            if you're running a multi-threaded application.
            </summary>
            <param name="serviceType">The base type or interface for elements in the collection.</param>
            <param name="containerUncontrolledCollection">The collection of items to register.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the supplied arguments is a null 
            reference (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="serviceType"/> represents an
            open generic type.</exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterConditional``2(System.Predicate{SimpleInjector.PredicateContext})">
            <summary>
            Conditionally registers that a new instance of <typeparamref name="TImplementation"/> will be 
            returned every time a <typeparamref name="TService"/> is requested (transient) and where the
            supplied <paramref name="predicate"/> returns true. The predicate will only be evaluated a finite
            number of times; the predicate is unsuited for making decisions based on runtime conditions.
            </summary>
            <remarks>
            This method uses the container's 
            <see cref="P:SimpleInjector.ContainerOptions.LifestyleSelectionBehavior">LifestyleSelectionBehavior</see> to select
            the exact lifestyle for the specified type. By default this will be 
            <see cref="F:SimpleInjector.Lifestyle.Transient">Transient</see>.
            </remarks>
            <typeparam name="TService">The interface or base type that can be used to retrieve the instances.</typeparam>
            <typeparam name="TImplementation">The concrete type that will be registered.</typeparam>
            <param name="predicate">The predicate that determines whether the <typeparamref name="TImplementation"/> 
            can be applied for the requested service type. This predicate
            can be used to build a fallback mechanism where multiple registrations for the same service type
            are made.</param>
            <exception cref="T:System.ArgumentNullException">
            Thrown when one of the arguments is a null reference (Nothing in VB).
            </exception>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered.
            </exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterConditional``2(SimpleInjector.Lifestyle,System.Predicate{SimpleInjector.PredicateContext})">
            <summary>
            Conditionally registers that an instance of <typeparamref name="TImplementation"/> will be 
            returned every time a <typeparamref name="TService"/> is requested and where the supplied 
            <paramref name="predicate"/> returns true. The instance is cached according to the supplied 
            <paramref name="lifestyle"/>. The predicate will only be evaluated a finite number of times; the 
            predicate is unsuited for making decisions based on runtime conditions.
            </summary>
            <typeparam name="TService">The interface or base type that can be used to retrieve the instances.</typeparam>
            <typeparam name="TImplementation">The concrete type that will be registered.</typeparam>
            <param name="lifestyle">The lifestyle that specifies how the returned instance will be cached.</param>
            <param name="predicate">The predicate that determines whether the 
            <typeparamref name="TImplementation"/> can be applied for the requested service type. This predicate
            can be used to build a fallback mechanism where multiple registrations for the same service type
            are made.</param>
            <exception cref="T:System.ArgumentNullException">
            Thrown when one of the arguments is a null reference (Nothing in VB).
            </exception>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered.
            </exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterConditional(System.Type,System.Type,System.Predicate{SimpleInjector.PredicateContext})">
            <summary>
            Conditionally registers that a new instance of <paramref name="implementationType"/> will be 
            returned every time a <paramref name="serviceType"/> is requested (transient) and where the
            supplied <paramref name="predicate"/> returns true. The predicate will only be evaluated a finite
            number of times; the predicate is unsuited for making decisions based on runtime conditions.
            </summary>
            <remarks>
            This method uses the container's 
            <see cref="P:SimpleInjector.ContainerOptions.LifestyleSelectionBehavior">LifestyleSelectionBehavior</see> to select
            the exact lifestyle for the specified type. By default this will be 
            <see cref="F:SimpleInjector.Lifestyle.Transient">Transient</see>.
            </remarks>
            <param name="serviceType">The base type or interface to register. This can be an open-generic type.</param>
            <param name="implementationType">The actual type that will be returned when requested.</param>
            <param name="predicate">The predicate that determines whether the 
            <paramref name="implementationType"/> can be applied for the requested service type. This predicate
            can be used to build a fallback mechanism where multiple registrations for the same service type
            are made.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the arguments is a null reference
            (Nothing in VB).</exception>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered.
            </exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterConditional(System.Type,System.Type,SimpleInjector.Lifestyle,System.Predicate{SimpleInjector.PredicateContext})">
            <summary>
            Conditionally registers that an instance of <paramref name="implementationType"/> will be 
            returned every time a <paramref name="serviceType"/> is requested and where the supplied 
            <paramref name="predicate"/> returns true. The instance is cached according to the supplied 
            <paramref name="lifestyle"/>. The predicate will only be evaluated a finite number of times; the 
            predicate is unsuited for making decisions based on runtime conditions.
            </summary>
            <param name="serviceType">The base type or interface to register. This can be an open-generic type.</param>
            <param name="implementationType">The actual type that will be returned when requested.</param>
            <param name="lifestyle">The lifestyle that defines how returned instances are cached.</param>
            <param name="predicate">The predicate that determines whether the 
            <paramref name="implementationType"/> can be applied for the requested service type. This predicate
            can be used to build a fallback mechanism where multiple registrations for the same service type
            are made.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the arguments is a null reference
            (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="serviceType"/> and 
            <paramref name="implementationType"/> are not a generic type or when <paramref name="serviceType"/>
            is a partially-closed generic type.
            </exception>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered.
            </exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterConditional(System.Type,System.Func{SimpleInjector.TypeFactoryContext,System.Type},SimpleInjector.Lifestyle,System.Predicate{SimpleInjector.PredicateContext})">
            <summary>
            Conditionally registers that an instance of the type returned from 
            <paramref name="implementationTypeFactory"/> will be returned every time a 
            <paramref name="serviceType"/> is requested and where the supplied <paramref name="predicate"/> 
            returns true. The instance is cached according to the supplied 
            <paramref name="lifestyle"/>. Both the <paramref name="predicate"/> and 
            <paramref name="implementationTypeFactory"/> will only be evaluated a finite number of times; 
            they unsuited for making decisions based on runtime conditions.
            </summary>
            <param name="serviceType">The base type or interface to register. This can be an open-generic type.</param>
            <param name="implementationTypeFactory">A factory that allows building Type objects that define the
            implementation type to inject, based on the given contextual information. The delegate is allowed 
            to return (partially) open-generic types.</param>
            <param name="lifestyle">The lifestyle that defines how returned instances are cached.</param>
            <param name="predicate">The predicate that determines whether the registration can be applied for
            the requested service type. This predicate can be used to build a fallback mechanism where 
            multiple registrations for the same service type are made.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the arguments is a null reference
            (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="serviceType"/> is a 
            partially-closed generic type.
            </exception>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered.
            </exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterConditional(System.Type,SimpleInjector.Registration,System.Predicate{SimpleInjector.PredicateContext})">
            <summary>
            Conditionally registers that <paramref name="registration"/> will be used every time a 
            <paramref name="serviceType"/> is requested and where the supplied <paramref name="predicate"/> 
            returns true. The predicate will only be evaluated a finite number of times; the predicate is 
            unsuited for making decisions based on runtime conditions.
            </summary>
            <param name="serviceType">The base type or interface to register. This can be an open-generic type.</param>
            <param name="registration">The <see cref="T:SimpleInjector.Registration"/> instance to register.</param>
            <param name="predicate">The predicate that determines whether the 
            <paramref name="registration"/> can be applied for the requested service type. This predicate
            can be used to build a fallback mechanism where multiple registrations for the same service type
            are made.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the arguments is a null reference
            (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="serviceType"/> is open generic or
            <paramref name="registration" /> is not assignable to <paramref name="serviceType"/>.</exception>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered.
            </exception>
        </member>
        <member name="E:SimpleInjector.Container.ResolveUnregisteredType">
             <summary>
             Occurs when an instance of a type is requested that has not been registered explicitly, allowing 
             resolution of unregistered types before the container tries to create the type.
             </summary>
             <remarks>
             <para>
             The <see cref="E:SimpleInjector.Container.ResolveUnregisteredType"/> event is called by the container every time an 
             unregistered type is requested for the first time, allowing a developer to do unregistered type 
             resolution. By calling the 
             <see cref="M:SimpleInjector.UnregisteredTypeEventArgs.Register(SimpleInjector.Registration)">Register</see> method on the
             <see cref="T:SimpleInjector.UnregisteredTypeEventArgs"/>, a <see cref="T:SimpleInjector.Registration"/>, <see cref="T:System.Linq.Expressions.Expression"/> or
             <see cref="T:System.Func`1"/> delegate can be registered allowing the container to retrieve 
             instances of the requested type. This registration is cached and it prevents the 
             <b>ResolveUnregisteredType</b> event from being called again for the same type.
             </para>
             <para>
             When no registered event handled the registration of an unregistered type, the container will try
             to create the type when this type is either concrete or is the <see cref="T:System.Collections.Generic.IEnumerable`1"/>
             interface. Concrete types will be registered with the <see cref="F:SimpleInjector.Lifestyle.Transient">Transient</see>
             lifestyle and <see cref="T:System.Collections.Generic.IEnumerable`1"/> registrations will return an empty collection. When no 
             even handled the registration and the container could not create it, an exception is thrown.
             </para>
             <para>
             <b>Thread-safety:</b> Please note that the container will not ensure that the hooked delegates
             are executed only once. While the calls to <see cref="E:SimpleInjector.Container.ResolveUnregisteredType" /> for a given type
             are finite (and will in most cases happen just once), a container can call the delegate multiple 
             times and make parallel calls to the delegate. You must make sure that the code can be called 
             multiple times and is thread-safe.
             </para>
             </remarks>
             <example>
             The following example shows the usage of the <see cref="E:SimpleInjector.Container.ResolveUnregisteredType" /> event:
             <code lang="cs"><![CDATA[
             public interface IValidator<T>
             {
                 void Validate(T instance);
             }
            
             // Implementation of the null object pattern.
             public class EmptyValidator<T> : IValidator<T>
             {
                 public void Validate(T instance)
                 {
                     // Does nothing.
                 }
             }
             
             [TestMethod]
             public void TestResolveUnregisteredType()
             {
                 // Arrange
                 var container = new Container();
             
                 // Register an EmptyValidator<T> to be returned when a IValidator<T> is requested:
                 container.ResolveUnregisteredType += (sender, e) =>
                 {
                     if (e.UnregisteredServiceType.IsGenericType &&
                         e.UnregisteredServiceType.GetGenericTypeDefinition() == typeof(IValidator<>))
                     {
                         var validatorType = typeof(EmptyValidator<>).MakeGenericType(
                             e.UnregisteredServiceType.GetGenericArguments());
                 
                         object emptyValidator = container.GetInstance(validatorType);
                 
                         // Register the instance as singleton.
                         e.Register(() => emptyValidator);
                     }
                 };
                 
                 // Act
                 var orderValidator = container.GetInstance<IValidator<Order>>();
                 var customerValidator = container.GetInstance<IValidator<Customer>>();
             
                 // Assert
                 Assert.IsInstanceOfType(orderValidator, typeof(EmptyValidator<Order>));
                 Assert.IsInstanceOfType(customerValidator, typeof(EmptyValidator<Customer>));
             }
             ]]></code>
             <para>
             The example above registers a delegate that is raised every time an unregistered type is requested
             from the container. The delegate checks whether the requested type is a closed generic
             implementation of the <b>IValidator&lt;T&gt;</b> interface (such as 
             <b>IValidator&lt;Order&gt;</b> or <b>IValidator&lt;Customer&gt;</b>). In that case it
             will request the container for a concrete <b>EmptyValidator&lt;T&gt;</b> implementation that
             implements the given 
             <see cref="P:SimpleInjector.UnregisteredTypeEventArgs.UnregisteredServiceType">UnregisteredServiceType</see>, and
             registers a delegate that will return this created instance. The <b>e.Register</b> call
             registers the method in the container, preventing the <see cref="E:SimpleInjector.Container.ResolveUnregisteredType"/> from
             being called again for the exact same service type, preventing any performance penalties.
             </para>
             <para>
             Please note that given example is just an uhhmm... example. In the case of the example the
             <b>EmptyValidator&lt;T&gt;</b> can be better registered using of the built-in 
             <see cref="M:SimpleInjector.Container.Register(System.Type,System.Type,SimpleInjector.Lifestyle)">Register</see> methods instead. These methods take 
             care of any given generic type constraint and allow the implementation to be integrated into the 
             container's pipeline, which allows it to be intercepted using the <see cref="E:SimpleInjector.Container.ExpressionBuilding"/> 
             event and allow any registered <see cref="M:SimpleInjector.Container.RegisterInitializer``1(System.Action{``0})">initializers</see> to be 
             applied.
             </para>
             </example>
        </member>
        <member name="E:SimpleInjector.Container.ExpressionBuilt">
             <summary>
             Occurs after the creation of the <see cref="T:System.Linq.Expressions.Expression" /> of a registered type is complete (the 
             lifestyle has been applied), allowing the created <see cref="T:System.Linq.Expressions.Expression" /> to be wrapped, 
             changed, or replaced. Multiple delegates may handle the same service type.
             </summary>
             <remarks>
             <para>
             The <b>ExpressionBuilt</b> event is called by the container every time an registered type is 
             getting compiled, allowing a developer to change the way the type is created. The delegate that
             hooks to the <b>ExpressionBuilt</b> event, can change the 
             <see cref="P:SimpleInjector.ExpressionBuiltEventArgs.Expression" /> property on the 
             <see cref="T:SimpleInjector.ExpressionBuiltEventArgs"/>, which allows changing the way the type is constructed.
             </para>
             <para>
             <b>Thread-safety:</b> Please note that the container will not ensure that the hooked delegates
             are executed only once per service type. While the calls to <see cref="E:SimpleInjector.Container.ExpressionBuilt" /> for a 
             given type are finite (and will in most cases happen just once), a container can call the delegate 
             multiple times and make parallel calls to the delegate. You must make sure that the code can be 
             called multiple times and is thread-safe.
             </para>
             </remarks>
             <example>
             The following example shows the usage of the <b>ExpressionBuilt</b> event:
             <code lang="cs"><![CDATA[
             public interface IValidator<T>
             {
                 void Validate(T instance);
             }
            
             public interface ILogger
             {
                 void Write(string message);
             }
            
             // Implementation of the decorator pattern.
             public class MonitoringValidator<T> : IValidator<T>
             {
                 private readonly IValidator<T> validator;
                 private readonly ILogger logger;
            
                 public MonitoringValidator(IValidator<T> validator, ILogger logger)
                 {
                     this.validator = validator;
                     this.logger = logger;
                 }
            
                 public void Validate(T instance)
                 {
                     this.logger.Write("Validating " + typeof(T).Name);
                     this.validator.Validate(instance);
                     this.logger.Write("Validated " + typeof(T).Name);
                 }
             }
            
             [TestMethod]
             public void TestExpressionBuilt()
             {
                 // Arrange
                 var container = new Container();
            
                 container.Register<ILogger, ConsoleLogger>(Lifestyle.Singleton);
                 container.Register<IValidator<Order>, OrderValidator>();
                 container.Register<IValidator<Customer>, CustomerValidator>();
            
                 // Intercept the creation of IValidator<T> instances and wrap them in a MonitoringValidator<T>:
                 container.ExpressionBuilt += (sender, e) =>
                 {
                     if (e.RegisteredServiceType.IsGenericType &&
                         e.RegisteredServiceType.GetGenericTypeDefinition() == typeof(IValidator<>))
                     {
                         var decoratorType = typeof(MonitoringValidator<>)
                             .MakeGenericType(e.RegisteredServiceType.GetGenericArguments());
            
                         // Wrap the IValidator<T> in a MonitoringValidator<T>.
                         e.Expression = Expression.New(decoratorType.GetConstructors()[0], new Expression[]
                         {
                             e.Expression,
                             container.GetRegistration(typeof(ILogger)).BuildExpression(),
                         });
                     }
                 };
            
                 // Act
                 var orderValidator = container.GetInstance<IValidator<Order>>();
                 var customerValidator = container.GetInstance<IValidator<Customer>>();
            
                 // Assert
                 Assert.IsInstanceOfType(orderValidator, typeof(MonitoringValidator<Order>));
                 Assert.IsInstanceOfType(customerValidator, typeof(MonitoringValidator<Customer>));
             }
             ]]></code>
             <para>
             The example above registers a delegate that is raised every time the container compiles the
             expression for an registered type. The delegate checks whether the requested type is a closed generic
             implementation of the <b>IValidator&lt;T&gt;</b> interface (such as 
             <b>IValidator&lt;Order&gt;</b> or <b>IValidator&lt;Customer&gt;</b>). In that case it
             will changes the current <see cref="P:SimpleInjector.ExpressionBuiltEventArgs.Expression"/> with a new one that creates
             a new <b>MonitoringValidator&lt;T&gt;</b> that takes the current validator (and an <b>ILogger</b>)
             as an dependency.
             </para>
             <para>
             Please note that given example is just an uhhmm... example. In the case of the example the
             <b>MonitoringValidator&lt;T&gt;</b> is a decorator and instead of manually writing this code that
             many limitations, you can use one of the built-in 
             <see cref="M:SimpleInjector.Container.RegisterDecorator(System.Type,System.Type,SimpleInjector.Lifestyle)">RegisterDecorator</see> methods instead.
             These extension methods take care of any given generic type constraint, allow to register decorators
             conditionally and allow the decorator to be integrated into the container's pipeline, which allows
             it to be intercepted using the <see cref="E:SimpleInjector.Container.ExpressionBuilding"/> event and allow any registered
             <see cref="M:SimpleInjector.Container.RegisterInitializer``1(System.Action{``0})">initializers</see> to be applied.
             </para>
             </example>
        </member>
        <member name="E:SimpleInjector.Container.ExpressionBuilding">
            <summary>
            Occurs directly after the creation of the <see cref="T:System.Linq.Expressions.Expression" /> of a registered type is made,
            but before any <see cref="M:SimpleInjector.Container.RegisterInitializer(System.Action{SimpleInjector.Advanced.InstanceInitializationData},System.Predicate{SimpleInjector.Advanced.InitializationContext})">initializer</see> and lifestyle specific caching
            has been applied, allowing the created <see cref="T:System.Linq.Expressions.Expression" /> to be altered. Multiple delegates 
            may handle the same service type.
            </summary>
            <remarks>
            <para>
            The <b>ExpressionBuilding</b> event is called by the container every time an registered type is 
            getting compiled, allowing a developer to change the way the type is created. The delegate that
            hooks to the <b>ExpressionBuilding</b> event, can change the 
            <see cref="P:SimpleInjector.ExpressionBuildingEventArgs.Expression" /> property on the 
            <see cref="T:SimpleInjector.ExpressionBuildingEventArgs"/>, which allows changing the way the type is constructed.
            </para>
            <para>
            The exact <see cref="T:System.Linq.Expressions.Expression"/> type supplied depends on the type of registration. 
            Registrations that explicitly supply the implementation type (such as 
            <see cref="M:SimpleInjector.Container.Register``2">Register&lt;TService, TImplementation&gt;()</see>)
            will result in an <see cref="T:System.Linq.Expressions.NewExpression"/>, while registrations that take a delegate (such as
            <see cref="M:SimpleInjector.Container.Register``1(System.Func{``0})">Register&lt;TService&gt;(Func&lt;TService&gt;)</see>)
            will result in an <see cref="T:System.Linq.Expressions.InvocationExpression"/>. Singletons that are passed in using their
            value (<see cref="M:SimpleInjector.Container.RegisterSingleton``1(``0)">RegisterSingleton&lt;TService&gt;(TService)</see>)
            will result in an <see cref="T:System.Linq.Expressions.ConstantExpression"/>. Note that other <b>ExpressionBuilding</b> 
            registrations might have changed the <see cref="P:SimpleInjector.ExpressionBuildingEventArgs.Expression" /> 
            property and might have supplied an <see cref="T:System.Linq.Expressions.Expression"/> of a different type. The order in
            which these events are registered might be of importance to you.
            </para>
            <para>
            <b>Thread-safety:</b> Please note that the container will not ensure that the hooked delegates
            are executed only once per service type. While the calls to registered <b>ExpressionBuilding</b>
            events for a  given type are finite (and will in most cases happen just once), a container can 
            call the delegate multiple times and make parallel calls to the delegate. You must make sure that 
            the code can be called multiple times and is thread-safe.
            </para>
            </remarks>
            <example>
            The following example shows the usage of the <b>ExpressionBuilding</b> event:
            <code lang="cs"><![CDATA[
            public class MyInjectPropertyAttribute : Attribute { }
            
            public static void Bootstrap()
            {
                var container = new Container();
                
                container.ExpressionBuilding += (sender, e) =>
                {
                    var expression = e.Expression as NewExpression;
                
                    if (expression != null)
                    {
                        var propertiesToInject =
                            from property in expression.Constructor.DeclaringType.GetProperties()
                            where property.GetCustomAttributes(typeof(MyInjectPropertyAttribute), true).Any()
                            let registration = container.GetRegistration(property.PropertyType, true)
                            select Tuple.Create(property, registration);
                
                        if (propertiesToInject.Any())
                        {
                            Func<object, Tuple<PropertyInfo, InstanceProducer>[], object> injectorDelegate =
                                (instance, dependencies) =>
                                {
                                    foreach (var dependency in dependencies)
                                    {
                                        dependency.Item1.SetValue(instance, dependency.Item2.GetInstance(), null);
                                    }
                
                                    return instance;
                                };
                
                            e.Expression = Expression.Convert(
                                Expression.Invoke(
                                    Expression.Constant(injectorDelegate),
                                    e.Expression,
                                    Expression.Constant(propertiesToInject.ToArray())),
                                expression.Constructor.DeclaringType);
                        }
                    }
                };
            }
            ]]></code>
            <para>
            The example above registers a delegate that is raised every time the container compiles the
            expression for an registered type. The delegate checks if the type contains properties that are
            decorated with the supplied <b>MyInjectPropertyAttribute</b>. If decorated properties are found,
            the given expression is replaced with an expression that injects decorated properties.
            </para>
            </example>
        </member>
        <member name="M:SimpleInjector.Container.Register``1">
            <summary>
            Registers that a new instance of <typeparamref name="TConcrete"/> will be returned every time it 
            is requested (transient).
            </summary>
            <remarks>
            This method uses the container's 
            <see cref="P:SimpleInjector.ContainerOptions.LifestyleSelectionBehavior">LifestyleSelectionBehavior</see> to select
            the exact lifestyle for the specified type. By default this will be 
            <see cref="F:SimpleInjector.Lifestyle.Transient">Transient</see>.
            </remarks>
            <typeparam name="TConcrete">The concrete type that will be registered.</typeparam>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered, or when an 
            the <typeparamref name="TConcrete"/> has already been registered.
            </exception>
            <exception cref="T:System.ArgumentException">Thrown when the <typeparamref name="TConcrete"/> is a type
            that can not be created by the container.</exception>
        </member>
        <member name="M:SimpleInjector.Container.Register``1(SimpleInjector.Lifestyle)">
            <summary>
            Registers that an  instance of <typeparamref name="TConcrete"/> will be returned when it 
            is requested. The instance is cached according to the supplied <paramref name="lifestyle"/>.
            </summary>
            <typeparam name="TConcrete">The concrete type that will be registered.</typeparam>
            <param name="lifestyle">The lifestyle that specifies how the returned instance will be cached.</param>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered, or when an 
            the <typeparamref name="TConcrete"/> has already been registered.
            </exception>
            <exception cref="T:System.ArgumentException">Thrown when the <typeparamref name="TConcrete"/> is a type
            that can not be created by the container.</exception>
        </member>
        <member name="M:SimpleInjector.Container.Register``2">
            <summary>
            Registers that a new instance of <typeparamref name="TImplementation"/> will be returned every time a
            <typeparamref name="TService"/> is requested (transient).
            </summary>
            <remarks>
            This method uses the container's 
            <see cref="P:SimpleInjector.ContainerOptions.LifestyleSelectionBehavior">LifestyleSelectionBehavior</see> to select
            the exact lifestyle for the specified type. By default this will be 
            <see cref="F:SimpleInjector.Lifestyle.Transient">Transient</see>.
            </remarks>
            <typeparam name="TService">The interface or base type that can be used to retrieve the instances.</typeparam>
            <typeparam name="TImplementation">The concrete type that will be registered.</typeparam>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered, or when an 
            the <typeparamref name="TService"/> has already been registered.</exception>
            <exception cref="T:System.ArgumentException">Thrown when the given <typeparamref name="TImplementation"/> 
            type is not a type that can be created by the container.
            </exception>
        </member>
        <member name="M:SimpleInjector.Container.Register``2(SimpleInjector.Lifestyle)">
            <summary>
            Registers that an instance of <typeparamref name="TImplementation"/> will be returned when an
            instance of type <typeparamref name="TService"/> is requested. The instance is cached according to 
            the supplied <paramref name="lifestyle"/>.
            </summary>
            <typeparam name="TService">The interface or base type that can be used to retrieve the instances.</typeparam>
            <typeparam name="TImplementation">The concrete type that will be registered.</typeparam>
            <param name="lifestyle">The lifestyle that specifies how the returned instance will be cached.</param>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered, or when an 
            the <typeparamref name="TService"/> has already been registered.</exception>
            <exception cref="T:System.ArgumentException">Thrown when the given <typeparamref name="TImplementation"/> 
            type is not a type that can be created by the container.
            </exception>
        </member>
        <member name="M:SimpleInjector.Container.Register``1(System.Func{``0})">
            <summary>
            Registers the specified delegate that allows returning transient instances of 
            <typeparamref name="TService"/>. The delegate is expected to always return a new instance on
            each call.
            </summary>
            <remarks>
            This method uses the container's 
            <see cref="P:SimpleInjector.ContainerOptions.LifestyleSelectionBehavior">LifestyleSelectionBehavior</see> to select
            the exact lifestyle for the specified type. By default this will be 
            <see cref="F:SimpleInjector.Lifestyle.Transient">Transient</see>.
            </remarks>
            <typeparam name="TService">The interface or base type that can be used to retrieve instances.</typeparam>
            <param name="instanceCreator">The delegate that allows building or creating new instances.</param>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered, or when the 
            <typeparamref name="TService"/> has already been registered.</exception>
            <exception cref="T:System.ArgumentNullException">
            Thrown when <paramref name="instanceCreator"/> is a null reference.</exception>
        </member>
        <member name="M:SimpleInjector.Container.Register``1(System.Func{``0},SimpleInjector.Lifestyle)">
            <summary>
            Registers the specified delegate <paramref name="instanceCreator"/> that will produce instances of
            type <typeparamref name="TService"/> and will be returned when an instance of type 
            <typeparamref name="TService"/> is requested. The delegate is expected to produce new instances on
            each call. The instances are cached according to the supplied <paramref name="lifestyle"/>.
            </summary>
            <typeparam name="TService">The interface or base type that can be used to retrieve instances.</typeparam>
            <param name="instanceCreator">The delegate that allows building or creating new instances.</param>
            <param name="lifestyle">The lifestyle that specifies how the returned instance will be cached.</param>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered, or when the 
            <typeparamref name="TService"/> has already been registered.</exception>
            <exception cref="T:System.ArgumentNullException">
            Thrown when one of the supplied arguments is a null reference (Nothing in VB).</exception>
        </member>
        <member name="M:SimpleInjector.Container.Register(System.Type)">
            <summary>
            Registers that a new instance of <paramref name="concreteType"/> will be returned every time it 
            is requested (transient).
            </summary>
            <remarks>
            This method uses the container's 
            <see cref="P:SimpleInjector.ContainerOptions.LifestyleSelectionBehavior">LifestyleSelectionBehavior</see> to select
            the exact lifestyle for the specified type. By default this will be 
            <see cref="F:SimpleInjector.Lifestyle.Transient">Transient</see>.
            </remarks>
            <param name="concreteType">The concrete type that will be registered.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="concreteType"/> is a null 
            references (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="concreteType"/> represents an 
            open generic type or is a type that can not be created by the container.
            </exception>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered, or when an 
            the <paramref name="concreteType"/> has already been registered.
            </exception>
        </member>
        <member name="M:SimpleInjector.Container.Register(System.Type,System.Type)">
            <summary>
            Registers that a new instance of <paramref name="implementationType"/> will be returned every time a
            <paramref name="serviceType"/> is requested. If <paramref name="serviceType"/> and 
            <paramref name="implementationType"/> represent the same type, the type is registered by itself.
            Open and closed generic types are supported.
            </summary>
            <remarks>
            This method uses the container's 
            <see cref="P:SimpleInjector.ContainerOptions.LifestyleSelectionBehavior">LifestyleSelectionBehavior</see> to select
            the exact lifestyle for the specified type. By default this will be 
            <see cref="F:SimpleInjector.Lifestyle.Transient">Transient</see>.
            </remarks>
            <param name="serviceType">The base type or interface to register. This can be an open-generic type.</param>
            <param name="implementationType">The actual type that will be returned when requested. 
            This can be an open-generic type.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="serviceType"/> or 
            <paramref name="implementationType"/> are null references (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="implementationType"/> is
            no sub type from <paramref name="serviceType"/> (or the same type).
            </exception>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered, or when an 
            the <paramref name="serviceType"/> has already been registered.
            </exception>
        </member>
        <member name="M:SimpleInjector.Container.Register(System.Type,System.Type,SimpleInjector.Lifestyle)">
            <summary>
            Registers that an instance of type <paramref name="implementationType"/> will be returned when an
            instance of type <paramref name="serviceType"/> is requested. The instance is cached according to 
            the supplied <paramref name="lifestyle"/>. Open and closed generic types are supported.
            </summary>
            <param name="serviceType">The interface or base type that can be used to retrieve the instances.
            This can be an open-generic type.</param>
            <param name="implementationType">The concrete type that will be registered.
            This can be an open-generic type.</param>
            <param name="lifestyle">The lifestyle that specifies how the returned instance will be cached.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the supplied arguments is a null
            reference (Nothing in VB).</exception>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered, or when the 
            <paramref name="serviceType"/> has already been registered.</exception>
            <exception cref="T:System.ArgumentException">Thrown when the given <paramref name="implementationType"/>
            type is not a type that can be created by the container, when either <paramref name="serviceType"/>
            or <paramref name="implementationType"/> are open generic types, or when 
            <paramref name="serviceType"/> is not assignable from the <paramref name="implementationType"/>.
            </exception>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the supplied arguments is a null
            reference (Nothing in VB).</exception>
        </member>
        <member name="M:SimpleInjector.Container.Register(System.Type,System.Func{System.Object})">
            <summary>
            Registers the specified delegate that allows returning instances of <paramref name="serviceType"/>.
            </summary>
            <remarks>
            This method uses the container's 
            <see cref="P:SimpleInjector.ContainerOptions.LifestyleSelectionBehavior">LifestyleSelectionBehavior</see> to select
            the exact lifestyle for the specified type. By default this will be 
            <see cref="F:SimpleInjector.Lifestyle.Transient">Transient</see>.
            </remarks>
            <param name="serviceType">The base type or interface to register.</param>
            <param name="instanceCreator">The delegate that will be used for creating new instances.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when either <paramref name="serviceType"/> or 
            <paramref name="instanceCreator"/> are null references (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="serviceType"/> represents an
            open generic type.</exception>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered, or when an 
            the <paramref name="serviceType"/> has already been registered.
            </exception>
        </member>
        <member name="M:SimpleInjector.Container.Register(System.Type,System.Func{System.Object},SimpleInjector.Lifestyle)">
            <summary>
            Registers the specified delegate <paramref name="instanceCreator"/> that will produce instances of
            type <paramref name="serviceType"/> and will be returned when an instance of type 
            <paramref name="serviceType"/> is requested. The delegate is expected to produce new instances on 
            each call. The instances are cached according to the supplied <paramref name="lifestyle"/>.
            </summary>
            <param name="serviceType">The interface or base type that can be used to retrieve instances.</param>
            <param name="instanceCreator">The delegate that allows building or creating new instances.</param>
            <param name="lifestyle">The lifestyle that specifies how the returned instance will be cached.</param>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered, or when the 
            <paramref name="serviceType"/> has already been registered.</exception>
            <exception cref="T:System.ArgumentNullException">
            Thrown when one of the supplied arguments is a null reference (Nothing in VB).</exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterSingleton``1(``0)">
            <summary>
            Registers a single instance that will be returned when an instance of type 
            <typeparamref name="TService"/> is requested. This <paramref name="instance"/> must be thread-safe
            when working in a multi-threaded environment.
            <b>NOTE:</b> Do note that instances supplied by this method <b>NEVER</b> get disposed by the
            container, since the instance is assumed to outlive this container instance. If disposing is
            required, use the overload that accepts a <see cref="T:System.Func`1"/> delegate.
            </summary>
            <typeparam name="TService">The interface or base type that can be used to retrieve the instance.</typeparam>
            <param name="instance">The instance to register.</param>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered, or when the 
            <typeparamref name="TService"/> has already been registered.</exception>
            <exception cref="T:System.ArgumentNullException">
            Thrown when <paramref name="instance"/> is a null reference.
            </exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterSingleton(System.Type,System.Object)">
            <summary>
            Registers a single instance that will be returned when an instance of type 
            <paramref name="serviceType"/> is requested. This <paramref name="instance"/> must be thread-safe
            when working in a multi-threaded environment.
            </summary>
            <param name="serviceType">The base type or interface to register.</param>
            <param name="instance">The instance to register.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when either <paramref name="serviceType"/> or 
            <paramref name="instance"/> are null references (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="instance"/> is
            no sub type from <paramref name="serviceType"/>.</exception>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered, or when an 
            the <paramref name="serviceType"/> has already been registered.
            </exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterSingleton``1">
            <summary>
            Registers a single concrete instance that will be constructed using constructor injection and will
            be returned when this instance is requested by type <typeparamref name="TConcrete"/>. 
            This <typeparamref name="TConcrete"/> must be thread-safe when working in a multi-threaded 
            environment.
            If <typeparamref name="TConcrete"/> implements <see cref="T:System.IDisposable"/>, a created instance will
            get disposed when <see cref="M:SimpleInjector.Container.Dispose">Container.Dispose</see> gets called.
            </summary>
            <typeparam name="TConcrete">The concrete type that will be registered.</typeparam>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered, or when 
            <typeparamref name="TConcrete"/> has already been registered.
            </exception>
            <exception cref="T:System.ArgumentException">Thrown when the <typeparamref name="TConcrete"/> is a type
            that can not be created by the container.</exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterSingleton``2">
            <summary>
            Registers that the same a single instance of type <typeparamref name="TImplementation"/> will be 
            returned every time an <typeparamref name="TService"/> type is requested. If 
            <typeparamref name="TService"/> and <typeparamref name="TImplementation"/>  represent the same 
            type, the type is registered by itself. <typeparamref name="TImplementation"/> must be thread-safe 
            when working in a multi-threaded environment.
            If <typeparamref name="TImplementation"/> implements <see cref="T:System.IDisposable"/>, a created instance will
            get disposed when <see cref="M:SimpleInjector.Container.Dispose">Container.Dispose</see> gets called.
            </summary>
            <typeparam name="TService">
            The interface or base type that can be used to retrieve the instances.
            </typeparam>
            <typeparam name="TImplementation">The concrete type that will be registered.</typeparam>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered, or when the 
            <typeparamref name="TService"/> has already been registered.</exception>
            <exception cref="T:System.ArgumentException">Thrown when the given <typeparamref name="TImplementation"/> 
            type is not a type that can be created by the container.
            </exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterSingleton``1(System.Func{``0})">
            <summary>
            Registers the specified delegate that allows constructing a single instance of 
            <typeparamref name="TService"/>. This delegate will be called at most once during the lifetime of 
            the application. The returned instance must be thread-safe when working in a multi-threaded 
            environment.
            If the instance returned from <paramref name="instanceCreator"/> implements <see cref="T:System.IDisposable"/>, 
            the created instance will get disposed when <see cref="M:SimpleInjector.Container.Dispose">Container.Dispose</see> 
            gets called.
            </summary>
            <typeparam name="TService">The interface or base type that can be used to retrieve instances.</typeparam>
            <param name="instanceCreator">The delegate that allows building or creating this single
            instance.</param>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered, or when a 
            <paramref name="instanceCreator"/> for <typeparamref name="TService"/> has already been registered.
            </exception>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="instanceCreator"/> is a 
            null reference.</exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterSingleton(System.Type,System.Type)">
            <summary>
            Registers that the same instance of type <paramref name="implementationType"/> will be returned every 
            time an instance of type <paramref name="serviceType"/> type is requested. If 
            <paramref name="serviceType"/> and <paramref name="implementationType"/> represent the same type, the 
            type is registered by itself. <paramref name="implementationType"/> must be thread-safe when working 
            in a multi-threaded environment. Open and closed generic types are supported.
            </summary>
            <param name="serviceType">The base type or interface to register.
            This can be an open-generic type.</param>
            <param name="implementationType">The actual type that will be returned when requested.
            This can be an open-generic type.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when either <paramref name="serviceType"/> or 
            <paramref name="implementationType"/> are null references (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="implementationType"/> is
            no sub type from <paramref name="serviceType"/>, or when one of them represents an open generic
            type.</exception>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered, or when an 
            the <paramref name="serviceType"/> has already been registered.
            </exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterSingleton(System.Type,System.Func{System.Object})">
            <summary>
            Registers the specified delegate that allows constructing a single <paramref name="serviceType"/> 
            instance. The container will call this delegate at most once during the lifetime of the application.
            </summary>
            <param name="serviceType">The base type or interface to register.</param>
            <param name="instanceCreator">The delegate that will be used for creating that single instance.</param>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="serviceType"/> represents an open
            generic type.</exception>
            <exception cref="T:System.ArgumentNullException">Thrown when either <paramref name="serviceType"/> or 
            <paramref name="instanceCreator"/> are null references (Nothing in
            VB).</exception>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered, or when an 
            the <paramref name="serviceType"/> has already been registered.
            </exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterInitializer``1(System.Action{``0})">
            <summary>
            Registers an <see cref="T:System.Action`1"/> delegate that runs after the creation of instances that
            implement or derive from the given <typeparamref name="TService"/>. Please note that only instances
            that are created by the container (using constructor injection) can be initialized this way.
            </summary>
            <typeparam name="TService">The type for which the initializer will be registered.</typeparam>
            <param name="instanceInitializer">The delegate that will be called after the instance has been
            constructed and before it is returned.</param>
            <exception cref="T:System.ArgumentNullException">
            Thrown when the <paramref name="instanceInitializer"/> is a null reference.
            </exception>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered.</exception>
            <remarks>
            <para>
            Multiple <paramref name="instanceInitializer"/> delegates can be registered per 
            <typeparamref name="TService"/> and multiple initializers can be applied on a created instance,
            before it is returned. For instance, when registering a <paramref name="instanceInitializer"/>
            for type <see cref="T:System.Object"/>, the delegate will be called for every instance created by
            the container, which can be nice for debugging purposes.
            </para>
            <para>
            Note: Initializers are guaranteed to be executed in the order they are registered.
            </para>
            <para>
            The following example shows the usage of the 
            <see cref="M:SimpleInjector.Container.RegisterInitializer``1(System.Action{``0})">RegisterInitializer</see> method:
            </para>
            <code lang="cs"><![CDATA[
            public interface ITimeProvider { DateTime Now { get; } }
            public interface ICommand { bool SendAsync { get; set; } }
            
            public abstract class CommandBase : ICommand
            {
                ITimeProvider Clock { get; set; }
                
                public bool SendAsync { get; set; }
            }
            
            public class ConcreteCommand : CommandBase { }
            
            [TestMethod]
            public static void TestRegisterInitializer()
            {
                // Arrange
                var container = new Container();
            
                container.Register<ICommand, ConcreteCommand>();
            
                // Configuring property injection for types that implement ICommand:
                container.RegisterInitializer<ICommand>(command =>
                {
                    command.SendAsync = true;
                });
            
                // Configuring property injection for types that implement CommandBase:
                container.RegisterInitializer<CommandBase>(command =>
                {
                    command.Clock = container.GetInstance<ITimeProvider>();
                });
                
                // Act
                var command = (ConcreteCommand)container.GetInstance<ICommand>();
            
                // Assert
                // Because ConcreteCommand implements both ICommand and CommandBase, 
                // both the initializers will have been executed.
                Assert.IsTrue(command.SendAsync);
                Assert.IsNotNull(command.Clock);
            }
            ]]></code>
            <para>
            The container does not use the type information of the requested service type, but it uses the 
            type information of the actual implementation to find all initialized that apply for that 
            type. This makes it possible to have multiple initializers to be applied on a single returned
            instance while keeping performance high.
            </para>
            <para>
            Registered initializers will only be applied to instances that are created by the container self
            (using constructor injection). Types that are newed up manually by supplying a 
            <see cref="T:System.Func`1"/> delegate to the container (using the 
            <see cref="M:SimpleInjector.Container.Register``1(System.Func{``0})"/> method) or registered as single instance
            (using <see cref="M:SimpleInjector.Container.RegisterSingleton``1(``0)"/>) will not trigger initialization.
            When initialization of these instances is needed, this must be done manually, as can be seen in 
            the following example:
            <code lang="cs"><![CDATA[
            [TestMethod]
            public static void TestRegisterInitializer()
            {
                // Arrange
                int initializerCallCount = 0;
                
                var container = new Container();
                
                // Define a initializer for ICommand
                Action<ICommand> commandInitializer = command =>
                {
                    initializerCallCount++;
                });
                
                // Configuring that initializer.
                container.RegisterInitializer<ICommand>(commandInitializer);
                
                container.Register<ICommand>(() =>
                {
                    // Create a ConcreteCommand manually: will not be initialized.
                    var command = new ConcreteCommand("Data Source=.;Initial Catalog=db;");
                
                    // Run the initializer manually.
                    commandInitializer(command);
                
                    return command;
                });
                
                // Act
                var command = container.GetInstance<ICommand>();
            
                // Assert
                // The initializer will only be called once.
                Assert.AreEqual(1, initializerCallCount);
            }
            ]]></code>
            The previous example shows how a manually created instance can still be initialized. Try to
            prevent creating types manually, by changing the design of those classes. If possible, create a
            single public constructor that only contains dependencies that can be resolved.
            </para>
            </remarks>
        </member>
        <member name="M:SimpleInjector.Container.RegisterInitializer(System.Action{SimpleInjector.Advanced.InstanceInitializationData},System.Predicate{SimpleInjector.Advanced.InitializationContext})">
            <summary>
            Registers an <see cref="T:System.Action`1"/> delegate that runs after the 
            creation of instances for which the supplied <paramref name="predicate"/> returns true. Please 
            note that only instances that are created by the container can be initialized this way.
            </summary>
            <param name="instanceInitializer">The delegate that will be called after the instance has been
            constructed and before it is returned.</param>
            <param name="predicate">The predicate that will be used to check whether the given delegate must
            be applied to a registration or not. The given predicate will be called once for each registration
            in the container.</param>
            <exception cref="T:System.ArgumentNullException">
            Thrown when either the <paramref name="instanceInitializer"/> or <paramref name="predicate"/> are 
            null references.
            </exception>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered.</exception>
            <remarks>
            <para>
            Note: Initializers are guaranteed to be executed in the order they are registered.
            </para>
            <para>
            Note: The <paramref name="predicate"/> is <b>not</b> guaranteed to be called once per registration;
            when a registration's instance is requested for the first time simultaneously over multiple thread,
            the predicate might be called multiple times. The caller of this method is responsible of supplying
            a predicate that is thread-safe.
            </para>
            </remarks>
        </member>
        <member name="M:SimpleInjector.Container.AddRegistration(System.Type,SimpleInjector.Registration)">
            <summary>
            Adds the <paramref name="registration"/> for the supplied <paramref name="serviceType"/>. This
            method can be used to apply the same <see cref="T:SimpleInjector.Registration"/> to multiple different service
            types.
            </summary>
            <param name="serviceType">The base type or interface to register.</param>
            <param name="registration">The registration that should be stored for the given 
            <paramref name="serviceType"/>.</param>
            <example>
            <code lang="cs"><![CDATA[
            public interface IFoo { }
            public interface IBar { }
            public class FooBar : IFoo, IBar { }
            
            public void AddRegistration_SuppliedWithSameSingletonRegistrationTwice_ReturnsSameInstance()
            {
                // Arrange
                Registration registration =
                    Lifestyle.Singleton.CreateRegistration<FooBar, FooBar>(container);
            
                container.AddRegistration(typeof(IFoo), registration);
                container.AddRegistration(typeof(IBar), registration);
            
                // Act
                IFoo foo = container.GetInstance<IFoo>();
                IBar bar  = container.GetInstance<IBar>();
            
                // Assert
                bool fooAndBareAreTheSameInstance = object.ReferenceEquals(foo, bar);
                Assert.IsTrue(fooAndBareAreTheSameInstance);
            }
            ]]></code>
            <para>
            In the example above a singleton registration is created for type <c>FooBar</c> and this 
            registration is added to the container for each interface (<c>IFoo</c> and <c>IBar</c>) that it
            implements. Since both services use the same singleton registration, requesting those services 
            will result in the return of the same (singleton) instance.
            </para>
            <para>
            <see cref="E:SimpleInjector.Container.ExpressionBuilding"/> events are applied to the <see cref="T:System.Linq.Expressions.Expression"/> of the
            <see cref="T:SimpleInjector.Registration"/> instance and are therefore applied once. <see cref="E:SimpleInjector.Container.ExpressionBuilt"/> 
            events on the other hand get applied to the <b>Expression</b> of the <see cref="T:SimpleInjector.InstanceProducer"/>.
            Since each <b>AddRegistration</b> gets its own instance producer (that wraps the 
            <b>Registration</b> instance), this means that the <b>ExpressionBuilt</b> events will be 
            applied for each registered service type.
            </para>
            <para>
            The most practical example of this is the use of decorators using one of the 
            <see cref="M:SimpleInjector.Container.RegisterDecorator(System.Type,System.Type)">RegisterDecorator</see> overloads (decorator registration 
            use the <b>ExpressionBuilt</b> event under the covers). Take a look at the following example:
            </para>
            <code lang="cs"><![CDATA[
            public interface IFoo { }
            public interface IBar { }
            public class FooBar : IFoo, IBar { }
            
            public class BarDecorator : IBar
            {
                public BarDecorator(IBar decoratedBar)
                {
                    this.DecoratedBar = decoratedBar;
                }
                
                public IBar DecoratedBar { get; private set; }
            }
            
            public void AddRegistration_SameSingletonRegistrationTwiceAndOneDecoratorApplied_ReturnsSameInstance()
            {
                // Arrange
                Registration registration =
                    Lifestyle.Singleton.CreateRegistration<FooBar, FooBar>(container);
            
                container.AddRegistration(typeof(IFoo), registration);
                container.AddRegistration(typeof(IBar), registration);
                
                // Registere a decorator for IBar, but not for IFoo
                container.RegisterDecorator(typeof(IBar), typeof(BarDecorator));
            
                // Act
                var foo = container.GetInstance<IFoo>();
                var decorator = container.GetInstance<IBar>() as BarDecorator;
                var bar = decorator.DecoratedBar;
            
                // Assert
                bool fooAndBareAreTheSameInstance = object.ReferenceEquals(foo, bar);
                Assert.IsTrue(fooAndBareAreTheSameInstance);
            }
            ]]></code>
            The example shows that the decorator gets applied to <c>IBar</c> but not to <c>IFoo</c>, but that
            the decorated <c>IBar</c> is still the same instance as the resolved <c>IFoo</c> instance.
            </example>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the supplied arguments is a null
            reference (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="serviceType"/> is not a reference
            type, is open generic, is ambiguous, when it is not assignable from the 
            <paramref name="registration"/>'s <see cref="P:SimpleInjector.Registration.ImplementationType">ImplementationType</see>
            or when the supplied <paramref name="registration"/> is created for a different 
            <see cref="T:SimpleInjector.Container"/> instance.</exception>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered, or when an 
            the <paramref name="serviceType"/> has already been registered.
            </exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterDecorator``2">
            <summary>
            Ensures that the supplied <typeparamref name="TDecorator"/> decorator is returned, wrapping the 
            original registered <typeparamref name="TService"/>, by injecting that service type into the 
            constructor of the supplied <typeparamref name="TDecorator"/>. Multiple decorators may be applied 
            to the same <typeparamref name="TService"/>. By default, a new <typeparamref name="TDecorator"/> 
            instance will be returned on each request (according the 
            <see cref="F:SimpleInjector.Lifestyle.Transient">Transient</see> lifestyle), independently of the lifestyle of the 
            wrapped service.
            </summary>
            <remarks>
            <para>
            This method uses the container's 
            <see cref="P:SimpleInjector.ContainerOptions.LifestyleSelectionBehavior">LifestyleSelectionBehavior</see> to select
            the exact lifestyle for the specified type. By default this will be 
            <see cref="F:SimpleInjector.Lifestyle.Transient">Transient</see>.
            </para>
            <para>
            The <b>RegisterDecorator</b> method works by hooking onto the container's
            <see cref="E:SimpleInjector.Container.ExpressionBuilt">ExpressionBuilt</see> event. This event fires after the
            <see cref="E:SimpleInjector.Container.ResolveUnregisteredType">ResolveUnregisteredType</see> event, which allows
            decoration of types that are resolved using unregistered type resolution.
            </para>
            <para>
            Multiple decorators can be applied to the same service type. The order in which they are registered
            is the order they get applied in. This means that the decorator that gets registered first, gets
            applied first, which means that the next registered decorator, will wrap the first decorator, which
            wraps the original service type.
            </para>
            <para>
            Constructor injection will be used on that type, and although it may have many constructor 
            arguments, it must have exactly one argument of the type of <typeparamref name="TService"/>, or an 
            argument of type <see cref="T:System.Func`1"/> where <b>TResult</b> is <typeparamref name="TService"/>.
            An exception will be thrown when this is not the case.
            </para>
            <para>
            The registered <typeparamref name="TDecorator"/> may have a constructor with an argument of type
            <see cref="T:System.Func`1"/> where <b>T</b> is <typeparamref name="TService"/>. In this case, an decorated
            instance will not injected into the <typeparamref name="TService"/>, but it will inject a 
            <see cref="T:System.Func`1"/> that allows creating instances of the decorated type, according to the
            lifestyle of that type. This enables more advanced scenarios, such as executing the decorated 
            types on a different thread, or executing decorated instance within a certain scope (such as a 
            lifetime scope).
            </para>
            </remarks>
            <example>
            Please see <see cref="M:SimpleInjector.Container.RegisterDecorator(System.Type,System.Type)"/> for an example.
            </example>
            <typeparam name="TService">The service type that will be wrapped by the given 
            <typeparamref name="TDecorator"/>.</typeparam>
            <typeparam name="TDecorator">The decorator type that will be used to wrap the original service type.
            </typeparam>
            <exception cref="T:System.ArgumentException">Thrown when <typeparamref name="TDecorator"/> does not
            have a single public constructor, or when <typeparamref name="TDecorator"/> does not
            contain a constructor that has exactly one argument of type <typeparamref name="TService"/> or 
            <see cref="T:System.Func`1"/> where <b>T</b> is <typeparamref name="TService"/>.</exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterDecorator``2(SimpleInjector.Lifestyle)">
            <summary>
            Ensures that the supplied <typeparamref name="TDecorator"/> decorator is returned and cached with
            the given <paramref name="lifestyle"/>, wrapping the original registered 
            <typeparamref name="TService"/>, by injecting that service type into the constructor of the 
            supplied <typeparamref name="TDecorator"/>. Multiple decorators may be applied to the same 
            <typeparamref name="TService"/>. Decorators can be applied to both open, closed, and non-generic 
            service types.
            </summary>
            <remarks>
            <para>
            The <b>RegisterDecorator</b> method works by hooking onto the container's
            <see cref="E:SimpleInjector.Container.ExpressionBuilt">ExpressionBuilt</see> event. This event fires after the
            <see cref="E:SimpleInjector.Container.ResolveUnregisteredType">ResolveUnregisteredType</see> event, which allows
            decoration of types that are resolved using unregistered type resolution.
            </para>
            <para>
            Multiple decorators can be applied to the same service type. The order in which they are registered
            is the order they get registered. This means that the decorator that gets registered first, gets
            applied first, which means that the next registered decorator, will wrap the first decorator, which
            wraps the original service type.
            </para>
            <para>
            Constructor injection will be used on that type, and although it may have many constructor 
            arguments, it must have exactly one argument of the type of <typeparamref name="TService"/>, or an 
            argument of type <see cref="T:System.Func`1"/> where <b>TResult</b> is <typeparamref name="TService"/>.
            An exception will be thrown when this is not the case.
            </para>
            <para>
            The registered <typeparamref name="TDecorator"/> may have a constructor with an argument of type
            <see cref="T:System.Func`1"/> where <b>T</b> is <typeparamref name="TService"/>. In this case, the
            will not inject the decorated <typeparamref name="TService"/> itself into the 
            <typeparamref name="TDecorator"/> instance, but it will inject a <see cref="T:System.Func`1"/> that allows
            creating instances of the decorated type, according to the lifestyle of that type. This enables
            more advanced scenarios, such as executing the decorated types on a different thread, or executing
            decorated instance within a certain scope (such as a lifetime scope).
            </para>
            </remarks>
            <example>
            Please see <see cref="M:SimpleInjector.Container.RegisterDecorator(System.Type,System.Type)"/> for an example.
            </example>
            <typeparam name="TService">The service type that will be wrapped by the given 
            <typeparamref name="TDecorator"/>.</typeparam>
            <typeparam name="TDecorator">The decorator type that will be used to wrap the original service type.</typeparam>
            <param name="lifestyle">The lifestyle that specifies how the returned decorator will be cached.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the arguments is a null reference.</exception>
            <exception cref="T:System.ArgumentException">Thrown when <typeparamref name="TDecorator"/>
            does not have a single public constructor, or when <typeparamref name="TDecorator"/> does 
            not contain a constructor that has exactly one argument of type 
            <typeparamref name="TService"/> or <see cref="T:System.Func`1"/> where <b>T</b> is
            <typeparamref name="TService"/>.</exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterDecorator(System.Type,System.Type)">
             <summary>
             Ensures that the supplied <paramref name="decoratorType"/> decorator is returned, wrapping the 
             original registered <paramref name="serviceType"/>, by injecting that service type into the 
             constructor of the supplied <paramref name="decoratorType"/>. Multiple decorators may be applied 
             to the same <paramref name="serviceType"/>. Decorators can be applied to both open, closed, and 
             non-generic service types. By default, a new <paramref name="decoratorType"/> instance will be 
             returned on each request (according the <see cref="F:SimpleInjector.Lifestyle.Transient">Transient</see> lifestyle),
             independently of the lifestyle of the wrapped service.
             </summary>
             <remarks>
             <para>
             This method uses the container's 
             <see cref="P:SimpleInjector.ContainerOptions.LifestyleSelectionBehavior">LifestyleSelectionBehavior</see> to select
             the exact lifestyle for the specified type. By default this will be 
             <see cref="F:SimpleInjector.Lifestyle.Transient">Transient</see>.
             </para>
             <para>
             The <b>RegisterDecorator</b> method works by hooking onto the container's
             <see cref="E:SimpleInjector.Container.ExpressionBuilt">ExpressionBuilt</see> event. This event fires after the
             <see cref="E:SimpleInjector.Container.ResolveUnregisteredType">ResolveUnregisteredType</see> event, which allows
             decoration of types that are resolved using unregistered type resolution.
             </para>
             <para>
             Multiple decorators can be applied to the same service type. The order in which they are registered
             is the order they get applied in. This means that the decorator that gets registered first, gets
             applied first, which means that the next registered decorator, will wrap the first decorator, which
             wraps the original service type.
             </para>
             <para>
             Constructor injection will be used on that type, and although it may have many constructor 
             arguments, it must have exactly one argument of the type of <paramref name="serviceType"/>, or an 
             argument of type <see cref="T:System.Func`1"/> where <b>TResult</b> is <paramref name="serviceType"/>.
             An exception will be thrown when this is not the case.
             </para>
             <para>
             The registered <paramref name="decoratorType"/> may have a constructor with an argument of type
             <see cref="T:System.Func`1"/> where <b>T</b> is <paramref name="serviceType"/>. In this case, an decorated
             instance will not injected into the <paramref name="decoratorType"/>, but it will inject a 
             <see cref="T:System.Func`1"/> that allows creating instances of the decorated type, according to the
             lifestyle of that type. This enables more advanced scenarios, such as executing the decorated 
             types on a different thread, or executing decorated instance within a certain scope (such as a 
             lifetime scope).
             </para>
             </remarks>
             <example>
             The following example shows the definition of a generic <b>ICommandHandler&lt;T&gt;</b> interface,
             a <b>CustomerMovedCommandHandler</b> implementing that interface, and a 
             <b>ValidatorCommandHandlerDecorator&lt;T&gt;</b> that acts as a decorator for that interface.
             <code lang="cs"><![CDATA[
             using System.ComponentModel.DataAnnotations;
             using System.Diagnostics;
             using System.Linq;
             
             using Microsoft.VisualStudio.TestTools.UnitTesting;
             
             using SimpleInjector;
             using SimpleInjector.Extensions;
             
             public interface ICommandHandler<TCommand>
             {
                 void Handle(TCommand command);
             }
            
             public class CustomerMovedCommand
             {
                 [Required]
                 public int CustomerId { get; set; }
            
                 [Required]
                 public Address Address { get; set; }
             }
            
             public class CustomerMovedCommandHandler
                 : ICommandHandler<CustomerMovedCommand>
             {
                 public void Handle(CustomerMovedCommand command)
                 {
                     // some logic
                 }
             }
            
             // Decorator that validates commands before they get executed.
             public class ValidatorCommandHandlerDecorator<TCommand>
                 : ICommandHandler<TCommand>
             {
                 private readonly ICommandHandler<TCommand> decoratedHandler;
                 private readonly Container container;
            
                 public ValidatorCommandHandlerDecorator(
                     ICommandHandler<TCommand> decoratedHandler,
                     Container container)
                 {
                     this.decoratedHandler = decoratedHandler;
                     this.container = container;
                 }
            
                 public void Handle(TCommand command)
                 {
                     this.Validate(command);
            
                     this.decoratedHandler.Handle(command);
                 }
            
                 private void Validate(TCommand command)
                 {
                     var validationContext =
                         new ValidationContext(command, this.container, null);
            
                     Validator.ValidateObject(command, validationContext);
                 }
             }
             
             // Decorator that measures the time it takes to execute a command.
             public class MonitoringCommandHandlerDecorator<TCommand>
                 : ICommandHandler<TCommand>
             {
                 private readonly ICommandHandler<TCommand> decoratedHandler;
                 private readonly ILogger logger;
            
                 public MonitoringCommandHandlerDecorator(
                     ICommandHandler<TCommand> decoratedHandler,
                     ILogger logger)
                 {
                     this.decoratedHandler = decoratedHandler;
                     this.logger = logger;
                 }
            
                 public void Handle(TCommand command)
                 {
                     var watch = Stopwatch.StartNew();
            
                     this.decoratedHandler.Handle(command);
            
                     this.logger.Log(string.Format("{0} executed in {1} ms.",
                         command.GetType().Name, watch.ElapsedMilliseconds));
                 }
             }
             
             [TestMethod]
             public static void TestRegisterOpenGenericDecorator()
             {
                 // Arrange
                 var container = new Container();
            
                 container.Register<ILogger, DebugLogger>(Lifestyle.Singleton);
            
                 // Search the given assembly and register all concrete types that 
                 // implement ICommandHandler<TCommand>.
                 container.RegisterManyForOpenGeneric(typeof(ICommandHandler<>),
                     typeof(ICommandHandler<>).Assembly);
            
                 // Wrap all ICommandHandler<TCommand> service types with a decorator
                 // that measures and logs the duration of that handler.
                 container.RegisterDecorator(typeof(ICommandHandler<>),
                     typeof(MonitoringCommandHandlerDecorator<>));
            
                 // Wrap all ICommandHandler<TCommand> types (in this case it will
                 // wrap the monitoring decorator), but only if the TCommand contains
                 // any properties.
                 container.RegisterDecorator(typeof(ICommandHandler<>),
                     typeof(ValidatorCommandHandlerDecorator<>), context =>
                     {
                         var commandType = context.ServiceType.GetGenericArguments()[0];
                         bool mustDecorate = commandType.GetProperties().Any();
                         return mustDecorate;
                     });
            
                 // Act
                 var handler = 
                     container.GetInstance<ICommandHandler<CustomerMovedCommand>>();
            
                 // Assert
                 Assert.IsInstanceOfType(handler, 
                     typeof(ValidatorCommandHandlerDecorator<CustomerMovedCommand>));
             }
             ]]></code>
             </example>
             <param name="serviceType">The (possibly open generic) service type that will be wrapped by the 
             given <paramref name="decoratorType"/>.</param>
             <param name="decoratorType">The (possibly the open generic) decorator type that will
             be used to wrap the original service type.</param>
             <exception cref="T:System.ArgumentNullException">Thrown when one of the arguments is a null reference.</exception>
             <exception cref="T:System.ArgumentException">Thrown when <paramref name="serviceType"/>  is not
             an open generic type, when <paramref name="decoratorType"/> does not inherit from or implement 
             <paramref name="serviceType"/>, when <paramref name="decoratorType"/> does not
             have a single public constructor, or when <paramref name="decoratorType"/> does not
             contain a constructor that has exactly one argument of type 
             <paramref name="serviceType"/> or <see cref="T:System.Func`1"/> where <b>T</b> is
             <paramref name="serviceType"/>.</exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterDecorator(System.Type,System.Type,SimpleInjector.Lifestyle)">
            <summary>
            Ensures that the supplied <paramref name="decoratorType"/> decorator is returned and cached with
            the given <paramref name="lifestyle"/>, wrapping the original registered 
            <paramref name="serviceType"/>, by injecting that service type into the constructor of the 
            supplied <paramref name="decoratorType"/>. Multiple decorators may be applied to the same 
            <paramref name="serviceType"/>. Decorators can be applied to both open, closed, and non-generic 
            service types.
            </summary>
            <remarks>
            <para>
            The <b>RegisterDecorator</b> method works by hooking onto the container's
            <see cref="E:SimpleInjector.Container.ExpressionBuilt">ExpressionBuilt</see> event. This event fires after the
            <see cref="E:SimpleInjector.Container.ResolveUnregisteredType">ResolveUnregisteredType</see> event, which allows
            decoration of types that are resolved using unregistered type resolution.
            </para>
            <para>
            Multiple decorators can be applied to the same service type. The order in which they are registered
            is the order they get registered. This means that the decorator that gets registered first, gets
            applied first, which means that the next registered decorator, will wrap the first decorator, which
            wraps the original service type.
            </para>
            <para>
            Constructor injection will be used on that type, and although it may have many constructor 
            arguments, it must have exactly one argument of the type of <paramref name="serviceType"/>, or an 
            argument of type <see cref="T:System.Func`1"/> where <b>TResult</b> is <paramref name="serviceType"/>.
            An exception will be thrown when this is not the case.
            </para>
            <para>
            The registered <paramref name="decoratorType"/> may have a constructor with an argument of type
            <see cref="T:System.Func`1"/> where <b>T</b> is <paramref name="serviceType"/>. In this case, the
            will not inject the decorated <paramref name="serviceType"/> itself into the 
            <paramref name="decoratorType"/> instance, but it will inject a <see cref="T:System.Func`1"/> that allows
            creating instances of the decorated type, according to the lifestyle of that type. This enables
            more advanced scenarios, such as executing the decorated types on a different thread, or executing
            decorated instance within a certain scope (such as a lifetime scope).
            </para>
            </remarks>
            <example>
            Please see the <see cref="M:SimpleInjector.Container.RegisterDecorator(System.Type,System.Type)">RegisterDecorator</see> method
            for more information.
            </example>
            <param name="serviceType">The definition of the (possibly open generic) service type that will
            be wrapped by the given <paramref name="decoratorType"/>.</param>
            <param name="decoratorType">The definition of the (possibly open generic) decorator type that will
            be used to wrap the original service type.</param>
            <param name="lifestyle">The lifestyle that specifies how the returned decorator will be cached.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the arguments is a null reference.</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="serviceType"/> is not
            an open generic type, when <paramref name="decoratorType"/> does not inherit from or 
            implement <paramref name="serviceType"/>, when <paramref name="decoratorType"/>
            does not have a single public constructor, or when <paramref name="decoratorType"/> does 
            not contain a constructor that has exactly one argument of type 
            <paramref name="serviceType"/> or <see cref="T:System.Func`1"/> where <b>T</b> is
            <paramref name="serviceType"/>.</exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterDecorator(System.Type,System.Type,SimpleInjector.Lifestyle,System.Predicate{SimpleInjector.DecoratorPredicateContext})">
            <summary>
            Ensures that the supplied <paramref name="decoratorType"/> decorator is returned when the supplied
            <paramref name="predicate"/> returns <b>true</b> and cached with the given 
            <paramref name="lifestyle"/>, wrapping the original registered <paramref name="serviceType"/>, by 
            injecting that service type into the constructor of the supplied <paramref name="decoratorType"/>. 
            Multiple decorators may be applied to the same <paramref name="serviceType"/>. Decorators can be 
            applied to both open, closed, and non-generic service types.
            </summary>
            <remarks>
            <para>
            The <b>RegisterDecorator</b> method works by hooking onto the container's
            <see cref="E:SimpleInjector.Container.ExpressionBuilt">ExpressionBuilt</see> event. This event fires after the
            <see cref="E:SimpleInjector.Container.ResolveUnregisteredType">ResolveUnregisteredType</see> event, which allows
            decoration of types that are resolved using unregistered type resolution.
            </para>
            <para>
            Multiple decorators can be applied to the same service type. The order in which they are registered
            is the order they get applied in. This means that the decorator that gets registered first, gets
            applied first, which means that the next registered decorator, will wrap the first decorator, which
            wraps the original service type.
            </para>
            <para>
            Constructor injection will be used on that type, and although it may have many constructor 
            arguments, it must have exactly one argument of the type of <paramref name="serviceType"/>, or an 
            argument of type <see cref="T:System.Func`1"/> where <b>TResult</b> is <paramref name="serviceType"/>.
            An exception will be thrown when this is not the case.
            </para>
            <para>
            The registered <paramref name="decoratorType"/> may have a constructor with an argument of type
            <see cref="T:System.Func`1"/> where <b>T</b> is <paramref name="serviceType"/>. In this case, the
            will not inject the decorated <paramref name="serviceType"/> itself into the 
            <paramref name="decoratorType"/> instance, but it will inject a <see cref="T:System.Func`1"/> that allows
            creating instances of the decorated type, according to the lifestyle of that type. This enables
            more advanced scenarios, such as executing the decorated types on a different thread, or executing
            decorated instance within a certain scope (such as a lifetime scope).
            </para>
            </remarks>
            <example>
            Please see the <see cref="M:SimpleInjector.Container.RegisterDecorator(System.Type,System.Type)">RegisterDecorator</see> method
            for more information.
            </example>
            <param name="serviceType">The definition of the (possibly open generic) service type that will
            be wrapped by the given <paramref name="decoratorType"/>.</param>
            <param name="decoratorType">The definition of the (possibly open generic) decorator type that will
            be used to wrap the original service type.</param>
            <param name="lifestyle">The lifestyle that specifies how the returned decorator will be cached.</param>
            <param name="predicate">The predicate that determines whether the 
            <paramref name="decoratorType"/> must be applied to a service type.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the arguments is a null reference.</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="serviceType"/> is not
            an open generic type, when <paramref name="decoratorType"/> does not inherit from or 
            implement <paramref name="serviceType"/>, when <paramref name="decoratorType"/>
            does not have a single public constructor, or when <paramref name="decoratorType"/> does 
            not contain a constructor that has exactly one argument of type 
            <paramref name="serviceType"/> or <see cref="T:System.Func`1"/> where <b>T</b> is
            <paramref name="serviceType"/>.</exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterDecorator(System.Type,System.Func{SimpleInjector.DecoratorPredicateContext,System.Type},SimpleInjector.Lifestyle,System.Predicate{SimpleInjector.DecoratorPredicateContext})">
            <summary>
            Ensures that the decorator type that is returned from <paramref name="decoratorTypeFactory"/> is 
            supplied when the supplied <paramref name="predicate"/> returns <b>true</b> and cached with the given 
            <paramref name="lifestyle"/>, wrapping the original registered <paramref name="serviceType"/>, by 
            injecting that service type into the constructor of the decorator type that is returned by the
            supplied <paramref name="decoratorTypeFactory"/>. 
            Multiple decorators may be applied to the same <paramref name="serviceType"/>. Decorators can be 
            applied to both open, closed, and non-generic service types.
            </summary>
            <remarks>
            <para>
            The types returned from the <paramref name="decoratorTypeFactory"/> may be non-generic, 
            closed-generic, open-generic and even partially-closed generic. The container will try to fill in 
            the generic parameters based on the resolved service type.
            </para>
            <para>
            The <b>RegisterDecorator</b> method works by hooking onto the container's
            <see cref="E:SimpleInjector.Container.ExpressionBuilt">ExpressionBuilt</see> event. This event fires after the
            <see cref="E:SimpleInjector.Container.ResolveUnregisteredType">ResolveUnregisteredType</see> event, which allows
            decoration of types that are resolved using unregistered type resolution.
            </para>
            <para>
            Multiple decorators can be applied to the same service type. The order in which they are registered
            is the order they get applied in. This means that the decorator that gets registered first, gets
            applied first, which means that the next registered decorator, will wrap the first decorator, which
            wraps the original service type.
            </para>
            <para>
            Constructor injection will be used on that type, and although it may have many constructor 
            arguments, it must have exactly one argument of the type of <paramref name="serviceType"/>, or an 
            argument of type <see cref="T:System.Func`1"/> where <b>TResult</b> is <paramref name="serviceType"/>.
            An exception will be thrown when this is not the case.
            </para>
            <para>
            The type returned from <paramref name="decoratorTypeFactory"/> may have a constructor with an 
            argument of type <see cref="T:System.Func`1"/> where <b>T</b> is <paramref name="serviceType"/>. In this 
            case, the library will not inject the decorated <paramref name="serviceType"/> itself into the 
            decorator instance, but it will inject a <see cref="T:System.Func`1"/> that allows
            creating instances of the decorated type, according to the lifestyle of that type. This enables
            more advanced scenarios, such as executing the decorated types on a different thread, or executing
            decorated instance within a certain scope (such as a lifetime scope).
            </para>
            </remarks>
            <example>
            The following is an example of the registration of a decorator through the factory delegate:
            <code lang="cs"><![CDATA[
            container.Register<ICommandHandler<MoveCustomerCommand>, MoveCustomerCommandHandler>();
            
            container.RegisterDecorator(
                typeof(ICommandHandler<>),
                context => typeof(LoggingCommandHandler<,>).MakeGenericType(
                    typeof(LoggingCommandHandler<,>).GetGenericArguments().First(),
                    context.ImplementationType),
                Lifestyle.Transient,
                context => true);
                
            var handler = container.GetInstance<ICommandHandler<MoveCustomerCommand>>();
            
            Assert.IsInstanceOfType(handler,
                typeof(LoggingCommandHandler<MoveCustomerCommand, MoveCustomerCommandHandler>));
            
            ]]></code>
            The code above allows a generic <b>LoggingCommandHandler&lt;TCommand, TImplementation&gt;</b> to
            be applied to command handlers, where the second generic argument will be filled in using the
            contextual information.
            </example>
            <param name="serviceType">The definition of the (possibly open generic) service type that will
            be wrapped by the decorator type that is returned from <paramref name="decoratorTypeFactory"/>.</param>
            <param name="decoratorTypeFactory">A factory that allows building Type objects that define the
            decorators to inject, based on the given contextual information. The delegate is allowed to return
            (partially) open-generic types.</param>
            <param name="lifestyle">The lifestyle that specifies how the returned decorator will be cached.</param>
            <param name="predicate">The predicate that determines whether the decorator must be applied to a 
            service type.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the arguments is a null reference.</exception>
        </member>
        <member name="M:SimpleInjector.Container.RegisterDecorator(System.Type,System.Type,System.Predicate{SimpleInjector.DecoratorPredicateContext})">
            <summary>
            Ensures that the supplied <paramref name="decoratorType"/> decorator is returned when the supplied
            <paramref name="predicate"/> returns <b>true</b>, wrapping the original registered 
            <paramref name="serviceType"/>, by injecting that service type into the constructor of the 
            supplied <paramref name="decoratorType"/>. Multiple decorators may be applied to the same 
            <paramref name="serviceType"/>. Decorators can be applied to both open, closed, and non-generic 
            service types. By default, a new <paramref name="decoratorType"/> instance will be returned on 
            each request (according the <see cref="F:SimpleInjector.Lifestyle.Transient">Transient</see> lifestyle), 
            independently of the lifestyle of the wrapped service.
            </summary>
            <remarks>
            <para>
            This method uses the container's 
            <see cref="P:SimpleInjector.ContainerOptions.LifestyleSelectionBehavior">LifestyleSelectionBehavior</see> to select
            the exact lifestyle for the specified type. By default this will be 
            <see cref="F:SimpleInjector.Lifestyle.Transient">Transient</see>.
            </para>
            <para>
            The <b>RegisterDecorator</b> method works by hooking onto the container's
            <see cref="E:SimpleInjector.Container.ExpressionBuilt">ExpressionBuilt</see> event. This event fires after the
            <see cref="E:SimpleInjector.Container.ResolveUnregisteredType">ResolveUnregisteredType</see> event, which allows
            decoration of types that are resolved using unregistered type resolution.
            </para>
            <para>
            Multiple decorators can be applied to the same service type. The order in which they are registered
            is the order they get applied in. This means that the decorator that gets registered first, gets
            applied first, which means that the next registered decorator, will wrap the first decorator, which
            wraps the original service type.
            </para>
            <para>
            Constructor injection will be used on that type, and although it may have many constructor 
            arguments, it must have exactly one argument of the type of <paramref name="serviceType"/>, or an 
            argument of type <see cref="T:System.Func`1"/> where <b>TResult</b> is <paramref name="serviceType"/>.
            An exception will be thrown when this is not the case.
            </para>
            <para>
            The registered <paramref name="decoratorType"/> may have a constructor with an argument of type
            <see cref="T:System.Func`1"/> where <b>T</b> is <paramref name="serviceType"/>. In this case, the
            will not inject the decorated <paramref name="serviceType"/> itself into the 
            <paramref name="decoratorType"/> instance, but it will inject a <see cref="T:System.Func`1"/> that allows
            creating instances of the decorated type, according to the lifestyle of that type. This enables
            more advanced scenarios, such as executing the decorated types on a different thread, or executing
            decorated instance within a certain scope (such as a lifetime scope).
            </para>
            </remarks>
            <example>
            Please see the <see cref="M:SimpleInjector.Container.RegisterDecorator(System.Type,System.Type)">RegisterDecorator</see> method
            for more information.
            </example>
            <param name="serviceType">The definition of the (possibly open generic) service type that will
            be wrapped by the given <paramref name="decoratorType"/>.</param>
            <param name="decoratorType">The definition of the (possibly open generic) decorator type that will
            be used to wrap the original service type.</param>
            <param name="predicate">The predicate that determines whether the 
            <paramref name="decoratorType"/> must be applied to a service type.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the arguments is a null reference.</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="decoratorType"/> does not inherit 
            from or implement <paramref name="serviceType"/>, when <paramref name="decoratorType"/>
            does not have a single public constructor, or when <paramref name="decoratorType"/> does 
            not contain a constructor that has exactly one argument of type 
            <paramref name="serviceType"/> or <see cref="T:System.Func`1"/> where <b>T</b> is
            <paramref name="serviceType"/>.</exception>
        </member>
        <member name="M:SimpleInjector.Container.GetInstance``1">
            <summary>Gets an instance of the given <typeparamref name="TService"/>.</summary>
            <typeparam name="TService">Type of object requested.</typeparam>
            <returns>The requested service instance.</returns>
            <exception cref="T:SimpleInjector.ActivationException">Thrown when there are errors resolving the service instance.</exception>
        </member>
        <member name="M:SimpleInjector.Container.GetInstance(System.Type)">
            <summary>Gets an instance of the given <paramref name="serviceType"/>.</summary>
            <param name="serviceType">Type of object requested.</param>
            <returns>The requested service instance.</returns>
            <exception cref="T:SimpleInjector.ActivationException">Thrown when there are errors resolving the service instance.</exception>
        </member>
        <member name="M:SimpleInjector.Container.GetAllInstances``1">
            <summary>
            Gets all instances of the given <typeparamref name="TService"/> currently registered in the container.
            </summary>
            <typeparam name="TService">Type of object requested.</typeparam>
            <returns>A sequence of instances of the requested TService.</returns>
            <exception cref="T:SimpleInjector.ActivationException">Thrown when there are errors resolving the service instance.</exception>
        </member>
        <member name="M:SimpleInjector.Container.GetAllInstances(System.Type)">
            <summary>
            Gets all instances of the given <paramref name="serviceType"/> currently registered in the container.
            </summary>
            <param name="serviceType">Type of object requested.</param>
            <returns>A sequence of instances of the requested serviceType.</returns>
            <exception cref="T:SimpleInjector.ActivationException">Thrown when there are errors resolving the service instance.</exception>
        </member>
        <member name="M:SimpleInjector.Container.System#IServiceProvider#GetService(System.Type)">
            <summary>Gets the service object of the specified type.</summary>
            <param name="serviceType">An object that specifies the type of service object to get.</param>
            <returns>A service object of type serviceType.  -or- null if there is no service object of type 
            <paramref name="serviceType"/>.</returns>
        </member>
        <member name="M:SimpleInjector.Container.GetRegistration(System.Type)">
            <summary>
            Gets the <see cref="T:SimpleInjector.InstanceProducer"/> for the given <paramref name="serviceType"/>. When no
            registration exists, the container will try creating a new producer. A producer can be created
            when the type is a concrete reference type, there is an <see cref="E:SimpleInjector.Container.ResolveUnregisteredType"/>
            event registered that acts on that type, or when the service type is an <see cref="T:System.Collections.Generic.IEnumerable`1"/>.
            Otherwise <b>null</b> (Nothing in VB) is returned.
            </summary>
            <remarks>
            <para>
            A call to this method locks the container. No new registrations can't be made after a call to this 
            method.
            </para>
            <para>
            <b>Note:</b> This method is <i>not</i> guaranteed to always return the same 
            <see cref="T:SimpleInjector.InstanceProducer"/> instance for a given <see cref="T:System.Type"/>. It will however either 
            always return <b>null</b> or always return a producer that is able to return the expected instance.
            </para>
            </remarks>
            <param name="serviceType">The <see cref="T:System.Type"/> that the returned instance producer should produce.</param>
            <returns>An <see cref="T:SimpleInjector.InstanceProducer"/> or <b>null</b> (Nothing in VB).</returns>
        </member>
        <member name="M:SimpleInjector.Container.GetRegistration(System.Type,System.Boolean)">
            <summary>
            Gets the <see cref="T:SimpleInjector.InstanceProducer"/> for the given <paramref name="serviceType"/>. When no
            registration exists, the container will try creating a new producer. A producer can be created
            when the type is a concrete reference type, there is an <see cref="E:SimpleInjector.Container.ResolveUnregisteredType"/>
            event registered that acts on that type, or when the service type is an <see cref="T:System.Collections.Generic.IEnumerable`1"/>.
            Otherwise <b>null</b> (Nothing in VB) is returned, or an exception is throw when
            <paramref name="throwOnFailure"/> is set to <b>true</b>.
            </summary>
            <remarks>
            <para>
            A call to this method locks the container. No new registrations can't be made after a call to this 
            method.
            </para>
            <para>
            <b>Note:</b> This method is <i>not</i> guaranteed to always return the same 
            <see cref="T:SimpleInjector.InstanceProducer"/> instance for a given <see cref="T:System.Type"/>. It will however either 
            always return <b>null</b> or always return a producer that is able to return the expected instance.
            </para>
            </remarks>
            <param name="serviceType">The <see cref="T:System.Type"/> that the returned instance producer should produce.</param>
            <param name="throwOnFailure">The indication whether the method should return null or throw
            an exception when the type is not registered.</param>
            <returns>An <see cref="T:SimpleInjector.InstanceProducer"/> or <b>null</b> (Nothing in VB).</returns>
        </member>
        <member name="M:SimpleInjector.Container.Verify">
            <summary>
            Verifies and diagnoses this <b>Container</b> instance. This method will call all registered 
            delegates, iterate registered collections and throws an exception if there was an error.
            </summary>
            <exception cref="T:System.InvalidOperationException">Thrown when the registration of instances was
            invalid.</exception>
        </member>
        <member name="M:SimpleInjector.Container.Verify(SimpleInjector.VerificationOption)">
            <summary>
            Verifies the <b>Container</b>. This method will call all registered delegates, 
            iterate registered collections and throws an exception if there was an error.
            </summary>
            <param name="option">Specifies how the container should verify its configuration.</param>
            <exception cref="T:System.InvalidOperationException">Thrown when the registration of instances was
            invalid.</exception>
            <exception cref="T:SimpleInjector.DiagnosticVerificationException">Thrown in case there are diagnostic errors and
            the <see cref="F:SimpleInjector.VerificationOption.VerifyAndDiagnose"/> option is supplied.</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="option"/> has an invalid value.</exception>
        </member>
        <member name="T:SimpleInjector.ResolveInterceptor">
            <summary>
            Delegate that allows intercepting calls to <see cref="M:SimpleInjector.Container.GetInstance(System.Type)"/> and 
            <see cref="M:SimpleInjector.InstanceProducer.GetInstance"/>.
            </summary>
            <param name="context">Contextual information about the to be created object.</param>
            <param name="instanceProducer">A delegate that produces the actual instance according to its
            lifestyle settings.</param>
            <returns>The instance that is returned from <paramref name="instanceProducer"/> or an intercepted instance.</returns>
        </member>
        <member name="T:SimpleInjector.ContainerOptions">
            <summary>Configuration options for the <see cref="T:SimpleInjector.Container">Container</see>.</summary>
            <example>
            The following example shows the typical usage of the <b>ContainerOptions</b> class.
            <code lang="cs"><![CDATA[
            var container = new Container();
            
            container.Register<ITimeProvider, DefaultTimeProvider>();
            
            // Use of ContainerOptions class here.
            container.Options.AllowOverridingRegistrations = true;
            
            // Replaces the previous registration of ITimeProvider
            container.Register<ITimeProvider, CustomTimeProvider>();
            ]]></code>
            </example>
        </member>
        <member name="M:SimpleInjector.ContainerOptions.#ctor">
            <summary>Initializes a new instance of the <see cref="T:SimpleInjector.ContainerOptions"/> class.</summary>
        </member>
        <member name="P:SimpleInjector.ContainerOptions.Container">
            <summary>
            Gets the container to which this <b>ContainerOptions</b> instance belongs to.
            </summary>
            <value>The current <see cref="T:SimpleInjector.Container">Container</see>.</value>
        </member>
        <member name="P:SimpleInjector.ContainerOptions.AllowOverridingRegistrations">
            <summary>
            Gets or sets a value indicating whether the container allows overriding registrations. The default
            is false.
            </summary>
            <value>The value indicating whether the container allows overriding registrations.</value>
        </member>
        <member name="P:SimpleInjector.ContainerOptions.SuppressLifestyleMismatchVerification">
            <summary>
            Gets or sets a value indicating whether the container should suppress checking for lifestyle
            mismatches (see: https://simpleinjector.org/dialm) when a component is resolved. The default
            is false.
            </summary>
            <value>The value indicating whether the container should suppress checking for lifestyle
            mismatches.</value>
        </member>
        <member name="P:SimpleInjector.ContainerOptions.ResolveUnregisteredCollections">
            <summary>
            Gets or sets a value indicating whether the container will return an empty collection when
            a collection is requested that hasn't been explicitly registered. The default value is <b>false</b>,
            which means that the container will throw an exception. Set the value to <b>true</b> to get the
            old behavior of Simple Injector v1.x and v2.x.
            </summary>
            <value>The value indicating whether the container will return an empty collection.</value>
        </member>
        <member name="P:SimpleInjector.ContainerOptions.UseFullyQualifiedTypeNames">
            <summary>
            Gets or sets a value indicating whether all the containers in the current AppDomain should throw
            exceptions that contain fully qualified type name. The default is <c>false</c> which means
            the type's namespace is omitted.
            </summary>
            <value>The value indicating whether exception message should emit full type names.</value>
        </member>
        <member name="P:SimpleInjector.ContainerOptions.ConstructorResolutionBehavior">
            <summary>
            Gets or sets the constructor resolution behavior. By default, the container only supports types
            that have a single public constructor.
            </summary>
            <value>The constructor resolution behavior.</value>
            <exception cref="T:System.NullReferenceException">Thrown when the supplied value is a null reference.</exception>
            <exception cref="T:System.InvalidOperationException">
            Thrown when the container already contains registrations.
            </exception>
        </member>
        <member name="P:SimpleInjector.ContainerOptions.ConstructorVerificationBehavior">
            <summary>
            Gets or sets the constructor verification behavior. The container's default behavior is to
            disallow constructors with value types and strings.
            <b>NOTE:</b> This property has been removed. Please use the <see cref="P:SimpleInjector.ContainerOptions.ConstructorInjectionBehavior"/> 
            property to override Simple Injector's verification behavior.
            </summary>
            <value>The constructor resolution behavior.</value>
        </member>
        <member name="P:SimpleInjector.ContainerOptions.ConstructorInjectionBehavior">
            <summary>Gets or sets the constructor injection behavior.</summary>
            <value>The constructor injection behavior.</value>
            <exception cref="T:System.NullReferenceException">Thrown when the supplied value is a null reference.</exception>
            <exception cref="T:System.InvalidOperationException">
            Thrown when the container already contains registrations.
            </exception>
        </member>
        <member name="P:SimpleInjector.ContainerOptions.DependencyInjectionBehavior">
            <summary>Gets or sets the dependency injection behavior.</summary>
            <value>The constructor injection behavior.</value>
            <exception cref="T:System.NullReferenceException">Thrown when the supplied value is a null reference.</exception>
            <exception cref="T:System.InvalidOperationException">
            Thrown when the container already contains registrations.
            </exception>
        </member>
        <member name="P:SimpleInjector.ContainerOptions.PropertySelectionBehavior">
            <summary>
            Gets or sets the property selection behavior. The container's default behavior is to do no
            property injection.
            </summary>
            <value>The property selection behavior.</value>
            <exception cref="T:System.NullReferenceException">Thrown when the supplied value is a null reference.</exception>
            <exception cref="T:System.InvalidOperationException">
            Thrown when the container already contains registrations.
            </exception>
        </member>
        <member name="P:SimpleInjector.ContainerOptions.LifestyleSelectionBehavior">
            <summary>
            Gets or sets the lifestyle selection behavior. The container's default behavior is to make
            registrations using the <see cref="F:SimpleInjector.Lifestyle.Transient"/> lifestyle.</summary>
            <value>The lifestyle selection behavior.</value>
            <exception cref="T:System.NullReferenceException">Thrown when the supplied value is a null reference.</exception>
            <exception cref="T:System.InvalidOperationException">
            Thrown when the container already contains registrations.
            </exception>
        </member>
        <member name="P:SimpleInjector.ContainerOptions.DefaultScopedLifestyle">
            <summary>
            Gets or sets the default scoped lifestyle that the container should use when a registration is
            made using <see cref="F:SimpleInjector.Lifestyle.Scoped">Lifestyle.Scoped</see>.</summary>
            <value>The default scoped lifestyle.</value>
            <exception cref="T:System.NullReferenceException">Thrown when the supplied value is a null reference.</exception>
            <exception cref="T:System.InvalidOperationException">
            Thrown when the container already contains registrations.
            </exception>
        </member>
        <member name="P:SimpleInjector.ContainerOptions.EnableDynamicAssemblyCompilation">
            <summary>
            Gets or sets a value indicating whether the container will use dynamic assemblies for compilation. 
            By default, this value is <b>true</b> for the first few containers that are created in an AppDomain 
            and <b>false</b> for all other containers. You can set this value explicitly to <b>false</b>
            to prevent the use of dynamic assemblies or you can set this value explicitly to <b>true</b> to
            force more container instances to use dynamic assemblies. Note that creating an infinite number
            of <see cref="T:SimpleInjector.Container">Container</see> instances (for instance one per web request) 
            with this property set to <b>true</b> will result in a memory leak; dynamic assemblies take up 
            memory and will only be unloaded when the AppDomain is unloaded.
            </summary>
            <value>A boolean indicating whether the container should use a dynamic assembly for compilation.
            </value>
        </member>
        <member name="M:SimpleInjector.ContainerOptions.RegisterResolveInterceptor(SimpleInjector.ResolveInterceptor,System.Predicate{SimpleInjector.Advanced.InitializationContext})">
            <summary>
            Registers an <see cref="T:SimpleInjector.ResolveInterceptor"/> delegate that allows intercepting calls to
            <see cref="M:SimpleInjector.Container.GetInstance(System.Type)">GetInstance</see> and 
            <see cref="M:SimpleInjector.InstanceProducer.GetInstance"/>.
            </summary>
            <remarks>
            If multiple registered <see cref="T:SimpleInjector.ResolveInterceptor"/> instances must be applied, they will be
            applied/wrapped in the order of registration, i.e. the first registered interceptor will call the 
            original instance producer delegate, the second interceptor will call the first interceptor, etc. 
            The last registered interceptor will become the outermost method in the chain and will be called 
            first.
            </remarks>
            <param name="interceptor">The <see cref="T:SimpleInjector.ResolveInterceptor"/> delegate to register.</param>
            <param name="predicate">The predicate that will be used to check whether the given delegate must
            be applied to a registration or not. The given predicate will be called once for each registration
            in the container.</param>
            <exception cref="T:System.ArgumentNullException">
            Thrown when either the <paramref name="interceptor"/> or <paramref name="predicate"/> are 
            null references.
            </exception>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered.
            </exception>
            <example>
            The following example shows the usage of the <see cref="M:SimpleInjector.ContainerOptions.RegisterResolveInterceptor(SimpleInjector.ResolveInterceptor,System.Predicate{SimpleInjector.Advanced.InitializationContext})" /> method:
            <code lang="cs"><![CDATA[
            var container = new Container();
            
            container.Options.RegisterResolveInterceptor((context, producer) =>
                {
                    object instance = producer.Invoke();
                    Console.WriteLine(instance.GetType().Name + " resolved for " + context.Producer.ServiceType.Name);
                    return instance;
                },
                context => context.Producer.ServiceType.Name.EndsWith("Controller"));
                
            container.Register<IHomeViewModel, HomeViewModel>();
            container.Register<IUserRepository, SqlUserRepository>();
            
            // This line will write "HomeViewModel resolved for IHomeViewModel" to the console.
            container.GetInstance<IHomeViewModel>();
            ]]></code>
            </example>
        </member>
        <member name="M:SimpleInjector.ContainerOptions.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:SimpleInjector.DecoratorContext">
            <summary>
            An instance of this type can be injected into constructors of decorator classes that are registered
            using <see cref="M:SimpleInjector.Container.RegisterDecorator(System.Type,System.Type)">RegisterDecorator</see>. This type contains 
            contextual information about the applied decoration and it allows users to examine the given instance 
            to make runtime decisions.
            </summary>
        </member>
        <member name="P:SimpleInjector.DecoratorContext.ServiceType">
            <summary>
            Gets the closed generic service type for which the decorator is about to be applied. The original
            service type will be returned, even if other decorators have already been applied to this type.
            </summary>
            <value>The closed generic service type.</value>
        </member>
        <member name="P:SimpleInjector.DecoratorContext.ImplementationType">
            <summary>
            Gets the type of the implementation that is created by the container and for which the decorator
            is about to be applied. The original implementation type will be returned, even if other decorators
            have already been applied to this type. Please not that the implementation type can not always be
            determined. In that case the closed generic service type will be returned.
            </summary>
            <value>The implementation type.</value>
        </member>
        <member name="P:SimpleInjector.DecoratorContext.AppliedDecorators">
            <summary>
            Gets the list of the types of decorators that have already been applied to this instance.
            </summary>
            <value>The applied decorators.</value>
        </member>
        <member name="P:SimpleInjector.DecoratorContext.Expression">
            <summary>
            Gets the current <see cref="P:SimpleInjector.DecoratorContext.Expression"/> object that describes the intention to create a new
            instance with its currently applied decorators.
            </summary>
            <value>The current expression that is about to be decorated.</value>
        </member>
        <member name="T:SimpleInjector.DecoratorPredicateContext">
            <summary>
            An instance of this type will be supplied to the <see cref="T:System.Predicate`1"/>
            delegate that is that is supplied to the 
            <see cref="M:SimpleInjector.Container.RegisterDecorator(System.Type,System.Type,System.Predicate{SimpleInjector.DecoratorPredicateContext})">RegisterDecorator</see>
            overload that takes this delegate. This type contains information about the decoration that is about
            to be applied and it allows users to examine the given instance to see whether the decorator should
            be applied or not.
            </summary>
            <remarks>
            Please see the 
            <see cref="M:SimpleInjector.Container.RegisterDecorator(System.Type,System.Type,System.Predicate{SimpleInjector.DecoratorPredicateContext})">RegisterDecorator</see>
            method for more information.
            </remarks>
        </member>
        <member name="P:SimpleInjector.DecoratorPredicateContext.ServiceType">
            <summary>
            Gets the closed generic service type for which the decorator is about to be applied. The original
            service type will be returned, even if other decorators have already been applied to this type.
            </summary>
            <value>The closed generic service type.</value>
        </member>
        <member name="P:SimpleInjector.DecoratorPredicateContext.ImplementationType">
            <summary>
            Gets the type of the implementation that is created by the container and for which the decorator
            is about to be applied. The original implementation type will be returned, even if other decorators
            have already been applied to this type. Please not that the implementation type can not always be
            determined. In that case the closed generic service type will be returned.
            </summary>
            <value>The implementation type.</value>
        </member>
        <member name="P:SimpleInjector.DecoratorPredicateContext.AppliedDecorators">
            <summary>
            Gets the list of the types of decorators that have already been applied to this instance.
            </summary>
            <value>The applied decorators.</value>
        </member>
        <member name="P:SimpleInjector.DecoratorPredicateContext.Expression">
            <summary>
            Gets the current <see cref="P:SimpleInjector.DecoratorPredicateContext.Expression"/> object that describes the intention to create a new
            instance with its currently applied decorators.
            </summary>
            <value>The current expression that is about to be decorated.</value>
        </member>
        <member name="T:SimpleInjector.Decorators.DecoratorExpressionInterceptor">
            <summary>
            Hooks into the building process and adds a decorator if needed.
            </summary>
        </member>
        <member name="T:SimpleInjector.Diagnostics.AmbiguousLifestylesDiagnosticResult">
            <summary>
            Diagnostic result that warns about when a multiple registrations map to the same implementation type 
            and lifestyle, which might cause multiple instances to be created during the lifespan of that lifestyle.
            For more information, see: https://simpleinjector.org/diaal.
            </summary>
        </member>
        <member name="P:SimpleInjector.Diagnostics.AmbiguousLifestylesDiagnosticResult.Lifestyles">
            <summary>Gets the lifestyles that causes the registrations to be conflicting.</summary>
            <value><see cref="T:SimpleInjector.Lifestyle"/> instances.</value>
        </member>
        <member name="P:SimpleInjector.Diagnostics.AmbiguousLifestylesDiagnosticResult.ImplementationType">
            <summary>Gets the implementation type that the affected registrations map to.</summary>
            <value>A <see cref="T:System.Type"/>.</value>
        </member>
        <member name="P:SimpleInjector.Diagnostics.AmbiguousLifestylesDiagnosticResult.DiagnosedRegistration">
            <summary>Gets the registration that caused this warning.</summary>
            /// <value>An <see cref="T:SimpleInjector.InstanceProducer"/>.</value>
        </member>
        <member name="P:SimpleInjector.Diagnostics.AmbiguousLifestylesDiagnosticResult.ConflictingRegistrations">
            <summary>
            Gets the list of registrations that are in conflict with the <see cref="P:SimpleInjector.Diagnostics.AmbiguousLifestylesDiagnosticResult.DiagnosedRegistration"/>.
            </summary>
            <value>A list of <see cref="T:SimpleInjector.InstanceProducer"/> instances.</value>
        </member>
        <member name="T:SimpleInjector.Diagnostics.Analyzer">
            <summary>
            Entry point for doing diagnostic analysis on <see cref="T:SimpleInjector.Container"/> instances.
            </summary>
            <example>
            The following example shows the usage of the <b>Analyzer</b> class:
            <code lang="cs"><![CDATA[
            DiagnosticResult[] results = Analyzer.Analyze(container);
            
            var typesWithAShortedLifetimeThanTheirDependencies =
                from result in results
                where result.DiagnosticType == DiagnosticType.LifestyleMismatch
                let mismatch = (LifestyleMismatchDiagnosticResult)result
                select mismatch.Relationship.ImplementationType;
            ]]></code>
            </example>
        </member>
        <member name="M:SimpleInjector.Diagnostics.Analyzer.Analyze(SimpleInjector.Container)">
            <summary>
            Analyzes the supplied <paramref name="container"/> instance.
            </summary>
            <param name="container">The container instance to analyze.</param>
            <returns>A collection of <see cref="T:SimpleInjector.Diagnostics.DiagnosticResult"/> sub types that describe the diagnostic
            warnings and messages.</returns>
        </member>
        <member name="T:SimpleInjector.Diagnostics.ContainerRegisteredServiceDiagnosticResult">
            <summary>
            Diagnostic result for a warning about a concrete type that was not registered explicitly and was not 
            resolved using unregistered type resolution, but was created by the container using the transient 
            lifestyle.
            For more information, see: https://simpleinjector.org/diaut.
            </summary>
        </member>
        <member name="P:SimpleInjector.Diagnostics.ContainerRegisteredServiceDiagnosticResult.Relationships">
            <summary>Gets a collection of <see cref="T:SimpleInjector.Advanced.KnownRelationship"/> instances that describe all 
            container-registered dependencies for the given component.</summary>
            <value>List of <see cref="T:SimpleInjector.Advanced.KnownRelationship"/> objects.</value>
        </member>
        <member name="T:SimpleInjector.Diagnostics.DiagnosticGroup">
            <summary>
            A hierarchical group of <see cref="T:SimpleInjector.Diagnostics.DiagnosticResult"/>.
            </summary>
        </member>
        <member name="P:SimpleInjector.Diagnostics.DiagnosticGroup.GroupType">
            <summary>
            Gets the base <see cref="P:SimpleInjector.Diagnostics.DiagnosticGroup.DiagnosticType"/> that describes the service types of its 
            <see cref="P:SimpleInjector.Diagnostics.DiagnosticGroup.Results"/>. The value often be either <see cref="T:System.Object"/> (in case this is a
            root group) or a partial generic type to allow hierarchical grouping of a large number of related
            generic types.
            </summary>
            <value>The <see cref="T:System.Type"/>.</value>
        </member>
        <member name="P:SimpleInjector.Diagnostics.DiagnosticGroup.Name">
            <summary>Gets the friendly name of the group.</summary>
            <value>The name.</value>
        </member>
        <member name="P:SimpleInjector.Diagnostics.DiagnosticGroup.Description">
            <summary>Gets the description of the group.</summary>
            <value>The description.</value>
        </member>
        <member name="P:SimpleInjector.Diagnostics.DiagnosticGroup.DiagnosticType">
            <summary>Gets the diagnostic type of all grouped <see cref="T:SimpleInjector.Diagnostics.DiagnosticResult"/> instances.</summary>
            <value>The <see cref="P:SimpleInjector.Diagnostics.DiagnosticGroup.DiagnosticType"/>.</value>
        </member>
        <member name="P:SimpleInjector.Diagnostics.DiagnosticGroup.Parent">
            <summary>Gets the parent <see cref="T:SimpleInjector.Diagnostics.DiagnosticGroup"/> or null (Nothing in VB) when this is the
            root group.</summary>
            <value>The <see cref="T:SimpleInjector.Diagnostics.DiagnosticGroup"/>.</value>
        </member>
        <member name="P:SimpleInjector.Diagnostics.DiagnosticGroup.Children">
            <summary>Gets the collection of child <see cref="T:SimpleInjector.Diagnostics.DiagnosticGroup"/>s.</summary>
            <value>A collection of <see cref="T:SimpleInjector.Diagnostics.DiagnosticGroup"/> elements.</value>
        </member>
        <member name="P:SimpleInjector.Diagnostics.DiagnosticGroup.Results">
            <summary>Gets the collection of <see cref="T:SimpleInjector.Diagnostics.DiagnosticResult"/> instances.</summary>
            /// <value>A collection of <see cref="T:SimpleInjector.Diagnostics.DiagnosticResult"/> elements.</value>
        </member>
        <member name="T:SimpleInjector.Diagnostics.DiagnosticResult">
            <summary>
            Base class for types that hold information about a single diagnostic message or warning for a
            particular type or part of the configuration.
            </summary>
        </member>
        <member name="P:SimpleInjector.Diagnostics.DiagnosticResult.Severity">
            <summary>Gets the severity of this result.</summary>
            <value>The <see cref="T:SimpleInjector.Diagnostics.DiagnosticSeverity"/>.</value>
        </member>
        <member name="P:SimpleInjector.Diagnostics.DiagnosticResult.DiagnosticType">
            <summary>Gets the diagnostic type of this result.</summary>
            <value>The <see cref="P:SimpleInjector.Diagnostics.DiagnosticResult.DiagnosticType"/>.</value>
        </member>
        <member name="P:SimpleInjector.Diagnostics.DiagnosticResult.ServiceType">
            <summary>Gets the service type to which this warning is related.</summary>
            <value>A <see cref="T:System.Type"/>.</value>
        </member>
        <member name="P:SimpleInjector.Diagnostics.DiagnosticResult.Description">
            <summary>Gets the description of the diagnostic result.</summary>
            <value>A <see cref="T:System.String"/> with the description.</value>
        </member>
        <member name="P:SimpleInjector.Diagnostics.DiagnosticResult.DocumentationUrl">
            <summary>Gets the documentation URL of the diagnostic result.</summary>
            <value>A <see cref="T:System.String"/> with the URL.</value>
        </member>
        <member name="P:SimpleInjector.Diagnostics.DiagnosticResult.Group">
            <summary>Gets the hierarchical group to which this diagnostic result belongs.</summary>
            <value>The <see cref="T:SimpleInjector.Diagnostics.DiagnosticGroup"/>.</value>
        </member>
        <member name="T:SimpleInjector.Diagnostics.DiagnosticSeverity">
            <summary>
            Specifies the list of severity levels that diagnostic results can have.
            </summary>
        </member>
        <member name="F:SimpleInjector.Diagnostics.DiagnosticSeverity.Information">
            <summary>Information messages and tips about the configuration.</summary>
        </member>
        <member name="F:SimpleInjector.Diagnostics.DiagnosticSeverity.Warning">
            <summary>Warning messages that are likely to cause problems in your application.</summary>
        </member>
        <member name="T:SimpleInjector.Diagnostics.DiagnosticType">
            <summary>
            Specifies the list of diagnostic types that are currently supported by the diagnostic 
            <see cref="T:SimpleInjector.Diagnostics.Analyzer"/>. Note that new diagnostic types might be added in future versions.
            For more information, please read the 
            <a href="https://simpleinjector.org/diagnostics">Diagnosing your configuration using the Diagnostic 
            Services</a> wiki documentation.
            </summary>
        </member>
        <member name="F:SimpleInjector.Diagnostics.DiagnosticType.ContainerRegisteredComponent">
            <summary>
            Diagnostic type that warns about 
            a concrete type that was not registered explicitly and was not resolved using unregistered type 
            resolution, but was created by the container using the transient lifestyle.
            For more information, see: https://simpleinjector.org/diaut.
            </summary>
        </member>
        <member name="F:SimpleInjector.Diagnostics.DiagnosticType.LifestyleMismatch">
            <summary>
            Diagnostic type that warns when a 
            component depends on a service with a lifestyle that is shorter than that of the component.
            For more information, see: https://simpleinjector.org/dialm.
            </summary>
        </member>
        <member name="F:SimpleInjector.Diagnostics.DiagnosticType.PotentialLifestyleMismatch">
            <summary>
            This enum options has been replaced with <see cref="F:SimpleInjector.Diagnostics.DiagnosticType.LifestyleMismatch"/>.
            </summary>
        </member>
        <member name="F:SimpleInjector.Diagnostics.DiagnosticType.ShortCircuitedDependency">
            <summary>
            Diagnostic type that warns when a
            component depends on an unregistered concrete type and this concrete type has a lifestyle that is 
            different than the lifestyle of an explicitly registered type that uses this concrete type as its 
            implementation.
            For more information, see: https://simpleinjector.org/diasc.
            </summary>
        </member>
        <member name="F:SimpleInjector.Diagnostics.DiagnosticType.SingleResponsibilityViolation">
            <summary>
            Diagnostic type that warns when a component depends on (too) many services.
            For more information, see: https://simpleinjector.org/diasr.
            </summary>
        </member>
        <member name="F:SimpleInjector.Diagnostics.DiagnosticType.TornLifestyle">
            <summary>
            Diagnostic type that warns when multiple registrations map to the same component and
            lifestyle, which might cause multiple instances to be created during the lifespan of that lifestyle.
            For more information, see: https://simpleinjector.org/diatl.
            </summary>
        </member>
        <member name="F:SimpleInjector.Diagnostics.DiagnosticType.DisposableTransientComponent">
            <summary>
            Diagnostic type that warns when a component is registered as transient, while implementing
            <see cref="T:System.IDisposable"/>.
            For more information, see: https://simpleinjector.org/diadt.
            </summary>
        </member>
        <member name="F:SimpleInjector.Diagnostics.DiagnosticType.AmbiguousLifestyles">
            <summary>
            Diagnostic type that warns when multiple registrations exist that map to the same component but
            with different lifestyles, which will cause the component to be cached in different -possibly
            incompatible- ways.
            For more information, see: https://simpleinjector.org/diaal.
            </summary>
        </member>
        <member name="T:SimpleInjector.Diagnostics.DisposableTransientComponentDiagnosticResult">
            <summary>
            Diagnostic result for a warning about a component that is registered as transient, but implements 
            <see cref="T:System.IDisposable"/>.
            For more information, see: https://simpleinjector.org/diadt.
            </summary>
        </member>
        <member name="P:SimpleInjector.Diagnostics.DisposableTransientComponentDiagnosticResult.Registration">
            <summary>Gets the object that describes the relationship between the component and its dependency.</summary>
            <value>A <see cref="T:SimpleInjector.Advanced.KnownRelationship"/> instance.</value>
        </member>
        <member name="T:SimpleInjector.Diagnostics.LifestyleMismatchDiagnosticResult">
            <summary>
            Diagnostic result for a warning about a
            component that depends on a service with a lifestyle that is shorter than that of the component.
            For more information, see: https://simpleinjector.org/dialm.
            </summary>
        </member>
        <member name="P:SimpleInjector.Diagnostics.LifestyleMismatchDiagnosticResult.Relationship">
            <summary>Gets the object that describes the relationship between the component and its dependency.</summary>
            <value>A <see cref="T:SimpleInjector.Advanced.KnownRelationship"/> instance.</value>
        </member>
        <member name="T:SimpleInjector.Diagnostics.PotentialLifestyleMismatchDiagnosticResult">
            <summary>
            This type is obsolete. Please use <see cref="T:SimpleInjector.Diagnostics.LifestyleMismatchDiagnosticResult"/> instead.
            </summary>
        </member>
        <member name="P:SimpleInjector.Diagnostics.PotentialLifestyleMismatchDiagnosticResult.Relationship">
            <summary>Gets the object that describes the relationship between the component and its dependency.</summary>
            <value>A <see cref="T:SimpleInjector.Advanced.KnownRelationship"/> instance.</value>
        </member>
        <member name="T:SimpleInjector.Diagnostics.ShortCircuitedDependencyDiagnosticResult">
            <summary>
            Diagnostic result that warns about a
            component that depends on an unregistered concrete type and this concrete type has a lifestyle that is 
            different than the lifestyle of an explicitly registered type that uses this concrete type as its 
            implementation.
            For more information, see: https://simpleinjector.org/diasc.
            </summary>
        </member>
        <member name="P:SimpleInjector.Diagnostics.ShortCircuitedDependencyDiagnosticResult.Relationship">
            <summary>Gets the instance that describes the current relationship between the checked component
            and the short-circuited dependency.</summary>
            <value>The <see cref="T:SimpleInjector.Advanced.KnownRelationship"/>.</value>
        </member>
        <member name="P:SimpleInjector.Diagnostics.ShortCircuitedDependencyDiagnosticResult.ExpectedDependencies">
            <summary>
            Gets the collection of registrations that have the component's current dependency as 
            implementation type, but have a lifestyle that is different than the current dependency.
            </summary>
            <value>A collection of <see cref="T:SimpleInjector.InstanceProducer"/> instances.</value>
        </member>
        <member name="T:SimpleInjector.Diagnostics.SingleResponsibilityViolationDiagnosticResult">
            <summary>
            Diagnostic result that warns about a component that depends on (too) many services.
            For more information, see: https://simpleinjector.org/diasr.
            </summary>
        </member>
        <member name="P:SimpleInjector.Diagnostics.SingleResponsibilityViolationDiagnosticResult.ImplementationType">
            <summary>Gets the created type.</summary>
            <value>A <see cref="T:System.Type"/>.</value>
        </member>
        <member name="P:SimpleInjector.Diagnostics.SingleResponsibilityViolationDiagnosticResult.Dependencies">
            <summary>Gets the list of registrations that are dependencies of the <see cref="P:SimpleInjector.Diagnostics.SingleResponsibilityViolationDiagnosticResult.ImplementationType"/>.</summary>
            <value>A collection of <see cref="T:SimpleInjector.InstanceProducer"/> instances.</value>
        </member>
        <member name="T:SimpleInjector.Diagnostics.TornLifestyleDiagnosticResult">
            <summary>
            Diagnostic result that warns about when a multiple registrations map to the same implementation type 
            and lifestyle, which might cause multiple instances to be created during the lifespan of that lifestyle.
            For more information, see: https://simpleinjector.org/diatl.
            </summary>
        </member>
        <member name="P:SimpleInjector.Diagnostics.TornLifestyleDiagnosticResult.Lifestyle">
            <summary>Gets the lifestyle on which instances are torn.</summary>
            <value>A <see cref="P:SimpleInjector.Diagnostics.TornLifestyleDiagnosticResult.Lifestyle"/>.</value>
        </member>
        <member name="P:SimpleInjector.Diagnostics.TornLifestyleDiagnosticResult.ImplementationType">
            <summary>Gets the implementation type that the affected registrations map to.</summary>
            <value>A <see cref="T:System.Type"/>.</value>
        </member>
        <member name="P:SimpleInjector.Diagnostics.TornLifestyleDiagnosticResult.AffectedRegistrations">
            <summary>Gets the list of registrations that are affected by this warning.</summary>
            <value>A list of <see cref="T:SimpleInjector.InstanceProducer"/> instances.</value>
        </member>
        <member name="T:SimpleInjector.DiagnosticVerificationException">
            <summary>
            Thrown by the container in case of a diagnostic error.
            </summary>
        </member>
        <member name="M:SimpleInjector.DiagnosticVerificationException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SimpleInjector.DiagnosticVerificationException" /> class.
            </summary>
        </member>
        <member name="M:SimpleInjector.DiagnosticVerificationException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:SimpleInjector.DiagnosticVerificationException" /> class with a specified error 
            message.
            </summary>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:SimpleInjector.DiagnosticVerificationException.#ctor(System.Collections.Generic.IList{SimpleInjector.Diagnostics.DiagnosticResult})">
            <summary>
            Initializes a new instance of the <see cref="T:SimpleInjector.DiagnosticVerificationException" /> class with a specified error 
            message.
            </summary>
            <param name="errors">The list of errors.</param>
        </member>
        <member name="M:SimpleInjector.DiagnosticVerificationException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:SimpleInjector.DiagnosticVerificationException" /> class with a specified error 
            message and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="message">
            The error message that explains the reason for the exception. 
            </param>
            <param name="innerException">
            The exception that is the cause of the current exception, or a null reference (Nothing in Visual 
            Basic) if no inner exception is specified. 
            </param>
        </member>
        <member name="P:SimpleInjector.DiagnosticVerificationException.Errors">
            <summary>Gets the list of <see cref="T:SimpleInjector.Diagnostics.DiagnosticResult"/> instances.</summary>
            <value>A list of <see cref="T:SimpleInjector.Diagnostics.DiagnosticResult"/> instances.</value>
        </member>
        <member name="M:SimpleInjector.DiagnosticVerificationException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:SimpleInjector.DiagnosticVerificationException" /> class with serialized data.
            </summary>
            <param name="info">
            The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception 
            being thrown. 
            </param>
            <param name="context">
            The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or 
            destination. 
            </param>
            <exception cref="T:System.ArgumentNullException">
            The <paramref name="info" /> parameter is null. 
            </exception>
            <exception cref="T:System.Runtime.Serialization.SerializationException">
            The class name is null or hresult is zero (0). 
            </exception>
        </member>
        <member name="T:SimpleInjector.ExpressionBuildingEventArgs">
            <summary>
            Provides data for and interaction with the 
            <see cref="E:SimpleInjector.Container.ExpressionBuilding">ExpressionBuilding</see> event of 
            the <see cref="T:SimpleInjector.Container"/>. An observer can change the 
            <see cref="P:SimpleInjector.ExpressionBuildingEventArgs.Expression"/> property to change the component that is 
            currently being built.
            </summary>
        </member>
        <member name="P:SimpleInjector.ExpressionBuildingEventArgs.RegisteredServiceType">
            <summary>Gets the registered service type that is currently requested.</summary>
            <value>The registered service type that is currently requested.</value>
        </member>
        <member name="P:SimpleInjector.ExpressionBuildingEventArgs.KnownImplementationType">
            <summary>
            Gets the type that is known to be returned by the 
            <see cref="P:SimpleInjector.ExpressionBuildingEventArgs.Expression">Expression</see> (most often the implementation
            type used in the <b>Register</b> call). This type will be a derivative of
            <see cref="P:SimpleInjector.ExpressionBuildingEventArgs.RegisteredServiceType">RegisteredServiceType</see> (or
            or <b>RegisteredServiceType</b> itself). If the <b>Expression</b> is changed, the new expression 
            must also return an instance of type <b>KnownImplementationType</b> or a sub type. 
            This information must be described in the new Expression.
            </summary>
            <value>A <see cref="T:System.Type"/>.</value>
        </member>
        <member name="P:SimpleInjector.ExpressionBuildingEventArgs.Lifestyle">
            <summary>Gets the lifestyle for the component that is currently being built.</summary>
            <value>The <see cref="P:SimpleInjector.ExpressionBuildingEventArgs.Lifestyle"/>.</value>
        </member>
        <member name="P:SimpleInjector.ExpressionBuildingEventArgs.Expression">
            <summary>Gets or sets the currently registered 
            <see cref="T:System.Linq.Expressions.Expression">Expression</see>.</summary>
            <value>The current registration.</value>
            <exception cref="T:System.ArgumentNullException">Thrown when the supplied value is a null reference.</exception>
        </member>
        <member name="P:SimpleInjector.ExpressionBuildingEventArgs.KnownRelationships">
            <summary>
            Gets the collection of currently known relationships. This information is used by the Diagnostics 
            Debug View. Change the contents of this collection to represent the changes made to the
            <see cref="P:SimpleInjector.ExpressionBuildingEventArgs.Expression">Expression</see> property (if any). This allows
            the Diagnostics Debug View to analyze those new relationships as well.
            </summary>
            <value>The collection of <see cref="T:SimpleInjector.Advanced.KnownRelationship"/> instances.</value>
        </member>
        <member name="T:SimpleInjector.ExpressionBuiltEventArgs">
            <summary>
            Provides data for and interaction with the 
            <see cref="E:SimpleInjector.Container.ExpressionBuilt">ExpressionBuilt</see> event of 
            the <see cref="T:SimpleInjector.Container"/>. An observer can change the 
            <see cref="P:SimpleInjector.ExpressionBuiltEventArgs.Expression"/> property to change the component that is currently 
            being built. 
            </summary>
        </member>
        <member name="M:SimpleInjector.ExpressionBuiltEventArgs.#ctor(System.Type,System.Linq.Expressions.Expression)">
            <summary>Initializes a new instance of the <see cref="T:SimpleInjector.ExpressionBuiltEventArgs"/> class.</summary>
            <param name="registeredServiceType">Type of the registered service.</param>
            <param name="expression">The registered expression.</param>
        </member>
        <member name="P:SimpleInjector.ExpressionBuiltEventArgs.RegisteredServiceType">
            <summary>Gets the registered service type that is currently requested.</summary>
            <value>The registered service type that is currently requested.</value>
        </member>
        <member name="P:SimpleInjector.ExpressionBuiltEventArgs.Expression">
            <summary>Gets or sets the currently registered 
            <see cref="T:System.Linq.Expressions.Expression">Expression</see>.</summary>
            <value>The current registration.</value>
            <exception cref="T:System.ArgumentNullException">Thrown when the supplied value is a null reference.</exception>
        </member>
        <member name="P:SimpleInjector.ExpressionBuiltEventArgs.Lifestyle">
            <summary>Gets or sets the current lifestyle of the registration.</summary>
            <value>The original lifestyle of the registration.</value>
        </member>
        <member name="P:SimpleInjector.ExpressionBuiltEventArgs.KnownRelationships">
            <summary>
            Gets the collection of currently known relationships. This information is used by the Diagnostics 
            Debug View. Change the contents of this collection to represent the changes made to the
            <see cref="P:SimpleInjector.ExpressionBuiltEventArgs.Expression">Expression</see> property (if any). This allows
            the Diagnostics Debug View to analyze those new relationships as well.
            </summary>
            <value>The collection of <see cref="T:SimpleInjector.Advanced.KnownRelationship"/> instances.</value>
        </member>
        <member name="T:SimpleInjector.Helpers">
            <summary>
            Helper methods for the container.
            </summary>
        </member>
        <member name="M:SimpleInjector.Helpers.GetBaseTypeCandidates(System.Type,System.Type)">
            <summary>
            Returns a list of base types and interfaces of implementationType that either
            equal to serviceType or are closed or partially closed version of serviceType (in case 
            serviceType itself is generic).
            So:
            -in case serviceType is non generic, only serviceType will be returned.
            -If implementationType is open generic, serviceType will be returned (or a partially closed 
             version of serviceType is returned).
            -If serviceType is generic and implementationType is not, a closed version of serviceType will
             be returned.
            -If implementationType implements multiple (partially) closed versions of serviceType, all those
             (partially) closed versions will be returned.
            </summary>
            <param name="serviceType">The (open generic) service type to match.</param>
            <param name="implementationType">The implementationType to search.</param>
            <returns>A list of types.</returns>
        </member>
        <member name="T:SimpleInjector.InjectionConsumerInfo">
            <summary>
            Contains contextual information about the direct consumer for which the given dependency is injected
            into.
            </summary>
        </member>
        <member name="M:SimpleInjector.InjectionConsumerInfo.#ctor(System.Type,System.Type,System.Reflection.ParameterInfo)">
            <summary>Initializes a new instance of the <see cref="T:SimpleInjector.InjectionConsumerInfo"/> class.</summary>
            <param name="serviceType">The service type of the consumer of the component that should be created.</param>
            <param name="implementationType">The implementation type of the consumer of the component that should be created.</param>
            <param name="parameter">The constructor parameter for the created component.</param>
        </member>
        <member name="P:SimpleInjector.InjectionConsumerInfo.ServiceType">
            <summary>Gets the service type of the consumer of the component that should be created.</summary>
            <value>The closed generic service type.</value>
        </member>
        <member name="P:SimpleInjector.InjectionConsumerInfo.ImplementationType">
            <summary>Gets the implementation type of the consumer of the component that should be created.</summary>
            <value>The implementation type.</value>
        </member>
        <member name="P:SimpleInjector.InjectionConsumerInfo.Target">
            <summary>
            Gets the information about the consumer's target in which the dependency is injected. The target
            can be either a property or a constructor parameter.
            </summary>
            <value>The <see cref="T:SimpleInjector.InjectionTargetInfo"/> for this context.</value>
        </member>
        <member name="M:SimpleInjector.InjectionConsumerInfo.ToString">
            <summary>Returns a string that represents the <see cref="T:SimpleInjector.InjectionConsumerInfo"/>.</summary>
            <returns>A string.</returns>
        </member>
        <member name="T:SimpleInjector.InjectionTargetInfo">
            <summary>
            Discovers the attributes of the code element (a property or parameter) where a dependency will be
            injected into, and provides access to its meta data.
            </summary>
        </member>
        <member name="P:SimpleInjector.InjectionTargetInfo.Parameter">
            <summary>Gets the constructor argument of the consumer of the component where the dependency will be
            injected into. The property can return null.</summary>
            <value>The <see cref="T:System.Reflection.ParameterInfo"/> or null when the dependency is injected into a property.</value>
        </member>
        <member name="P:SimpleInjector.InjectionTargetInfo.Property">
            <summary>Gets the property of the consumer of the component where the dependency will be injected into. 
            The property can return null.</summary>
            <value>The <see cref="T:System.Reflection.PropertyInfo"/> or null when the dependency is injected into a constructor
            argument instead.</value>
        </member>
        <member name="P:SimpleInjector.InjectionTargetInfo.Name">
            <summary>Gets the name of the target.</summary>
            <value>A string containing the name of the target.</value>
        </member>
        <member name="P:SimpleInjector.InjectionTargetInfo.TargetType">
            <summary>Gets the type of the target.</summary>
            <value>A <see cref="T:System.Type"/> containing the type of the target.</value>
        </member>
        <member name="P:SimpleInjector.InjectionTargetInfo.Member">
            <summary>Gets the member of the target. This is either the constructor of the parameter, or in
            case the target is a property, the property itself will be returned.</summary>
            <value>A <see cref="P:SimpleInjector.InjectionTargetInfo.TargetType"/> containing the type of the target.</value>
        </member>
        <member name="M:SimpleInjector.InjectionTargetInfo.GetCustomAttributes(System.Boolean)">
            <summary>
            Returns an array of all of the custom attributes defined on either the <see cref="P:SimpleInjector.InjectionTargetInfo.Parameter"/> or
            the <see cref="P:SimpleInjector.InjectionTargetInfo.Property"/>, excluding named attributes, or an empty array if there are no custom 
            attributes.
            </summary>
            <param name="inherit">When true, look up the hierarchy chain for the inherited custom attribute.</param>
            <returns>An array of Objects representing custom attributes, or an empty array.</returns>
            <exception cref="T:System.TypeLoadException">The custom attribute type cannot be loaded.</exception>
            <exception cref="T:System.Reflection.AmbiguousMatchException">There is more than one attribute of type attributeType 
            defined on this member.</exception>
        </member>
        <member name="M:SimpleInjector.InjectionTargetInfo.GetCustomAttributes(System.Type,System.Boolean)">
            <summary>
            Returns an array of custom attributes defined on either the <see cref="P:SimpleInjector.InjectionTargetInfo.Parameter"/> or
            the <see cref="P:SimpleInjector.InjectionTargetInfo.Property"/>, identified by type, or an empty array if there are no custom 
            attributes of that type.
            </summary>
            <param name="attributeType">The type of the custom attributes.</param>
            <param name="inherit">When true, look up the hierarchy chain for the inherited custom attribute.</param>
            <returns>An array of Objects representing custom attributes, or an empty array.</returns>
            <exception cref="T:System.TypeLoadException">The custom attribute type cannot be loaded.</exception>
            <exception cref="T:System.ArgumentNullException">attributeType is null.</exception>
        </member>
        <member name="M:SimpleInjector.InjectionTargetInfo.IsDefined(System.Type,System.Boolean)">
            <summary>
            Indicates whether one or more instance of attributeType is defined on this either the 
            <see cref="P:SimpleInjector.InjectionTargetInfo.Parameter"/> or the <see cref="P:SimpleInjector.InjectionTargetInfo.Property"/>.
            </summary>
            <param name="attributeType">The type of the custom attributes.</param>
            <param name="inherit">When true, look up the hierarchy chain for the inherited custom attribute.</param>
            <returns>true if the attributeType is defined on this member; false otherwise.</returns>
        </member>
        <member name="M:SimpleInjector.InjectionTargetInfo.GetCustomAttribute``1">
            <summary>
            Retrieves a custom attribute of a specified type that is applied to a specified parameter.
            </summary>
            <typeparam name="T">The parameter to inspect.</typeparam>
            <returns>A custom attribute that matches T, or null if no such attribute is found.</returns>
        </member>
        <member name="M:SimpleInjector.InjectionTargetInfo.GetCustomAttribute``1(System.Boolean)">
            <summary>
            Retrieves a custom attribute of a specified type that is applied to a specified parameter, and 
            optionally inspects the ancestors of that parameter.
            </summary>
            <typeparam name="T">The parameter to inspect.The parameter to inspect.</typeparam>
            <param name="inherit">True to inspect the ancestors of element; otherwise, false.</param>
            <returns>A custom attribute that matches T, or null if no such attribute is found.</returns>
        </member>
        <member name="M:SimpleInjector.InjectionTargetInfo.GetCustomAttribute(System.Type)">
            <summary>
            Retrieves a custom attribute of a specified type that is applied to a specified parameter.
            </summary>
            <param name="attributeType">The type of attribute to search for.</param>
            <returns>A custom attribute that matches attributeType, or null if no such attribute is found.</returns>
        </member>
        <member name="M:SimpleInjector.InjectionTargetInfo.GetCustomAttribute(System.Type,System.Boolean)">
            <summary>
            Retrieves a custom attribute of a specified type that is applied to a specified parameter, and 
            optionally inspects the ancestors of that parameter.
            </summary>
            <param name="attributeType">The type of attribute to search for.</param>
            <param name="inherit">True to inspect the ancestors of element; otherwise, false.</param>
            <returns>A custom attribute matching attributeType, or null if no such attribute is found.</returns>
        </member>
        <member name="M:SimpleInjector.InjectionTargetInfo.GetCustomAttributes``1">
            <summary>
            Retrieves a collection of custom attributes of a specified type that are applied to a specified parameter.
            </summary>
            <typeparam name="T">The type of attribute to search for.</typeparam>
            <returns>A collection of the custom attributes that are applied to element and that match T, or 
            an empty collection if no such attributes exist.</returns>
        </member>
        <member name="M:SimpleInjector.InjectionTargetInfo.GetCustomAttributes``1(System.Boolean)">
            <summary>
            Retrieves a collection of custom attributes of a specified type that are applied to a specified 
            parameter, and optionally inspects the ancestors of that parameter.
            </summary>
            <typeparam name="T">The type of attribute to search for.</typeparam>
            <param name="inherit">True to inspect the ancestors of element; otherwise, false.</param>
            <returns>A collection of the custom attributes that are applied to element and that match T, or an 
            empty collection if no such attributes exist.</returns>
        </member>
        <member name="T:SimpleInjector.InstanceProducer">
            <summary>
            Produces instances for a given registration. Instances of this type are generally created by the
            container when calling one of the <b>Register</b> overloads. Instances can be retrieved by calling
            <see cref="M:SimpleInjector.Container.GetCurrentRegistrations">GetCurrentRegistrations()</see> or 
            <see cref="M:SimpleInjector.Container.GetRegistration(System.Type,System.Boolean)">GetRegistration(Type, bool)</see>.
            </summary>
            <remarks>
            The <b>Register</b> method overloads create <b>InstanceProducer</b> instances internally, but
            <b>InstanceProducer</b>s can be created manually to implement special scenarios. An 
            <b>InstanceProducer</b> wraps <see cref="P:SimpleInjector.InstanceProducer.Registration"/> instance. The <b>Registration</b> builds an
            <see cref="T:System.Linq.Expressions.Expression"/> that describes the intend to create the instance according to a certain
            lifestyle. The <b>InstanceProducer</b> on the other hand transforms this <b>Expression</b> to a
            delegate and allows the actual instance to be created. A <b>Registration</b> itself can't create any
            instance. The <b>InsanceProducer</b> allows intercepting created instances by hooking onto the
            <see cref="E:SimpleInjector.Container.ExpressionBuilt">Container.ExpressionBuilt</see> event. The
            <see cref="M:SimpleInjector.Container.RegisterDecorator(System.Type,System.Type)">RegisterDecorator</see> methods for 
            instance work by hooking onto the <b>ExpressionBuilt</b> event and allow wrapping the returned instance 
            with a decorator.
            </remarks>
            <example>
            The following example shows the creation of two different <b>InstanceProducer</b> instances that wrap
            the same <b>Registration</b> instance. Since the <b>Registration</b> is created using the 
            <see cref="F:SimpleInjector.Lifestyle.Singleton">Singleton</see> lifestyle, both producers will return 
            the same instance. The <b>InstanceProducer</b> for the <code>Interface1</code> however, will wrap that
            instance in a (transient) <code>Interface1Decorator</code>.
            <code lang="cs"><![CDATA[
            var container = new Container();
            
            // ServiceImpl implements both Interface1 and Interface2.
            var registration = Lifestyle.Singleton.CreateRegistration<ServiceImpl, ServiceImpl>(container);
            
            var producer1 = new InstanceProducer(typeof(Interface1), registration);
            var producer2 = new InstanceProducer(typeof(Interface2), registration);
            
            container.RegisterDecorator(typeof(Interface1), typeof(Interface1Decorator));
            
            var instance1 = (Interface1)producer1.GetInstance();
            var instance2 = (Interface2)producer2.GetInstance();
            
            Assert.IsInstanceOfType(instance1, typeof(Interface1Decorator));
            Assert.IsInstanceOfType(instance2, typeof(ServiceImpl));
            
            Assert.AreSame(((Interface1Decorator)instance1).DecoratedInstance, instance2);
            ]]></code>
            </example>
        </member>
        <member name="M:SimpleInjector.InstanceProducer.#ctor(System.Type,SimpleInjector.Registration)">
            <summary>Initializes a new instance of the <see cref="T:SimpleInjector.InstanceProducer"/> class.</summary>
            <param name="serviceType">The service type for which this instance is created.</param>
            <param name="registration">The <see cref="P:SimpleInjector.InstanceProducer.Registration"/>.</param>
        </member>
        <member name="P:SimpleInjector.InstanceProducer.Lifestyle">
            <summary>
            Gets the <see cref="P:SimpleInjector.InstanceProducer.Lifestyle"/> for this registration. The returned lifestyle can differ from the
            lifestyle that is used during the registration. This can happen for instance when the registration
            is changed by an <see cref="E:SimpleInjector.Container.ExpressionBuilt">ExpressionBuilt</see> 
            registration or gets decorated.
            </summary>
            <value>The <see cref="P:SimpleInjector.InstanceProducer.Lifestyle"/> for this registration.</value>
        </member>
        <member name="P:SimpleInjector.InstanceProducer.ServiceType">
            <summary>Gets the service type for which this producer produces instances.</summary>
            <value>A <see cref="T:System.Type"/> instance.</value>
        </member>
        <member name="P:SimpleInjector.InstanceProducer.Registration">
            <summary>Gets the <see cref="P:SimpleInjector.InstanceProducer.Registration"/> instance for this instance.</summary>
            <value>The <see cref="P:SimpleInjector.InstanceProducer.Registration"/>.</value>
        </member>
        <member name="M:SimpleInjector.InstanceProducer.GetInstance">
            <summary>Produces an instance.</summary>
            <returns>An instance. Will never return null.</returns>
            <exception cref="T:SimpleInjector.ActivationException">When the instance could not be retrieved or is null.</exception>
        </member>
        <member name="M:SimpleInjector.InstanceProducer.BuildExpression">
            <summary>
            Builds an expression that expresses the intent to get an instance by the current producer. A call 
            to this method locks the container. No new registrations can't be made after a call to this method.
            </summary>
            <returns>An Expression.</returns>
        </member>
        <member name="M:SimpleInjector.InstanceProducer.GetRelationships">
            <summary>
            Gets the collection of relationships for this instance that the container knows about.
            This includes relationships between the registered type and its dependencies and relationships 
            between applied decorators and their dependencies. Note that types that are not newed up by the 
            container and properties that are injected inside a custom delegate that is registered using the
            <see cref="M:SimpleInjector.Container.RegisterInitializer``1(System.Action{``0})">RegisterInitializer</see> 
            method are unknown to the container and are not returned from this method.
            Also note that this method will return an empty collection when called before the
            registered type is requested from the container (or before 
            <see cref="M:SimpleInjector.Container.Verify">Verify</see> is called). 
            </summary>
            <returns>An array of <see cref="T:SimpleInjector.Advanced.KnownRelationship"/> instances.</returns>
        </member>
        <member name="M:SimpleInjector.InstanceProducer.VisualizeObjectGraph">
            <summary>
            Builds a string representation of the object graph with the current instance as root of the
            graph.
            </summary>
            <returns>A string representation of the object graph.</returns>
            <exception cref="T:System.InvalidOperationException">Thrown when this method is called before 
            <see cref="M:SimpleInjector.InstanceProducer.GetInstance"/> or <see cref="M:SimpleInjector.InstanceProducer.BuildExpression"/> have been called. These calls can be
            done directly and explicitly by the user on this instance, indirectly by calling
            <see cref="M:SimpleInjector.InstanceProducer.GetInstance"/> or <see cref="M:SimpleInjector.InstanceProducer.BuildExpression"/> on an instance that depends on this
            instance, or by calling <see cref="M:SimpleInjector.Container.Verify">Verify</see> on the container.
            </exception>
        </member>
        <member name="T:SimpleInjector.InstanceProducer`1">
            <summary>
            Produces instances for a given registration. Instances of this type are generally created by the
            container when calling one of the <b>Register</b> overloads. Instances can be retrieved by calling
            <see cref="M:SimpleInjector.Container.GetCurrentRegistrations"/> or <see cref="M:SimpleInjector.Container.GetRegistration(System.Type,System.Boolean)"/>.
            </summary>
            <typeparam name="TService">The service type.</typeparam>
        </member>
        <member name="M:SimpleInjector.InstanceProducer`1.#ctor(SimpleInjector.Registration)">
            <summary>Initializes a new instance of the <see cref="T:SimpleInjector.InstanceProducer`1"/> class.</summary>
            <param name="registration">The <see cref="T:SimpleInjector.Registration"/>.</param>
        </member>
        <member name="M:SimpleInjector.InstanceProducer`1.GetInstance">
            <summary>Produces an instance.</summary>
            <returns>An instance. Will never return null.</returns>
            <exception cref="T:SimpleInjector.ActivationException">When the instance could not be retrieved or is null.</exception>
        </member>
        <member name="T:SimpleInjector.Internals.ArgumentMapping">
            <summary>
            A map containing a generic argument (such as T) and the concrete type (such as Int32) that it
            represents.
            </summary>
        </member>
        <member name="M:SimpleInjector.Internals.ArgumentMapping.System#IEquatable{SimpleInjector#Internals#ArgumentMapping}#Equals(SimpleInjector.Internals.ArgumentMapping)">
            <summary>Implements equality. Needed for doing LINQ distinct operations.</summary>
            <param name="other">The other to compare to.</param>
            <returns>True or false.</returns>
        </member>
        <member name="M:SimpleInjector.Internals.ArgumentMapping.GetHashCode">
            <summary>Overrides the default hash code. Needed for doing LINQ distinct operations.</summary>
            <returns>An 32 bit integer.</returns>
        </member>
        <member name="T:SimpleInjector.Internals.ContainerControlledItem">
            <summary>
            Container controlled collections can be supplied with both Type objects or direct Registration
            instances.
            </summary>
        </member>
        <member name="F:SimpleInjector.Internals.ContainerControlledItem.ImplementationType">
            <summary>Will never be null. Can be open-generic.</summary>
        </member>
        <member name="F:SimpleInjector.Internals.ContainerControlledItem.Registration">
            <summary>Can be null.</summary>
        </member>
        <member name="T:SimpleInjector.Internals.CyclicDependencyValidator">
            <summary>
            Allows verifying whether a given type has a direct or indirect dependency on itself. Verifying is done
            by preventing recursive calls to a IInstanceProvider. An instance of this type is related to a single 
            instance of a IInstanceProvider. A RecursiveDependencyValidator instance checks a single 
            IInstanceProvider and therefore a single service type.
            </summary>
        </member>
        <member name="T:SimpleInjector.Internals.GenericArgumentFinder">
            <summary>
            Allows retrieving the concrete types of the generic type arguments of that must be used to create a
            closed generic implementation of a given open generic implementation, based on on the concrete
            arguments of the given closed base type.
            </summary>
        </member>
        <member name="T:SimpleInjector.Internals.GenericTypeBuilder">
            <summary>
            Helper class for building closed generic type for a given open generic type and a closed generic base.
            </summary>
        </member>
        <member name="T:SimpleInjector.Internals.GenericTypeBuilder.BuildResult">
            <summary>Result of the GenericTypeBuilder.</summary>
        </member>
        <member name="T:SimpleInjector.Internals.GenericTypeBuilder.CandicateServiceType">
            <summary>
            A open generic type with the concrete arguments that can be used to create a closed generic type.
            </summary>
        </member>
        <member name="T:SimpleInjector.Internals.IContainerControlledCollection">
            <summary>This interface is not meant for public use.</summary>
        </member>
        <member name="M:SimpleInjector.Internals.IContainerControlledCollection.GetRelationships">
            <summary>Please do not use.</summary>
            <returns>Do not use.</returns>
        </member>
        <member name="M:SimpleInjector.Internals.IContainerControlledCollection.Append(SimpleInjector.Internals.ContainerControlledItem)">
            <summary>PLease do not use.</summary>
            <param name="registration">Do not use.</param>
        </member>
        <member name="T:SimpleInjector.Internals.TypeConstraintValidator">
            <summary>
            Allows validating an ArgumentMapping.
            </summary>
        </member>
        <member name="T:SimpleInjector.CreateLifestyleApplier">
            <summary>
            Factory for the creation of a delegate that applies caching to the supplied 
            <paramref name="transientInstanceCreator"/>.
            </summary>
            <param name="transientInstanceCreator">A factory for creating new instances.</param>
            <returns>A factory that returns cached instances.</returns>
        </member>
        <member name="T:SimpleInjector.Lifestyle">
            <summary>
            Instances returned from the container can be cached. The <see cref="T:SimpleInjector.Container"/> contains several
            overloads of the <b>Register</b> method that take a <b>Lifestyle</b> instance as argument to define 
            how returned instances should be cached. The core library contains two lifestyles out of the box. By
            supplying <see cref="F:SimpleInjector.Lifestyle.Transient">Lifestyle.Transient</see>, the registered instance is not
            cached; a new instance is returned every time it is requested or injected. By supplying
            <see cref="F:SimpleInjector.Lifestyle.Singleton">Lifestyle.Singleton</see> instances can be cached indefinitely; only
            a single instance of the registered component will be returned by that container instance. Other
            lifestyles are defined in integration and extension packages. The 
            <see cref="M:SimpleInjector.Lifestyle.CreateCustom(System.String,SimpleInjector.CreateLifestyleApplier)">CreateCustom</see> method allows defining a custom lifestyle and 
            the <see cref="M:SimpleInjector.Lifestyle.CreateHybrid(System.Func{System.Boolean},SimpleInjector.Lifestyle,SimpleInjector.Lifestyle)">CreateHybrid</see> method 
            allows creating a lifestyle that mixes multiple other lifestyles.
            </summary>
            <remarks>
            This type is abstract and can be overridden to implement a custom lifestyle.
            </remarks>
        </member>
        <member name="F:SimpleInjector.Lifestyle.Transient">
            <summary>
            The lifestyle instance that doesn't cache instances. A new instance of the specified
            component is created every time the registered service it is requested or injected.
            </summary>
            <example>
            The following example registers the <c>SomeServiceImpl</c> implementation for the
            <c>ISomeService</c> service type using the <b>Transient</b> lifestyle:
            <code lang="cs"><![CDATA[
            var container = new Container();
            
            container.Register<ISomeService, SomeServiceImpl>(Lifestyle.Transient);
            ]]></code>
            Note that <b>Transient</b> is the default lifestyle, the previous registration can be reduced to
            the following:
            <code lang="cs"><![CDATA[
            var container = new Container();
            
            // Transient registration.
            container.Register<ISomeService, SomeServiceImpl>();
            ]]></code>
            </example>
        </member>
        <member name="F:SimpleInjector.Lifestyle.Scoped">
            <summary>
            <para>
            The lifestyle that caches components according to the lifetime of the container's configured
            scoped lifestyle.
            </para>
            <para>
            In case the type of a cached instance implements <see cref="T:System.IDisposable"/>, the container will
            ensure its disposal when the active scope gets disposed.
            </para>
            </summary>
            <example>
            The following example registers the <c>RealTimeProvider</c> implementation for the
            <c>ITimeProvider</c> service type using the <b>Scoped</b> lifestyle:
            <code lang="cs"><![CDATA[
            // Create a Container instance, configured with a scoped lifestyle.
            var container = new Container(new WebRequestLifestyle());
            
            container.Register<ITimeProvider, RealTimeProvider>(Lifestyle.Scoped);
            ]]></code>
            </example>
        </member>
        <member name="F:SimpleInjector.Lifestyle.Singleton">
            <summary>
            <para>
            The lifestyle that caches components during the lifetime of the <see cref="T:SimpleInjector.Container"/> instance
            and guarantees that only a single instance of that component is created for that instance. Since
            general use is to create a single <b>Container</b> instance for the lifetime of the application /
            AppDomain, this would mean that only a single instance of that component would exist during the
            lifetime of the application. In a multi-threaded applications, implementations registered using 
            this lifestyle must be thread-safe.
            </para>
            <para>
            In case the type of a cached instance implements <see cref="T:System.IDisposable"/>, the container will
            ensure its disposal when the container gets disposed.
            </para>
            </summary>
            <example>
            The following example registers the <c>RealTimeProvider</c> implementation for the
            <c>ITimeProvider</c> service type using the <b>Singleton</b> lifestyle:
            <code lang="cs"><![CDATA[
            var container = new Container();
            
            container.Register<ITimeProvider, RealTimeProvider>(Lifestyle.Singleton);
            ]]></code>
            </example>
        </member>
        <member name="M:SimpleInjector.Lifestyle.#ctor(System.String)">
            <summary>Initializes a new instance of the <see cref="T:SimpleInjector.Lifestyle"/> class.</summary>
            <param name="name">The user friendly name of this lifestyle.</param>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="name"/> is null (Nothing in VB) 
            or an empty string.</exception>
        </member>
        <member name="P:SimpleInjector.Lifestyle.Name">
            <summary>Gets the user friendly name of this lifestyle.</summary>
            <value>The user friendly name of this lifestyle.</value>
        </member>
        <member name="P:SimpleInjector.Lifestyle.Length">
            <summary>
            Gets the length of the lifestyle. Implementers must implement this property. The diagnostic
            services use this value to compare lifestyles with each other to determine lifestyle 
            misconfigurations.
            </summary>
            <value>The <see cref="T:System.Int32"/> representing the length of this lifestyle.</value>
        </member>
        <member name="M:SimpleInjector.Lifestyle.CreateHybrid(System.Func{System.Boolean},SimpleInjector.Lifestyle,SimpleInjector.Lifestyle)">
            <summary>
            The hybrid lifestyle allows mixing two lifestyles in a single registration. Based on the supplied
            <paramref name="lifestyleSelector"/> delegate the hybrid lifestyle will redirect the creation of 
            the instance to the correct lifestyle. The result of the <paramref name="lifestyleSelector"/> 
            delegate will not be cached; it is invoked each time an instance is requested or injected. By 
            nesting hybrid lifestyles, any number of lifestyles can be mixed.
            </summary>
            <param name="lifestyleSelector">The <see cref="T:System.Func`1"/> delegate that determines which 
            lifestyle should be used. The <paramref name="trueLifestyle"/> will be used if <b>true</b> is 
            returned; the <paramref name="falseLifestyle"/> otherwise. This delegate will be called every
            time an instance needs to be resolved or injected.</param>
            <param name="trueLifestyle">The lifestyle to use when <paramref name="lifestyleSelector"/> 
            returns <b>true</b>.</param>
            <param name="falseLifestyle">The lifestyle to use when <paramref name="lifestyleSelector"/> 
            returns <b>false</b>.</param>
            <returns>A new hybrid lifestyle that wraps the supplied lifestyles.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the supplied arguments is a null
            reference (Nothing in VB).</exception>
            <example>
            <para>
            The following example shows the creation of a <b>HybridLifestyle</b> that mixes an 
            <b>WebRequestLifestyle</b> and <b>LifetimeScopeLifestyle</b>:
            </para>
            <code lang="cs"><![CDATA[
            // NOTE: WebRequestLifestyle is located in SimpleInjector.Integration.Web.dll.
            // NOTE: LifetimeScopeLifestyle is located in SimpleInjector.Extensions.LifetimeScoping.dll.
            var mixedScopeLifestyle = Lifestyle.CreateHybrid(
                () => HttpContext.Current != null,
                new WebRequestLifestyle(),
                new LifetimeScopeLifestyle());
            
            // The created lifestyle can be reused for many registrations.
            container.Register<IUserRepository, SqlUserRepository>(mixedScopeLifestyle);
            container.Register<ICustomerRepository, SqlCustomerRepository>(mixedScopeLifestyle);
            ]]></code>
            <para>
            Hybrid lifestyles can be nested:
            </para>
            <code lang="cs"><![CDATA[
            var mixedLifetimeTransientLifestyle = Lifestyle.CreateHybrid(
                () => container.GetCurrentLifetimeScope() != null,
                new LifetimeScopeLifestyle(),
                Lifestyle.Transient);
            
            var mixedScopeLifestyle = Lifestyle.CreateHybrid(
                () => HttpContext.Current != null,
                new WebRequestLifestyle(),
                mixedLifetimeTransientLifestyle);
            ]]></code>
            <para>
            The <b>mixedScopeLifestyle</b> now mixed three lifestyles: Web Request, Lifetime Scope and 
            Transient.
            </para>
            </example>
        </member>
        <member name="M:SimpleInjector.Lifestyle.CreateHybrid(System.Func{System.Boolean},SimpleInjector.ScopedLifestyle,SimpleInjector.ScopedLifestyle)">
            <summary>
            The hybrid lifestyle allows mixing two lifestyles in a single registration. Based on the supplied
            <paramref name="lifestyleSelector"/> delegate the hybrid lifestyle will redirect the creation of 
            the instance to the correct lifestyle. The result of the <paramref name="lifestyleSelector"/> 
            delegate will not be cached; it is invoked each time an instance is requested or injected. By 
            nesting hybrid lifestyles, any number of lifestyles can be mixed.
            </summary>
            <param name="lifestyleSelector">The <see cref="T:System.Func`1"/> delegate that determines which 
            lifestyle should be used. The <paramref name="trueLifestyle"/> will be used if <b>true</b> is 
            returned; the <paramref name="falseLifestyle"/> otherwise. This delegate will be called every
            time an instance needs to be resolved or injected.</param>
            <param name="trueLifestyle">The scoped lifestyle to use when <paramref name="lifestyleSelector"/> 
            returns <b>true</b>.</param>
            <param name="falseLifestyle">The scoped lifestyle to use when <paramref name="lifestyleSelector"/> 
            returns <b>false</b>.</param>
            <returns>A new scoped hybrid lifestyle that wraps the supplied lifestyles.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the supplied arguments is a null
            reference (Nothing in VB).</exception>
            <example>
            <para>
            The following example shows the creation of a <b>HybridLifestyle</b> that mixes an 
            <b>WebRequestLifestyle</b> and <b>LifetimeScopeLifestyle</b>:
            </para>
            <code lang="cs"><![CDATA[
            // NOTE: WebRequestLifestyle is located in SimpleInjector.Integration.Web.dll.
            // NOTE: LifetimeScopeLifestyle is located in SimpleInjector.Extensions.LifetimeScoping.dll.
            var mixedScopeLifestyle = Lifestyle.CreateHybrid(
                () => HttpContext.Current != null,
                new WebRequestLifestyle(),
                new LifetimeScopeLifestyle());
            
            // The created lifestyle can be reused for many registrations.
            container.Register<IUserRepository, SqlUserRepository>(mixedScopeLifestyle);
            container.Register<ICustomerRepository, SqlCustomerRepository>(mixedScopeLifestyle);
            ]]></code>
            </example>
        </member>
        <member name="M:SimpleInjector.Lifestyle.CreateCustom(System.String,SimpleInjector.CreateLifestyleApplier)">
            <summary>
            Creates a custom lifestyle using the supplied <paramref name="lifestyleApplierFactory"/> delegate.
            </summary>
            <remarks>
            The supplied <paramref name="lifestyleApplierFactory" /> will be called just once per registered 
            service. The supplied <paramref name="lifestyleApplierFactory" /> will be called by the framework
            when the type is resolved for the first time, and the framework will supply the factory with a
            <b>Func&lt;object&gt;</b> for creating new (transient) instances of that type (that might
            have been <see cref="E:SimpleInjector.Container.ExpressionBuilding">intercepted</see> and
            <see cref="M:SimpleInjector.Container.RegisterInitializer``1(System.Action{``0})">initializers</see> might have been applied). 
            It is the job of the <paramref name="lifestyleApplierFactory" /> to return a <b>Func&lt;object&gt;</b>
            that applies the proper caching. The <b>Func&lt;object&gt;</b> that is returned by the 
            <paramref name="lifestyleApplierFactory" /> will be stored for that registration (every 
            registration will store its own <b>Func&lt;object&gt;</b> delegate) and this delegate will be
            called every time the service is resolved (by calling 
            <code>container.GetInstance&lt;TService&gt;</code> or when that service is injected into another
            type). 
            </remarks>
            <param name="name">The name of the lifestyle to create. The name is used to display the lifestyle
            in the debugger.</param>
            <param name="lifestyleApplierFactory">A factory delegate that takes a <b>Func&lt;object&gt;</b> delegate
            that will produce a transient instance and returns a delegate that returns cached instances.</param>
            <returns>A new <see cref="T:SimpleInjector.Lifestyle"/>.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the arguments is a null reference
            (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="name"/> is an empty string.</exception>
            <example>
            The following example shows the creation of a lifestyle that caches registered instances for 10
            minutes:
            <code lang="cs"><![CDATA[
            var customLifestyle = Lifestyle.CreateCustom("Absolute 10 Minute Expiration", instanceCreator =>
            {
                TimeSpan timeout = TimeSpan.FromMinutes(10);
                var syncRoot = new object();
                var expirationTime = DateTime.MinValue;
                object instance = null;
            
                // If the application has multiple registrations using this lifestyle, each registration
                // will get its own Func<object> delegate (created here) and therefore get its own set
                // of variables as defined above.
                return () =>
                {
                    lock (syncRoot)
                    {
                        if (expirationTime < DateTime.UtcNow)
                        {
                            instance = instanceCreator();
                            expirationTime = DateTime.UtcNow.Add(timeout);
                        }
            
                        return instance;
                    }
                };
            });
            
            var container = new Container();
            
            // We can reuse the created lifestyle for multiple registrations.
            container.Register<IService, MyService>(customLifestyle);
            container.Register<AnotherService, MeTwoService>(customLifestyle);
            ]]></code>
            </example>
        </member>
        <member name="M:SimpleInjector.Lifestyle.CreateProducer``2(SimpleInjector.Container)">
            <summary>
            Creates a new <see cref="T:SimpleInjector.InstanceProducer"/> instance for the given <typeparamref name="TService"/>
            that will create new instances of specified <typeparamref name="TImplementation"/> with the 
            caching as specified by this lifestyle.
            </summary>
            <typeparam name="TService">The interface or base type that can be used to retrieve the instances.</typeparam>
            <typeparam name="TImplementation">The concrete type that will be created.</typeparam>
            <param name="container">The <see cref="T:SimpleInjector.Container"/> instance for which a 
            <see cref="T:SimpleInjector.InstanceProducer"/> must be created.</param>
            <returns>A new <see cref="T:SimpleInjector.InstanceProducer"/> instance.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="container"/> is a null
            reference (Nothing in VB).</exception>
        </member>
        <member name="M:SimpleInjector.Lifestyle.CreateProducer``1(System.Type,SimpleInjector.Container)">
            <summary>
            Creates a new <see cref="T:SimpleInjector.InstanceProducer"/> instance for the given <typeparamref name="TService"/>
            that will create new instances of specified <paramref name="implementationType"/> caching as 
            specified by this lifestyle.
            </summary>
            <typeparam name="TService">The interface or base type that can be used to retrieve the instances.</typeparam>
            <param name="implementationType">The concrete type that will be created.</param>
            <param name="container">The <see cref="T:SimpleInjector.Container"/> instance for which a 
            <see cref="T:SimpleInjector.InstanceProducer"/> must be created.</param>
            <returns>A new <see cref="T:SimpleInjector.InstanceProducer"/> instance.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown when either <paramref name="implementationType"/> or
            <paramref name="container"/> are null references (Nothing in VB).</exception>
        </member>
        <member name="M:SimpleInjector.Lifestyle.CreateProducer``1(System.Func{``0},SimpleInjector.Container)">
            <summary>
            Creates a new <see cref="T:SimpleInjector.InstanceProducer"/> instance for the given <typeparamref name="TService"/>
            that will create new instances instance using the supplied <paramref name="instanceCreator"/> 
            with the caching as specified by this lifestyle.
            </summary>
            <typeparam name="TService">The interface or base type that can be used to retrieve the instances.</typeparam>
            <param name="instanceCreator">A delegate that will create a new instance of 
            <typeparamref name="TService"/> every time it is called.</param>
            <param name="container">The <see cref="T:SimpleInjector.Container"/> instance for which a 
            <see cref="T:SimpleInjector.InstanceProducer"/> must be created.</param>
            <returns>A new <see cref="T:SimpleInjector.InstanceProducer"/> instance.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown when either <paramref name="instanceCreator"/> or
            <paramref name="container"/> are null references (Nothing in VB).</exception>
        </member>
        <member name="M:SimpleInjector.Lifestyle.CreateProducer(System.Type,System.Type,SimpleInjector.Container)">
            <summary>
            Creates a new <see cref="T:SimpleInjector.InstanceProducer"/> instance for the given <paramref name="serviceType"/>
            that will create new instances of specified <paramref name="implementationType"/> with the 
            caching as specified by this lifestyle.
            </summary>
            <param name="serviceType">The interface or base type that can be used to retrieve the instances.</param>
            <param name="implementationType">The concrete type that will be registered.</param>
            <param name="container">The <see cref="T:SimpleInjector.Container"/> instance for which a 
            <see cref="T:SimpleInjector.Registration"/> must be created.</param>
            <returns>A new <see cref="T:SimpleInjector.InstanceProducer"/> instance.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown when on of the supplied arguments is a null 
            reference (Nothing in VB).</exception>
        </member>
        <member name="M:SimpleInjector.Lifestyle.CreateRegistration``1(SimpleInjector.Container)">
            <summary>
            Creates a new <see cref="T:SimpleInjector.Registration"/> instance defining the creation of the
            specified <typeparamref name="TConcrete"/> with the caching as specified by this lifestyle.
            </summary>
            <typeparam name="TConcrete">The concrete type that will be registered.</typeparam>
            <param name="container">The <see cref="T:SimpleInjector.Container"/> instance for which a 
            <see cref="T:SimpleInjector.Registration"/> must be created.</param>
            <returns>A new <see cref="T:SimpleInjector.Registration"/> instance.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="container"/> is a null
            reference (Nothing in VB).</exception>
        </member>
        <member name="M:SimpleInjector.Lifestyle.CreateRegistration``2(SimpleInjector.Container)">
            <summary>
            Creates a new <see cref="T:SimpleInjector.Registration"/> instance defining the creation of the
            specified <typeparamref name="TImplementation"/> with the caching as specified by this lifestyle.
            </summary>
            <typeparam name="TService">The interface or base type that can be used to retrieve the instances.</typeparam>
            <typeparam name="TImplementation">The concrete type that will be created.</typeparam>
            <param name="container">The <see cref="T:SimpleInjector.Container"/> instance for which a 
            <see cref="T:SimpleInjector.Registration"/> must be created.</param>
            <returns>A new <see cref="T:SimpleInjector.Registration"/> instance.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="container"/> is a null
            reference (Nothing in VB).</exception>
        </member>
        <member name="M:SimpleInjector.Lifestyle.CreateRegistration``1(System.Func{``0},SimpleInjector.Container)">
            <summary>
            Creates a new <see cref="T:SimpleInjector.Registration"/> instance defining the creation of the
            specified <typeparamref name="TService"/> using the supplied <paramref name="instanceCreator"/> 
            with the caching as specified by this lifestyle.
            </summary>
            <typeparam name="TService">The interface or base type that can be used to retrieve the instances.</typeparam>
            <param name="instanceCreator">A delegate that will create a new instance of 
            <typeparamref name="TService"/> every time it is called.</param>
            <param name="container">The <see cref="T:SimpleInjector.Container"/> instance for which a 
            <see cref="T:SimpleInjector.Registration"/> must be created.</param>
            <returns>A new <see cref="T:SimpleInjector.Registration"/> instance.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown when either <paramref name="instanceCreator"/> or
            <paramref name="container"/> are null references (Nothing in VB).</exception>
        </member>
        <member name="M:SimpleInjector.Lifestyle.CreateRegistration(System.Type,SimpleInjector.Container)">
            <summary>
            Creates a new <see cref="T:SimpleInjector.Registration"/> instance defining the creation of the
            specified <paramref name="concreteType"/> with the caching as specified by this lifestyle.
            This method might fail when run in a partial trust sandbox when <paramref name="concreteType"/>
            is an internal type.
            </summary>
            <param name="concreteType">The concrete type that will be registered.</param>
            <param name="container">The <see cref="T:SimpleInjector.Container"/> instance for which a 
            <see cref="T:SimpleInjector.Registration"/> must be created.</param>
            <returns>A new <see cref="T:SimpleInjector.Registration"/> instance.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown when on of the supplied arguments is a null 
            reference (Nothing in VB).</exception>
        </member>
        <member name="M:SimpleInjector.Lifestyle.CreateRegistration(System.Type,System.Type,SimpleInjector.Container)">
            <summary>
            Creates a new <see cref="T:SimpleInjector.Registration"/> instance defining the creation of the
            specified <paramref name="implementationType"/> with the caching as specified by this lifestyle.
            This method might fail when run in a partial trust sandbox when <paramref name="implementationType"/>
            is an internal type.
            </summary>
            <param name="serviceType">The interface or base type that can be used to retrieve the instances.</param>
            <param name="implementationType">The concrete type that will be registered.</param>
            <param name="container">The <see cref="T:SimpleInjector.Container"/> instance for which a 
            <see cref="T:SimpleInjector.Registration"/> must be created.</param>
            <returns>A new <see cref="T:SimpleInjector.Registration"/> instance.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown when on of the supplied arguments is a null 
            reference (Nothing in VB).</exception>
        </member>
        <member name="M:SimpleInjector.Lifestyle.CreateRegistration(System.Type,System.Func{System.Object},SimpleInjector.Container)">
            <summary>
            Creates a new <see cref="T:SimpleInjector.Registration"/> instance defining the creation of the
            specified <paramref name="serviceType"/>  using the supplied <paramref name="instanceCreator"/> 
            with the caching as specified by this lifestyle.
            </summary>
            <param name="serviceType">The interface or base type that can be used to retrieve the instances.</param>
            <param name="instanceCreator">The delegate that will be responsible for creating new instances.</param>
            <param name="container">The <see cref="T:SimpleInjector.Container"/> instance for which a 
            <see cref="T:SimpleInjector.Registration"/> must be created.</param>
            <returns>A new <see cref="T:SimpleInjector.Registration"/> instance.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown when on of the supplied arguments is a null 
            reference (Nothing in VB).</exception>
        </member>
        <member name="M:SimpleInjector.Lifestyle.CreateRegistrationCore``2(SimpleInjector.Container)">
            <summary>
            When overridden in a derived class, 
            creates a new <see cref="T:SimpleInjector.Registration"/> instance defining the creation of the
            specified <typeparamref name="TImplementation"/> with the caching as specified by this lifestyle.
            </summary>
            <typeparam name="TService">The interface or base type that can be used to retrieve the instances.</typeparam>
            <typeparam name="TImplementation">The concrete type that will be registered.</typeparam>
            <param name="container">The <see cref="T:SimpleInjector.Container"/> instance for which a 
            <see cref="T:SimpleInjector.Registration"/> must be created.</param>
            <returns>A new <see cref="T:SimpleInjector.Registration"/> instance.</returns>
            <remarks>
            If you are implementing your own lifestyle, override this method to implement the code necessary 
            to create and return a new <see cref="T:SimpleInjector.Registration"/>. Note that you should <b>always</b> create
            a new <see cref="T:SimpleInjector.Registration"/> instance. They should never be cached.
            </remarks>
        </member>
        <member name="M:SimpleInjector.Lifestyle.CreateRegistrationCore``1(System.Func{``0},SimpleInjector.Container)">
            <summary>
            When overridden in a derived class, 
            creates a new <see cref="T:SimpleInjector.Registration"/> instance defining the creation of the
            specified <typeparamref name="TService"/> using the supplied <paramref name="instanceCreator"/> 
            with the caching as specified by this lifestyle.
            </summary>
            <typeparam name="TService">The interface or base type that can be used to retrieve the instances.</typeparam>
            <param name="instanceCreator">A delegate that will create a new instance of 
            <typeparamref name="TService"/> every time it is called.</param>
            <param name="container">The <see cref="T:SimpleInjector.Container"/> instance for which a 
            <see cref="T:SimpleInjector.Registration"/> must be created.</param>
            <returns>A new <see cref="T:SimpleInjector.Registration"/> instance.</returns>
            <remarks>
            If you are implementing your own lifestyle, override this method to implement the code necessary 
            to create and return a new <see cref="T:SimpleInjector.Registration"/>. Note that you should <b>always</b> create
            a new <see cref="T:SimpleInjector.Registration"/> instance. They should never be cached.
            </remarks>
        </member>
        <member name="T:SimpleInjector.Lifestyles.LifestyleSelectionBehaviorProxyLifestyle">
            <summary>
            Forwards CreateRegistration calls to the lifestyle that is returned from the registered
            container.Options.LifestyleSelectionBehavior.
            </summary>
        </member>
        <member name="T:SimpleInjector.PredicateContext">
            <summary>
            An instance of this type will be supplied to the <see cref="T:System.Predicate`1"/>
            delegate that is that is supplied to the 
            <see cref="M:SimpleInjector.Container.RegisterConditional(System.Type,System.Type,SimpleInjector.Lifestyle,System.Predicate{SimpleInjector.PredicateContext})">RegisterConditional</see>
            overload that takes this delegate. This type contains information about the open generic service that 
            is about to be created and it allows the user to examine the given instance to decide whether this 
            implementation should be created or not.
            </summary>
            <remarks>
            Please see the 
            <see cref="M:SimpleInjector.Container.RegisterConditional(System.Type,System.Type,SimpleInjector.Lifestyle,System.Predicate{SimpleInjector.PredicateContext})">Register</see>
            method for more information.
            </remarks>
        </member>
        <member name="P:SimpleInjector.PredicateContext.ServiceType">
            <summary>Gets the closed generic service type that is to be created.</summary>
            <value>The closed generic service type.</value>
        </member>
        <member name="P:SimpleInjector.PredicateContext.ImplementationType">
            <summary>Gets the closed generic implementation type that will be created by the container.</summary>
            <value>The implementation type.</value>
        </member>
        <member name="P:SimpleInjector.PredicateContext.Handled">
            <summary>Gets a value indicating whether a previous <b>Register</b> registration has already
            been applied for the given <see cref="P:SimpleInjector.PredicateContext.ServiceType"/>.</summary>
            <value>The indication whether the event has been handled.</value>
        </member>
        <member name="P:SimpleInjector.PredicateContext.Consumer">
            <summary>
            Gets the contextual information of the consuming component that directly depends on the resolved
            service. This property will return null in case the service is resolved directly from the container.
            </summary>
            <value>The <see cref="T:SimpleInjector.InjectionConsumerInfo"/> or null.</value>
        </member>
        <member name="T:SimpleInjector.Registration">
            <summary>
            A <b>Registration</b> implements lifestyle based caching for a single service and allows building an
            <see cref="T:System.Linq.Expressions.Expression"/> that describes the creation of the service.
            </summary>
            <remarks>
            <see cref="P:SimpleInjector.Registration.Lifestyle"/> implementations create a new <b>Registration</b> instance for each registered
            service type. <see cref="T:System.Linq.Expressions.Expression"/>s returned from the 
            <see cref="M:SimpleInjector.Registration.BuildExpression">BuildExpression</see> method can be intercepted by any event
            registered with <see cref="E:SimpleInjector.Container.ExpressionBuilding" />, have 
            <see cref="M:SimpleInjector.Container.RegisterInitializer``1(System.Action{``0})">initializers</see> 
            applied, and the caching particular to its lifestyle have been applied. Interception using the 
            <see cref="E:SimpleInjector.Container.ExpressionBuilt">Container.ExpressionBuilt</see> will <b>not</b> 
            be applied in the <b>Registration</b>, but will be applied in <see cref="T:SimpleInjector.InstanceProducer"/>.</remarks>
            <example>
            See the <see cref="P:SimpleInjector.Registration.Lifestyle"/> documentation for an example.
            </example>
        </member>
        <member name="M:SimpleInjector.Registration.#ctor(SimpleInjector.Lifestyle,SimpleInjector.Container)">
            <summary>
            Initializes a new instance of the <see cref="T:SimpleInjector.Registration"/> class.
            </summary>
            <param name="lifestyle">The <see cref="P:SimpleInjector.Registration.Lifestyle"/> this that created this registration.</param>
            <param name="container">The <see cref="P:SimpleInjector.Registration.Container"/> instance for this registration.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the supplied arguments is a null
            reference (Nothing in VB).</exception>
        </member>
        <member name="P:SimpleInjector.Registration.ImplementationType">
            <summary>Gets the type that this instance will create.</summary>
            <value>The type that this instance will create.</value>
        </member>
        <member name="P:SimpleInjector.Registration.Lifestyle">
            <summary>Gets the <see cref="P:SimpleInjector.Registration.Lifestyle"/> this that created this registration.</summary>
            <value>The <see cref="P:SimpleInjector.Registration.Lifestyle"/> this that created this registration.</value>
        </member>
        <member name="P:SimpleInjector.Registration.Container">
            <summary>Gets the <see cref="P:SimpleInjector.Registration.Container"/> instance for this registration.</summary>
            <value>The <see cref="P:SimpleInjector.Registration.Container"/> instance for this registration.</value>
        </member>
        <member name="P:SimpleInjector.Registration.WrapsInstanceCreationDelegate">
            <summary>Gets or sets a value indicating whether this registration object contains a user 
            supplied instanceCreator factory delegate.</summary>
        </member>
        <member name="M:SimpleInjector.Registration.BuildExpression">
            <summary>
            Builds a new <see cref="T:System.Linq.Expressions.Expression"/> with the correct caching (according to the specifications of
            its <see cref="P:SimpleInjector.Registration.Lifestyle"/>) applied.
            </summary>
            <returns>An <see cref="T:System.Linq.Expressions.Expression"/>.</returns>
        </member>
        <member name="M:SimpleInjector.Registration.GetRelationships">
            <summary>
            Gets the list of <see cref="T:SimpleInjector.Advanced.KnownRelationship"/> instances. Note that the list is only available
            after calling <see cref="M:SimpleInjector.Registration.BuildExpression"/>.
            </summary>
            <returns>A new array containing the <see cref="T:SimpleInjector.Advanced.KnownRelationship"/> instances.</returns>
        </member>
        <member name="M:SimpleInjector.Registration.InitializeInstance(System.Object)">
            <summary>
            Initializes an already created instance and applies properties and initializers to that instance.
            </summary>
            <remarks>
            This method is especially useful in integration scenarios where the given platform is in control
            of creating certain types. By passing the instance created by the platform to this method, the
            container is still able to apply any properties (as defined using a custom
            <see cref="T:SimpleInjector.Advanced.IPropertySelectionBehavior"/>) and by applying any initializers.
            </remarks>
            <param name="instance">The instance to initialize.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="instance"/> is a null reference
            (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when the supplied <paramref name="instance"/> is not
            of type <see cref="P:SimpleInjector.Registration.ImplementationType"/>.</exception>
        </member>
        <member name="M:SimpleInjector.Registration.SuppressDiagnosticWarning(SimpleInjector.Diagnostics.DiagnosticType,System.String)">
            <summary>
            Suppressing the supplied <see cref="T:SimpleInjector.Diagnostics.DiagnosticType"/> for the given registration.
            </summary>
            <param name="type">The <see cref="T:SimpleInjector.Diagnostics.DiagnosticType"/>.</param>
            <param name="justification">The justification of why the warning must be suppressed.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="justification"/> is a null  
            reference.</exception>
            <exception cref="T:System.ArgumentException">Thrown when either <paramref name="justification"/> is an
            empty string or when <paramref name="type"/> is not a valid value of <see cref="T:SimpleInjector.Diagnostics.DiagnosticType"/>.
            </exception>
        </member>
        <member name="M:SimpleInjector.Registration.BuildTransientDelegate``1(System.Func{``0})">
            <summary>
            Builds a <see cref="T:System.Func`1"/> delegate for the creation of the <typeparamref name="TService"/>
            using the supplied <paramref name="instanceCreator"/>. The returned <see cref="T:System.Func`1"/> might
            be intercepted by a 
            <see cref="E:SimpleInjector.Container.ExpressionBuilding">Container.ExpressionBuilding</see> event, 
            and the <paramref name="instanceCreator"/> will have been wrapped with a delegate that executes the
            registered <see cref="M:SimpleInjector.Container.RegisterInitializer``1(System.Action{``0})">initializers</see> 
            that are applicable to the given <typeparamref name="TService"/> (if any).
            </summary>
            <typeparam name="TService">The interface or base type that can be used to retrieve instances.</typeparam>
            <param name="instanceCreator">
            The delegate supplied by the user that allows building or creating new instances.</param>
            <returns>A <see cref="T:System.Func`1"/> delegate.</returns>
        </member>
        <member name="M:SimpleInjector.Registration.BuildTransientDelegate``2">
            <summary>
            Builds a <see cref="T:System.Func`1"/> delegate for the creation of <typeparamref name="TImplementation"/>.
            The returned <see cref="T:System.Func`1"/> might be intercepted by a 
            <see cref="E:SimpleInjector.Container.ExpressionBuilding">Container.ExpressionBuilding</see> event, 
            and the creation of the <typeparamref name="TImplementation"/> will have been wrapped with a 
            delegate that executes the registered 
            <see cref="M:SimpleInjector.Container.RegisterInitializer``1(System.Action{``0})">initializers</see> 
            that are applicable to the given <typeparamref name="TService"/> (if any).
            </summary>
            <typeparam name="TService">The interface or base type that can be used to retrieve instances.</typeparam>
            <typeparam name="TImplementation">The concrete type that will be registered.</typeparam>
            <returns>A <see cref="T:System.Func`1"/> delegate.</returns>
        </member>
        <member name="M:SimpleInjector.Registration.BuildTransientExpression``1(System.Func{``0})">
            <summary>
            Builds an <see cref="T:System.Linq.Expressions.Expression"/> that describes the creation of the <typeparamref name="TService"/>
            using the supplied <paramref name="instanceCreator"/>. The returned <see cref="T:System.Linq.Expressions.Expression"/> might
            be intercepted by a 
            <see cref="E:SimpleInjector.Container.ExpressionBuilding">Container.ExpressionBuilding</see> event, 
            and the <paramref name="instanceCreator"/> will have been wrapped with a delegate that executes the
            registered <see cref="M:SimpleInjector.Container.RegisterInitializer(System.Action{SimpleInjector.Advanced.InstanceInitializationData},System.Predicate{SimpleInjector.Advanced.InitializationContext})">initializers</see> that are 
            applicable to the given <typeparamref name="TService"/> (if any).
            </summary>
            <typeparam name="TService">The interface or base type that can be used to retrieve instances.</typeparam>
            <param name="instanceCreator">
            The delegate supplied by the user that allows building or creating new instances.</param>
            <returns>An <see cref="T:System.Linq.Expressions.Expression"/>.</returns>
        </member>
        <member name="M:SimpleInjector.Registration.BuildTransientExpression``2">
            <summary>
            Builds an <see cref="T:System.Linq.Expressions.Expression"/> that describes the creation of 
            <typeparamref name="TImplementation"/>. The returned <see cref="T:System.Linq.Expressions.Expression"/> might be intercepted
            by a <see cref="E:SimpleInjector.Container.ExpressionBuilding">Container.ExpressionBuilding</see>
            event, and the creation of the <typeparamref name="TImplementation"/> will have been wrapped with
            a delegate that executes the registered 
            <see cref="M:SimpleInjector.Container.RegisterInitializer(System.Action{SimpleInjector.Advanced.InstanceInitializationData},System.Predicate{SimpleInjector.Advanced.InitializationContext})">initializers</see> 
            that are applicable to the given <typeparamref name="TService"/> (if any).
            </summary>
            <typeparam name="TService">The interface or base type that can be used to retrieve instances.</typeparam>
            <typeparam name="TImplementation">The concrete type that will be registered.</typeparam>
            <returns>An <see cref="T:System.Linq.Expressions.Expression"/>.</returns>
        </member>
        <member name="T:SimpleInjector.Scope">
            <summary>Implements a cache for <see cref="T:SimpleInjector.ScopedLifestyle"/> implementations.</summary>
            <remarks>
            <see cref="T:SimpleInjector.Scope"/> is thread-safe can be used over multiple threads concurrently, but note that the
            cached instances might not be thread-safe.
            </remarks>
        </member>
        <member name="M:SimpleInjector.Scope.#ctor">
            <summary>Initializes a new instance of the <see cref="T:SimpleInjector.Scope"/> class.</summary>
        </member>
        <member name="M:SimpleInjector.Scope.#ctor(SimpleInjector.Container)">
            <summary>Initializes a new instance of the <see cref="T:SimpleInjector.Scope"/> class.</summary>
            <param name="container">The container instance that the scope belongs to.</param>
        </member>
        <member name="P:SimpleInjector.Scope.Container">
            <summary>Gets the container instance that this scope belongs to.</summary>
            <value>The <see cref="P:SimpleInjector.Scope.Container"/> instance.</value>
        </member>
        <member name="M:SimpleInjector.Scope.GetInstance``1">
            <summary>Gets an instance of the given <typeparamref name="TService"/> for the current scope.</summary>
            <typeparam name="TService">The type of the service to resolve.</typeparam>
            <returns>An instance of the given service type.</returns>
        </member>
        <member name="M:SimpleInjector.Scope.GetInstance(System.Type)">
            <summary>Gets an instance of the given <paramref name="serviceType" /> for the current scope.</summary>
            <param name="serviceType">The type of the service to resolve.</param>
            <returns>An instance of the given service type.</returns>
        </member>
        <member name="M:SimpleInjector.Scope.WhenScopeEnds(System.Action)">
            <summary>
            Allows registering an <paramref name="action"/> delegate that will be called when the scope ends,
            but before the scope disposes any instances.
            </summary>
            <remarks>
            During the call to <see cref="M:SimpleInjector.Scope.Dispose"/> all registered <see cref="T:System.Action"/> delegates are
            processed in the order of registration. Do note that registered actions <b>are not guaranteed
            to run</b>. In case an exception is thrown during the call to <see cref="M:SimpleInjector.Scope.Dispose"/>, the 
            <see cref="T:SimpleInjector.Scope"/> will stop running any actions that might not have been invoked at that point. 
            Instances that are registered for disposal using <see cref="M:SimpleInjector.Scope.RegisterForDisposal(System.IDisposable)"/> on the other
            hand, are guaranteed to be disposed. Note that registered actions won't be invoked during a call
            to <see cref="M:SimpleInjector.Container.Verify" />.
            </remarks>
            <param name="action">The delegate to run when the scope ends.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the arguments is a null reference
            (Nothing in VB).</exception>
            <exception cref="T:System.ObjectDisposedException">Thrown when the scope has been disposed.</exception>
        </member>
        <member name="M:SimpleInjector.Scope.RegisterForDisposal(System.IDisposable)">
            <summary>
            Adds the <paramref name="disposable"/> to the list of items that will get disposed when the
            scope ends.
            </summary>
            <remarks>
            Instances that are registered for disposal, will be disposed in opposite order of registration and
            they are guaranteed to be disposed when <see cref="M:SimpleInjector.Scope.Dispose"/> is called (even when 
            exceptions are thrown). This mimics the behavior of the C# and VB <code>using</code> statements,
            where the <see cref="M:System.IDisposable.Dispose"/> method is called inside the <code>finally</code> block.
            </remarks>
            <param name="disposable">The instance that should be disposed when the scope ends.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the arguments is a null reference
            (Nothing in VB).</exception>
            <exception cref="T:System.ObjectDisposedException">Thrown when the scope has been disposed.</exception>
        </member>
        <member name="M:SimpleInjector.Scope.GetItem(System.Object)">
            <summary>
            Retrieves an item from the scope stored by the given <paramref name="key"/> or null when no
            item is stored by that key.
            </summary>
            <remarks>
            <b>Thread-safety:</b> Calls to this method are thread-safe, but users should take proper
            percussions when they call both <b>GetItem</b> and <see cref="M:SimpleInjector.Scope.SetItem(System.Object,System.Object)"/>.
            </remarks>
            <param name="key">The key of the item to retrieve.</param>
            <returns>The stored item or null (Nothing in VB).</returns>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the supplied arguments is a null
            reference (Nothing in VB).</exception>
        </member>
        <member name="M:SimpleInjector.Scope.SetItem(System.Object,System.Object)">
            <summary>Stores an item by the given <paramref name="key"/> in the scope.</summary>
            <remarks>
            <b>Thread-safety:</b> Calls to this method are thread-safe, but users should take proper
            percussions when they call both <see cref="M:SimpleInjector.Scope.GetItem(System.Object)"/> and <b>SetItem</b>.
            </remarks>
            <param name="key">The key of the item to insert or override.</param>
            <param name="item">The actual item. May be null.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when paramref name="key"/> is a null reference 
            (Nothing in VB).</exception>
        </member>
        <member name="M:SimpleInjector.Scope.Dispose">
            <summary>Releases all instances that are cached by the <see cref="T:SimpleInjector.Scope"/> object.</summary>
        </member>
        <member name="M:SimpleInjector.Scope.Dispose(System.Boolean)">
            <summary>
            Releases all instances that are cached by the <see cref="T:SimpleInjector.Scope"/> object.
            </summary>
            <param name="disposing">False when only unmanaged resources should be released.</param>
        </member>
        <member name="T:SimpleInjector.ScopedLifestyle">
            <summary>
            Base class for scoped lifestyles. A scoped lifestyle caches instances for the duration of an implicitly
            or explicitly defined scope. Such scope can be an (implicitly defined) web request or an explicitly
            defined Lifetime Scope. The lifetime of instances registered with a scoped lifestyle is always equal
            or bigger than one-instance-per-object-graph. In other words, a call to GetInstance() will never create
            more than one instance of such registered type.
            </summary>
        </member>
        <member name="M:SimpleInjector.ScopedLifestyle.#ctor(System.String)">
            <summary>Initializes a new instance of the <see cref="T:SimpleInjector.ScopedLifestyle"/> class.</summary>
            <param name="name">The user friendly name of this lifestyle.</param>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="name"/> is null (Nothing in VB) 
            or an empty string.</exception>
        </member>
        <member name="M:SimpleInjector.ScopedLifestyle.#ctor(System.String,System.Boolean)">
            <summary>Initializes a new instance of the <see cref="T:SimpleInjector.ScopedLifestyle"/> class.</summary>
            <param name="name">The user friendly name of this lifestyle.</param>
            <param name="disposeInstances">Signals the lifestyle whether instances should be
            disposed or not.</param>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="name"/> is null (Nothing in VB) 
            or an empty string.</exception>
        </member>
        <member name="P:SimpleInjector.ScopedLifestyle.Length">
            <summary>Gets the length of the lifestyle.</summary>
            <value>The <see cref="T:System.Int32"/> representing the length of this lifestyle.</value>
        </member>
        <member name="M:SimpleInjector.ScopedLifestyle.WhenScopeEnds(SimpleInjector.Container,System.Action)">
            <summary>
            Allows registering an <paramref name="action"/> delegate that will be called when the scope ends,
            but before the scope disposes any instances.
            </summary>
            <remarks>
            During the call to <see cref="M:SimpleInjector.Scope.Dispose"/> all registered <see cref="T:System.Action"/> delegates are
            processed in the order of registration. Do note that registered actions <b>are not guaranteed
            to run</b>. In case an exception is thrown during the call to <see cref="M:SimpleInjector.Scope.Dispose"/>, the 
            <see cref="T:SimpleInjector.Scope"/> will stop running any actions that might not have been invoked at that point. 
            Instances that are registered for disposal using <see cref="M:SimpleInjector.ScopedLifestyle.RegisterForDisposal(SimpleInjector.Container,System.IDisposable)"/> on the other
            hand, are guaranteed to be disposed. Note that registered actions won't be invoked during a call
            to <see cref="M:SimpleInjector.Container.Verify" />.
            </remarks>
            <param name="container">The <see cref="T:SimpleInjector.Container"/> instance.</param>
            <param name="action">The delegate to run when the scope ends.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the arguments is a null reference
            (Nothing in VB).</exception>
            <exception cref="T:System.InvalidOperationException">Will be thrown when there is currently no active
            scope for the supplied <paramref name="container"/>.</exception>
        </member>
        <member name="M:SimpleInjector.ScopedLifestyle.RegisterForDisposal(SimpleInjector.Container,System.IDisposable)">
            <summary>
            Adds the <paramref name="disposable"/> to the list of items that will get disposed when the
            scope ends.
            </summary>
            <param name="container">The <see cref="T:SimpleInjector.Container"/> instance.</param>
            <param name="disposable">The instance that should be disposed when the scope ends.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the arguments is a null reference
            (Nothing in VB).</exception>
            <exception cref="T:System.InvalidOperationException">Will be thrown when there is currently no active
            scope for the supplied <paramref name="container"/>.</exception>
        </member>
        <member name="M:SimpleInjector.ScopedLifestyle.GetCurrentScope(SimpleInjector.Container)">
            <summary>
            Returns the current <see cref="T:SimpleInjector.Scope"/> for this lifestyle and the given 
            <paramref name="container"/>, or null when this method is executed outside the context of a scope.
            </summary>
            <param name="container">The container instance that is related to the scope to return.</param>
            <returns>A <see cref="T:SimpleInjector.Scope"/> instance or null when there is no scope active in this context.</returns>
        </member>
        <member name="M:SimpleInjector.ScopedLifestyle.CreateCurrentScopeProvider(SimpleInjector.Container)">
            <summary>
            Creates a delegate that upon invocation return the current <see cref="T:SimpleInjector.Scope"/> for this
            lifestyle and the given <paramref name="container"/>, or null when the delegate is executed outside
            the context of such scope.
            </summary>
            <param name="container">The container for which the delegate gets created.</param>
            <returns>A <see cref="T:System.Func`1"/> delegate. This method should never return null.</returns>
        </member>
        <member name="M:SimpleInjector.ScopedLifestyle.GetCurrentScopeCore(SimpleInjector.Container)">
            <summary>
            Returns the current <see cref="T:SimpleInjector.Scope"/> for this lifestyle and the given 
            <paramref name="container"/>, or null when this method is executed outside the context of a scope.
            </summary>
            <remarks>
            By default, this method calls the <see cref="M:SimpleInjector.ScopedLifestyle.CreateCurrentScopeProvider(SimpleInjector.Container)"/> method and invokes the
            returned delegate. This method can be overridden to provide an optimized way for getting the
            current scope.
            </remarks>
            <param name="container">The container instance that is related to the scope to return.</param>
            <returns>A <see cref="T:SimpleInjector.Scope"/> instance or null when there is no scope active in this context.</returns>
        </member>
        <member name="M:SimpleInjector.ScopedLifestyle.CreateRegistrationCore``1(System.Func{``0},SimpleInjector.Container)">
            <summary>
            Creates a new <see cref="T:SimpleInjector.Registration"/> instance defining the creation of the
            specified <typeparamref name="TService"/> using the supplied <paramref name="instanceCreator"/> 
            with the caching as specified by this lifestyle.
            </summary>
            <typeparam name="TService">The interface or base type that can be used to retrieve the instances.</typeparam>
            <param name="instanceCreator">A delegate that will create a new instance of 
            <typeparamref name="TService"/> every time it is called.</param>
            <param name="container">The <see cref="T:SimpleInjector.Container"/> instance for which a 
            <see cref="T:SimpleInjector.Registration"/> must be created.</param>
            <returns>A new <see cref="T:SimpleInjector.Registration"/> instance.</returns>
        </member>
        <member name="M:SimpleInjector.ScopedLifestyle.CreateRegistrationCore``2(SimpleInjector.Container)">
            <summary>
            Creates a new <see cref="T:SimpleInjector.Registration"/> instance defining the creation of the
            specified <typeparamref name="TImplementation"/> with the caching as specified by this lifestyle.
            </summary>
            <typeparam name="TService">The interface or base type that can be used to retrieve the instances.</typeparam>
            <typeparam name="TImplementation">The concrete type that will be registered.</typeparam>
            <param name="container">The <see cref="T:SimpleInjector.Container"/> instance for which a 
            <see cref="T:SimpleInjector.Registration"/> must be created.</param>
            <returns>A new <see cref="T:SimpleInjector.Registration"/> instance.</returns>
        </member>
        <member name="T:SimpleInjector.StringResources">
            <summary>Internal helper for string resources.</summary>
        </member>
        <member name="T:SimpleInjector.TypeFactoryContext">
            <summary>
            Contains contextual information for creating an implementation type.
            </summary>
            <remarks>
            Please see the 
            <see cref="M:SimpleInjector.Container.RegisterConditional(System.Type,System.Type,SimpleInjector.Lifestyle,System.Predicate{SimpleInjector.PredicateContext})">Register</see>
            method for more information.
            </remarks>
        </member>
        <member name="P:SimpleInjector.TypeFactoryContext.ServiceType">
            <summary>Gets the closed generic service type that is to be created.</summary>
            <value>The closed generic service type.</value>
        </member>
        <member name="P:SimpleInjector.TypeFactoryContext.Consumer">
            <summary>
            Gets the contextual information of the consuming component that directly depends on the resolved
            service. This property will return null in case the service is resolved directly from the container.
            </summary>
            <value>The <see cref="T:SimpleInjector.InjectionConsumerInfo"/> or null.</value>
        </member>
        <member name="T:SimpleInjector.TypesToRegisterOptions">
            <summary>
            Defines options to control the types returned from the
            <see cref="M:SimpleInjector.Container.GetTypesToRegister(System.Type,System.Collections.Generic.IEnumerable{System.Reflection.Assembly},SimpleInjector.TypesToRegisterOptions)">GetTypesToRegister</see>
            method. For a type to be returned, it should match all the conditions described by the class's
            properties. In other words, in case the searched assembly contains a generic type, that is both a
            decorator and a composite, it will only be returned by <b>GetTypesToRegister</b> in case both
            <see cref="P:SimpleInjector.TypesToRegisterOptions.IncludeGenericTypeDefinitions"/>, <see cref="P:SimpleInjector.TypesToRegisterOptions.IncludeDecorators"/> and 
            <see cref="P:SimpleInjector.TypesToRegisterOptions.IncludeComposites"/> are set to true.
            </summary>
        </member>
        <member name="M:SimpleInjector.TypesToRegisterOptions.#ctor">
            <summary>Initializes a new instance of the <see cref="T:SimpleInjector.TypesToRegisterOptions"/> class.</summary>
        </member>
        <member name="P:SimpleInjector.TypesToRegisterOptions.IncludeDecorators">
            <summary>
            Gets or sets a value indicating whether decorator types should be included in the result. The default
            value of this property is <b>false</b>. A type is considered a decorator if the type's constructor 
            contains a parameter of the type that exactly matches the <code>serviceType</code> argument, 
            supplied to the
            <see cref="M:SimpleInjector.Container.GetTypesToRegister(System.Type,System.Collections.Generic.IEnumerable{System.Reflection.Assembly},SimpleInjector.TypesToRegisterOptions)">GetTypesToRegister</see>
            method, or when there is a <see cref="T:System.Func`1"/> argument where <code>T</code> matches the
            <code>serviceType</code> argument.
            </summary>
            <value>A boolean.</value>
        </member>
        <member name="P:SimpleInjector.TypesToRegisterOptions.IncludeGenericTypeDefinitions">
            <summary>
            Gets or sets a value indicating whether generic type definitions (types that have
            <see cref="P:System.Type.IsGenericTypeDefinition">Type.IsGenericTypeDefinition</see> set to true) 
            should be included in the result. The default value for this property is <b>false</b>.
            </summary>
            <value>A boolean.</value>
        </member>
        <member name="P:SimpleInjector.TypesToRegisterOptions.IncludeComposites">
            <summary>
            Gets or sets a value indicating whether composite types should be included in the result. The default
            value of this property is <b>true</b>. A type is considered a composite if the type's constructor
            contains a parameter of <code>IEnumerable&lt;T&gt;</code>, <code>ICollection&lt;T&gt;</code>,
            <code>IList&lt;T&gt;</code>, <code>IReadOnlyCollection&lt;T&gt;</code>, 
            <code>IReadOnlyList&lt;T&gt;</code> or <code>T[]</code> (array of T), where <code>T</code>
            exactly matches the <code>serviceType</code> argument, supplied to the
            <see cref="M:SimpleInjector.Container.GetTypesToRegister(System.Type,System.Collections.Generic.IEnumerable{System.Reflection.Assembly},SimpleInjector.TypesToRegisterOptions)">GetTypesToRegister</see>
            method.
            </summary>
            <value>A boolean.</value>
        </member>
        <member name="T:SimpleInjector.UnregisteredTypeEventArgs">
            <summary>
            Provides data for and interaction with the 
            <see cref="E:SimpleInjector.Container.ResolveUnregisteredType">ResolveUnregisteredType</see> event of 
            the <see cref="T:SimpleInjector.Container"/>. An observer can check the 
            <see cref="P:SimpleInjector.UnregisteredTypeEventArgs.UnregisteredServiceType"/> to see whether the unregistered type can be handled. The
            <see cref="M:SimpleInjector.UnregisteredTypeEventArgs.Register(System.Func{System.Object})"/> method can be called to register a <see cref="T:System.Func`1"/> delegate 
            that allows creation of instances of the unregistered for this and future requests.
            </summary>
        </member>
        <member name="P:SimpleInjector.UnregisteredTypeEventArgs.UnregisteredServiceType">
            <summary>Gets the unregistered service type that is currently requested.</summary>
            <value>The unregistered service type that is currently requested.</value>
        </member>
        <member name="P:SimpleInjector.UnregisteredTypeEventArgs.Handled">
            <summary>
            Gets a value indicating whether the event represented by this instance has been handled. 
            This property will return <b>true</b> when <see cref="M:SimpleInjector.UnregisteredTypeEventArgs.Register(System.Func{System.Object})"/> has been called on
            this instance.
            </summary>
            <value>The indication whether the event has been handled.</value>
        </member>
        <member name="M:SimpleInjector.UnregisteredTypeEventArgs.Register(System.Func{System.Object})">
            <summary>
            Registers a <see cref="T:System.Func`1"/> delegate that allows creation of instances of the type
            expressed by the <see cref="P:SimpleInjector.UnregisteredTypeEventArgs.UnregisteredServiceType"/> for this and future requests. The delegate
            will be caches and future requests will directly call that delegate.
            </summary>
            <param name="instanceCreator">The delegate that allows creation of instances of the type
            expressed by the <see cref="P:SimpleInjector.UnregisteredTypeEventArgs.UnregisteredServiceType"/>.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when the <paramref name="instanceCreator"/> is a
            null reference.</exception>
            <exception cref="T:SimpleInjector.ActivationException">Thrown when multiple observers that have registered to
            the <see cref="E:SimpleInjector.Container.ResolveUnregisteredType">ResolveUnregisteredType</see> event
            called this method for the same type.</exception>
        </member>
        <member name="M:SimpleInjector.UnregisteredTypeEventArgs.Register(System.Linq.Expressions.Expression)">
            <summary>
            Registers an <see cref="P:SimpleInjector.UnregisteredTypeEventArgs.Expression"/> that describes the creation of instances of the type
            expressed by the <see cref="P:SimpleInjector.UnregisteredTypeEventArgs.UnregisteredServiceType"/> for this and future requests. The delegate
            will be cached and future requests will directly use that expression or the compiled delegate.
            </summary>
            <remarks>
            NOTE: If possible, use the <see cref="M:SimpleInjector.UnregisteredTypeEventArgs.Register(SimpleInjector.Registration)">Register(Registration)</see> overload,
            since this allows the analysis services to determine any configuration errors on the lifestyle of
            the registration.
            </remarks>
            <param name="expression">The expression that describes the creation of instances of the type
            expressed by the <see cref="P:SimpleInjector.UnregisteredTypeEventArgs.UnregisteredServiceType"/>.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when the <paramref name="expression"/> is a
            null reference.</exception>
            <exception cref="T:SimpleInjector.ActivationException">Thrown when multiple observers that have registered to
            the <see cref="E:SimpleInjector.Container.ResolveUnregisteredType">ResolveUnregisteredType</see> event
            called this method for the same type.</exception>
        </member>
        <member name="M:SimpleInjector.UnregisteredTypeEventArgs.Register(SimpleInjector.Registration)">
            <summary>
            Registers a <see cref="P:SimpleInjector.UnregisteredTypeEventArgs.Registration"/> that describes the creation of instances of the type
            expressed by the <see cref="P:SimpleInjector.UnregisteredTypeEventArgs.UnregisteredServiceType"/> for this and future requests. The 
            registration will be cached and future requests will directly call unon that registration, the
            expression that it generates or the delegate that gets compiled from that expression.
            </summary>
            <param name="registration">The registration that describes the creation of instances according to
            the registration's lifestyle of the type expressed by the <see cref="P:SimpleInjector.UnregisteredTypeEventArgs.UnregisteredServiceType"/>.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when the <paramref name="registration"/> is a
            null reference.</exception>
            <exception cref="T:System.ArgumentException">Thrown when the <paramref name="registration"/> is a
            not exactly of type <see cref="T:System.Func`1"/> where T equals the <see cref="P:SimpleInjector.UnregisteredTypeEventArgs.UnregisteredServiceType"/>.
            </exception>
            <exception cref="T:SimpleInjector.ActivationException">Thrown when multiple observers that have registered to
            the <see cref="E:SimpleInjector.Container.ResolveUnregisteredType">ResolveUnregisteredType</see> event
            called this method for the same type.</exception>
        </member>
        <member name="T:SimpleInjector.VerificationOption">
            <summary>
            This enumeration has defines in which way the container should run the verification process.
            </summary>
        </member>
        <member name="F:SimpleInjector.VerificationOption.VerifyOnly">
            <summary>
            Specifies that the container performs verification only, which means that it will test whether
            all registrations can be constructed by iterating the registrations and letting the container
            create at least one instance of each registration. An <see cref="T:System.InvalidOperationException"/>
            will be thrown in case the configuration is invalid.
            </summary>
        </member>
        <member name="F:SimpleInjector.VerificationOption.VerifyAndDiagnose">
            <summary>
            Specifies that the container will run diagnostic analysis after the verification succeeded. The
            container will diagnose the configuration with a subset of the available diagnostic warnings, that 
            are most likely an indication of a configuration mistake. A complete set of diagnostic warnings
            can be retrieved by calling 
            <see cref="M:SimpleInjector.Diagnostics.Analyzer.Analyze(SimpleInjector.Container)">Analyzer.Analyze</see> or by viewing the 
            container in the Visual Studio debugger, after the verification has succeeded.
            </summary>
        </member>
        <member name="T:SimpleInjector.Extensions.DecoratorExtensions">
            <summary>
            Extension methods for applying decorators.
            </summary>
        </member>
        <member name="M:SimpleInjector.Extensions.DecoratorExtensions.RegisterDecorator(SimpleInjector.Container,System.Type,System.Type,SimpleInjector.Lifestyle)">
            <summary>This method has been removed.</summary>
            <param name="container">The container to make the registrations in.</param>
            <param name="serviceType">The definition of the open generic service type that will
            be wrapped by the given <paramref name="decoratorType"/>.</param>
            <param name="decoratorType">The definition of the open generic decorator type that will
            be used to wrap the original service type.</param>
            <param name="lifestyle">The lifestyle that specifies how the returned decorator will be cached.</param>
        </member>
        <member name="M:SimpleInjector.Extensions.DecoratorExtensions.RegisterDecorator(SimpleInjector.Container,System.Type,System.Type,SimpleInjector.Lifestyle,System.Predicate{SimpleInjector.DecoratorPredicateContext})">
            <summary>This method has been removed.</summary>
            <param name="container">The container to make the registrations in.</param>
            <param name="serviceType">The definition of the open generic service type that will
            be wrapped by the given <paramref name="decoratorType"/>.</param>
            <param name="decoratorType">The definition of the open generic decorator type that will
            be used to wrap the original service type.</param>
            <param name="lifestyle">The lifestyle that specifies how the returned decorator will be cached.</param>
            <param name="predicate">The predicate that determines whether the 
            <paramref name="decoratorType"/> must be applied to a service type.</param>
        </member>
        <member name="M:SimpleInjector.Extensions.DecoratorExtensions.RegisterDecorator(SimpleInjector.Container,System.Type,System.Func{SimpleInjector.DecoratorPredicateContext,System.Type},SimpleInjector.Lifestyle,System.Predicate{SimpleInjector.DecoratorPredicateContext})">
            <summary>This method has been removed.</summary>
            <param name="container">The container to make the registrations in.</param>
            <param name="serviceType">The definition of the open generic service type that will
            be wrapped by the decorator type returned by the supplied <paramref name="decoratorTypeFactory"/>.</param>
            <param name="decoratorTypeFactory">A factory that allows building Type objects that define the
            decorators to inject, based on the given contextual information. The delegate is allowed to return
            open-generic types.</param>
            <param name="lifestyle">The lifestyle that specifies how the returned decorator will be cached.</param>
            <param name="predicate">The predicate that determines whether the decorator must be applied to a 
            service type.</param>
        </member>
        <member name="M:SimpleInjector.Extensions.DecoratorExtensions.RegisterDecorator(SimpleInjector.Container,System.Type,System.Type)">
            <summary>This method has been removed.</summary>
            <param name="container">The container to make the registrations in.</param>
            <param name="serviceType">The definition of the open generic service type that will
            be wrapped by the given <paramref name="decoratorType"/>.</param>
            <param name="decoratorType">The definition of the open generic decorator type that will
            be used to wrap the original service type.</param>
        </member>
        <member name="M:SimpleInjector.Extensions.DecoratorExtensions.RegisterDecorator(SimpleInjector.Container,System.Type,System.Type,System.Predicate{SimpleInjector.DecoratorPredicateContext})">
            <summary>This method has been removed.</summary>
            <param name="container">The container to make the registrations in.</param>
            <param name="serviceType">The definition of the open generic service type that will
            be wrapped by the given <paramref name="decoratorType"/>.</param>
            <param name="decoratorType">The definition of the open generic decorator type that will
            be used to wrap the original service type.</param>
            <param name="predicate">The predicate that determines whether the 
            <paramref name="decoratorType"/> must be applied to a service type.</param>
        </member>
        <member name="M:SimpleInjector.Extensions.DecoratorExtensions.RegisterSingleDecorator(SimpleInjector.Container,System.Type,System.Type)">
            <summary>This method has been removed.</summary>
            <param name="container">The container to make the registrations in.</param>
            <param name="serviceType">The definition of the open generic service type that will
            be wrapped by the given <paramref name="decoratorType"/>.</param>
            <param name="decoratorType">The definition of the open generic decorator type that will
            be used to wrap the original service type.</param>
        </member>
        <member name="M:SimpleInjector.Extensions.DecoratorExtensions.RegisterSingleDecorator(SimpleInjector.Container,System.Type,System.Type,System.Predicate{SimpleInjector.DecoratorPredicateContext})">
            <summary>This method has been removed.</summary>
            <param name="container">The container to make the registrations in.</param>
            <param name="serviceType">The definition of the open generic service type that will
            be wrapped by the given <paramref name="decoratorType"/>.</param>
            <param name="decoratorType">The definition of the open generic decorator type that will
            be used to wrap the original service type.</param>
            <param name="predicate">The predicate that determines whether the 
            <paramref name="decoratorType"/> must be applied to a service type.</param>
        </member>
        <member name="T:SimpleInjector.Extensions.BatchRegistrationCallback">
            <summary>
            Represents the method that will called to register one or multiple concrete non-generic
            <paramref name="implementations"/> of the given closed generic type 
            <paramref name="closedServiceType"/>.
            </summary>
            <param name="closedServiceType">The service type that needs to be registered.</param>
            <param name="implementations">One or more concrete types that implement the given 
            <paramref name="closedServiceType"/>.</param>
            <example>
            <code lang="cs"><![CDATA[
            var container = new Container();
            
            BatchRegistrationCallback registerAsCollectionAsSingletons = (closedServiceType, implementations) =>
            {
                foreach (Type implementation in implementations)
                {
                    container.Register(implementation, implementation, Lifestyle.Singleton);
                }
                
                container.RegisterCollection(closedServiceType, implementations);
            };
            
            container.RegisterManyForOpenGeneric(
                typeof(ICommandHandler<>),
                registerAsCollectionAsSingletons, 
                typeof(ICommandHandler<>).Assembly);
            ]]></code>
            The <b>BatchRegistrationCallback</b> can be supplied to some overloads of the
            <see cref="T:SimpleInjector.Extensions.OpenGenericBatchRegistrationExtensions">RegisterManyForOpenGeneric</see> extension methods.
            The default behavior of the <b>RegisterManyForOpenGeneric</b> methods is to register a closed generic
            type with the corresponding implementation (and will throw when multiple implementations are found for
            a single closed generic service type). The given example overrides this default registration by 
            registering the found list of implementations (one or more) as collection of singletons for the given 
            closed generic service type.
            </example>
        </member>
        <member name="T:SimpleInjector.Extensions.AccessibilityOption">
            <summary>Defines the accessibility of the types to search.</summary>
        </member>
        <member name="F:SimpleInjector.Extensions.AccessibilityOption.AllTypes">
            <summary>Load both public as internal types from the given assemblies.</summary>
        </member>
        <member name="F:SimpleInjector.Extensions.AccessibilityOption.PublicTypesOnly">
            <summary>Only load publicly exposed types from the given assemblies.</summary>
        </member>
        <member name="T:SimpleInjector.Extensions.OpenGenericBatchRegistrationExtensions">
            <summary>
            Provides a set of static (Shared in Visual Basic) methods for registration many concrete types at
            once that implement the same open generic service types in the <see cref="T:SimpleInjector.Container"/>.
            </summary>
        </member>
        <member name="M:SimpleInjector.Extensions.OpenGenericBatchRegistrationExtensions.RegisterManyForOpenGeneric(SimpleInjector.Container,System.Type,System.Reflection.Assembly[])">
            <summary>
            Registers all concrete, non-generic, public and internal types in the given set of
            <paramref name="assemblies"/> that implement the given <paramref name="openGenericServiceType"/> 
            with a transient lifetime.
            </summary>
            <param name="container">The container to make the registrations in.</param>
            <param name="openGenericServiceType">The definition of the open generic type.</param>
            <param name="assemblies">A list of assemblies that will be searched.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="container"/>,
            <paramref name="openGenericServiceType"/>, or <paramref name="assemblies"/> contain a null
            reference (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="openGenericServiceType"/> is not
            an open generic type.</exception>
            <exception cref="T:System.InvalidOperationException">Thrown when the given set of 
            <paramref name="assemblies"/> contain multiple types that implement the same 
            closed generic version of the given <paramref name="openGenericServiceType"/>.</exception>
        </member>
        <member name="M:SimpleInjector.Extensions.OpenGenericBatchRegistrationExtensions.RegisterManyForOpenGeneric(SimpleInjector.Container,System.Type,System.Collections.Generic.IEnumerable{System.Reflection.Assembly})">
            <summary>
            Registers all concrete, non-generic, public and internal types that are located in the given 
            <paramref name="assemblies"/> that implement the given <paramref name="openGenericServiceType"/> 
            with a transient lifetime.
            </summary>
            <param name="container">The container to make the registrations in.</param>
            <param name="openGenericServiceType">The definition of the open generic type.</param>
            <param name="assemblies">A list of assemblies that will be searched.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="container"/>,
            <paramref name="openGenericServiceType"/>, or <paramref name="assemblies"/> contain a null
            reference (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="openGenericServiceType"/> is not
            an open generic type.</exception>
            <exception cref="T:System.InvalidOperationException">Thrown when the given set of 
            <paramref name="assemblies"/> contain multiple types that implement the same 
            closed generic version of the given <paramref name="openGenericServiceType"/>.</exception>
        </member>
        <member name="M:SimpleInjector.Extensions.OpenGenericBatchRegistrationExtensions.RegisterManyForOpenGeneric(SimpleInjector.Container,System.Type,SimpleInjector.Lifestyle,System.Reflection.Assembly[])">
            <summary>
            Registers all concrete, non-generic, public and internal types in the given set of
            <paramref name="assemblies"/> that implement the given <paramref name="openGenericServiceType"/> 
            with the supplied <paramref name="lifestyle"/>. When a found type implements multiple 
            closed-generic versions of the given <paramref name="openGenericServiceType"/>, both closed-generic
            service types will point at the same registration and return the same instance based on the caching
            behavior of the supplied <paramref name="lifestyle"/>.
            </summary>
            <param name="container">The container to make the registrations in.</param>
            <param name="openGenericServiceType">The definition of the open generic type.</param>
            <param name="lifestyle">The lifestyle that will be used for the registration of the types.</param>
            <param name="assemblies">A list of assemblies that will be searched.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="container"/>,
            <paramref name="openGenericServiceType"/>, <paramref name="lifestyle"/> or 
            <paramref name="assemblies"/> contain a null reference (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="openGenericServiceType"/> is not
            an open generic type.</exception>
            <exception cref="T:System.InvalidOperationException">Thrown when the given set of 
            <paramref name="assemblies"/> contain multiple types that implement the same 
            closed generic version of the given <paramref name="openGenericServiceType"/>.</exception>
        </member>
        <member name="M:SimpleInjector.Extensions.OpenGenericBatchRegistrationExtensions.RegisterManyForOpenGeneric(SimpleInjector.Container,System.Type,SimpleInjector.Lifestyle,System.Collections.Generic.IEnumerable{System.Reflection.Assembly})">
            <summary>
            Registers all concrete, non-generic, public and internal types that are located in the given 
            <paramref name="assemblies"/> that implement the given <paramref name="openGenericServiceType"/> 
            with the supplied <paramref name="lifestyle"/>. When a found type implements multiple 
            closed-generic versions of the given <paramref name="openGenericServiceType"/>, both closed-generic
            service types will point at the same registration and return the same instance based on the caching
            behavior of the supplied <paramref name="lifestyle"/>.
            </summary>
            <param name="container">The container to make the registrations in.</param>
            <param name="openGenericServiceType">The definition of the open generic type.</param>
            <param name="lifestyle">The lifestyle that will be used for the registration of the types.</param>
            <param name="assemblies">A list of assemblies that will be searched.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="container"/>,
            <paramref name="openGenericServiceType"/>, <paramref name="lifestyle"/> or 
            <paramref name="assemblies"/> contain a null reference (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="openGenericServiceType"/> is not
            an open generic type.</exception>
            <exception cref="T:System.InvalidOperationException">Thrown when the given set of 
            <paramref name="assemblies"/> contain multiple types that implement the same 
            closed generic version of the given <paramref name="openGenericServiceType"/>.</exception>
        </member>
        <member name="M:SimpleInjector.Extensions.OpenGenericBatchRegistrationExtensions.RegisterManyForOpenGeneric(SimpleInjector.Container,System.Type,SimpleInjector.Extensions.BatchRegistrationCallback,System.Reflection.Assembly[])">
            <summary>
            Allows registration of all concrete, public and internal, non-generic types that are located in the given set of 
            <paramref name="assemblies"/> that implement the given <paramref name="openGenericServiceType"/>, 
            by supplying a <see cref="T:SimpleInjector.Extensions.BatchRegistrationCallback"/> delegate, that will be called for each 
            found closed generic implementation of the given <paramref name="openGenericServiceType"/>.
            </summary>
            <param name="container">The container to make the registrations in.</param>
            <param name="openGenericServiceType">The definition of the open generic type.</param>
            <param name="callback">The delegate that will be called for each found closed generic version of
            the given open generic <paramref name="openGenericServiceType"/> to do the actual registration.</param>
            <param name="assemblies">A list of assemblies that will be searched.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="container"/>,
            <paramref name="openGenericServiceType"/>, <paramref name="callback"/>, or 
            <paramref name="assemblies"/> contain a null reference (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="openGenericServiceType"/> is not
            an open generic type.</exception>
        </member>
        <member name="M:SimpleInjector.Extensions.OpenGenericBatchRegistrationExtensions.RegisterManyForOpenGeneric(SimpleInjector.Container,System.Type,SimpleInjector.Extensions.BatchRegistrationCallback,System.Collections.Generic.IEnumerable{System.Reflection.Assembly})">
            <summary>
            Allows registration of all concrete, public and internal, non-generic types that are located in the given set of 
            <paramref name="assemblies"/> that implement the given <paramref name="openGenericServiceType"/>, 
            by supplying a <see cref="T:SimpleInjector.Extensions.BatchRegistrationCallback"/> delegate, that will be called for each 
            found closed generic implementation of the given <paramref name="openGenericServiceType"/>.
            </summary>
            <param name="container">The container to make the registrations in.</param>
            <param name="openGenericServiceType">The definition of the open generic type.</param>
            <param name="callback">The delegate that will be called for each found closed generic version of
            the given open generic <paramref name="openGenericServiceType"/> to do the actual registration.</param>
            <param name="assemblies">A list of assemblies that will be searched.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="container"/>,
            <paramref name="openGenericServiceType"/>, <paramref name="callback"/>, or 
            <paramref name="assemblies"/> contain a null reference (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="openGenericServiceType"/> is not
            an open generic type.</exception>
        </member>
        <member name="M:SimpleInjector.Extensions.OpenGenericBatchRegistrationExtensions.RegisterManyForOpenGeneric(SimpleInjector.Container,System.Type,System.Type[])">
            <summary>
            Registers all supplied <paramref name="typesToRegister"/> by a closed generic definition of the
            given <paramref name="openGenericServiceType"/> with a transient lifetime.
            </summary>
            <param name="container">The container to make the registrations in.</param>
            <param name="openGenericServiceType">The definition of the open generic type.</param>
            <param name="typesToRegister">The list of types that must be registered according to the given
            <paramref name="openGenericServiceType"/> definition.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="container"/>, 
            <paramref name="openGenericServiceType"/>, or <paramref name="typesToRegister"/> contain a null
            reference (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="typesToRegister"/> contains a null
            (Nothing in VB) element, when the <paramref name="openGenericServiceType"/> is not an open generic
            type, or one of the types supplied in <paramref name="typesToRegister"/> does not implement a 
            closed version of <paramref name="openGenericServiceType"/>.
            </exception>
            <exception cref="T:System.InvalidOperationException">Thrown when there are multiple types in the given
            <paramref name="typesToRegister"/> collection that implement the same closed version of the
            supplied <paramref name="openGenericServiceType"/>.
            </exception>
        </member>
        <member name="M:SimpleInjector.Extensions.OpenGenericBatchRegistrationExtensions.RegisterManyForOpenGeneric(SimpleInjector.Container,System.Type,System.Collections.Generic.IEnumerable{System.Type})">
            <summary>
            Registers all supplied <paramref name="typesToRegister"/> by a closed generic definition of the
            given <paramref name="openGenericServiceType"/> with a transient lifetime.
            </summary>
            <param name="container">The container to make the registrations in.</param>
            <param name="openGenericServiceType">The definition of the open generic type.</param>
            <param name="typesToRegister">The list of types that must be registered according to the given
            <paramref name="openGenericServiceType"/> definition.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="container"/>, 
            <paramref name="openGenericServiceType"/>, or <paramref name="typesToRegister"/> contain a null
            reference (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="typesToRegister"/> contains a null
            (Nothing in VB) element, when the <paramref name="openGenericServiceType"/> is not an open generic
            type, or one of the types supplied in <paramref name="typesToRegister"/> does not implement a 
            closed version of <paramref name="openGenericServiceType"/>.
            </exception>
            <exception cref="T:System.InvalidOperationException">Thrown when there are multiple types in the given
            <paramref name="typesToRegister"/> collection that implement the same closed version of the
            supplied <paramref name="openGenericServiceType"/>.
            </exception>
        </member>
        <member name="M:SimpleInjector.Extensions.OpenGenericBatchRegistrationExtensions.RegisterManyForOpenGeneric(SimpleInjector.Container,System.Type,SimpleInjector.Extensions.BatchRegistrationCallback,System.Collections.Generic.IEnumerable{System.Type})">
            <summary>
            Allows registration of all supplied <paramref name="typesToRegister"/> by a closed generic 
            definition of the given <paramref name="openGenericServiceType"/>, by supplying a 
            <see cref="T:SimpleInjector.Extensions.BatchRegistrationCallback"/> delegate, that will be called for each found closed generic 
            implementation.
            If the list contains open generic types, matching closed generic versions of each open generic
            type will be added to the list of implementations that is passed on to the 
            <paramref name="callback"/> delegate.
            </summary>
            <param name="container">The container to make the registrations in.</param>
            <param name="openGenericServiceType">The definition of the open generic type.</param>
            <param name="callback">The delegate that will be called for each found closed generic version of
            the given open generic <paramref name="openGenericServiceType"/> to do the actual registration.</param>
            <param name="typesToRegister">The list of types that must be registered according to the given
            <paramref name="openGenericServiceType"/> definition.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="container"/>, 
            <paramref name="openGenericServiceType"/>, <paramref name="callback"/>, or 
            <paramref name="typesToRegister"/> contain a null reference (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="typesToRegister"/> contains a null
            (Nothing in VB) element, when the <paramref name="openGenericServiceType"/> is not an open generic
            type, or one of the types supplied in <paramref name="typesToRegister"/> does not implement a 
            closed version of <paramref name="openGenericServiceType"/>.
            </exception>
        </member>
        <member name="M:SimpleInjector.Extensions.OpenGenericBatchRegistrationExtensions.RegisterManyForOpenGeneric(SimpleInjector.Container,System.Type,SimpleInjector.Lifestyle,System.Type[])">
            <summary>
            Registers all supplied <paramref name="typesToRegister"/> by a closed generic definition of the
            given <paramref name="openGenericServiceType"/> with the supplied <paramref name="lifestyle"/>.
            When a found type implements multiple 
            closed-generic versions of the given <paramref name="openGenericServiceType"/>, both closed-generic
            service types will return the exact same instance.
            </summary>
            <param name="container">The container to make the registrations in.</param>
            <param name="openGenericServiceType">The definition of the open generic type.</param>
            <param name="lifestyle">The lifestyle the registrations are made in.</param>
            <param name="typesToRegister">The list of types that must be registered according to the given
            <paramref name="openGenericServiceType"/> definition.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="container"/>, 
            <paramref name="openGenericServiceType"/>, or <paramref name="typesToRegister"/> contain a null
            reference (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="typesToRegister"/> contains a null
            (Nothing in VB) element, when the <paramref name="openGenericServiceType"/> is not an open generic
            type, or one of the types supplied in <paramref name="typesToRegister"/> does not implement a 
            closed version of <paramref name="openGenericServiceType"/>.
            </exception>
            <exception cref="T:System.InvalidOperationException">Thrown when there are multiple types in the given
            <paramref name="typesToRegister"/> collection that implement the same closed version of the
            supplied <paramref name="openGenericServiceType"/>.
            </exception>
        </member>
        <member name="M:SimpleInjector.Extensions.OpenGenericBatchRegistrationExtensions.RegisterManyForOpenGeneric(SimpleInjector.Container,System.Type,SimpleInjector.Lifestyle,System.Collections.Generic.IEnumerable{System.Type})">
            <summary>
            Registers all supplied <paramref name="typesToRegister"/> by a closed generic definition of the
            given <paramref name="openGenericServiceType"/> with the supplied <paramref name="lifestyle"/>
            When a found type implements multiple 
            closed-generic versions of the given <paramref name="openGenericServiceType"/>, both closed-generic
            service types will return the exact same instance.
            </summary>
            <param name="container">The container to make the registrations in.</param>
            <param name="openGenericServiceType">The definition of the open generic type.</param>
            <param name="lifestyle">The lifestyle the registrations are made in.</param>
            <param name="typesToRegister">The list of types that must be registered according to the given
            <paramref name="openGenericServiceType"/> definition.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="container"/>, 
            <paramref name="openGenericServiceType"/>, or <paramref name="typesToRegister"/> contain a null
            reference (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="typesToRegister"/> contains a null
            (Nothing in VB) element, when the <paramref name="openGenericServiceType"/> is not an open generic
            type, or one of the types supplied in <paramref name="typesToRegister"/> does not implement a 
            closed version of <paramref name="openGenericServiceType"/>.
            </exception>
            <exception cref="T:System.InvalidOperationException">Thrown when there are multiple types in the given
            <paramref name="typesToRegister"/> collection that implement the same closed version of the
            supplied <paramref name="openGenericServiceType"/>.
            </exception>
        </member>
        <member name="M:SimpleInjector.Extensions.OpenGenericBatchRegistrationExtensions.RegisterManyForOpenGeneric(SimpleInjector.Container,System.Type,SimpleInjector.Extensions.AccessibilityOption,System.Reflection.Assembly[])">
            <summary>
            Registers  all concrete, non-generic types with the given <paramref name="accessibility"/>
            that are located in the given <paramref name="assemblies"/> that implement the given 
            <paramref name="openGenericServiceType"/> with a transient lifetime.
            </summary>
            <param name="container">The container to make the registrations in.</param>
            <param name="openGenericServiceType">The definition of the open generic type.</param>
            <param name="accessibility">Defines which types should be used from the given assemblies.</param>
            <param name="assemblies">A list of assemblies that will be searched.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="container"/>,
            <paramref name="openGenericServiceType"/>, or <paramref name="assemblies"/> contain a null
            reference (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="openGenericServiceType"/> is not
            an open generic type or when <paramref name="accessibility"/> contains an invalid value.</exception>
            <exception cref="T:System.InvalidOperationException">Thrown when the given set of 
            <paramref name="assemblies"/> contain multiple types that implement the same closed generic 
            version of the given <paramref name="openGenericServiceType"/>.</exception>
        </member>
        <member name="M:SimpleInjector.Extensions.OpenGenericBatchRegistrationExtensions.RegisterManyForOpenGeneric(SimpleInjector.Container,System.Type,SimpleInjector.Extensions.AccessibilityOption,System.Collections.Generic.IEnumerable{System.Reflection.Assembly})">
            <summary>
            Registers all concrete, non-generic types with the given <paramref name="accessibility"/> 
            that are located in the given <paramref name="assemblies"/> that implement the given 
            <paramref name="openGenericServiceType"/> with a transient lifetime.
            </summary>
            <param name="container">The container to make the registrations in.</param>
            <param name="openGenericServiceType">The definition of the open generic type.</param>
            <param name="accessibility">Defines which types should be used from the given assemblies.</param>
            <param name="assemblies">A list of assemblies that will be searched.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="container"/>,
            <paramref name="openGenericServiceType"/>, or <paramref name="assemblies"/> contain a null
            reference (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="openGenericServiceType"/> is not
            an open generic type or when the <paramref name="accessibility"/> argument contains an invalid
            value.</exception>
            <exception cref="T:System.InvalidOperationException">Thrown when the given set of 
            <paramref name="assemblies"/> contain multiple types that implement the same 
            closed generic version of the given <paramref name="openGenericServiceType"/>.</exception>
        </member>
        <member name="M:SimpleInjector.Extensions.OpenGenericBatchRegistrationExtensions.RegisterManyForOpenGeneric(SimpleInjector.Container,System.Type,SimpleInjector.Extensions.AccessibilityOption,SimpleInjector.Lifestyle,System.Reflection.Assembly[])">
            <summary>
            Registers  all concrete, non-generic types with the given <paramref name="accessibility"/>
            that are located in the given <paramref name="assemblies"/> that implement the given 
            <paramref name="openGenericServiceType"/> with the supplied <paramref name="lifestyle"/>.
            When a found type implements multiple 
            closed-generic versions of the given <paramref name="openGenericServiceType"/>, both closed-generic
            service types will point at the same registration and return the same instance based on the caching
            behavior of the supplied <paramref name="lifestyle"/>.
            </summary>
            <param name="container">The container to make the registrations in.</param>
            <param name="openGenericServiceType">The definition of the open generic type.</param>
            <param name="accessibility">Defines which types should be used from the given assemblies.</param>
            <param name="lifestyle">The lifestyle that will be used for the registration of the types.</param>
            <param name="assemblies">A list of assemblies that will be searched.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="container"/>,
            <paramref name="openGenericServiceType"/>, <paramref name="lifestyle"/> or 
            <paramref name="assemblies"/> contain a null reference (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="openGenericServiceType"/> is not
            an open generic type or when <paramref name="accessibility"/> contains an invalid value.</exception>
            <exception cref="T:System.InvalidOperationException">Thrown when the given set of 
            <paramref name="assemblies"/> contain multiple types that implement the same closed generic 
            version of the given <paramref name="openGenericServiceType"/>.</exception>
        </member>
        <member name="M:SimpleInjector.Extensions.OpenGenericBatchRegistrationExtensions.RegisterManyForOpenGeneric(SimpleInjector.Container,System.Type,SimpleInjector.Extensions.AccessibilityOption,SimpleInjector.Lifestyle,System.Collections.Generic.IEnumerable{System.Reflection.Assembly})">
            <summary>
            Registers all concrete, non-generic types with the given <paramref name="accessibility"/> 
            that are located in the given <paramref name="assemblies"/> that implement the given 
            <paramref name="openGenericServiceType"/> with the supplied <paramref name="lifestyle"/>.
            When a found type implements multiple 
            closed-generic versions of the given <paramref name="openGenericServiceType"/>, both closed-generic
            service types will point at the same registration and return the same instance based on the caching
            behavior of the supplied <paramref name="lifestyle"/>.
            </summary>
            <param name="container">The container to make the registrations in.</param>
            <param name="openGenericServiceType">The definition of the open generic type.</param>
            <param name="accessibility">Defines which types should be used from the given assemblies.</param>
            <param name="lifestyle">The lifestyle that will be used for the registration of the types.</param>
            <param name="assemblies">A list of assemblies that will be searched.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="container"/>,
            <paramref name="openGenericServiceType"/>, <paramref name="lifestyle"/> or 
            <paramref name="assemblies"/> contain a null reference (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="openGenericServiceType"/> is not
            an open generic type or when <paramref name="accessibility"/> contains an invalid value.</exception>
            <exception cref="T:System.InvalidOperationException">Thrown when the given set of 
            <paramref name="assemblies"/> contain multiple types that implement the same 
            closed generic version of the given <paramref name="openGenericServiceType"/>.</exception>
        </member>
        <member name="M:SimpleInjector.Extensions.OpenGenericBatchRegistrationExtensions.RegisterManyForOpenGeneric(SimpleInjector.Container,System.Type,SimpleInjector.Extensions.AccessibilityOption,SimpleInjector.Extensions.BatchRegistrationCallback,System.Reflection.Assembly[])">
            <summary>
            Allows registration of all concrete, non-generic types with the given 
            <paramref name="accessibility"/> that are located in the given set of <paramref name="assemblies"/> 
            that implement the given <paramref name="openGenericServiceType"/>, by supplying a 
            <see cref="T:SimpleInjector.Extensions.BatchRegistrationCallback"/> delegate, that will be called for each found closed generic 
            implementation of the given <paramref name="openGenericServiceType"/>.
            </summary>
            <param name="container">The container to make the registrations in.</param>
            <param name="openGenericServiceType">The definition of the open generic type.</param>
            <param name="accessibility">Defines which types should be used from the given assemblies.</param>
            <param name="callback">The delegate that will be called for each found closed generic version of
            the given open generic <paramref name="openGenericServiceType"/> to do the actual registration.</param>
            <param name="assemblies">A list of assemblies that will be searched.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="container"/>,
            <paramref name="openGenericServiceType"/>, <paramref name="callback"/>, or 
            <paramref name="assemblies"/> contain a null reference (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="openGenericServiceType"/> is not
            an open generic type or when <paramref name="accessibility"/> contains an invalid value.</exception>
        </member>
        <member name="M:SimpleInjector.Extensions.OpenGenericBatchRegistrationExtensions.RegisterManyForOpenGeneric(SimpleInjector.Container,System.Type,SimpleInjector.Extensions.AccessibilityOption,SimpleInjector.Extensions.BatchRegistrationCallback,System.Collections.Generic.IEnumerable{System.Reflection.Assembly})">
            <summary>
            Allows registration of all concrete, non-generic types with the given 
            <paramref name="accessibility"/> that are located in the given set of <paramref name="assemblies"/> 
            that implement the given <paramref name="openGenericServiceType"/>, by supplying a 
            <see cref="T:SimpleInjector.Extensions.BatchRegistrationCallback"/> delegate, that will be called for each found closed generic 
            implementation of the given <paramref name="openGenericServiceType"/>.
            </summary>
            <param name="container">The container to make the registrations in.</param>
            <param name="openGenericServiceType">The definition of the open generic type.</param>
            <param name="accessibility">Defines which types should be used from the given assemblies.</param>
            <param name="callback">The delegate that will be called for each found closed generic version of
            the given open generic <paramref name="openGenericServiceType"/> to do the actual registration.</param>
            <param name="assemblies">A list of assemblies that will be searched.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="container"/>,
            <paramref name="openGenericServiceType"/>, <paramref name="callback"/>, or 
            <paramref name="assemblies"/> contain a null reference (Nothing in VB).</exception>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="openGenericServiceType"/> is not
            an open generic type or when <paramref name="accessibility"/> contains an invalid value.</exception>
        </member>
        <member name="M:SimpleInjector.Extensions.OpenGenericBatchRegistrationExtensions.GetTypesToRegister(SimpleInjector.Container,System.Type,SimpleInjector.Extensions.AccessibilityOption,System.Reflection.Assembly[])">
            <summary>
            Returns all types that are located in the supplied <paramref name="assemblies"/> 
            and implement or inherit from the supplied <paramref name="openGenericServiceType"/>.
            </summary>
            <remarks>
            Use this method when you need influence the types that are registered using 
            <see cref="M:SimpleInjector.Extensions.OpenGenericBatchRegistrationExtensions.RegisterManyForOpenGeneric(SimpleInjector.Container,System.Type,System.Reflection.Assembly[])">RegisterManyForOpenGeneric</see>. 
            The <b>RegisterManyForOpenGeneric</b> overloads that take a collection of <see cref="T:System.Reflection.Assembly"/> 
            objects use this method to get the list of types that need to be registered. Instead of calling 
            such overload, you can call an overload that takes a list of <see cref="T:System.Type"/> objects and pass 
            in a filtered result from this <b>GetTypesToRegister</b> method.
            <code lang="cs"><![CDATA[
            var container = new Container();
            
            var types = OpenGenericBatchRegistrationExtensions
                .GetTypesToRegister(typeof(ICommandHandler<>), AccessibilityOption.PublicTypesOnly,
                    typeof(ICommandHandler<>).Assembly)
                .Where(type => !type.Name.EndsWith("Decorator"));
            
            container.RegisterManyForOpenGeneric(typeof(ICommandHandler<>), types);
            ]]></code>
            This example calls the <b>GetTypesToRegister</b> method to request a list of concrete implementations
            of the <b>ICommandHandler&lt;T&gt;</b> interface from the assembly of that interface. After that
            all types which name ends with 'Decorator' are filtered out. This list is supplied to an
            <see cref="M:SimpleInjector.Extensions.OpenGenericBatchRegistrationExtensions.RegisterManyForOpenGeneric(SimpleInjector.Container,System.Type,System.Reflection.Assembly[])">RegisterManyForOpenGeneric</see>
            overload that takes a list of types to finish the
            registration.
            </remarks>
            <param name="container">The container to use.</param>
            <param name="openGenericServiceType">The definition of the open generic type.</param>
            <param name="accessibility">Defines which types should be used from the given assemblies.</param>
            <param name="assemblies">A list of assemblies that will be searched.</param>
            <returns>A list of types.</returns>
            <exception cref="T:System.ArgumentException">Thrown when 
            <paramref name="accessibility"/> contains an invalid value.</exception>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="openGenericServiceType"/>, or 
            <paramref name="assemblies"/> contain a null reference (Nothing in VB).</exception>
        </member>
        <member name="M:SimpleInjector.Extensions.OpenGenericBatchRegistrationExtensions.GetTypesToRegister(SimpleInjector.Container,System.Type,SimpleInjector.Extensions.AccessibilityOption,System.Collections.Generic.IEnumerable{System.Reflection.Assembly})">
            <summary>
            Returns all types that are located in the supplied <paramref name="assemblies"/> 
            and implement or inherit from the supplied <paramref name="openGenericServiceType"/>.
            </summary>
            <remarks>
            Use this method when you need influence the types that are registered using 
            <see cref="M:SimpleInjector.Extensions.OpenGenericBatchRegistrationExtensions.RegisterManyForOpenGeneric(SimpleInjector.Container,System.Type,System.Reflection.Assembly[])">RegisterManyForOpenGeneric</see>.
            The <b>RegisterManyForOpenGeneric</b> overloads that take a collection of <see cref="T:System.Reflection.Assembly"/> 
            objects use this method to get the list of types that need to be registered. Instead of calling 
            such overload, you can call an overload that takes a list of <see cref="T:System.Type"/> objects and pass 
            in a filtered result from this <b>GetTypesToRegister</b> method.
            <code lang="cs"><![CDATA[
            var container = new Container();
            
            var types = OpenGenericBatchRegistrationExtensions
                .GetTypesToRegister(typeof(ICommandHandler<>), AccessibilityOption.PublicTypesOnly, 
                    typeof(ICommandHandler<>).Assembly)
                .Where(type => !type.Name.EndsWith("Decorator"));
            
            container.RegisterManyForOpenGeneric(typeof(ICommandHandler<>), types);
            ]]></code>
            This example calls the <b>GetTypesToRegister</b> method to request a list of concrete implementations
            of the <b>ICommandHandler&lt;T&gt;</b> interface from the assembly of that interface. After that
            all types which name ends with 'Decorator' are filtered out. This list is supplied to an
            <see cref="M:SimpleInjector.Extensions.OpenGenericBatchRegistrationExtensions.RegisterManyForOpenGeneric(SimpleInjector.Container,System.Type,System.Reflection.Assembly[])">RegisterManyForOpenGeneric</see>
            overload that takes a list of types to finish the registration.
            </remarks>
            <param name="container">The container to use.</param>
            <param name="openGenericServiceType">The definition of the open generic type.</param>
            <param name="accessibility">Defines which types should be used from the given assemblies.</param>
            <param name="assemblies">A list of assemblies that will be searched.</param>
            <returns>A list of types.</returns>
            <exception cref="T:System.ArgumentException">Thrown when 
            <paramref name="accessibility"/> contains an invalid value.</exception>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="openGenericServiceType"/>, or 
            <paramref name="assemblies"/> contain a null reference (Nothing in VB).</exception>
        </member>
        <member name="M:SimpleInjector.Extensions.OpenGenericBatchRegistrationExtensions.GetTypesToRegister(SimpleInjector.Container,System.Type,System.Reflection.Assembly[])">
            <summary>
            Returns all public and internal types that are located in the supplied <paramref name="assemblies"/> 
            and implement or inherit from the supplied <paramref name="openGenericServiceType"/>.
            Types that are considered to be decorators are not returned.
            </summary>
            <remarks>
            Use this method when you need influence the types that are registered using 
            <see cref="M:SimpleInjector.Extensions.OpenGenericBatchRegistrationExtensions.RegisterManyForOpenGeneric(SimpleInjector.Container,System.Type,System.Reflection.Assembly[])">RegisterManyForOpenGeneric</see>. 
            The <b>RegisterManyForOpenGeneric</b> overloads that take a collection of <see cref="T:System.Reflection.Assembly"/> 
            objects use this method to get the list of types that need to be registered. Instead of calling 
            such overload, you can call an overload that takes a list of <see cref="T:System.Type"/> objects and pass 
            in a filtered result from this <b>GetTypesToRegister</b> method.
            <code lang="cs"><![CDATA[
            var container = new Container();
            
            var types = OpenGenericBatchRegistrationExtensions
                .GetTypesToRegister(typeof(ICommandHandler<>), typeof(ICommandHandler<>).Assembly)
                .Where(type => !type.Name.EndsWith("Decorator"));
            
            container.RegisterManyForOpenGeneric(typeof(ICommandHandler<>), types);
            ]]></code>
            This example calls the <b>GetTypesToRegister</b> method to request a list of concrete implementations
            of the <b>ICommandHandler&lt;T&gt;</b> interface from the assembly of that interface. After that
            all types which name ends with 'Decorator' are filtered out. This list is supplied to an
            <see cref="M:SimpleInjector.Extensions.OpenGenericBatchRegistrationExtensions.RegisterManyForOpenGeneric(SimpleInjector.Container,System.Type,System.Reflection.Assembly[])">RegisterManyForOpenGeneric</see>
            overload that takes a list of types to finish the
            registration.
            </remarks>
            <param name="container">The container to use.</param>
            <param name="openGenericServiceType">The definition of the open generic type.</param>
            <param name="assemblies">A list of assemblies that will be searched.</param>
            <returns>A list of types.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="openGenericServiceType"/>, or 
            <paramref name="assemblies"/> contain a null reference (Nothing in VB).</exception>
        </member>
        <member name="M:SimpleInjector.Extensions.OpenGenericBatchRegistrationExtensions.GetTypesToRegister(SimpleInjector.Container,System.Type,System.Collections.Generic.IEnumerable{System.Reflection.Assembly})">
            <summary>
            Returns all public and internal types that are located in the supplied <paramref name="assemblies"/> 
            and implement or inherit from the supplied <paramref name="openGenericServiceType"/>.
            Types that are considered to be decorators are not returned.
            </summary>
            <remarks>
            Use this method when you need influence the types that are registered using 
            <see cref="M:SimpleInjector.Extensions.OpenGenericBatchRegistrationExtensions.RegisterManyForOpenGeneric(SimpleInjector.Container,System.Type,System.Reflection.Assembly[])">RegisterManyForOpenGeneric</see>. 
            The <b>RegisterManyForOpenGeneric</b> overloads that take a collection of <see cref="T:System.Reflection.Assembly"/> 
            objects use this method to get the list of types that need to be registered. Instead of calling 
            such overload, you can call an overload that takes a list of <see cref="T:System.Type"/> objects and pass 
            in a filtered result from this <b>GetTypesToRegister</b> method.
            <code lang="cs"><![CDATA[
            var container = new Container();
            
            var types = OpenGenericBatchRegistrationExtensions
                .GetTypesToRegister(typeof(ICommandHandler<>), typeof(ICommandHandler<>).Assembly)
                .Where(type => !type.Name.EndsWith("Decorator"));
            
            container.RegisterManyForOpenGeneric(typeof(ICommandHandler<>), types);
            ]]></code>
            This example calls the <b>GetTypesToRegister</b> method to request a list of concrete implementations
            of the <b>ICommandHandler&lt;T&gt;</b> interface from the assembly of that interface. After that
            all types which name ends with 'Decorator' are filtered out. This list is supplied to an
            <see cref="M:SimpleInjector.Extensions.OpenGenericBatchRegistrationExtensions.RegisterManyForOpenGeneric(SimpleInjector.Container,System.Type,System.Reflection.Assembly[])">RegisterManyForOpenGeneric</see>
            overload that takes a list of types to finish the registration.
            </remarks>
            <param name="container">The container to use.</param>
            <param name="openGenericServiceType">The definition of the open generic type.</param>
            <param name="assemblies">A list of assemblies that will be searched.</param>
            <returns>A list of types.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="openGenericServiceType"/>, or 
            <paramref name="assemblies"/> contain a null reference (Nothing in VB).</exception>
        </member>
        <member name="T:SimpleInjector.Extensions.OpenGenericRegistrationExtensions">
            <summary>
            Provides a set of static (Shared in Visual Basic) methods for registration of open generic service
            types in the <see cref="T:SimpleInjector.Container"/>.
            </summary>
        </member>
        <member name="M:SimpleInjector.Extensions.OpenGenericRegistrationExtensions.RegisterOpenGeneric(SimpleInjector.Container,System.Type,System.Type)">
            <summary>
            Registers that a new instance of <paramref name="openGenericImplementation"/> will be returned 
            every time a <paramref name="openGenericServiceType"/> is requested.
            </summary>
            <example>
            Please see the 
            <see cref="M:SimpleInjector.Extensions.OpenGenericRegistrationExtensions.RegisterOpenGeneric(SimpleInjector.Container,System.Type,System.Type,SimpleInjector.Lifestyle)">RegisterOpenGeneric(Container,Type,Type,Lifestyle)</see>
            overload for an example.
            </example>
            <param name="container">The container to make the registrations in.</param>
            <param name="openGenericServiceType">The definition of the open generic service type that can be 
            used to retrieve instances.</param>
            <param name="openGenericImplementation">The definition of the open generic implementation type
            that will be returned when a <paramref name="openGenericServiceType"/> is requested.</param>
        </member>
        <member name="M:SimpleInjector.Extensions.OpenGenericRegistrationExtensions.RegisterSingleOpenGeneric(SimpleInjector.Container,System.Type,System.Type)">
            <summary>
            Registers that the same instance of <paramref name="openGenericImplementation"/> will be returned 
            every time a <paramref name="openGenericServiceType"/> is requested.
            </summary>
            <example>
            Please see the 
            <see cref="M:SimpleInjector.Extensions.OpenGenericRegistrationExtensions.RegisterOpenGeneric(SimpleInjector.Container,System.Type,System.Type,SimpleInjector.Lifestyle)">RegisterOpenGeneric(Container,Type,Type,Lifestyle)</see>
            overload for an example.
            </example>
            <param name="container">The container to make the registrations in.</param>
            <param name="openGenericServiceType">The definition of the open generic service type that can be 
            used to retrieve instances..</param>
            <param name="openGenericImplementation">The definition of the open generic implementation type
            that will be returned when a <paramref name="openGenericServiceType"/> is requested.</param>
        </member>
        <member name="M:SimpleInjector.Extensions.OpenGenericRegistrationExtensions.RegisterOpenGeneric(SimpleInjector.Container,System.Type,System.Type,SimpleInjector.Lifestyle)">
            <summary>
            Registers that a closed generic instance of the supplied 
            <paramref name="openGenericImplementation"/> will be returned when a closed generic version of
            the <paramref name="openGenericServiceType"/> is requested. The instance will be cached 
            according to the specified <paramref name="lifestyle"/>.
            </summary>
            <param name="container">The container to make the registrations in.</param>
            <param name="openGenericServiceType">The definition of the open generic service type that can be 
            used to retrieve instances.</param>
            <param name="openGenericImplementation">The definition of the open generic implementation type
            that will be returned when a <paramref name="openGenericServiceType"/> is requested.</param>
            <param name="lifestyle">The lifestyle that defines how returned instances are cached.</param>
        </member>
        <member name="M:SimpleInjector.Extensions.OpenGenericRegistrationExtensions.RegisterOpenGeneric(SimpleInjector.Container,System.Type,System.Type,SimpleInjector.Lifestyle,System.Predicate{SimpleInjector.PredicateContext})">
            <summary>
            Registers that the same instance of <paramref name="openGenericImplementationType"/> will be returned 
            every time a <paramref name="openGenericServiceType"/> is requested.
            </summary>
            <example>
            Please see the 
            <see cref="M:SimpleInjector.Extensions.OpenGenericRegistrationExtensions.RegisterOpenGeneric(SimpleInjector.Container,System.Type,System.Type,SimpleInjector.Lifestyle)">RegisterOpenGeneric(Container,Type,Type,Lifestyle)</see>
            overload for an example.
            </example>
            <param name="container">The container to make the registrations in.</param>
            <param name="openGenericServiceType">The definition of the open generic service type that can be 
            used to retrieve instances..</param>
            <param name="openGenericImplementationType">The definition of the open generic implementation type
            that will be returned when a <paramref name="openGenericServiceType"/> is requested.</param>
            <param name="lifestyle">The lifestyle that defines how returned instances are cached.</param>
            <param name="predicate">The predicate that determines whether the 
            <paramref name="openGenericImplementationType"/> can implement the service type.</param>
        </member>
        <member name="M:SimpleInjector.Extensions.OpenGenericRegistrationExtensions.RegisterAllOpenGeneric(SimpleInjector.Container,System.Type,System.Type[])">
            <summary>This method has been removed.</summary>
            <param name="container">The container to make the registrations in.</param>
            <param name="openGenericServiceType">The definition of the open generic service type that can be 
            used to retrieve instances.</param>
            <param name="openGenericImplementations">The list of open generic implementation types
            that will be returned when a collection of <paramref name="openGenericServiceType"/> is requested.
            </param>
        </member>
        <member name="M:SimpleInjector.Extensions.OpenGenericRegistrationExtensions.RegisterAllOpenGeneric(SimpleInjector.Container,System.Type,System.Collections.Generic.IEnumerable{System.Type})">
            <summary>This method has been removed.</summary>
            <param name="container">The container to make the registrations in.</param>
            <param name="openGenericServiceType">The definition of the open generic service type that can be 
            used to retrieve instances.</param>
            <param name="openGenericImplementations">The list of open generic implementation types
            that will be returned when a collection of <paramref name="openGenericServiceType"/> is requested.
            </param>
        </member>
        <member name="M:SimpleInjector.Extensions.OpenGenericRegistrationExtensions.RegisterAllOpenGeneric(SimpleInjector.Container,System.Type,SimpleInjector.Lifestyle,System.Type[])">
            <summary>This method has been removed.</summary>
            <param name="container">The container to make the registrations in.</param>
            <param name="openGenericServiceType">The definition of the open generic service type that can be 
            used to retrieve instances.</param>
            <param name="lifestyle">The lifestyle that defines how returned instances are cached.</param>
            <param name="openGenericImplementations">The list of open generic implementation types
            that will be returned when a collection of <paramref name="openGenericServiceType"/> is requested.
            </param>
        </member>
        <member name="M:SimpleInjector.Extensions.OpenGenericRegistrationExtensions.RegisterAllOpenGeneric(SimpleInjector.Container,System.Type,SimpleInjector.Lifestyle,System.Collections.Generic.IEnumerable{System.Type})">
            <summary>This method has been removed.</summary>
            <param name="container">The container to make the registrations in.</param>
            <param name="openGenericServiceType">The definition of the open generic service type that can be 
            used to retrieve instances.</param>
            <param name="lifestyle">The lifestyle that defines how returned instances are cached.</param>
            <param name="openGenericImplementations">The list of open generic implementation types
            that will be returned when a collection of <paramref name="openGenericServiceType"/> is requested.
            </param>
        </member>
    </members>
</doc>
