<?xml version="1.0"?>
<doc>
    <assembly>
        <name>SimpleInjector.Extensions.ExecutionContextScoping</name>
    </assembly>
    <members>
        <member name="T:SimpleInjector.Extensions.ExecutionContextScoping.ExecutionContextScope">
            <summary>
            Logical execution context and container specific cache for services that are registered with the
            the <see cref="T:SimpleInjector.Extensions.ExecutionContextScoping.ExecutionContextScopeLifestyle"/>.
            </summary>
        </member>
        <member name="M:SimpleInjector.Extensions.ExecutionContextScoping.ExecutionContextScope.Dispose(System.Boolean)">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged 
            resources.
            </summary>
            <param name="disposing">True to release both managed and unmanaged resources; false to release 
            only unmanaged resources.</param>
        </member>
        <member name="T:SimpleInjector.Extensions.ExecutionContextScoping.ExecutionContextScopeLifestyle">
            <summary>
            Defines a lifestyle that caches instances during the lifetime of an explicitly defined scope using the
            <see cref="M:SimpleInjector.Extensions.ExecutionContextScoping.SimpleInjectorExecutionContextScopeExtensions.BeginExecutionContextScope(SimpleInjector.Container)">BeginExecutionContextScope</see>
            method. An execution context scope flows with the logical execution context. Scopes can be nested and
            nested scopes will get their own instance. Instances created by this lifestyle can be disposed when 
            the created scope gets disposed. 
            </summary>
            <example>
            The following example shows the usage of the <b>ExecutionContextScopeLifestyle</b> class:
            <code lang="cs"><![CDATA[
            var container = new Container();
            
            container.Register<IUnitOfWork, EntityFrameworkUnitOfWork>(new ExecutionContextScopeLifestyle());
            
            using (container.BeginExecutionContextScope())
            {
                var instance1 = container.GetInstance<IUnitOfWork>();
                // ...
            }
            ]]></code>
            </example>
        </member>
        <member name="M:SimpleInjector.Extensions.ExecutionContextScoping.ExecutionContextScopeLifestyle.#ctor">
            <summary>Initializes a new instance of the <see cref="T:SimpleInjector.Extensions.ExecutionContextScoping.ExecutionContextScopeLifestyle"/> class.
            The created and cached instance will be disposed when the created 
            <see cref="T:SimpleInjector.Scope"/> instance gets disposed and when the created object implements 
            <see cref="T:System.IDisposable"/>.
            </summary>
        </member>
        <member name="M:SimpleInjector.Extensions.ExecutionContextScoping.ExecutionContextScopeLifestyle.#ctor(System.Boolean)">
            <summary>Initializes a new instance of the <see cref="T:SimpleInjector.Extensions.ExecutionContextScoping.ExecutionContextScopeLifestyle"/> class.</summary>
            <param name="disposeInstanceWhenScopeEnds">
            Specifies whether the created and cached instance will be disposed when the created 
            <see cref="T:SimpleInjector.Scope"/> instance gets disposed and when the created object implements 
            <see cref="T:System.IDisposable"/>. 
            </param>
        </member>
        <member name="M:SimpleInjector.Extensions.ExecutionContextScoping.ExecutionContextScopeLifestyle.#ctor(System.String,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:SimpleInjector.Extensions.ExecutionContextScoping.ExecutionContextScopeLifestyle"/> class.
            </summary>
            <param name="name">The user friendly name of this lifestyle.</param>
            <param name="disposeInstanceWhenScopeEnds">
            Specifies whether the created and cached instance will be disposed when the created.</param>
        </member>
        <member name="M:SimpleInjector.Extensions.ExecutionContextScoping.ExecutionContextScopeLifestyle.GetCurrentScopeCore(SimpleInjector.Container)">
            <summary>
            Returns the current <see cref="T:SimpleInjector.Scope"/> for this lifestyle and the given 
            <paramref name="container"/>, or null when this method is executed outside the context of a scope.
            </summary>
            <param name="container">The container instance that is related to the scope to return.</param>
            <returns>A <see cref="T:SimpleInjector.Scope"/> instance or null when there is no scope active in this context.</returns>
        </member>
        <member name="M:SimpleInjector.Extensions.ExecutionContextScoping.ExecutionContextScopeLifestyle.CreateCurrentScopeProvider(SimpleInjector.Container)">
            <summary>
            Creates a delegate that upon invocation return the current <see cref="T:SimpleInjector.Scope"/> for this
            lifestyle and the given <paramref name="container"/>, or null when the delegate is executed outside
            the context of such scope.
            </summary>
            <param name="container">The container for which the delegate gets created.</param>
            <returns>A <see cref="T:System.Func`1"/> delegate. This method never returns null.</returns>
        </member>
        <member name="T:SimpleInjector.Extensions.ExecutionContextScoping.SimpleInjectorExecutionContextScopeExtensions">
            <summary>
            Extension methods for enabling execution context scoping for the Simple Injector.
            </summary>
        </member>
        <member name="M:SimpleInjector.Extensions.ExecutionContextScoping.SimpleInjectorExecutionContextScopeExtensions.BeginExecutionContextScope(SimpleInjector.Container)">
             <summary>
             Begins a new execution context scope for the given <paramref name="container"/>. 
             Services, registered using the <see cref="T:SimpleInjector.Extensions.ExecutionContextScoping.ExecutionContextScopeLifestyle"/> are cached during the 
             lifetime of that scope. The scope should be disposed explicitly.
             </summary>
             <param name="container">The container.</param>
             <returns>A new <see cref="T:SimpleInjector.Scope"/> instance.</returns>
             <exception cref="T:System.ArgumentNullException">
             Thrown when the <paramref name="container"/> is a null reference.</exception>
             <example>
             <code lang="cs"><![CDATA[
             using (container.BeginExecutionContextScope())
             {
                 var handler container.GetInstance(rootType) as IRequestHandler;
            
                 handler.Handle(request);
             }
             ]]></code>
             </example>
        </member>
        <member name="M:SimpleInjector.Extensions.ExecutionContextScoping.SimpleInjectorExecutionContextScopeExtensions.GetCurrentExecutionContextScope(SimpleInjector.Container)">
            <summary>
            Gets the Execution Context <see cref="T:SimpleInjector.Scope"/> that is currently in scope or <b>null</b> when no
            <see cref="T:SimpleInjector.Scope"/> is currently in scope.
            </summary>
            <example>
            The following example registers a <b>ServiceImpl</b> type as transient (a new instance will be
            returned every time) and registers an initializer for that type that will register that instance
            for disposal in the <see cref="T:SimpleInjector.Scope"/> in which context it is created:
            <code lang="cs"><![CDATA[
            container.Register<IService, ServiceImpl>();
            container.RegisterInitializer<ServiceImpl>(instance =>
            {
                container.GetCurrentExecutionContextScope().RegisterForDisposal(instance);
            });
            ]]></code>
            </example>
            <param name="container">The container.</param>
            <returns>A new <see cref="T:SimpleInjector.Scope"/> instance.</returns>
            <exception cref="T:System.ArgumentNullException">
            Thrown when the <paramref name="container"/> is a null reference.</exception>
        </member>
    </members>
</doc>
