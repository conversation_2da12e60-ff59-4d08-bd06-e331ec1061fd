<?xml version="1.0"?>
<doc>
    <assembly>
        <name>SimpleInjector.Integration.Web</name>
    </assembly>
    <members>
        <member name="T:SimpleInjector.Integration.Web.WebRequestLifestyle">
            <summary>
            Defines a lifestyle that caches instances during the execution of a single HTTP Web Request.
            Unless explicitly stated otherwise, instances created by this lifestyle will be disposed at the end
            of the web request.
            </summary>
            <example>
            The following example shows the usage of the <b>WebRequestLifestyle</b> class:
            <code lang="cs"><![CDATA[
            var container = new Container();
            
            container.Register<IUnitOfWork, EntityFrameworkUnitOfWork>(new WebRequestLifestyle());
            ]]></code>
            </example>
        </member>
        <member name="M:SimpleInjector.Integration.Web.WebRequestLifestyle.#ctor">
            <summary>Initializes a new instance of the <see cref="T:SimpleInjector.Integration.Web.WebRequestLifestyle"/> class. The instance
            will ensure that created and cached instance will be disposed after the execution of the web
            request ended and when the created object implements <see cref="T:System.IDisposable"/>.</summary>
        </member>
        <member name="M:SimpleInjector.Integration.Web.WebRequestLifestyle.#ctor(System.Boolean)">
            <summary>Initializes a new instance of the <see cref="T:SimpleInjector.Integration.Web.WebRequestLifestyle"/> class.</summary>
            <param name="disposeInstanceWhenWebRequestEnds">
            Specifies whether the created and cached instance will be disposed after the execution of the web
            request ended and when the created object implements <see cref="T:System.IDisposable"/>. 
            </param>
        </member>
        <member name="M:SimpleInjector.Integration.Web.WebRequestLifestyle.WhenCurrentRequestEnds(SimpleInjector.Container,System.Action)">
            <summary>
            Allows registering an <paramref name="action"/> delegate that will be called when the scope ends,
            but before the scope disposes any instances.
            </summary>
            <param name="container">The <see cref="T:SimpleInjector.Container"/> instance.</param>
            <param name="action">The delegate to run when the scope ends.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when one of the arguments is a null reference
            (Nothing in VB).</exception>
            <exception cref="T:System.InvalidOperationException">Will be thrown when the current thread isn't running
            in the context of a web request.</exception>
        </member>
        <member name="M:SimpleInjector.Integration.Web.WebRequestLifestyle.GetCurrentScopeCore(SimpleInjector.Container)">
            <summary>
            Returns the current <see cref="T:SimpleInjector.Scope"/> for this lifestyle and the given 
            <paramref name="container"/>, or null when this method is executed outside the context of a scope.
            </summary>
            <param name="container">The container instance that is related to the scope to return.</param>
            <returns>A <see cref="T:SimpleInjector.Scope"/> instance or null when there is no scope active in this context.</returns>
        </member>
        <member name="M:SimpleInjector.Integration.Web.WebRequestLifestyle.CreateCurrentScopeProvider(SimpleInjector.Container)">
            <summary>
            Creates a delegate that upon invocation return the current <see cref="T:SimpleInjector.Scope"/> for this
            lifestyle and the given <paramref name="container"/>, or null when the delegate is executed outside
            the context of such scope.
            </summary>
            <param name="container">The container for which the delegate gets created.</param>
            <returns>A <see cref="T:System.Func`1"/> delegate. This method never returns null.</returns>
        </member>
        <member name="T:SimpleInjector.Integration.Web.SimpleInjectorWebInitializer">
            <summary>
            Pre application start code.
            </summary>
        </member>
        <member name="M:SimpleInjector.Integration.Web.SimpleInjectorWebInitializer.Initialize">
            <summary>Registers an HttpModule that allows disposing instances that are registered as
            Per Web Request.</summary>
        </member>
        <member name="T:SimpleInjector.Integration.Web.SimpleInjectorHttpModule">
            <summary>
            Simple Injector web integration HTTP Module. This module is registered automatically by ASP.NET when
            the assembly of this class is included in the application's bin folder. The module will trigger the
            disposing of created instances that are flagged as needing to be disposed at the end of the web 
            request.
            </summary>
        </member>
        <member name="M:SimpleInjector.Integration.Web.SimpleInjectorHttpModule.System#Web#IHttpModule#Init(System.Web.HttpApplication)">
            <summary>Initializes a module and prepares it to handle requests.</summary>
            <param name="context">An <see cref="T:System.Web.HttpApplication"/> that provides access to the methods, 
            properties, and events common to all application objects within an ASP.NET application.</param>
        </member>
        <member name="M:SimpleInjector.Integration.Web.SimpleInjectorHttpModule.System#Web#IHttpModule#Dispose">
            <summary>
            Disposes of the resources (other than memory) used by the module that implements 
            <see cref="T:System.Web.IHttpModule"/>.
            </summary>
        </member>
        <member name="T:SimpleInjector.SimpleInjectorWebExtensions">
            <summary>
            Extension methods for integrating Simple Injector with ASP.NET web applications.
            </summary>
        </member>
        <member name="M:SimpleInjector.SimpleInjectorWebExtensions.RegisterPerWebRequest``1(SimpleInjector.Container)">
            <summary>
            Registers that one instance of <typeparamref name="TConcrete"/> will be returned for every web
            request and ensures that -if <typeparamref name="TConcrete"/> implements 
            <see cref="T:System.IDisposable"/>- this instance will get disposed on the end of the web request. 
            </summary>
            <typeparam name="TConcrete">The concrete type that will be registered.</typeparam>
            <param name="container">The container to make the registrations in.</param>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered, or when an 
            the <typeparamref name="TConcrete"/> has already been registered.
            </exception>
            <exception cref="T:System.ArgumentException">Thrown when the <typeparamref name="TConcrete"/> is a type
            that can not be created by the container.</exception>
            <exception cref="T:System.ArgumentNullException">Thrown when the <paramref name="container"/> is a null
            reference.</exception>
        </member>
        <member name="M:SimpleInjector.SimpleInjectorWebExtensions.RegisterPerWebRequest``2(SimpleInjector.Container)">
            <summary>
            Registers that one instance of <typeparamref name="TImplementation"/> will be returned for every 
            web request every time a <typeparamref name="TService"/> is requested and ensures that -if 
            <typeparamref name="TImplementation"/> implements <see cref="T:System.IDisposable"/>- this instance 
            will get disposed on the end of the web request.
            </summary>
            <typeparam name="TService">The interface or base type that can be used to retrieve the instances.
            </typeparam>
            <typeparam name="TImplementation">The concrete type that will be registered.</typeparam>
            <param name="container">The container to make the registrations in.</param>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered, or when an 
            the <typeparamref name="TService"/> has already been registered.</exception>
            <exception cref="T:System.ArgumentException">Thrown when the given <typeparamref name="TImplementation"/> 
            type is not a type that can be created by the container.
            </exception>
            <exception cref="T:System.ArgumentNullException">Thrown when the <paramref name="container"/> is a null
            reference.</exception>
        </member>
        <member name="M:SimpleInjector.SimpleInjectorWebExtensions.RegisterPerWebRequest``1(SimpleInjector.Container,System.Func{``0})">
            <summary>
            Registers the specified delegate that allows returning instances of <typeparamref name="TService"/>
            and the returned instance will be reused for the duration of a single web request and ensures that,
            if the returned instance implements <see cref="T:System.IDisposable"/>, that instance will get
            disposed on the end of the web request.
            </summary>
            <typeparam name="TService">The interface or base type that can be used to retrieve instances.</typeparam>
            <param name="container">The container to make the registrations in.</param>
            <param name="instanceCreator">The delegate that allows building or creating new instances.</param>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered, or when the
            <typeparamref name="TService"/> has already been registered.</exception>
            <exception cref="T:System.ArgumentNullException">
            Thrown when either <paramref name="container"/> or <paramref name="instanceCreator"/> are null
            references.</exception>
        </member>
        <member name="M:SimpleInjector.SimpleInjectorWebExtensions.RegisterPerWebRequest``1(SimpleInjector.Container,System.Func{``0},System.Boolean)">
            <summary>
            Registers the specified delegate that allows returning instances of <typeparamref name="TService"/>
            and the returned instance will be reused for the duration of a single web request and ensures that,
            if the returned instance implements <see cref="T:System.IDisposable"/>, and
            <paramref name="disposeInstanceWhenWebRequestEnds"/> is set to <b>true</b>, that instance will get
            disposed on the end of the web request.
            </summary>
            <typeparam name="TService">The interface or base type that can be used to retrieve instances.</typeparam>
            <param name="container">The container to make the registrations in.</param>
            <param name="instanceCreator">The delegate that allows building or creating new instances.</param>
            <param name="disposeInstanceWhenWebRequestEnds">If set to <c>true</c>, the instance will get disposed
            when it implements <see cref="T:System.IDisposable"/> at the end of the web request.</param>
            <exception cref="T:System.InvalidOperationException">
            Thrown when this container instance is locked and can not be altered, or when the
            <typeparamref name="TService"/> has already been registered.</exception>
            <exception cref="T:System.ArgumentNullException">
            Thrown when either <paramref name="container"/> or <paramref name="instanceCreator"/> are null
            references.</exception>
        </member>
        <member name="M:SimpleInjector.SimpleInjectorWebExtensions.RegisterForDisposal(System.IDisposable)">
            <summary>This method is obsolete.</summary>
            <param name="disposable">The disposable.</param>
        </member>
    </members>
</doc>
