<?xml version="1.0"?>
<doc>
    <assembly>
        <name>System.Net.Http.WinHttpHandler</name>
    </assembly>
    <members>
        <member name="P:System.SR.net_securityprotocolnotsupported">
            <summary>The requested security protocol is not supported.</summary>
        </member>
        <member name="P:System.SR.net_http_invalid_cookiecontainer">
            <summary>When using CookieUsePolicy.UseSpecifiedCookieContainer, the CookieContainer property must not be null.</summary>
        </member>
        <member name="P:System.SR.net_http_invalid_proxyusepolicy">
            <summary>When using a non-null Proxy, the WindowsProxyUsePolicy property must be set to WindowsProxyUsePolicy.UseCustomProxy.</summary>
        </member>
        <member name="P:System.SR.net_http_invalid_proxy">
            <summary>When using WindowsProxyUsePolicy.UseCustomProxy, the Proxy property must not be null.</summary>
        </member>
        <member name="P:System.SR.net_http_handler_norequest">
            <summary>A request message must be provided. It cannot be null.</summary>
        </member>
        <member name="P:System.SR.net_http_operation_started">
            <summary>This instance has already started one or more requests. Properties can only be modified before sending the first request.</summary>
        </member>
        <member name="P:System.SR.net_http_client_execution_error">
            <summary>An error occurred while sending the request.</summary>
        </member>
        <member name="P:System.SR.net_http_io_read">
            <summary>The read operation failed, see inner exception.</summary>
        </member>
        <member name="P:System.SR.net_http_io_read_incomplete">
            <summary>Unable to read data from the transport connection. The connection was closed before all data could be read. Expected {0} bytes, read {1} bytes.</summary>
        </member>
        <member name="P:System.SR.net_http_io_write">
            <summary>The write operation failed, see inner exception.</summary>
        </member>
        <member name="P:System.SR.net_http_chunked_not_allowed_with_empty_content">
            <summary>'Transfer-Encoding: chunked' header can not be used when content object is not specified.</summary>
        </member>
        <member name="P:System.SR.net_http_invalid_enable_first">
            <summary>The {0} property must be set to '{1}' to use this property.</summary>
        </member>
        <member name="P:System.SR.net_http_value_must_be_greater_than">
            <summary>The specified value must be greater than {0}.</summary>
        </member>
        <member name="P:System.SR.net_http_username_empty_string">
            <summary>The username for a credential object cannot be null or empty.</summary>
        </member>
        <member name="P:System.SR.net_http_no_concurrent_io_allowed">
            <summary>The stream does not support concurrent I/O read or write operations.</summary>
        </member>
        <member name="P:System.SR.net_http_buffer_insufficient_length">
            <summary>The buffer was not long enough.</summary>
        </member>
        <member name="P:System.SR.ArgumentOutOfRange_NeedPosNum">
            <summary>Positive number required.</summary>
        </member>
        <member name="P:System.SR.NotSupported_UnreadableStream">
            <summary>Stream does not support reading.</summary>
        </member>
        <member name="P:System.SR.NotSupported_UnwritableStream">
            <summary>Stream does not support writing.</summary>
        </member>
        <member name="P:System.SR.ObjectDisposed_StreamClosed">
            <summary>Cannot access a closed stream.</summary>
        </member>
        <member name="P:System.SR.net_http_content_stream_already_read">
            <summary>The stream was already consumed. It cannot be read again.</summary>
        </member>
        <member name="P:System.SR.net_http_winhttp_error">
            <summary>Error {0} calling {1}, '{2}'.</summary>
        </member>
        <member name="P:System.SR.PlatformNotSupported_WinHttpHandler">
            <summary>WinHttpHandler is only supported on .NET Framework and .NET runtimes on Windows. It is not supported for Windows Store Applications (UWP) or Unix platforms.</summary>
        </member>
        <member name="P:System.SR.net_http_unsupported_requesturi_scheme">
            <summary>The '{0}' scheme is not supported.</summary>
        </member>
        <member name="P:System.SR.net_http_client_invalid_requesturi">
            <summary>An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.</summary>
        </member>
        <member name="T:System.Runtime.InteropServices.LibraryImportAttribute">
            <summary>
            Attribute used to indicate a source generator should create a function for marshalling
            arguments instead of relying on the runtime to generate an equivalent marshalling function at run-time.
            </summary>
            <remarks>
            This attribute is meaningless if the source generator associated with it is not enabled.
            The current built-in source generator only supports C# and only supplies an implementation when
            applied to static, partial, non-generic methods.
            </remarks>
        </member>
        <member name="M:System.Runtime.InteropServices.LibraryImportAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.InteropServices.LibraryImportAttribute"/>.
            </summary>
            <param name="libraryName">Name of the library containing the import.</param>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.LibraryName">
            <summary>
            Gets the name of the library containing the import.
            </summary>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.EntryPoint">
            <summary>
            Gets or sets the name of the entry point to be called.
            </summary>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshalling">
            <summary>
            Gets or sets how to marshal string arguments to the method.
            </summary>
            <remarks>
            If this field is set to a value other than <see cref="F:System.Runtime.InteropServices.StringMarshalling.Custom" />,
            <see cref="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshallingCustomType" /> must not be specified.
            </remarks>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshallingCustomType">
            <summary>
            Gets or sets the <see cref="T:System.Type"/> used to control how string arguments to the method are marshalled.
            </summary>
            <remarks>
            If this field is specified, <see cref="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshalling" /> must not be specified
            or must be set to <see cref="F:System.Runtime.InteropServices.StringMarshalling.Custom" />.
            </remarks>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.SetLastError">
            <summary>
            Gets or sets whether the callee sets an error (SetLastError on Windows or errno
            on other platforms) before returning from the attributed method.
            </summary>
        </member>
        <member name="T:System.Runtime.InteropServices.StringMarshalling">
            <summary>
            Specifies how strings should be marshalled for generated p/invokes
            </summary>
        </member>
        <member name="F:System.Runtime.InteropServices.StringMarshalling.Custom">
            <summary>
            Indicates the user is suppling a specific marshaller in <see cref="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshallingCustomType"/>.
            </summary>
        </member>
        <member name="F:System.Runtime.InteropServices.StringMarshalling.Utf8">
            <summary>
            Use the platform-provided UTF-8 marshaller.
            </summary>
        </member>
        <member name="F:System.Runtime.InteropServices.StringMarshalling.Utf16">
            <summary>
            Use the platform-provided UTF-16 marshaller.
            </summary>
        </member>
    </members>
</doc>
