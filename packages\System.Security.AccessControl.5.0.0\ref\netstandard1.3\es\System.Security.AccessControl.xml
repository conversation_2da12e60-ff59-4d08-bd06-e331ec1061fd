﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Security.AccessControl</name>
  </assembly>
  <members>
    <member name="T:System.Security.AccessControl.AccessControlActions">
      <summary>Especifica las acciones permitidas para objetos que se pueden proteger.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlActions.Change">
      <summary>Especifica acceso de sólo escritura.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlActions.None">
      <summary>No especifica ningún acceso.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlActions.View">
      <summary>Especifica acceso de sólo lectura.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AccessControlModification">
      <summary>Especifica el tipo de modificación del control de acceso que se va a realizar.Los métodos de la clase <see cref="T:System.Security.AccessControl.ObjectSecurity" /> y sus descendientes utilizan esta enumeración.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.Add">
      <summary>Agregue la regla de autorización especificada a la lista de control de acceso (ACL).</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.Remove">
      <summary>Quita de la ACL las reglas de autorización que contienen el mismo identificador de seguridad (SID) y la misma máscara de acceso que la regla de autorización especificada.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.RemoveAll">
      <summary>Quita de la ACL las reglas de autorización que contienen el mismo SID que la regla de autorización especificada.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.RemoveSpecific">
      <summary>Quita de la ACL las reglas de autorización que coinciden exactamente con la regla de autorización especificada.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.Reset">
      <summary>Quita de la ACL las reglas de autorización que contienen el mismo SID que la regla de autorización especificada y, a continuación, agrega a la ACL la regla de autorización especificada.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.Set">
      <summary>Quita de la ACL todas las reglas de autorización, a continuación agrega a la ACL la regla de autorización especificada.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AccessControlSections">
      <summary>Especifica las secciones de un descriptor de seguridad que se van a guardar o cargar.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.Access">
      <summary>Lista de control de acceso discrecional (DACL, Discretionary Access Control List).</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.All">
      <summary>Todo el descriptor de seguridad.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.Audit">
      <summary>Lista de control de acceso al sistema (SACL, System Access Control List).</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.Group">
      <summary>Grupo principal.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.None">
      <summary>Sin secciones.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.Owner">
      <summary>Propietario.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AccessControlType">
      <summary>Especifica si un objeto <see cref="T:System.Security.AccessControl.AccessRule" /> se utiliza para permitir o para denegar el acceso.Estos valores no son marcadores y no se pueden combinar.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlType.Allow">
      <summary>El objeto <see cref="T:System.Security.AccessControl.AccessRule" /> se utiliza para permitir el acceso a un objeto protegido.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlType.Deny">
      <summary>El objeto <see cref="T:System.Security.AccessControl.AccessRule" /> se utiliza para denegar el acceso a un objeto protegido.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AccessRule">
      <summary>Representa una combinación de la identidad de un usuario, una máscara de acceso y un tipo de control de acceso (conceder o denegar).Un objeto <see cref="T:System.Security.AccessControl.AccessRule" /> también contiene información sobre cómo los objetos secundarios heredan la regla y cómo se propaga esa herencia.</summary>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.AccessRule" /> utilizando los valores especificados.</summary>
      <param name="identity">Identidad a la que se aplica la regla de acceso.Este parámetro debe ser un objeto que permita su conversión al tipo <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="accessMask">La máscara de acceso de esta regla.La máscara de acceso es una colección de 32 bits de bits anónimos, cuyo significado queda definido por cada uno de sus integrantes individuales.</param>
      <param name="isInherited">Es true si esta regla se hereda de un contenedor primario.</param>
      <param name="inheritanceFlags">Las propiedades de herencia de la regla de acceso. </param>
      <param name="propagationFlags">Indica si las reglas de acceso heredadas se propagan automáticamente.Los marcadores de propagación se omiten si el valor del parámetro <paramref name="inheritanceFlags" /> se establece en <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="type">El tipo de control de acceso válido.</param>
      <exception cref="T:System.ArgumentException">El valor del parámetro <paramref name="identity" /> no puede convertirse en un objeto <see cref="T:System.Security.Principal.SecurityIdentifier" /> o el parámetro <paramref name="type" /> contiene un valor que no es válido.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El valor del parámetro <paramref name="accessMask" /> es cero o los parámetros <paramref name="inheritanceFlags" /> o <paramref name="propagationFlags" /> contienen valores de marcador no reconocidos.</exception>
    </member>
    <member name="P:System.Security.AccessControl.AccessRule.AccessControlType">
      <summary>Obtiene el valor de <see cref="T:System.Security.AccessControl.AccessControlType" /> asociado a este objeto <see cref="T:System.Security.AccessControl.AccessRule" />.</summary>
      <returns>Valor de <see cref="T:System.Security.AccessControl.AccessControlType" /> asociado a este objeto <see cref="T:System.Security.AccessControl.AccessRule" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.AccessRule`1">
      <summary>Representa una combinación de la identidad de un usuario, una máscara de acceso y un tipo de control de acceso (conceder o denegar).Un objeto AccessRule`1 de regla de acceso también contiene información sobre cómo los objetos secundarios heredan la regla y cómo se propaga esa herencia.</summary>
      <typeparam name="T">Tipo de derechos de acceso para la regla de acceso.</typeparam>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule`1.#ctor(System.Security.Principal.IdentityReference,`0,System.Security.AccessControl.AccessControlType)">
      <summary>Inicializa una nueva instancia de la clase AccessRule’1 utilizando los valores especificados.</summary>
      <param name="identity">Identidad a la que se aplica la regla de acceso.</param>
      <param name="rights">Derechos de regla de acceso.</param>
      <param name="type">El tipo de control de acceso válido.</param>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule`1.#ctor(System.Security.Principal.IdentityReference,`0,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>Inicializa una nueva instancia de la clase AccessRule’1 utilizando los valores especificados.</summary>
      <param name="identity">Identidad a la que se aplica la regla de acceso.</param>
      <param name="rights">Derechos de regla de acceso.</param>
      <param name="inheritanceFlags">Las propiedades de herencia de la regla de acceso. </param>
      <param name="propagationFlags">Indica si las reglas de acceso heredadas se propagan automáticamente.Los marcadores de propagación se omiten si el valor del parámetro <paramref name="inheritanceFlags" /> se establece en <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="type">El tipo de control de acceso válido.</param>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule`1.#ctor(System.String,`0,System.Security.AccessControl.AccessControlType)">
      <summary>Inicializa una nueva instancia de la clase AccessRule’1 utilizando los valores especificados.</summary>
      <param name="identity">Identidad a la que se aplica la regla de acceso.</param>
      <param name="rights">Derechos de regla de acceso.</param>
      <param name="type">El tipo de control de acceso válido.</param>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule`1.#ctor(System.String,`0,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>Inicializa una nueva instancia de la clase AccessRule’1 utilizando los valores especificados.</summary>
      <param name="identity">Identidad a la que se aplica la regla de acceso.</param>
      <param name="rights">Derechos de regla de acceso.</param>
      <param name="inheritanceFlags">Las propiedades de herencia de la regla de acceso. </param>
      <param name="propagationFlags">Indica si las reglas de acceso heredadas se propagan automáticamente.Los marcadores de propagación se omiten si el valor del parámetro <paramref name="inheritanceFlags" /> se establece en <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="type">El tipo de control de acceso válido.</param>
    </member>
    <member name="P:System.Security.AccessControl.AccessRule`1.Rights">
      <summary>Obtiene los derechos de la instancia actual.</summary>
      <returns>Los derechos, convertidos en tipo &lt;T&gt;, de la instancia actual.</returns>
    </member>
    <member name="T:System.Security.AccessControl.AceEnumerator">
      <summary>Proporciona la capacidad para recorrer en iteración las entradas de control de acceso (ACE) de una lista de control de acceso (ACL). </summary>
    </member>
    <member name="P:System.Security.AccessControl.AceEnumerator.Current">
      <summary>Obtiene el elemento actual de la colección <see cref="T:System.Security.AccessControl.GenericAce" />.Esta propiedad obtiene la versión descriptiva de tipo del objeto.</summary>
      <returns>Elemento actual de la colección <see cref="T:System.Security.AccessControl.GenericAce" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.AceEnumerator.MoveNext">
      <summary>Desplaza el enumerador al siguiente elemento de la colección <see cref="T:System.Security.AccessControl.GenericAce" />.</summary>
      <returns>true si el enumerador avanzó con éxito hasta el siguiente elemento; false si el enumerador alcanzó el final de la colección.</returns>
      <exception cref="T:System.InvalidOperationException">La colección se modificó después de crear el enumerador.</exception>
    </member>
    <member name="M:System.Security.AccessControl.AceEnumerator.Reset">
      <summary>Establece el enumerador en su posición inicial, que es antes del primer elemento de la colección <see cref="T:System.Security.AccessControl.GenericAce" />.</summary>
      <exception cref="T:System.InvalidOperationException">La colección se modificó después de crear el enumerador.</exception>
    </member>
    <member name="P:System.Security.AccessControl.AceEnumerator.System#Collections#IEnumerator#Current"></member>
    <member name="T:System.Security.AccessControl.AceFlags">
      <summary>Especifica la herencia y el comportamiento de la auditoría de una entrada de control de acceso (ACE).</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.AuditFlags">
      <summary>Se auditan todos los intentos de acceso.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.ContainerInherit">
      <summary>La máscara de acceso se propaga a los objetos de contenedor secundarios.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.FailedAccess">
      <summary>Se auditan los intentos de acceso con error. </summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.InheritanceFlags">
      <summary>Operación OR lógica de <see cref="F:System.Security.AccessControl.AceFlags.ObjectInherit" />, <see cref="F:System.Security.AccessControl.AceFlags.ContainerInherit" />, <see cref="F:System.Security.AccessControl.AceFlags.NoPropagateInherit" /> y <see cref="F:System.Security.AccessControl.AceFlags.InheritOnly" />.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.Inherited">
      <summary>Se hereda una ACE de un contenedor primario en lugar de establecerse explícitamente para un objeto.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.InheritOnly">
      <summary>La máscara de acceso sólo se propaga a los objetos secundarios.donde se incluyen tanto el contenedor como los objetos hoja secundarios.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.None">
      <summary>No se establecen marcadores ACE.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.NoPropagateInherit">
      <summary>Las comprobaciones de acceso no se aplican al objeto; sólo se aplican a sus elementos secundarios.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.ObjectInherit">
      <summary>La máscara de acceso se propaga a los objetos hoja secundarios.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.SuccessfulAccess">
      <summary>Se auditan los intentos de acceso con éxito.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AceQualifier">
      <summary>Especifica la función de una entrada de control de acceso (ACE).</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceQualifier.AccessAllowed">
      <summary>Permita el acceso.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceQualifier.AccessDenied">
      <summary>Deniegue el acceso.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceQualifier.SystemAlarm">
      <summary>Provoque una alarma del sistema.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceQualifier.SystemAudit">
      <summary>Provoque una auditoría del sistema.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AceType">
      <summary>Define los tipos de entrada de control de acceso (ACE) disponibles.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowed">
      <summary>Concede el acceso a un objeto para un administrador de confianza concreto identificado por un objeto <see cref="T:System.Security.Principal.IdentityReference" />.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowedCallback">
      <summary>Concede el acceso a un objeto para un administrador de confianza concreto identificado por un objeto <see cref="T:System.Security.Principal.IdentityReference" />.Este tipo de ACE puede contener los datos de devolución de llamada opcionales.Los datos de devolución de llamada son objetos BLOB específicos del administrador de recursos que no se interpretan.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowedCallbackObject">
      <summary>Concede el acceso a un objeto, un conjunto de propiedades o una propiedad.La ACE contiene un conjunto de derechos de acceso, un GUID que identifica el tipo de objeto y un objeto <see cref="T:System.Security.Principal.IdentityReference" /> que identifica el administrador de confianza a quien el sistema concederá el acceso.La ACE también contiene un GUID y un conjunto de marcadores que controlan la herencia de la ACE mediante objetos secundarios.Este tipo de ACE puede contener los datos de devolución de llamada opcionales.Los datos de devolución de llamada son objetos BLOB específicos del administrador de recursos que no se interpretan.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowedCompound">
      <summary>Se define pero nunca se utiliza.Se incluye aquí para integridad.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowedObject">
      <summary>Concede el acceso a un objeto, un conjunto de propiedades o una propiedad.La ACE contiene un conjunto de derechos de acceso, un GUID que identifica el tipo de objeto y un objeto <see cref="T:System.Security.Principal.IdentityReference" /> que identifica el administrador de confianza a quien el sistema concederá el acceso.La ACE también contiene un GUID y un conjunto de marcadores que controlan la herencia de la ACE mediante objetos secundarios.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessDenied">
      <summary>Deniega el acceso a un objeto para un administrador de confianza concreto identificado por un objeto <see cref="T:System.Security.Principal.IdentityReference" />.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessDeniedCallback">
      <summary>Deniega el acceso a un objeto para un administrador de confianza concreto identificado por un objeto <see cref="T:System.Security.Principal.IdentityReference" />.Este tipo de ACE puede contener los datos de devolución de llamada opcionales.Los datos de devolución de llamada son objetos BLOB específicos del administrador de recursos que no se interpretan.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessDeniedCallbackObject">
      <summary>Deniega el acceso a un objeto, un conjunto de propiedades o una propiedad.La ACE contiene un conjunto de derechos de acceso, un GUID que identifica el tipo de objeto y un objeto <see cref="T:System.Security.Principal.IdentityReference" /> que identifica el administrador de confianza a quien el sistema concederá el acceso.La ACE también contiene un GUID y un conjunto de marcadores que controlan la herencia de la ACE mediante objetos secundarios.Este tipo de ACE puede contener los datos de devolución de llamada opcionales.Los datos de devolución de llamada son objetos BLOB específicos del administrador de recursos que no se interpretan.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessDeniedObject">
      <summary>Deniega el acceso a un objeto, un conjunto de propiedades o una propiedad.La ACE contiene un conjunto de derechos de acceso, un GUID que identifica el tipo de objeto y un objeto <see cref="T:System.Security.Principal.IdentityReference" /> que identifica el administrador de confianza a quien el sistema concederá el acceso.La ACE también contiene un GUID y un conjunto de marcadores que controlan la herencia de la ACE mediante objetos secundarios.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.MaxDefinedAceType">
      <summary>Efectúa el seguimiento del tipo de ACE definido máximo de la enumeración.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAlarm">
      <summary>Reservado para un uso futuro.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAlarmCallback">
      <summary>Reservado para un uso futuro.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAlarmCallbackObject">
      <summary>Reservado para un uso futuro.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAlarmObject">
      <summary>Reservado para un uso futuro.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAudit">
      <summary>Produce un mensaje de auditoría que se va a registrar cuando un administrador de confianza especificado intenta obtener acceso a un objeto.Un objeto <see cref="T:System.Security.Principal.IdentityReference" /> identifica el administrador de confianza.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAuditCallback">
      <summary>Produce un mensaje de auditoría que se va a registrar cuando un administrador de confianza especificado intenta obtener acceso a un objeto.Un objeto <see cref="T:System.Security.Principal.IdentityReference" /> identifica el administrador de confianza.Este tipo de ACE puede contener los datos de devolución de llamada opcionales.Los datos de devolución de llamada son objetos BLOB específicos del administrador de recursos que no se interpretan.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAuditCallbackObject">
      <summary>Produce un mensaje de auditoría que se va a registrar cuando un administrador de confianza especificado intenta obtener acceso a un objeto o subobjetos como conjuntos de propiedades o propiedades.La ACE contiene un conjunto de derechos de acceso, un GUID que identifica el tipo de objeto o subobjeto y un objeto <see cref="T:System.Security.Principal.IdentityReference" /> que identifica el administrador de confianza a quien el sistema auditará el acceso.La ACE también contiene un GUID y un conjunto de marcadores que controlan la herencia de la ACE mediante objetos secundarios.Este tipo de ACE puede contener los datos de devolución de llamada opcionales.Los datos de devolución de llamada son objetos BLOB específicos del administrador de recursos que no se interpretan.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAuditObject">
      <summary>Produce un mensaje de auditoría que se va a registrar cuando un administrador de confianza especificado intenta obtener acceso a un objeto o subobjetos como conjuntos de propiedades o propiedades.La ACE contiene un conjunto de derechos de acceso, un GUID que identifica el tipo de objeto o subobjeto y un objeto <see cref="T:System.Security.Principal.IdentityReference" /> que identifica el administrador de confianza a quien el sistema auditará el acceso.La ACE también contiene un GUID y un conjunto de marcadores que controlan la herencia de la ACE mediante objetos secundarios.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AuditFlags">
      <summary>Especifica las condiciones de auditoría de los intentos de acceso a un objeto asegurable.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AuditFlags.Failure">
      <summary>Se van a auditar los intentos incorrectos de acceso.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AuditFlags.None">
      <summary>No se van a auditar los intentos incorrectos de acceso.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AuditFlags.Success">
      <summary>Se van a auditar los intentos correctos de acceso.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AuditRule">
      <summary>Representa una combinación de la identidad de un usuario y una máscara de acceso.Un objeto <see cref="T:System.Security.AccessControl.AuditRule" /> también contiene información sobre cómo los objetos secundarios heredan la regla, cómo se propaga la herencia y para qué condiciones se audita.</summary>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.AuditRule" /> utilizando los valores especificados.</summary>
      <param name="identity">La identidad a la que se aplica la regla de auditoría.Debe ser un objeto que pueda convertirse en un objeto <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="accessMask">La máscara de acceso de esta regla.La máscara de acceso es una colección de 32 bits de bits anónimos, cuyo significado queda definido por cada uno de sus integrantes individuales.</param>
      <param name="isInherited">Es true para heredar esta regla de un contenedor primario.</param>
      <param name="inheritanceFlags">Propiedades de herencia de la regla de auditoría.</param>
      <param name="propagationFlags">Determina si se propagan automáticamente las reglas de auditoría heredadas.Los marcadores de propagación se omiten si el valor del parámetro <paramref name="inheritanceFlags" /> se establece en <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="auditFlags">Condiciones para las que se audita la regla.</param>
      <exception cref="T:System.ArgumentException">El valor del parámetro <paramref name="identity" /> no se puede convertir al tipo <see cref="T:System.Security.Principal.SecurityIdentifier" /> o el parámetro <paramref name="auditFlags" /> contiene un valor no válido.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El valor del parámetro <paramref name="accessMask" /> es cero o los parámetros <paramref name="inheritanceFlags" /> o <paramref name="propagationFlags" /> contienen valores de marcador no reconocidos.</exception>
    </member>
    <member name="P:System.Security.AccessControl.AuditRule.AuditFlags">
      <summary>Obtiene los marcadores de auditoría para esta regla de auditoría.</summary>
      <returns>Combinación bit a bit de los valores de la enumeración.Esta combinación especifica las condiciones de auditoría para esta regla de auditoría.</returns>
    </member>
    <member name="T:System.Security.AccessControl.AuditRule`1">
      <summary>Representa una combinación de la identidad de un usuario y una máscara de acceso.</summary>
      <typeparam name="T"></typeparam>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule`1.#ctor(System.Security.Principal.IdentityReference,`0,System.Security.AccessControl.AuditFlags)">
      <summary>Inicializa una nueva instancia de la clase AuditRule’1 utilizando los valores especificados.</summary>
      <param name="identity">La identidad a la que se aplica esta regla de auditoría.</param>
      <param name="rights">Derechos de la regla de auditoría.</param>
      <param name="flags">Condiciones para las que se audita la regla.</param>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule`1.#ctor(System.Security.Principal.IdentityReference,`0,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>Inicializa una nueva instancia de la clase AuditRule’1 utilizando los valores especificados.</summary>
      <param name="identity">La identidad a la que se aplica la regla de auditoría. </param>
      <param name="rights">Derechos de la regla de auditoría.</param>
      <param name="inheritanceFlags">Propiedades de herencia de la regla de auditoría.</param>
      <param name="propagationFlags">Determina si se propagan automáticamente las reglas de auditoría heredadas.</param>
      <param name="flags">Condiciones para las que se audita la regla.</param>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule`1.#ctor(System.String,`0,System.Security.AccessControl.AuditFlags)">
      <summary>Inicializa una nueva instancia de la clase AuditRule’1 utilizando los valores especificados.</summary>
      <param name="identity">La identidad a la que se aplica la regla de auditoría.</param>
      <param name="rights">Derechos de la regla de auditoría.</param>
      <param name="flags">Propiedades de la regla de auditoría.</param>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule`1.#ctor(System.String,`0,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>Inicializa una nueva instancia de la clase AuditRule’1 utilizando los valores especificados.</summary>
      <param name="identity">La identidad a la que se aplica la regla de auditoría.</param>
      <param name="rights">Derechos de la regla de auditoría.</param>
      <param name="inheritanceFlags">Propiedades de herencia de la regla de auditoría.</param>
      <param name="propagationFlags">Determina si se propagan automáticamente las reglas de auditoría heredadas.</param>
      <param name="flags">Condiciones para las que se audita la regla.</param>
    </member>
    <member name="P:System.Security.AccessControl.AuditRule`1.Rights">
      <summary>Derechos de la regla de auditoría.</summary>
      <returns>Devuelve <see cref="{0}" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.AuthorizationRule">
      <summary>Determina el acceso a los objetos que se pueden proteger.Las clases derivadas <see cref="T:System.Security.AccessControl.AccessRule" /> y <see cref="T:System.Security.AccessControl.AuditRule" /> proporcionan especializaciones para la funcionalidad de acceso y auditoría.</summary>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AuthorizationControl.AccessRule" /> utilizando los valores especificados.</summary>
      <param name="identity">Identidad a la que se aplica la regla de acceso.  Este parámetro debe ser un objeto que permita su conversión al tipo <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="accessMask">La máscara de acceso de esta regla.La máscara de acceso es una colección de 32 bits de bits anónimos, cuyo significado queda definido por cada uno de sus integrantes individuales.</param>
      <param name="isInherited">Es true para heredar esta regla de un contenedor primario.</param>
      <param name="inheritanceFlags">Las propiedades de herencia de la regla de acceso. </param>
      <param name="propagationFlags">Indica si las reglas de acceso heredadas se propagan automáticamente.Los marcadores de propagación se omiten si el valor del parámetro <paramref name="inheritanceFlags" /> se establece en <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <exception cref="T:System.ArgumentException">El valor del parámetro <paramref name="identity" /> no se puede convertir al tipo <see cref="T:System.Security.Principal.SecurityIdentifier" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El valor del parámetro <paramref name="accessMask" /> es cero o los parámetros <paramref name="inheritanceFlags" /> o <paramref name="propagationFlags" /> contienen valores de marcador no reconocidos.</exception>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.AccessMask">
      <summary>Obtiene la máscara de acceso para esta regla.</summary>
      <returns>Máscara de acceso para esta regla.</returns>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.IdentityReference">
      <summary>Obtiene la identidad <see cref="T:System.Security.Principal.IdentityReference" /> a la que se aplica esta regla. </summary>
      <returns>La identidad <see cref="T:System.Security.Principal.IdentityReference" /> a la que se aplica esta regla. </returns>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.InheritanceFlags">
      <summary>Obtiene el valor de marcadores que determinan cómo heredan esta regla los objetos secundarios.</summary>
      <returns>Combinación bit a bit de los valores de la enumeración.</returns>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.IsInherited">
      <summary>Obtiene un valor que indica si esta regla se establece explícitamente o si se hereda de un objeto contenedor primario.</summary>
      <returns>Es true si esta regla no se establece explícitamente pero en cambio se hereda de un contenedor primario.</returns>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.PropagationFlags">
      <summary>Obtiene el valor de los marcadores de propagación que determinan cómo se propaga la herencia de esta regla a los objetos secundarios.Esta propiedad sólo es significativa cuando el valor de la enumeración <see cref="T:System.Security.AccessControl.InheritanceFlags" /> no es <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</summary>
      <returns>Combinación bit a bit de los valores de la enumeración.</returns>
    </member>
    <member name="T:System.Security.AccessControl.AuthorizationRuleCollection">
      <summary>Representa una colección de objetos <see cref="T:System.Security.AccessControl.AuthorizationRule" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.AuthorizationRuleCollection" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.AddRule(System.Security.AccessControl.AuthorizationRule)">
      <summary>Agrega un objeto <see cref="T:System.Web.Configuration.AuthorizationRule" /> a la colección.</summary>
      <param name="rule">Objeto <see cref="T:System.Web.Configuration.AuthorizationRule" /> que se va a agregar a la colección.</param>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.CopyTo(System.Security.AccessControl.AuthorizationRule[],System.Int32)">
      <summary>Copia el contenido de la colección en una matriz.</summary>
      <param name="rules">Matriz en la que se copia el contenido de la colección.</param>
      <param name="index">Índice de base cero desde el que se empieza a copiar.</param>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRuleCollection.Count"></member>
    <member name="P:System.Security.AccessControl.AuthorizationRuleCollection.Item(System.Int32)">
      <summary>Obtiene el objeto <see cref="T:System.Security.AccessControl.AuthorizationRule" /> en el índice especificado de la colección.</summary>
      <returns>El objeto <see cref="T:System.Security.AccessControl.AuthorizationRule" /> correspondiente al índice especificado.</returns>
      <param name="index">Índice de base cero del objeto <see cref="T:System.Security.AccessControl.AuthorizationRule" /> que se va a obtener.</param>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)"></member>
    <member name="P:System.Security.AccessControl.AuthorizationRuleCollection.System#Collections#ICollection#IsSynchronized"></member>
    <member name="P:System.Security.AccessControl.AuthorizationRuleCollection.System#Collections#ICollection#SyncRoot"></member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.System#Collections#IEnumerable#GetEnumerator"></member>
    <member name="T:System.Security.AccessControl.CommonAce">
      <summary>Representa una entrada de control de acceso (ACE).</summary>
    </member>
    <member name="M:System.Security.AccessControl.CommonAce.#ctor(System.Security.AccessControl.AceFlags,System.Security.AccessControl.AceQualifier,System.Int32,System.Security.Principal.SecurityIdentifier,System.Boolean,System.Byte[])">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.CommonAce" />.</summary>
      <param name="flags">Marcadores que especifican información sobre la herencia, la propagación de herencia y las condiciones de auditoría para la nueva entrada de control de acceso (ACE).</param>
      <param name="qualifier">El uso de la nueva ACE.</param>
      <param name="accessMask">La máscara de acceso para la ACE.</param>
      <param name="sid">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> asociado a la nueva ACE.</param>
      <param name="isCallback">Es true para especificar que la nueva ACE es una ACE de tipo de devolución de llamada. </param>
      <param name="opaque">Los datos opacos asociados a la nueva ACE.Sólo se permiten los datos opacos para los tipos ACE de devolución de llamada. La longitud de esta matriz no debe ser mayor que el valor devuelto del método <see cref="M:System.Security.AccessControl.CommonAce.MaxOpaqueLength(System.Boolean)" />.</param>
    </member>
    <member name="P:System.Security.AccessControl.CommonAce.BinaryLength">
      <summary>Obtiene la longitud, en bytes, de la representación binaria del objeto <see cref="T:System.Security.AccessControl.CommonAce" /> actual.Utilice esta longitud con el método <see cref="M:System.Security.AccessControl.CommonAce.GetBinaryForm(System.Byte[],System.Int32)" /> antes de calcular las referencias de la ACL en una matriz binaria.</summary>
      <returns>Longitud, en bytes, de la representación binaria del objeto <see cref="T:System.Security.AccessControl.CommonAce" /> actual.</returns>
    </member>
    <member name="M:System.Security.AccessControl.CommonAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Realiza el cálculo de referencias entre el contenido del objeto <see cref="T:System.Security.AccessControl.CommonAce" /> y la matriz de bytes especificada a partir de la posición de desplazamiento indicada.</summary>
      <param name="binaryForm">La matriz de bytes para la cual se va a realizar el cálculo de referencias del contenido del objeto <see cref="T:System.Security.AccessControl.CommonAce" />.</param>
      <param name="offset">El desplazamiento desde el cual comenzará el cálculo de referencias.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> es negativo o un valor demasiado alto para que se pueda copiar todo el contenido de <see cref="T:System.Security.AccessControl.CommonAce" /> en la matriz <paramref name="binaryForm" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.CommonAce.MaxOpaqueLength(System.Boolean)">
      <summary>Obtiene la máxima longitud permitida de un BLOB de datos opacos para entradas de control de acceso (ACE) de devolución de llamada. </summary>
      <returns>La longitud permitida de un BLOB de datos opacos.</returns>
      <param name="isCallback">Es true para especificar que el objeto <see cref="T:System.Security.AccessControl.CommonAce" /> es un tipo de ACE de devolución de llamada. </param>
    </member>
    <member name="T:System.Security.AccessControl.CommonAcl">
      <summary>Representa una lista de control de acceso (ACL) y es la clase base de las clases <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> y <see cref="T:System.Security.AccessControl.SystemAcl" />.</summary>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.BinaryLength">
      <summary>Obtiene la longitud, en bytes, de la representación binaria del objeto <see cref="T:System.Security.AccessControl.CommonAcl" /> actual.Debe usarse este valor de longitud antes de hacer el cálculo de referencias de la lista de control de acceso (ACL) en una matriz binaria mediante el método <see cref="M:System.Security.AccessControl.CommonAcl.GetBinaryForm(System.Byte[],System.Int32)" />.</summary>
      <returns>La longitud, en bytes, de la representación binaria del objeto <see cref="T:System.Security.AccessControl.CommonAcl" /> actual.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.Count">
      <summary>Obtiene el número de entradas de control de acceso (ACE) del objeto <see cref="T:System.Security.AccessControl.CommonAcl" /> actual.</summary>
      <returns>Número de ACE del objeto <see cref="T:System.Security.AccessControl.CommonAcl" /> actual.</returns>
    </member>
    <member name="M:System.Security.AccessControl.CommonAcl.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Realiza el cálculo de referencias del objeto <see cref="T:System.Security.AccessControl.CommonAcl" /> en la matriz de bytes especificada a partir de la posición de desplazamiento indicada.</summary>
      <param name="binaryForm">Matriz de bytes para la cual se va a realizar el cálculo de referencias del contenido del objeto <see cref="T:System.Security.AccessControl.CommonAcl" />.</param>
      <param name="offset">El desplazamiento desde el cual comenzará el cálculo de referencias.</param>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.IsCanonical">
      <summary>Obtiene un valor booleano que especifica si las entradas de control de acceso (ACE) del objeto <see cref="T:System.Security.AccessControl.CommonAcl" /> actual están en orden canónico.</summary>
      <returns>Es true si las ACE del objeto <see cref="T:System.Security.AccessControl.CommonAcl" /> actual están en orden canónico; en caso contrario, es false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.IsContainer">
      <summary>Establece si el objeto <see cref="T:System.Security.AccessControl.CommonAcl" /> es un contenedor. </summary>
      <returns>Es true si el objeto <see cref="T:System.Security.AccessControl.CommonAcl" /> actual es un contenedor.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.IsDS">
      <summary>Establece si el objeto <see cref="T:System.Security.AccessControl.CommonAcl" /> actual es una lista de control de acceso (ACL) de objetos de directorio.</summary>
      <returns>Es true si el objeto <see cref="T:System.Security.AccessControl.CommonAcl" /> actual es una ACL de objetos de directorio.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.Item(System.Int32)">
      <summary>Obtiene o establece la clase <see cref="T:System.Security.AccessControl.CommonAce" /> en el índice especificado.</summary>
      <returns>
        <see cref="T:System.Security.AccessControl.CommonAce" /> en el índice especificado.</returns>
      <param name="index">Índice de base cero de <see cref="T:System.Security.AccessControl.CommonAce" /> que se va a obtener o establecer.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonAcl.Purge(System.Security.Principal.SecurityIdentifier)">
      <summary>Quita todas las entradas de control de acceso (ACE) contenidas en este objeto <see cref="T:System.Security.AccessControl.CommonAcl" /> que están asociadas al objeto <see cref="T:System.Security.Principal.SecurityIdentifier" /> especificado.</summary>
      <param name="sid">Objeto <see cref="T:System.Security.Principal.SecurityIdentifier" /> que se va a comprobar.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonAcl.RemoveInheritedAces">
      <summary>Quita todas las entradas de control de acceso (ACE) heredadas de este objeto <see cref="T:System.Security.AccessControl.CommonAcl" />.</summary>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.Revision">
      <summary>Obtiene el nivel de revisión del objeto <see cref="T:System.Security.AccessControl.CommonAcl" />.</summary>
      <returns>Valor de tipo byte que indica el nivel de revisión del objeto <see cref="T:System.Security.AccessControl.CommonAcl" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.CommonObjectSecurity">
      <summary>Controla el acceso a los objetos sin manipulación directa de listas de control de acceso (ACL).Ésta es la clase base abstracta de la clase <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.#ctor(System.Boolean)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <param name="isContainer">Es true si el nuevo objeto es un contenedor.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.AddAccessRule(System.Security.AccessControl.AccessRule)">
      <summary>Agrega la regla de acceso especificada a la lista de control de acceso discrecional (DACL) asociada a este objeto <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <param name="rule">La regla de acceso que se va a agregar.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.AddAuditRule(System.Security.AccessControl.AuditRule)">
      <summary>Agrega la regla de auditoría especificada a la lista de control de acceso de sistema (SACL) asociada a este objeto <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <param name="rule">La regla de auditoría que se va a agregar.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.GetAccessRules(System.Boolean,System.Boolean,System.Type)">
      <summary>Obtiene una colección de las reglas de acceso asociadas al identificador de seguridad especificado.</summary>
      <returns>La colección de reglas de acceso asociadas al objeto <see cref="T:System.Security.Principal.SecurityIdentifier" /> especificado.</returns>
      <param name="includeExplicit">Es true para incluir explícitamente reglas de acceso establecidas para el objeto.</param>
      <param name="includeInherited">Es true para incluir las reglas de acceso heredadas.</param>
      <param name="targetType">Especifica si el identificador de seguridad para el que se deben recuperar reglas de acceso es del tipo T:System.Security.Principal.SecurityIdentifier o T:System.Security.Principal.NTAccount.El valor de este parámetro debe ser un tipo que se pueda traducir al tipo <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.GetAuditRules(System.Boolean,System.Boolean,System.Type)">
      <summary>Obtiene una colección de las reglas de auditoría asociadas al identificador de seguridad especificado.</summary>
      <returns>La colección de reglas de auditoría asociadas al objeto <see cref="T:System.Security.Principal.SecurityIdentifier" /> especificado.</returns>
      <param name="includeExplicit">Es true para incluir explícitamente reglas de auditoría establecidas para el objeto.</param>
      <param name="includeInherited">Es true para incluir las reglas de auditoría heredadas.</param>
      <param name="targetType">El identificador de seguridad para el que se van a recuperar las reglas de auditoría.Este debe ser un objeto que permita su conversión al tipo <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.ModifyAccess(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AccessRule,System.Boolean@)">
      <summary>Aplica la modificación especificada a la lista de control de acceso discrecional (DACL) asociada a este objeto <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <returns>Es true si se modifica correctamente la lista DACL; en caso contrario, es false.</returns>
      <param name="modification">La modificación que se va a aplicar a la DACL.</param>
      <param name="rule">La regla de acceso que se va a modificar.</param>
      <param name="modified">Es true si se modifica correctamente la lista DACL; en caso contrario, es false.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.ModifyAudit(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AuditRule,System.Boolean@)">
      <summary>Aplica la modificación especificada a la lista de control de acceso de sistema (SACL) asociada a este objeto <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <returns>Es true si se modifica correctamente la lista SACL; en caso contrario, es false.</returns>
      <param name="modification">Modificación que se va a aplicar a la SACL.</param>
      <param name="rule">La regla de auditoría que se va a modificar.</param>
      <param name="modified">Es true si se modifica correctamente la lista SACL; en caso contrario, es false.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAccessRule(System.Security.AccessControl.AccessRule)">
      <summary>Quita las reglas de acceso que contienen el mismo identificador de seguridad y máscara de acceso que la regla de acceso especificada de la lista de control de acceso discrecional (DACL) asociada a este objeto <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <returns>true si la regla se quitó satisfactoriamente; en caso contrario, false.</returns>
      <param name="rule">La regla de acceso que se va a quitar.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAccessRuleAll(System.Security.AccessControl.AccessRule)">
      <summary>Quita todas las reglas de acceso que tienen el mismo identificador de seguridad que la regla de acceso especificada de la lista de control de acceso discrecional (DACL) asociada a este objeto <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <param name="rule">La regla de acceso que se va a quitar.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAccessRuleSpecific(System.Security.AccessControl.AccessRule)">
      <summary>Quita todas las reglas de acceso que coinciden exactamente con la regla de acceso especificada de la lista de control de acceso discrecional (DACL) asociada a este objeto <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <param name="rule">La regla de acceso que se va a quitar.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAuditRule(System.Security.AccessControl.AuditRule)">
      <summary>Quita las reglas de auditoría que contienen el mismo identificador de seguridad y máscara de acceso que la regla de auditoría especificada de la lista de control de acceso de sistema (SACL) asociada a este objeto <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <returns>true si la regla de auditoría se quitó satisfactoriamente; en caso contrario, false.</returns>
      <param name="rule">La regla de auditoría que se va a quitar.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAuditRuleAll(System.Security.AccessControl.AuditRule)">
      <summary>Quita todas las reglas de auditoría que tienen el mismo identificador de seguridad que la regla de auditoría especificada de la lista de control de acceso de sistema (SACL) asociada a este objeto <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <param name="rule">La regla de auditoría que se va a quitar.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAuditRuleSpecific(System.Security.AccessControl.AuditRule)">
      <summary>Quita todas las reglas de auditoría que coinciden exactamente con la regla de auditoría especificada de la lista de control de acceso de sistema (SACL) asociada a este objeto <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <param name="rule">La regla de auditoría que se va a quitar.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.ResetAccessRule(System.Security.AccessControl.AccessRule)">
      <summary>Quita todas las reglas de acceso de la lista de control de acceso discrecional (DACL) asociada a este objeto <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> y, a continuación, agrega la regla de acceso especificada.</summary>
      <param name="rule">La regla de acceso que se va a restablecer.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.SetAccessRule(System.Security.AccessControl.AccessRule)">
      <summary>Quita todas las reglas de acceso que contienen el mismo identificador de seguridad y calificador que la regla de acceso especificada en la lista de control de acceso discrecional (DACL) asociada a ese objeto <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> y, a continuación, agrega la regla de acceso especificada.</summary>
      <param name="rule">La regla de acceso que se va a establecer.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.SetAuditRule(System.Security.AccessControl.AuditRule)">
      <summary>Quita todas las reglas de auditoría que contienen el mismo identificador de seguridad y calificador que la regla de auditoría especificada en la lista de control de acceso de sistema (SACL) asociada a ese objeto <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> y, a continuación, agrega la regla de auditoría especificada.</summary>
      <param name="rule">La regla de auditoría que se va a establecer.</param>
    </member>
    <member name="T:System.Security.AccessControl.CommonSecurityDescriptor">
      <summary>Representa un descriptor de seguridad.Un descriptor de seguridad incluye un propietario, un grupo primario, una lista de control de acceso discrecional (DACL) y una lista de control de acceso del sistema (SACL).</summary>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.#ctor(System.Boolean,System.Boolean,System.Byte[],System.Int32)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> a partir de la matriz de valores de tipo byte especificada.</summary>
      <param name="isContainer">Es true si el nuevo descriptor de seguridad está asociado a un objeto contenedor.</param>
      <param name="isDS">Es true si el nuevo descriptor de seguridad está asociado a un objeto de directorio.</param>
      <param name="binaryForm">La matriz de valores de tipo byte a partir de la que se va a crear el nuevo objeto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</param>
      <param name="offset">El desplazamiento en la matriz <paramref name="binaryForm" /> desde donde se empieza a copiar.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.#ctor(System.Boolean,System.Boolean,System.Security.AccessControl.ControlFlags,System.Security.Principal.SecurityIdentifier,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.SystemAcl,System.Security.AccessControl.DiscretionaryAcl)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> a partir de la información especificada.</summary>
      <param name="isContainer">Es true si el nuevo descriptor de seguridad está asociado a un objeto contenedor.</param>
      <param name="isDS">Es true si el nuevo descriptor de seguridad está asociado a un objeto de directorio.</param>
      <param name="flags">Marcadores que especifican el comportamiento del nuevo objeto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</param>
      <param name="owner">El propietario del nuevo objeto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</param>
      <param name="group">Objeto primario para el nuevo objeto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</param>
      <param name="systemAcl">Lista de control de acceso de sistema (SACL) para el nuevo objeto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</param>
      <param name="discretionaryAcl">Lista de control de acceso discrecional (DACL) para el nuevo objeto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.#ctor(System.Boolean,System.Boolean,System.Security.AccessControl.RawSecurityDescriptor)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> a partir del objeto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> especificado.</summary>
      <param name="isContainer">Es true si el nuevo descriptor de seguridad está asociado a un objeto contenedor.</param>
      <param name="isDS">Es true si el nuevo descriptor de seguridad está asociado a un objeto de directorio.</param>
      <param name="rawSecurityDescriptor">Objeto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> a partir del cual se va a crear el nuevo objeto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.#ctor(System.Boolean,System.Boolean,System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> a partir de la cadena de lenguaje de definición de descriptores de seguridad (SDDL) especificada.</summary>
      <param name="isContainer">Es true si el nuevo descriptor de seguridad está asociado a un objeto contenedor.</param>
      <param name="isDS">Es true si el nuevo descriptor de seguridad está asociado a un objeto de directorio.</param>
      <param name="sddlForm">Cadena SDDL a partir de la cual se va a crear el nuevo objeto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.AddDiscretionaryAcl(System.Byte,System.Int32)">
      <summary>Establece el <see cref="P:System.Security.AccessControl.CommonSecurityDescriptor.DiscretionaryAcl" /> propiedad para este <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> instancia y establece el <see cref="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclPresent" /> marca.</summary>
      <param name="revision">Nivel de revisión del nuevo objeto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />.</param>
      <param name="trusted">Número de entradas de control de acceso (ACE) que este objeto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> puede contener.Este número solamente se usará como una sugerencia.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.AddSystemAcl(System.Byte,System.Int32)">
      <summary>Establece el <see cref="P:System.Security.AccessControl.CommonSecurityDescriptor.SystemAcl" /> propiedad para este <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> instancia y establece el <see cref="F:System.Security.AccessControl.ControlFlags.SystemAclPresent" /> marca.</summary>
      <param name="revision">Nivel de revisión del nuevo objeto <see cref="T:System.Security.AccessControl.SystemAcl" />.</param>
      <param name="trusted">Número de entradas de control de acceso (ACE) que este objeto <see cref="T:System.Security.AccessControl.SystemAcl" /> puede contener.Este número solamente se usará como una sugerencia.</param>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.ControlFlags">
      <summary>Obtiene valores que especifican el comportamiento del objeto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</summary>
      <returns>Uno o más valores de la enumeración <see cref="T:System.Security.AccessControl.ControlFlags" /> combinados con la operación OR lógica.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.DiscretionaryAcl">
      <summary>Obtiene o establece la lista de control de acceso discrecional (DACL) para este objeto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.La DACL contiene las reglas de acceso.</summary>
      <returns>La DACL para este objeto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.Group">
      <summary>Obtiene o establece el grupo primario para este objeto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</summary>
      <returns>Grupo primario para este objeto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.IsContainer">
      <summary>Obtiene un valor booleano que especifica si el objeto asociado a este objeto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> es un objeto contenedor.</summary>
      <returns>Es true si el objeto asociado a este objeto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> es un objeto contenedor; en caso contrario, es false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.IsDiscretionaryAclCanonical">
      <summary>Obtiene un valor booleano que especifica si la lista de control de acceso discrecional (DACL) asociada a este objeto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> está en orden canónico.</summary>
      <returns>Es true si la DACL asociada a este objeto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> está en orden canónico; en caso contrario, es false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.IsDS">
      <summary>Obtiene un valor booleano que especifica si el objeto asociado a este objeto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> es un objeto de directorio.</summary>
      <returns>Es true si el objeto asociado a este objeto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> es un objeto de directorio; en caso contrario, es false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.IsSystemAclCanonical">
      <summary>Obtiene un valor booleano que especifica si la lista de control de acceso de sistema (SACL) asociada a este objeto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> está en orden canónico.</summary>
      <returns>Es true si la SACL asociada a este objeto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> está en orden canónico; en caso contrario, es false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.Owner">
      <summary>Obtiene o establece el propietario del objeto asociado a este objeto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</summary>
      <returns>El propietario del objeto asociado a este objeto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.PurgeAccessControl(System.Security.Principal.SecurityIdentifier)">
      <summary>Quita todas las reglas de acceso para el identificador de seguridad especificado de la lista de control de acceso discrecional (DACL) asociada a este objeto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</summary>
      <param name="sid">El identificador de seguridad para el que se van a quitar las reglas de acceso.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.PurgeAudit(System.Security.Principal.SecurityIdentifier)">
      <summary>Quita todas las reglas de auditoría para el identificador de seguridad especificado de la lista de control de acceso de sistema (SACL) asociada a este objeto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</summary>
      <param name="sid">El identificador de seguridad para el que se van a quitar las reglas de auditoría.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.SetDiscretionaryAclProtection(System.Boolean,System.Boolean)">
      <summary>Establece la protección de herencia para la lista de control de acceso discrecional (DACL) asociada a este objeto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.Las DACL que están protegidas no heredan las reglas de acceso de los contenedores primarios.</summary>
      <param name="isProtected">Es true para proteger la DACL contra herencia.</param>
      <param name="preserveInheritance">Es true para mantener las reglas de acceso heredadas en la DACL; es false para quitar las reglas de acceso heredadas de la DACL.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.SetSystemAclProtection(System.Boolean,System.Boolean)">
      <summary>Establece la protección de herencia para la lista de control de acceso de sistema (SACL) asociada a este objeto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.Las SACL que están protegidas no heredan las reglas de auditoría de los contenedores primarios.</summary>
      <param name="isProtected">Es true para proteger la SACL contra herencia.</param>
      <param name="preserveInheritance">Es true para mantener las reglas de auditoría heredadas en SACL; es false para quitar las reglas de auditoría heredadas de SACL.</param>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.SystemAcl">
      <summary>Obtiene o establece la lista de control de acceso de sistema (SACL) para este objeto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.La SACL contiene reglas de auditoría.</summary>
      <returns>SACL para este objeto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.CompoundAce">
      <summary>Representa una entrada de control de acceso (ACE) compuesta.</summary>
    </member>
    <member name="M:System.Security.AccessControl.CompoundAce.#ctor(System.Security.AccessControl.AceFlags,System.Int32,System.Security.AccessControl.CompoundAceType,System.Security.Principal.SecurityIdentifier)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.CompoundAce" />.</summary>
      <param name="flags">Contiene marcadores que especifican información sobre la herencia, su propagación y las condiciones de auditoría de la nueva entrada de control de acceso (ACE).</param>
      <param name="accessMask">La máscara de acceso para la ACE.</param>
      <param name="compoundAceType">Valor de la enumeración <see cref="T:System.Security.AccessControl.CompoundAceType" />.</param>
      <param name="sid">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> asociado a la nueva ACE.</param>
    </member>
    <member name="P:System.Security.AccessControl.CompoundAce.BinaryLength">
      <summary>Obtiene la longitud (en bytes) de la representación binaria del objeto <see cref="T:System.Security.AccessControl.CompoundAce" /> actual.Debe utilizarse este valor de longitud antes de calcular las referencias de la lista de control de acceso (ACL) en una matriz binaria mediante el método <see cref="M:System.Security.AccessControl.CompoundAce.GetBinaryForm" />.</summary>
      <returns>La longitud (en bytes) de la representación binaria del objeto <see cref="T:System.Security.AccessControl.CompoundAce" /> actual.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CompoundAce.CompoundAceType">
      <summary>Obtiene o establece el tipo de este objeto <see cref="T:System.Security.AccessControl.CompoundAce" />.</summary>
      <returns>Tipo de este objeto <see cref="T:System.Security.AccessControl.CompoundAce" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.CompoundAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Calcula las referencias del contenido del objeto <see cref="T:System.Security.AccessControl.CompoundAce" /> en la matriz de bytes especificada, comenzando por la posición de desplazamiento indicada.</summary>
      <param name="binaryForm">Matriz de bytes en la que se calculan las referencias del contenido del objeto <see cref="T:System.Security.AccessControl.CompoundAce" />.</param>
      <param name="offset">El desplazamiento desde el cual comenzará el cálculo de referencias.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">El valor del parámetro <paramref name="offset" /> es negativo o es un valor demasiado elevado como para poder copiar todo el contenido del objeto <see cref="T:System.Security.AccessControl.CompoundAce" /> en el parámetro <paramref name="array" />.</exception>
    </member>
    <member name="T:System.Security.AccessControl.CompoundAceType">
      <summary>Especifica el tipo de un objeto <see cref="T:System.Security.AccessControl.CompoundAce" />.</summary>
    </member>
    <member name="F:System.Security.AccessControl.CompoundAceType.Impersonation">
      <summary>El objeto <see cref="T:System.Security.AccessControl.CompoundAce" /> se usa para la suplantación.</summary>
    </member>
    <member name="T:System.Security.AccessControl.ControlFlags">
      <summary>Estos marcadores afectan al comportamiento del descriptor de seguridad.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclAutoInherited">
      <summary>Especifica que la Lista de control de acceso discrecional (DACL) se ha heredado automáticamente del elemento primario.Sólo pueden establecerlo los administradores de recursos.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclAutoInheritRequired">
      <summary>Se omitirá.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclDefaulted">
      <summary>Especifica que la DACL se ha obtenido mediante un mecanismo de aplicación de valores predeterminados.Sólo pueden establecerlo los administradores de recursos.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclPresent">
      <summary>Especifica que la DACL no es null.Pueden establecerlo los administradores de recursos o los usuarios.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclProtected">
      <summary>Especifica que el administrador de recursos impide la herencia automática.Pueden establecerlo los administradores de recursos o los usuarios.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclUntrusted">
      <summary>Se omitirá.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.GroupDefaulted">
      <summary>Especifica que el <see cref="T:System.Security.Principal.SecurityIdentifier" /> de grupo se ha obtenido mediante un mecanismo de aplicación de valores predeterminados.Sólo pueden establecerlo los administradores de recursos; no deberían establecerlo quienes llaman.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.None">
      <summary>No existe ningún marcador de control.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.OwnerDefaulted">
      <summary>Especifica que el <see cref="T:System.Security.Principal.SecurityIdentifier" /> de propietario se ha obtenido mediante un mecanismo de aplicación de valores predeterminados.Sólo pueden establecerlo los administradores de recursos; no deberían establecerlo quienes llaman.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.RMControlValid">
      <summary>Especifica que el contenido del campo Reserved es válido.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SelfRelative">
      <summary>Especifica que la representación binaria del descriptor de seguridad está en el formato autorrelativo.  Siempre se establece este marcador.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.ServerSecurity">
      <summary>Se omitirá.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclAutoInherited">
      <summary>Especifica que la lista de control de acceso de sistema (SACL) se ha heredado automáticamente del elemento primario.Sólo pueden establecerlo los administradores de recursos.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclAutoInheritRequired">
      <summary>Se omitirá.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclDefaulted">
      <summary>Especifica que la SACL se ha obtenido mediante un mecanismo de aplicación de valores predeterminados.Sólo pueden establecerlo los administradores de recursos.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclPresent">
      <summary>Especifica que la SACL no es null.Pueden establecerlo los administradores de recursos o los usuarios.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclProtected">
      <summary>Especifica que el administrador de recursos impide la herencia automática.Pueden establecerlo los administradores de recursos o los usuarios.</summary>
    </member>
    <member name="T:System.Security.AccessControl.CustomAce">
      <summary>Representa una entrada de control de acceso (ACE) que no ha definido uno de los miembros de la enumeración <see cref="T:System.Security.AccessControl.AceType" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.CustomAce.#ctor(System.Security.AccessControl.AceType,System.Security.AccessControl.AceFlags,System.Byte[])">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.CustomAce" />.</summary>
      <param name="type">Tipo de la nueva entrada de control de acceso (ACE).Este valor debe ser mayor que <see cref="F:System.Security.AccessControl.AceType.MaxDefinedAceType" />.</param>
      <param name="flags">Marcadores que especifican información sobre la herencia, la propagación de herencia y las condiciones de auditoría para la nueva ACE.</param>
      <param name="opaque">Matriz de valores de tipo byte que contiene los datos para la nueva ACE.Este valor puede ser null.La longitud de esta matriz no debe ser mayor que el valor del campo <see cref="F:System.Security.AccessControl.CustomAce.MaxOpaqueLength" /> y debe ser un múltiplo de cuatro.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">El valor del parámetro <paramref name="type" /> no es mayor que <see cref="F:System.Security.AccessControl.AceType.MaxDefinedAceType" /> o la longitud de la matriz <paramref name="opaque" /> o es mayor que el valor del campo <see cref="F:System.Security.AccessControl.CustomAce.MaxOpaqueLength" /> o no es múltiplo de cuatro.</exception>
    </member>
    <member name="P:System.Security.AccessControl.CustomAce.BinaryLength">
      <summary>Obtiene la longitud, en bytes, de la representación binaria del objeto <see cref="T:System.Security.AccessControl.CustomAce" /> actual.Debe utilizarse este valor de longitud antes de hacer el cálculo de referencias entre la lista ACL y una matriz binaria utilizando el método <see cref="M:System.Security.AccessControl.CustomAce.GetBinaryForm" />.</summary>
      <returns>La longitud, en bytes, de la representación binaria del objeto <see cref="T:System.Security.AccessControl.CustomAce" /> actual.</returns>
    </member>
    <member name="M:System.Security.AccessControl.CustomAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Realiza el cálculo de referencias entre el contenido del objeto <see cref="T:System.Security.AccessControl.CustomAce" /> y la matriz de bytes especificada a partir de la posición de desplazamiento indicada.</summary>
      <param name="binaryForm">La matriz de bytes para la cual se va a realizar el cálculo de referencias del contenido del objeto <see cref="T:System.Security.AccessControl.CustomAce" />.</param>
      <param name="offset">El desplazamiento desde el cual comenzará el cálculo de referencias.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> es negativo o un valor demasiado alto para que se pueda copiar todo el contenido de <see cref="T:System.Security.AccessControl.CustomAce" /> en <paramref name="array" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.CustomAce.GetOpaque">
      <summary>Devuelve los datos opacos asociados a este objeto <see cref="T:System.Security.AccessControl.CustomAce" />. </summary>
      <returns>Matriz de valores de tipo byte que representa los datos opacos asociados a este objeto <see cref="T:System.Security.AccessControl.CustomAce" />.</returns>
    </member>
    <member name="F:System.Security.AccessControl.CustomAce.MaxOpaqueLength">
      <summary>Devuelve la longitud máxima permitida de un objeto binario de datos opacos para este objeto <see cref="T:System.Security.AccessControl.CustomAce" />.</summary>
    </member>
    <member name="P:System.Security.AccessControl.CustomAce.OpaqueLength">
      <summary>Obtiene la longitud de los datos opacos asociada al objeto <see cref="T:System.Security.AccessControl.CustomAce" />.</summary>
      <returns>La longitud de los datos de devolución de llamada opacos.</returns>
    </member>
    <member name="M:System.Security.AccessControl.CustomAce.SetOpaque(System.Byte[])">
      <summary>Establece los datos de devolución de llamada opacos asociados a este objeto <see cref="T:System.Security.AccessControl.CustomAce" />.</summary>
      <param name="opaque">Matriz de valores de tipo byte que representa los datos de devolución de llamada opacos para este objeto <see cref="T:System.Security.AccessControl.CustomAce" />.</param>
    </member>
    <member name="T:System.Security.AccessControl.DiscretionaryAcl">
      <summary>Representa una lista de control de acceso discrecional (DACL).</summary>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.#ctor(System.Boolean,System.Boolean,System.Byte,System.Int32)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> con los valores especificados.</summary>
      <param name="isContainer">true si el nuevo objeto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> es un contenedor.</param>
      <param name="isDS">true si el nuevo objeto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> es una lista de control de acceso (ACL) de objetos de directorio.</param>
      <param name="revision">Nivel de revisión del nuevo objeto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />.</param>
      <param name="capacity">Número de entradas de control de acceso (ACE) que este objeto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> puede contener.Este número solamente se usará como una sugerencia.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.#ctor(System.Boolean,System.Boolean,System.Int32)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> con los valores especificados.</summary>
      <param name="isContainer">true si el nuevo objeto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> es un contenedor.</param>
      <param name="isDS">true si el nuevo objeto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> es una lista de control de acceso (ACL) de objetos de directorio.</param>
      <param name="capacity">Número de entradas de control de acceso (ACE) que este objeto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> puede contener.Este número solamente se usará como una sugerencia.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.#ctor(System.Boolean,System.Boolean,System.Security.AccessControl.RawAcl)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> con los valores indicados del objeto <see cref="T:System.Security.AccessControl.RawAcl" /> especificado.</summary>
      <param name="isContainer">true si el nuevo objeto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> es un contenedor.</param>
      <param name="isDS">true si el nuevo objeto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> es una lista de control de acceso (ACL) de objetos de directorio.</param>
      <param name="rawAcl">Objeto <see cref="T:System.Security.AccessControl.RawAcl" /> subyacente para el nuevo objeto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />.Especifique null para crear una ACL vacía.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.AddAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Agrega una entrada de control de acceso (ACE) al objeto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> actual con los valores de configuración especificados.</summary>
      <param name="accessType">El tipo de control de acceso (conceder o denegar) que se va a agregar.</param>
      <param name="sid">Identificador <see cref="T:System.Security.Principal.SecurityIdentifier" /> para el que se va a agregar una ACE.</param>
      <param name="accessMask">La regla de acceso para la nueva ACE.</param>
      <param name="inheritanceFlags">Marcas que especifican las propiedades de herencia para la nueva ACE.</param>
      <param name="propagationFlags">Marcas que especifican las propiedades de propagación de herencia para la nueva ACE.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.AddAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Agrega una entrada de control de acceso (ACE) al objeto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> actual con los valores de configuración especificados.Use este método para las listas de control de acceso (ACL) de objetos de directorio cuando especifique el tipo de objeto o el tipo de objeto heredado para la nueva ACE.</summary>
      <param name="accessType">El tipo de control de acceso (conceder o denegar) que se va a agregar.</param>
      <param name="sid">Identificador <see cref="T:System.Security.Principal.SecurityIdentifier" /> para el que se va a agregar una ACE.</param>
      <param name="accessMask">La regla de acceso para la nueva ACE.</param>
      <param name="inheritanceFlags">Marcas que especifican las propiedades de herencia para la nueva ACE.</param>
      <param name="propagationFlags">Marcas que especifican las propiedades de propagación de herencia para la nueva ACE.</param>
      <param name="objectFlags">Marcadores que especifican si los parámetros <paramref name="objectType" /> e <paramref name="inheritedObjectType" /> contienen valores distintos de null.</param>
      <param name="objectType">La identidad de la clase de objetos a los que se aplica la nueva ACE.</param>
      <param name="inheritedObjectType">La identidad de la clase de objetos secundarios que pueden heredar la nueva ACE.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.AddAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAccessRule)">
      <summary>Agrega una entrada de control de acceso (ACE) al objeto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> actual con los valores de configuración especificados.</summary>
      <param name="accessType">El tipo de control de acceso (conceder o denegar) que se va a agregar.</param>
      <param name="sid">Identificador <see cref="T:System.Security.Principal.SecurityIdentifier" /> para el que se va a agregar una ACE.</param>
      <param name="rule">El <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> para tener acceso al nuevo.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Quita la regla de control de acceso especificada del objeto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> actual.</summary>
      <returns>Es true si este método quita correctamente el acceso especificado; de lo contrario, es false.</returns>
      <param name="accessType">El tipo de control de acceso (conceder o denegar) que se va a quitar.</param>
      <param name="sid">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> para el que se va quitar una regla de control de acceso.</param>
      <param name="accessMask">Máscara de acceso de la regla que se va a quitar.</param>
      <param name="inheritanceFlags">Marcas que especifican las propiedades de herencia para la regla que se va a quitar.</param>
      <param name="propagationFlags">Marcas que especifican las propiedades de propagación de herencia para la regla que se va a quitar.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Quita la regla de control de acceso especificada del objeto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> actual.Use este método para las listas de control de acceso (ACL) de objetos de directorio cuando especifique el tipo de objeto o el tipo de objeto heredado.</summary>
      <returns>Es true si este método quita correctamente el acceso especificado; de lo contrario, es false.</returns>
      <param name="accessType">El tipo de control de acceso (conceder o denegar) que se va a quitar.</param>
      <param name="sid">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> para el que se va quitar una regla de control de acceso.</param>
      <param name="accessMask">Máscara de acceso de la regla de control de acceso que se va a quitar.</param>
      <param name="inheritanceFlags">Marcas que especifican las propiedades de herencia de la regla de control de acceso que se va a quitar.</param>
      <param name="propagationFlags">Marcas que especifican las propiedades de propagación de herencia para la regla de control de acceso que se va a quitar.</param>
      <param name="objectFlags">Marcadores que especifican si los parámetros <paramref name="objectType" /> e <paramref name="inheritedObjectType" /> contienen valores distintos de null.</param>
      <param name="objectType">La identidad de la clase de objetos a los que se aplica la regla de control de acceso quitada.</param>
      <param name="inheritedObjectType">La identidad de la clase de objetos secundarios que pueden heredar la regla de control de acceso quitada.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAccessRule)">
      <summary>Quita la regla de control de acceso especificada del objeto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> actual.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.</returns>
      <param name="accessType">El tipo de control de acceso (conceder o denegar) que se va a quitar.</param>
      <param name="sid">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> para el que se va quitar una regla de control de acceso.</param>
      <param name="rule">El <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> para el que se va a quitar el acceso.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccessSpecific(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Quita la entrada de control de acceso (ACE) especificada del objeto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> actual.</summary>
      <param name="accessType">El tipo de control de acceso (conceder o denegar) que se va a quitar.</param>
      <param name="sid">Identificador <see cref="T:System.Security.Principal.SecurityIdentifier" /> para el que se va a quitar una ACE.</param>
      <param name="accessMask">Máscara de acceso de la ACE que se va a quitar.</param>
      <param name="inheritanceFlags">Marcas que especifican las propiedades de herencia de la ACE que se va a quitar.</param>
      <param name="propagationFlags">Marcas que especifican las propiedades de propagación de herencia para la ACE que se va a quitar.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccessSpecific(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Quita la entrada de control de acceso (ACE) especificada del objeto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> actual.Use este método para las listas de control de acceso (ACL) de objetos de directorio cuando especifique el tipo de objeto o el tipo de objeto heredado para la ACE que se va a quitar.</summary>
      <param name="accessType">El tipo de control de acceso (conceder o denegar) que se va a quitar.</param>
      <param name="sid">Identificador <see cref="T:System.Security.Principal.SecurityIdentifier" /> para el que se va a quitar una ACE.</param>
      <param name="accessMask">Máscara de acceso de la ACE que se va a quitar.</param>
      <param name="inheritanceFlags">Marcas que especifican las propiedades de herencia de la ACE que se va a quitar.</param>
      <param name="propagationFlags">Marcas que especifican las propiedades de propagación de herencia para la ACE que se va a quitar.</param>
      <param name="objectFlags">Marcadores que especifican si los parámetros <paramref name="objectType" /> e <paramref name="inheritedObjectType" /> contienen valores distintos de null.</param>
      <param name="objectType">La identidad de la clase de objetos a los que se aplica la ACE quitada.</param>
      <param name="inheritedObjectType">La identidad de la clase de objetos secundarios que pueden heredar la ACE quitada.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccessSpecific(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAccessRule)">
      <summary>Quita la entrada de control de acceso (ACE) especificada del objeto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> actual.</summary>
      <param name="accessType">El tipo de control de acceso (conceder o denegar) que se va a quitar.</param>
      <param name="sid">Identificador <see cref="T:System.Security.Principal.SecurityIdentifier" /> para el que se va a quitar una ACE.</param>
      <param name="rule">El <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> para el que se va a quitar el acceso.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.SetAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Establece el control de acceso especificado para el objeto <see cref="T:System.Security.Principal.SecurityIdentifier" /> especificado.</summary>
      <param name="accessType">El tipo de control de acceso (conceder o denegar) que se va a establecer.</param>
      <param name="sid">Identificador <see cref="T:System.Security.Principal.SecurityIdentifier" /> para el que se va a establecer una ACE.</param>
      <param name="accessMask">La regla de acceso para la nueva ACE.</param>
      <param name="inheritanceFlags">Marcas que especifican las propiedades de herencia para la nueva ACE.</param>
      <param name="propagationFlags">Marcas que especifican las propiedades de propagación de herencia para la nueva ACE.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.SetAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Establece el control de acceso especificado para el objeto <see cref="T:System.Security.Principal.SecurityIdentifier" /> especificado.</summary>
      <param name="accessType">El tipo de control de acceso (conceder o denegar) que se va a establecer.</param>
      <param name="sid">Identificador <see cref="T:System.Security.Principal.SecurityIdentifier" /> para el que se va a establecer una ACE.</param>
      <param name="accessMask">La regla de acceso para la nueva ACE.</param>
      <param name="inheritanceFlags">Marcas que especifican las propiedades de herencia para la nueva ACE.</param>
      <param name="propagationFlags">Marcas que especifican las propiedades de propagación de herencia para la nueva ACE.</param>
      <param name="objectFlags">Marcadores que especifican si los parámetros <paramref name="objectType" /> e <paramref name="inheritedObjectType" /> contienen valores distintos de null.</param>
      <param name="objectType">La identidad de la clase de objetos a los que se aplica la nueva ACE.</param>
      <param name="inheritedObjectType">La identidad de la clase de objetos secundarios que pueden heredar la nueva ACE.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.SetAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAccessRule)">
      <summary>Establece el control de acceso especificado para el objeto <see cref="T:System.Security.Principal.SecurityIdentifier" /> especificado.</summary>
      <param name="accessType">El tipo de control de acceso (conceder o denegar) que se va a establecer.</param>
      <param name="sid">Identificador <see cref="T:System.Security.Principal.SecurityIdentifier" /> para el que se va a establecer una ACE.</param>
      <param name="rule">El <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> para el que se va a establecer el acceso.</param>
    </member>
    <member name="T:System.Security.AccessControl.GenericAce">
      <summary>Representa un entrada de control de acceso (ACE) y es la clase base de todas las demás clases ACE.</summary>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.AceFlags">
      <summary>Obtiene o establece la enumeración <see cref="T:System.Security.AccessControl.AceFlags" /> asociada al objeto <see cref="T:System.Security.AccessControl.GenericAce" />.</summary>
      <returns>Enumeración <see cref="T:System.Security.AccessControl.AceFlags" /> asociada al objeto <see cref="T:System.Security.AccessControl.GenericAce" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.AceType">
      <summary>Obtiene el tipo de esta entrada de control de acceso (ACE).</summary>
      <returns>Tipo de esta ACE.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.AuditFlags">
      <summary>Obtiene la información de auditoría asociada a esta entrada de control de acceso (ACE).</summary>
      <returns>Información de auditoría asociada a esta entrada de control de acceso (ACE).</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.BinaryLength">
      <summary>Obtiene la longitud, en bytes, de la representación binaria del objeto <see cref="T:System.Security.AccessControl.GenericAce" /> actual.Debe utilizarse este valor de longitud antes de hacer el cálculo de referencias entre la lista ACL y una matriz binaria utilizando el método <see cref="M:System.Security.AccessControl.GenericAce.GetBinaryForm" />.</summary>
      <returns>Longitud, en bytes, de la representación binaria del objeto <see cref="T:System.Security.AccessControl.GenericAce" /> actual.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.Copy">
      <summary>Crea una copia en profundidad de esta entrada de control de acceso (ACE).</summary>
      <returns>Objeto <see cref="T:System.Security.AccessControl.GenericAce" /> que este método crea.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.CreateFromBinaryForm(System.Byte[],System.Int32)">
      <summary>Crea un objeto <see cref="T:System.Security.AccessControl.GenericAce" /> a partir de los datos binarios especificados.</summary>
      <returns>El objeto <see cref="T:System.Security.AccessControl.GenericAce" /> que crea este método.</returns>
      <param name="binaryForm">Datos binarios a partir de los que se va a crear el nuevo objeto <see cref="T:System.Security.AccessControl.GenericAce" />.</param>
      <param name="offset">Posición de desplazamiento desde la cual comenzará la resolución de referencias.</param>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.Equals(System.Object)">
      <summary>Determina si el objeto <see cref="T:System.Security.AccessControl.GenericAce" /> especificado es igual al objeto <see cref="T:System.Security.AccessControl.GenericAce" /> actual.</summary>
      <returns>Es true si el objeto <see cref="T:System.Security.AccessControl.GenericAce" /> especificado es igual al objeto <see cref="T:System.Security.AccessControl.GenericAce" /> actual; en caso contrario, es false.</returns>
      <param name="o">Objeto <see cref="T:System.Security.AccessControl.GenericAce" /> que se va a comparar con el objeto <see cref="T:System.Security.AccessControl.GenericAce" /> actual.</param>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Realiza el cálculo de referencias entre el contenido del objeto <see cref="T:System.Security.AccessControl.GenericAce" /> y la matriz de bytes especificada a partir de la posición de desplazamiento indicada.</summary>
      <param name="binaryForm">Matriz de bytes para la cual se va a realizar el cálculo de referencias del contenido del objeto <see cref="T:System.Security.AccessControl.GenericAce" />.</param>
      <param name="offset">El desplazamiento desde el cual comenzará el cálculo de referencias.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> es negativo o un valor demasiado alto para que se pueda copiar todo el contenido de <see cref="T:System.Security.AccessControl.GenericAcl" /> en <paramref name="array" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.GetHashCode">
      <summary>Sirve como función hash para la clase <see cref="T:System.Security.AccessControl.GenericAce" />.El método <see cref="M:System.Security.AccessControl.GenericAce.GetHashCode" /> es apto para el uso en algoritmos hash y estructuras de datos como una tabla hash.</summary>
      <returns>Código hash para el objeto <see cref="T:System.Security.AccessControl.GenericAce" /> actual.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.InheritanceFlags">
      <summary>Obtiene marcadores que especifican las propiedades de herencia de esta entrada de control de acceso (ACE).</summary>
      <returns>Marcadores que especifican las propiedades de herencia de esta ACE.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.IsInherited">
      <summary>Obtiene un valor booleano que especifica si esta entrada de control de acceso (ACE) se hereda o se establece explícitamente.</summary>
      <returns>Es true si esta ACE se hereda; en caso contrario, es false.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.op_Equality(System.Security.AccessControl.GenericAce,System.Security.AccessControl.GenericAce)">
      <summary>Determina si los objetos <see cref="T:System.Security.AccessControl.GenericAce" /> especificados se consideran iguales.</summary>
      <returns>Es true si los dos objetos <see cref="T:System.Security.AccessControl.GenericAce" /> son iguales; en caso contrario, es false.</returns>
      <param name="left">Primer objeto <see cref="T:System.Security.AccessControl.GenericAce" /> que se va a comparar.</param>
      <param name="right">Segundo objeto <see cref="T:System.Security.AccessControl.GenericAce" /> que se va a comparar.</param>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.op_Inequality(System.Security.AccessControl.GenericAce,System.Security.AccessControl.GenericAce)">
      <summary>Determina si los objetos <see cref="T:System.Security.AccessControl.GenericAce" /> especificados se consideran distintos.</summary>
      <returns>Es true si los dos objetos <see cref="T:System.Security.AccessControl.GenericAce" /> son distintos; en caso contrario, es false.</returns>
      <param name="left">Primer objeto <see cref="T:System.Security.AccessControl.GenericAce" /> que se va a comparar.</param>
      <param name="right">Segundo objeto <see cref="T:System.Security.AccessControl.GenericAce" /> que se va a comparar.</param>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.PropagationFlags">
      <summary>Obtiene marcadores que especifican las propiedades de propagación de herencia de esta entrada de control de acceso (ACE).</summary>
      <returns>Marcadores que especifican las propiedades de propagación de herencia de esta ACE.</returns>
    </member>
    <member name="T:System.Security.AccessControl.GenericAcl">
      <summary>Representa una lista de control de acceso (ACL) y es la clase base de las clases <see cref="T:System.Security.AccessControl.CommonAcl" />, <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />, <see cref="T:System.Security.AccessControl.RawAcl" /> y <see cref="T:System.Security.AccessControl.SystemAcl" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.GenericAcl" />.</summary>
    </member>
    <member name="F:System.Security.AccessControl.GenericAcl.AclRevision">
      <summary>Nivel de revisión de la clase <see cref="T:System.Security.AccessControl.GenericAcl" /> actual.La propiedad <see cref="P:System.Security.AccessControl.GenericAcl.Revision" /> devuelve este valor para listas de control de acceso (ACL) no asociadas a objetos de Servicios de directorio. </summary>
    </member>
    <member name="F:System.Security.AccessControl.GenericAcl.AclRevisionDS">
      <summary>Nivel de revisión de la clase <see cref="T:System.Security.AccessControl.GenericAcl" /> actual.La propiedad <see cref="P:System.Security.AccessControl.GenericAcl.Revision" /> devuelve este valor para listas de control de acceso (ACL) asociadas a objetos de Servicios de directorio. </summary>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.BinaryLength">
      <summary>Obtiene la longitud, en bytes, de la representación binaria del objeto <see cref="T:System.Security.AccessControl.GenericAcl" /> actual.Debe utilizarse este valor de longitud antes de hacer el cálculo de referencias entre la lista ACL y una matriz binaria con el método <see cref="M:System.Security.AccessControl.GenericAcl.GetBinaryForm" />.</summary>
      <returns>La longitud, en bytes, de la representación binaria del objeto <see cref="T:System.Security.AccessControl.GenericAcl" /> actual.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.CopyTo(System.Security.AccessControl.GenericAce[],System.Int32)">
      <summary>Copia cada objeto <see cref="T:System.Security.AccessControl.GenericAce" /> del objeto <see cref="T:System.Security.AccessControl.GenericAcl" /> actual a la matriz especificada.</summary>
      <param name="array">Matriz en la que se colocan copias de los objetos <see cref="T:System.Security.AccessControl.GenericAce" /> de la clase <see cref="T:System.Security.AccessControl.GenericAcl" /> actual.</param>
      <param name="index">Índice de base cero de la matriz <paramref name="array" /> donde se comienza a copiar.</param>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.Count">
      <summary>Obtiene el número de entradas de control de acceso (ACE) del objeto <see cref="T:System.Security.AccessControl.GenericAcl" /> actual.</summary>
      <returns>Número de ACE del objeto <see cref="T:System.Security.AccessControl.GenericAcl" /> actual.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Realiza el cálculo de referencias entre el contenido del objeto <see cref="T:System.Security.AccessControl.GenericAcl" /> y la matriz de bytes especificada a partir de la posición de desplazamiento indicada.</summary>
      <param name="binaryForm">Matriz de bytes para la cual se va a realizar el cálculo de referencias del contenido del objeto <see cref="T:System.Security.AccessControl.GenericAcl" />.</param>
      <param name="offset">El desplazamiento desde el cual comenzará el cálculo de referencias.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> es negativo o un valor demasiado alto para que se pueda copiar todo el contenido de <see cref="T:System.Security.AccessControl.GenericAcl" /> en <paramref name="array" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.GetEnumerator">
      <summary>Devuelve una nueva instancia de la clase <see cref="T:System.Security.AccessControl.AceEnumerator" />.</summary>
      <returns>Objeto <see cref="T:Security.AccessControl.AceEnumerator" /> que este método devuelve.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.IsSynchronized">
      <summary>Esta propiedad siempre se establece en false.Sólo se implementa porque se requiere para la implementación de la interfaz <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>false, siempre.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.Item(System.Int32)">
      <summary>Obtiene o establece el objeto <see cref="T:System.Security.AccessControl.GenericAce" /> en el índice especificado.</summary>
      <returns>
        <see cref="T:System.Security.AccessControl.GenericAce" /> en el índice especificado.</returns>
      <param name="index">Índice de base cero del objeto <see cref="T:System.Security.AccessControl.GenericAce" /> que se va a obtener o establecer.</param>
    </member>
    <member name="F:System.Security.AccessControl.GenericAcl.MaxBinaryLength">
      <summary>Longitud máxima binaria permitida de un objeto <see cref="T:System.Security.AccessControl.GenericAcl" />.</summary>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.Revision">
      <summary>Obtiene el nivel de revisión del objeto <see cref="T:System.Security.AccessControl.GenericAcl" />.</summary>
      <returns>Valor de tipo byte que indica el nivel de revisión del objeto <see cref="T:System.Security.AccessControl.GenericAcl" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.SyncRoot">
      <summary>Esta propiedad devuelve siempre null.Sólo se implementa porque se requiere para la implementación de la interfaz <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Siempre devuelve null.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copia cada objeto <see cref="T:System.Security.AccessControl.GenericAce" /> del objeto <see cref="T:System.Security.AccessControl.GenericAcl" /> actual a la matriz especificada.</summary>
      <param name="array">Matriz en la que se colocan copias de los objetos <see cref="T:System.Security.AccessControl.GenericAce" /> de la clase <see cref="T:System.Security.AccessControl.GenericAcl" /> actual.</param>
      <param name="index">Índice de base cero de la matriz <paramref name="array" /> donde se comienza a copiar.</param>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.System#Collections#IEnumerable#GetEnumerator">
      <summary>Devuelve una nueva instancia de la clase <see cref="T:System.Security.AccessControl.AceEnumerator" /> convertida como una instancia de la interfaz <see cref="T:System.Collections.IEnumerator" />.</summary>
      <returns>Nuevo objeto <see cref="T:System.Security.AccessControl.AceEnumerator" />, convertido como instancia de la interfaz <see cref="T:System.Collections.IEnumerator" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.GenericSecurityDescriptor">
      <summary>Representa un descriptor de seguridad.Un descriptor de seguridad incluye un propietario, un grupo primario, una lista de control de acceso discrecional (DACL) y una lista de control de acceso de sistema (SACL).</summary>
    </member>
    <member name="M:System.Security.AccessControl.GenericSecurityDescriptor.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.GenericSecurity" />.</summary>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.BinaryLength">
      <summary>Obtiene la longitud, en bytes, de la representación binaria del objeto <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> actual.Debe utilizarse este valor de longitud antes de hacer el cálculo de referencias entre la lista ACL y una matriz binaria utilizando el método <see cref="M:System.Security.AccessControl.GenericSecurityDescriptor.GetBinaryForm" />.</summary>
      <returns>La longitud, en bytes, de la representación binaria del objeto <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> actual.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.ControlFlags">
      <summary>Obtiene valores que especifican el comportamiento del objeto <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</summary>
      <returns>Uno o más valores de la enumeración <see cref="T:System.Security.AccessControl.ControlFlags" /> combinados con la operación OR lógica.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericSecurityDescriptor.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Devuelve una matriz de valores de tipo byte que representa la información contenida en este objeto <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</summary>
      <param name="binaryForm">La matriz de bytes para la cual se va a realizar el cálculo de referencias del contenido del objeto <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</param>
      <param name="offset">El desplazamiento desde el cual comenzará el cálculo de referencias.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> es negativo o un valor demasiado alto para que se pueda copiar todo el contenido de <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> en <paramref name="array" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.GenericSecurityDescriptor.GetSddlForm(System.Security.AccessControl.AccessControlSections)">
      <summary>Devuelve la representación de lenguaje de definición de descriptores de seguridad (SDDL) de las secciones especificadas del descriptor de seguridad que este objeto <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> representa.</summary>
      <returns>Representación de SDDL de las secciones especificadas del descriptor de seguridad asociadas a este objeto <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</returns>
      <param name="includeSections">Especifica qué secciones (reglas de acceso, reglas de auditoría, grupo primario, propietario) del descriptor de seguridad se van a obtener.</param>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.Group">
      <summary>Obtiene o establece el grupo primario para este objeto <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</summary>
      <returns>Grupo primario para este objeto <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericSecurityDescriptor.IsSddlConversionSupported">
      <summary>Devuelve un valor booleano que especifica si el descriptor de seguridad asociado a este objeto <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> se puede convertir al formato de lenguaje de definición de descriptores de seguridad (SDDL).</summary>
      <returns>Es true si el descriptor de seguridad asociado a este objeto <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> se puede convertir al formato de lenguaje de definición de descriptores de seguridad (SDDL); en caso contrario, es false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.Owner">
      <summary>Obtiene o establece el propietario del objeto asociado a este objeto <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</summary>
      <returns>El propietario del objeto asociado a este objeto <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.Revision">
      <summary>Obtiene el nivel de revisión del objeto <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</summary>
      <returns>Valor de tipo byte que indica el nivel de revisión del objeto <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.InheritanceFlags">
      <summary>Los marcadores de herencia especifican la semántica de herencia de las entradas de control de acceso (ACE).</summary>
    </member>
    <member name="F:System.Security.AccessControl.InheritanceFlags.ContainerInherit">
      <summary>Los objetos contenedores secundarios se heredan de la ACE.</summary>
    </member>
    <member name="F:System.Security.AccessControl.InheritanceFlags.None">
      <summary>Los objetos secundarios no se heredan de la ACE.</summary>
    </member>
    <member name="F:System.Security.AccessControl.InheritanceFlags.ObjectInherit">
      <summary>Los objetos hoja secundarios se heredan de la ACE.</summary>
    </member>
    <member name="T:System.Security.AccessControl.KnownAce">
      <summary>Encapsula todos los tipos de entradas de control de acceso (ACE) actualmente definidas por Microsoft Corporation.Todos los objetos <see cref="T:System.Security.AccessControl.KnownAce" /> contienen una máscara de acceso de 32 bits y un objeto <see cref="T:System.Security.Principal.SecurityIdentifier" />.</summary>
    </member>
    <member name="P:System.Security.AccessControl.KnownAce.AccessMask">
      <summary>Obtiene o establece la máscara de acceso de este objeto <see cref="T:System.Security.AccessControl.KnownAce" />.</summary>
      <returns>Máscara de acceso de este objeto <see cref="T:System.Security.AccessControl.KnownAce" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.KnownAce.SecurityIdentifier">
      <summary>Obtiene o establece el objeto <see cref="T:System.Security.Principal.SecurityIdentifier" /> asociado a este objeto <see cref="T:System.Security.AccessControl.KnownAce" />.</summary>
      <returns>Objeto <see cref="T:System.Security.Principal.SecurityIdentifier" /> asociado a este objeto <see cref="T:System.Security.AccessControl.KnownAce" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.NativeObjectSecurity">
      <summary>Proporciona la capacidad para controlar el acceso a los objetos nativos sin la manipulación directa de listas de control de acceso (ACL).La enumeración <see cref="T:System.Security.AccessControl.ResourceType" /> define los tipos de objeto nativos.</summary>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> con los valores especificados.</summary>
      <param name="isContainer">Es true si el nuevo objeto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> es un contenedor.</param>
      <param name="resourceType">Tipo de objeto que se puede proteger al que se asocia el nuevo objeto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> con los valores especificados.Es recomendable pasar los valores de los parámetros <paramref name="includeSections" /> al constructor y que los métodos Persist sean idénticos.Para obtener más información, vea la sección Comentarios.</summary>
      <param name="isContainer">Es true si el nuevo objeto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> es un contenedor.</param>
      <param name="resourceType">Tipo de objeto que se puede proteger al que se asocia el nuevo objeto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="handle">El identificador del objeto que se puede proteger al que se asocia el nuevo objeto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="includeSections">Uno de los valores de la enumeración <see cref="T:System.Security.AccessControl.AccessControlSections" /> que especifica las secciones del descriptor de seguridad (reglas de acceso, reglas de auditoría, propietario y grupo principal) del objeto que se puede proteger y que se va a incluir en el objeto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> con los valores especificados.Es recomendable pasar los valores de los parámetros <paramref name="includeSections" /> al constructor y que los métodos Persist sean idénticos.Para obtener más información, vea la sección Comentarios.</summary>
      <param name="isContainer">Es true si el nuevo objeto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> es un contenedor.</param>
      <param name="resourceType">Tipo de objeto que se puede proteger al que se asocia el nuevo objeto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="handle">El identificador del objeto que se puede proteger al que se asocia el nuevo objeto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="includeSections">Uno de los valores de la enumeración <see cref="T:System.Security.AccessControl.AccessControlSections" /> que especifica las secciones del descriptor de seguridad (reglas de acceso, reglas de auditoría, propietario y grupo principal) del objeto que se puede proteger y que se va a incluir en el objeto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="exceptionFromErrorCode">Delegado implementado por integradores que proporciona las excepciones personalizadas. </param>
      <param name="exceptionContext">Objeto que contiene información contextual sobre el origen o el destino de la excepción.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> utilizando los valores especificados.</summary>
      <param name="isContainer">Es true si el nuevo objeto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> es un contenedor.</param>
      <param name="resourceType">Tipo de objeto que se puede proteger al que se asocia el nuevo objeto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="exceptionFromErrorCode">Delegado implementado por integradores que proporciona las excepciones personalizadas. </param>
      <param name="exceptionContext">Objeto que contiene información contextual sobre el origen o el destino de la excepción.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> con los valores especificados.Es recomendable pasar los valores de los parámetros <paramref name="includeSections" /> al constructor y que los métodos Persist sean idénticos.Para obtener más información, vea la sección Comentarios.</summary>
      <param name="isContainer">Es true si el nuevo objeto <see cref="T:System.Security.AccessControl.NativObjectSecurity" /> es un contenedor.</param>
      <param name="resourceType">Tipo de objeto que se puede proteger al que se asocia el nuevo objeto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="name">El nombre del objeto que se puede proteger al que se asocia el nuevo objeto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="includeSections">Uno de los valores de la enumeración <see cref="T:System.Security.AccessControl.AccessControlSections" /> que especifica las secciones del descriptor de seguridad (reglas de acceso, reglas de auditoría, propietario y grupo principal) del objeto que se puede proteger y que se va a incluir en el objeto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.String,System.Security.AccessControl.AccessControlSections,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> con los valores especificados.Es recomendable pasar los valores de los parámetros <paramref name="includeSections" /> al constructor y que los métodos Persist sean idénticos.Para obtener más información, vea la sección Comentarios.</summary>
      <param name="isContainer">Es true si el nuevo objeto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> es un contenedor.</param>
      <param name="resourceType">Tipo de objeto que se puede proteger al que se asocia el nuevo objeto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="name">El nombre del objeto que se puede proteger al que se asocia el nuevo objeto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="includeSections">Uno de los valores de la enumeración <see cref="T:System.Security.AccessControl.AccessControlSections" /> que especifica las secciones del descriptor de seguridad (reglas de acceso, reglas de auditoría, propietario y grupo principal) del objeto que se puede proteger y que se va a incluir en el objeto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="exceptionFromErrorCode">Delegado implementado por integradores que proporciona las excepciones personalizadas. </param>
      <param name="exceptionContext">Objeto que contiene información contextual sobre el origen o el destino de la excepción.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.Persist(System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections)">
      <summary>Guarda las secciones especificadas del descriptor de seguridad asociado a este objeto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> en una ubicación de almacenamiento permanente.Es recomendable pasar los valores de los parámetros <paramref name="includeSections" /> al constructor y que los métodos Persist sean idénticos.Para obtener más información, vea la sección Comentarios.</summary>
      <param name="handle">El identificador del objeto que se puede proteger al que se asocia este objeto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="includeSections">Uno de los valores de la enumeración <see cref="T:System.Security.AccessControl.AccessControlSections" /> que especifica las secciones del descriptor de seguridad (reglas de acceso, reglas de auditoría, propietario y grupo principal) del objeto que se puede proteger y que se va a guardar.</param>
      <exception cref="T:System.IO.FileNotFoundException">El objeto que se puede proteger al que se asocia este objeto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> es un directorio o un archivo y no se ha podido encontrar ese directorio o archivo.</exception>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.Persist(System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections,System.Object)">
      <summary>Guarda las secciones especificadas del descriptor de seguridad asociado a este objeto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> en una ubicación de almacenamiento permanente.Es recomendable pasar los valores de los parámetros <paramref name="includeSections" /> al constructor y que los métodos Persist sean idénticos.Para obtener más información, vea la sección Comentarios.</summary>
      <param name="handle">El identificador del objeto que se puede proteger al que se asocia este objeto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="includeSections">Uno de los valores de la enumeración <see cref="T:System.Security.AccessControl.AccessControlSections" /> que especifica las secciones del descriptor de seguridad (reglas de acceso, reglas de auditoría, propietario y grupo principal) del objeto que se puede proteger y que se va a guardar.</param>
      <param name="exceptionContext">Objeto que contiene información contextual sobre el origen o el destino de la excepción.</param>
      <exception cref="T:System.IO.FileNotFoundException">El objeto que se puede proteger al que se asocia este objeto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> es un directorio o un archivo y no se ha podido encontrar ese directorio o archivo.</exception>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.Persist(System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>Guarda las secciones especificadas del descriptor de seguridad asociado a este objeto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> en una ubicación de almacenamiento permanente.Es recomendable pasar los valores de los parámetros <paramref name="includeSections" /> al constructor y que los métodos Persist sean idénticos.Para obtener más información, vea la sección Comentarios.</summary>
      <param name="name">El nombre del objeto que se puede proteger al que se asocia este objeto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="includeSections">Uno de los valores de la enumeración <see cref="T:System.Security.AccessControl.AccessControlSections" /> que especifica las secciones del descriptor de seguridad (reglas de acceso, reglas de auditoría, propietario y grupo principal) del objeto que se puede proteger y que se va a guardar.</param>
      <exception cref="T:System.IO.FileNotFoundException">El objeto que se puede proteger al que se asocia este objeto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> es un directorio o un archivo y no se ha podido encontrar ese directorio o archivo.</exception>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.Persist(System.String,System.Security.AccessControl.AccessControlSections,System.Object)">
      <summary>Guarda las secciones especificadas del descriptor de seguridad asociado a este objeto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> en una ubicación de almacenamiento permanente.Es recomendable pasar los valores de los parámetros <paramref name="includeSections" /> al constructor y que los métodos Persist sean idénticos.Para obtener más información, vea la sección Comentarios.</summary>
      <param name="name">El nombre del objeto que se puede proteger al que se asocia este objeto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="includeSections">Uno de los valores de la enumeración <see cref="T:System.Security.AccessControl.AccessControlSections" /> que especifica las secciones del descriptor de seguridad (reglas de acceso, reglas de auditoría, propietario y grupo principal) del objeto que se puede proteger y que se va a guardar.</param>
      <param name="exceptionContext">Objeto que contiene información contextual sobre el origen o el destino de la excepción.</param>
      <exception cref="T:System.IO.FileNotFoundException">El objeto que se puede proteger al que se asocia este objeto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> es un directorio o un archivo y no se ha podido encontrar ese directorio o archivo.</exception>
    </member>
    <member name="T:System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode">
      <summary>Proporciona a los integradores una manera de asignar códigos de error numéricos a excepciones concretas que crean.</summary>
      <returns>Objeto <see cref="T:System.Exception" /> que este delegado crea.</returns>
      <param name="errorCode">Código de error numérico.</param>
      <param name="name">Nombre del objeto asegurable con el que el objeto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> está asociado.</param>
      <param name="handle">Identificador del objeto asegurable con el que el objeto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> está asociado.</param>
      <param name="context">Objeto que contiene información contextual sobre el origen o el destino de la excepción.</param>
    </member>
    <member name="T:System.Security.AccessControl.ObjectAccessRule">
      <summary>Representa una combinación de la identidad de un usuario, una máscara de acceso y un tipo de control de acceso (conceder o denegar).Un objeto <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> también contiene información sobre el tipo de objeto al que se aplica la regla, el tipo de objeto secundario que puede heredar la regla, cómo los objetos secundarios heredan la regla y cómo se propaga la herencia.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAccessRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Guid,System.Guid,System.Security.AccessControl.AccessControlType)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> con los valores especificados.</summary>
      <param name="identity">Identidad a la que se aplica la regla de acceso.  Debe ser un objeto que pueda convertirse en un objeto <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="accessMask">La máscara de acceso de esta regla.La máscara de acceso es una colección de 32 bits de bits anónimos, cuyo significado queda definido por cada uno de sus integrantes individuales.</param>
      <param name="isInherited">Es true si esta regla se hereda de un contenedor primario.</param>
      <param name="inheritanceFlags">Especifica las propiedades de herencia de la regla de acceso.</param>
      <param name="propagationFlags">Especifica si se propagan automáticamente las reglas de acceso heredadas.Los marcadores de propagación se omiten si el valor del parámetro <paramref name="inheritanceFlags" /> se establece en <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="objectType">Tipo de objeto al que se aplica la regla.</param>
      <param name="inheritedObjectType">Tipo de objeto secundario que puede heredar la regla.</param>
      <param name="type">Especifica si esta regla concede o deniega el acceso.</param>
      <exception cref="T:System.ArgumentException">El valor del parámetro <paramref name="identity" /> no puede convertirse en un objeto <see cref="T:System.Security.Principal.SecurityIdentifier" /> o el parámetro <paramref name="type" /> contiene un valor que no es válido.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El valor del parámetro <paramref name="accessMask" /> es 0, o los parámetros <paramref name="inheritanceFlags" /> o <paramref name="propagationFlags" /> contienen valores de marcadores no reconocidos.</exception>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAccessRule.InheritedObjectType">
      <summary>Obtiene el tipo de objeto secundario que puede heredar el objeto <see cref="System.Security.AccessControl.ObjectAccessRule" />.</summary>
      <returns>Tipo de objeto secundario que puede heredar el objeto <see cref="System.Security.AccessControl.ObjectAccessRule" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAccessRule.ObjectFlags">
      <summary>Obtiene marcadores que especifican si las propiedades <see cref="P:System.Security.AccessControl.ObjectAccessRule.ObjectType" /> y <see cref="P:System.Security.AccessControl.ObjectAccessRule.InheritedObjectType" /> del objeto <see cref="System.Security.AccessControl.ObjectAccessRule" /> contienen valores válidos.</summary>
      <returns>
        <see cref="F:System.Security.AccessControl.ObjectAceFlags.ObjectAceTypePresent" /> especifica que la propiedad <see cref="P:System.Security.AccessControl.ObjectAccessRule.ObjectType" /> contiene un valor válido.<see cref="F:System.Security.AccessControl.ObjectAceFlags.InheritedObjectAceTypePresent" /> especifica que la propiedad <see cref="P:System.Security.AccessControl.ObjectAccessRule.InheritedObjectType" /> contiene un valor válido.Estos valores pueden combinarse con un operador lógico OR.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAccessRule.ObjectType">
      <summary>Obtiene el tipo de objeto al que se aplica <see cref="System.Security.AccessControl.ObjectAccessRule" />.</summary>
      <returns>Tipo de objeto al que se aplica <see cref="System.Security.AccessControl.ObjectAccessRule" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.ObjectAce">
      <summary>Controla el acceso a los objetos de Servicios de directorio. Esta clase representa un entrada de control de acceso (ACE) asociada a un objeto de directorio.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAce.#ctor(System.Security.AccessControl.AceFlags,System.Security.AccessControl.AceQualifier,System.Int32,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid,System.Boolean,System.Byte[])">
      <summary>Inicia una nueva instancia de la clase <see cref="T:System.Security.AccessControl.ObjectAce" />.</summary>
      <param name="aceFlags">Herencia, propagación de herencia y condiciones de auditoría para la nueva entrada de control de acceso (ACE).</param>
      <param name="qualifier">El uso de la nueva ACE.</param>
      <param name="accessMask">La máscara de acceso para la ACE.</param>
      <param name="sid">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> asociado a la nueva ACE.</param>
      <param name="flags">Determina si los parámetros <paramref name="type" /> e <paramref name="inheritedType" /> contienen los GUID de objeto válidos. </param>
      <param name="type">GUID que identifica el tipo de objeto al que se aplica la nueva ACE.</param>
      <param name="inheritedType">GUID que identifica el tipo de objeto que puede heredar la nueva ACE.</param>
      <param name="isCallback">true si la nueva ACE es un tipo de devolución de llamada de ACE.</param>
      <param name="opaque">Los datos opacos asociados a la nueva ACE.Esto sólo se permite para tipos ACE de devolución de llamada.La longitud de esta matriz no debe ser mayor que el valor devuelto del método <see cref="M:System.Security.AccessControl.ObjectAceMaxOpaqueLength" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">El parámetro de calificador contiene un valor no válido o la longitud del valor del parámetro opaco es mayor que el valor devuelto del método <see cref="M:System.Security.AccessControl.ObjectAceMaxOpaqueLength" />.</exception>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAce.BinaryLength">
      <summary>Obtiene la longitud, en bytes, de la representación binaria del objeto <see cref="T:System.Security.AccessControl.ObjectAce" /> actual.Debe utilizarse este valor de longitud antes de hacer el cálculo de referencias entre la lista ACL y una matriz binaria utilizando el método <see cref="M:System.Security.AccessControl.ObjectAce.GetBinaryForm" />.</summary>
      <returns>Longitud, en bytes, de la representación binaria del objeto <see cref="T:System.Security.AccessControl.ObjectAce" /> actual.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Realiza el cálculo de referencias entre el contenido del objeto <see cref="T:System.Security.AccessControl.ObjectAce" /> y la matriz de bytes especificada a partir de la posición de desplazamiento indicada.</summary>
      <param name="binaryForm">Matriz de bytes para la cual se va a realizar el cálculo de referencias del contenido del objeto <see cref="T:System.Security.AccessControl.ObjectAce" />.</param>
      <param name="offset">El desplazamiento desde el cual comenzará el cálculo de referencias.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> es negativo o un valor demasiado alto para que se pueda copiar todo el contenido de <see cref="T:System.Security.AccessControl.ObjectAce" /> en <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAce.InheritedObjectAceType">
      <summary>Obtiene o establece el GUID del tipo de objeto que puede heredar la entrada de control de acceso (ACE) que este objeto <see cref="T:System.Security.AccessControl.ObjectAce" /> representa.</summary>
      <returns>GUID del tipo de objeto que puede heredar la entrada de control de acceso (ACE) que este objeto <see cref="T:System.Security.AccessControl.ObjectAce" /> representa.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAce.MaxOpaqueLength(System.Boolean)">
      <summary>Devuelve la longitud máxima permitida, en bytes, de un objeto binario de datos opaco para entradas de control de acceso (ACE) de devolución de llamada.</summary>
      <returns>Longitud máxima permitida, en bytes, de un objeto binario de datos opaco para entradas de control de acceso (ACE) de devolución de llamada.</returns>
      <param name="isCallback">Es true si <see cref="T:System.Security.AccessControl.ObjectAce" /> es un tipo ACE de devolución de llamada.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAce.ObjectAceFlags">
      <summary>Obtiene o establece marcadores que especifican si las propiedades <see cref="P:System.Security.AccessControl.ObjectAce.ObjectAceType" /> e <see cref="P:System.Security.AccessControl.ObjectAce.InheritedObjectAceType" /> contienen valores que identifican los tipos de objeto válidos.</summary>
      <returns>Uno o más miembros de la enumeración <see cref="T:System.Security.AccessControl.ObjectAceFlags" /> combinados con la operación O lógica.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAce.ObjectAceType">
      <summary>Obtiene o establece el GUID del tipo de objeto asociado a este objeto <see cref="T:System.Security.AccessControl.ObjectAce" />.</summary>
      <returns>GUID del tipo de objeto asociado a este objeto <see cref="T:System.Security.AccessControl.ObjectAce" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.ObjectAceFlags">
      <summary>Especifica la presencia de tipos de objeto para entradas de control de acceso (ACE).</summary>
    </member>
    <member name="F:System.Security.AccessControl.ObjectAceFlags.InheritedObjectAceTypePresent">
      <summary>Tipo de objeto que puede heredar la ACE.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ObjectAceFlags.None">
      <summary>No hay ningún tipo de objeto presente.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ObjectAceFlags.ObjectAceTypePresent">
      <summary>El tipo de objeto asociado a la ACE está presente.</summary>
    </member>
    <member name="T:System.Security.AccessControl.ObjectAuditRule">
      <summary>Representa una combinación de la identidad de un usuario, una máscara de acceso y las condiciones de auditoría.Un objeto <see cref="T:System.Security.AccessControl.ObjectAuditRule" /> también contiene información sobre el tipo de objeto al que se aplica la regla, el tipo de objeto secundario que puede heredarla, la forma en que la heredan los objetos secundarios y la forma en que se propaga esa herencia.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAuditRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Guid,System.Guid,System.Security.AccessControl.AuditFlags)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.ObjectAuditRule" />.</summary>
      <param name="identity">Identidad a la que se aplica la regla de acceso.  Debe ser un objeto que pueda convertirse en un objeto <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="accessMask">La máscara de acceso de esta regla.La máscara de acceso es una colección de 32 bits de bits anónimos, cuyo significado queda definido por cada uno de sus integrantes individuales.</param>
      <param name="isInherited">Es true si esta regla se hereda de un contenedor primario.</param>
      <param name="inheritanceFlags">Especifica las propiedades de herencia de la regla de acceso.</param>
      <param name="propagationFlags">Indica si las reglas de acceso heredadas se propagan automáticamente.Los marcadores de propagación se omiten si el valor del parámetro <paramref name="inheritanceFlags" /> se establece en <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="objectType">Tipo de objeto al que se aplica la regla.</param>
      <param name="inheritedObjectType">Tipo de objeto secundario que puede heredar la regla.</param>
      <param name="auditFlags">Condiciones de auditoría.</param>
      <exception cref="T:System.ArgumentException">El valor del parámetro <paramref name="identity" /> no puede convertirse en un objeto <see cref="T:System.Security.Principal.SecurityIdentifier" /> o el parámetro <paramref name="type" /> contiene un valor que no es válido.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El valor del parámetro <paramref name="accessMask" /> es 0, o los parámetros <paramref name="inheritanceFlags" /> o <paramref name="propagationFlags" /> contienen valores de marcadores no reconocidos.</exception>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAuditRule.InheritedObjectType">
      <summary>Obtiene el tipo de objeto secundario que puede heredar el objeto <see cref="System.Security.AccessControl.ObjectAuditRule" />.</summary>
      <returns>Tipo de objeto secundario que puede heredar el objeto <see cref="System.Security.AccessControl.ObjectAuditRule" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAuditRule.ObjectFlags">
      <summary>Las propiedades <see cref="P:System.Security.AccessControl.ObjectAuditRule.ObjectType" /> y <see cref="P:System.Security.AccessControl.ObjectAuditRule.InheritedObjectType" /> del objeto <see cref="System.Security.AccessControl.ObjectAuditRule" /> contienen valores válidos.</summary>
      <returns>
        <see cref="F:System.Security.AccessControl.ObjectAceFlags.ObjectAceTypePresent" /> especifica que la propiedad <see cref="P:System.Security.AccessControl.ObjectAuditRule.ObjectType" /> contiene un valor válido.<see cref="F:System.Security.AccessControl.ObjectAceFlags.InheritedObjectAceTypePresent" /> especifica que la propiedad <see cref="P:System.Security.AccessControl.ObjectAuditRule.InheritedObjectType" /> contiene un valor válido.Estos valores pueden combinarse con un operador lógico OR.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAuditRule.ObjectType">
      <summary>Obtiene el tipo de objeto al que se aplica <see cref="System.Security.AccessControl.ObjectAuditRule" />.</summary>
      <returns>Tipo de objeto al que se aplica <see cref="System.Security.AccessControl.ObjectAuditRule" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.ObjectSecurity">
      <summary>Proporciona la capacidad para controlar el acceso a los objetos sin la manipulación directa de listas de control de acceso (ACL).Esta clase es la clase base abstracta de las clases <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> y <see cref="T:System.Security.AccessControl.DirectoryObjectSecurity" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.#ctor(System.Boolean,System.Boolean)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <param name="isContainer">Es true si el nuevo objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> es un objeto contenedor.</param>
      <param name="isDS">Es true si el nuevo objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> es un objeto de directorio.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.#ctor(System.Security.AccessControl.CommonSecurityDescriptor)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <param name="securityDescriptor">Propiedad <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> de la nueva instancia de <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AccessRightType">
      <summary>Obtiene el objeto <see cref="T:System.Type" /> del objeto asegurable asociado a este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>Tipo del objeto asegurable asociado a este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.AccessRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.AccessRule" /> con los valores especificados.</summary>
      <returns>Objeto <see cref="T:System.Security.AccessControl.AccessRule" /> que crea este método.</returns>
      <param name="identityReference">Identidad a la que se aplica la regla de acceso.Debe ser un objeto que pueda convertirse en un objeto <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="accessMask">La máscara de acceso de esta regla.La máscara de acceso es una colección de 32 bits de bits anónimos, cuyo significado queda definido por cada uno de sus integrantes individuales.</param>
      <param name="isInherited">Es true si esta regla se hereda de un contenedor principal.</param>
      <param name="inheritanceFlags">Especifica las propiedades de herencia de la regla de acceso.</param>
      <param name="propagationFlags">Especifica si se propagan automáticamente las reglas de acceso heredadas.Los marcadores de propagación se omiten si el valor del parámetro <paramref name="inheritanceFlags" /> se establece en <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="type">Especifica el tipo de control de acceso válido.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AccessRulesModified">
      <summary>Obtiene o establece un valor booleano que especifica si se han modificado las reglas de acceso asociadas a este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>Es true si se han modificado las reglas de acceso asociadas a este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" />; de lo contrario, es false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AccessRuleType">
      <summary>Obtiene el objeto <see cref="T:System.Type" /> del objeto asociado a las reglas de acceso de este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.<see cref="T:System.Type" /> debe ser un objeto que permita su conversión al objeto <see cref="T:System.Security.Principal.SecurityIdentifier" />.</summary>
      <returns>Tipo del objeto asociado a las reglas de acceso de este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AreAccessRulesCanonical">
      <summary>Obtiene un valor booleano que especifica si las reglas de acceso asociadas a este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> están en orden canónico.</summary>
      <returns>Es true si las reglas de acceso están en orden canónico; en caso contrario, es false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AreAccessRulesProtected">
      <summary>Obtiene un valor booleano que especifica si está protegida la lista de control de acceso discrecional (DACL) asociada a este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>Es true si la DACL está protegida; en caso contrario, es false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AreAuditRulesCanonical">
      <summary>Obtiene un valor booleano que especifica si las reglas de auditoría asociadas a este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> están en orden canónico.</summary>
      <returns>Es true si las reglas de auditoría están en orden canónico; en caso contrario, es false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AreAuditRulesProtected">
      <summary>Obtiene un valor booleano que especifica si está protegida la lista de control de acceso del sistema (SACL) asociada a este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>Es true si la SACL está protegida; en caso contrario, es false.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.AuditRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.AuditRule" /> con los valores especificados.</summary>
      <returns>Objeto <see cref="T:System.Security.AccessControl.AuditRule" /> que crea este método.</returns>
      <param name="identityReference">La identidad a la que se aplica la regla de auditoría.Debe ser un objeto que pueda convertirse en un objeto <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="accessMask">La máscara de acceso de esta regla.La máscara de acceso es una colección de 32 bits de bits anónimos, cuyo significado queda definido por cada uno de sus integrantes individuales.</param>
      <param name="isInherited">Es true si esta regla se hereda de un contenedor primario.</param>
      <param name="inheritanceFlags">Especifica las propiedades de herencia de la regla de auditoría.</param>
      <param name="propagationFlags">Especifica si se propagan automáticamente las reglas de auditoría heredadas.Los marcadores de propagación se omiten si el valor del parámetro <paramref name="inheritanceFlags" /> se establece en <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="flags">Especifica las condiciones para las que se audita la regla.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AuditRulesModified">
      <summary>Obtiene o establece un valor booleano que especifica si se han modificado las reglas de auditoría asociadas a este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>Es true si se han modificado las reglas de auditoría asociadas a este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" />; de lo contrario, es false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AuditRuleType">
      <summary>Obtiene el objeto <see cref="T:System.Type" /> asociado a las reglas de auditoría de este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.<see cref="T:System.Type" /> debe ser un objeto que permita su conversión al objeto <see cref="T:System.Security.Principal.SecurityIdentifier" />.</summary>
      <returns>Tipo del objeto asociado a las reglas de auditoría de este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.GetGroup(System.Type)">
      <summary>Obtiene el grupo primario asociado al propietario especificado.</summary>
      <returns>El grupo primario asociado al propietario especificado.</returns>
      <param name="targetType">El propietario para el que se va a obtener el grupo primario. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.GetOwner(System.Type)">
      <summary>Obtiene el propietario asociado al grupo primario especificado.</summary>
      <returns>El propietario asociado al grupo especificado.</returns>
      <param name="targetType">El grupo primario para el que se va a obtener el propietario.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.GetSecurityDescriptorBinaryForm">
      <summary>Devuelve una matriz de valores de tipo byte que representa la información del descriptor de seguridad para este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>Matriz de valores de tipo byte que representa el descriptor de seguridad para este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.Este método devuelve null si no hay ninguna información de seguridad de este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.GetSecurityDescriptorSddlForm(System.Security.AccessControl.AccessControlSections)">
      <summary>Devuelve la representación de lenguaje de definición de descriptores de seguridad (SDDL) de las secciones especificadas del descriptor de seguridad asociadas a este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>Representación de SDDL de las secciones especificadas del descriptor de seguridad asociadas a este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</returns>
      <param name="includeSections">Especifica qué secciones (reglas de acceso, reglas de auditoría, grupo primario, propietario) del descriptor de seguridad se van a obtener.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.GroupModified">
      <summary>Obtiene o establece un valor booleano que especifica si se ha modificado el grupo asociado al objeto que se puede proteger. </summary>
      <returns>Es true si se ha modificado el grupo asociado al objeto que se puede proteger; de lo contrario es false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.IsContainer">
      <summary>Obtiene un valor booleano que especifica si este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> es un objeto contenedor.</summary>
      <returns>Es true si el objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> es un objeto contenedor; en caso contrario es false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.IsDS">
      <summary>Obtiene un valor booleano que especifica si este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> es un objeto de directorio.</summary>
      <returns>Es true si el objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> es un objeto de directorio; en caso contrario es false.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.IsSddlConversionSupported">
      <summary>Devuelve un valor booleano que especifica si el descriptor de seguridad asociado a este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> se puede convertir al formato de lenguaje de definición de descriptores de seguridad (SDDL).</summary>
      <returns>Es true si el descriptor de seguridad asociado a este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> se puede convertir al formato de lenguaje de definición de descriptores de seguridad (SDDL); en caso contrario, es false.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ModifyAccess(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AccessRule,System.Boolean@)">
      <summary>Aplica la modificación especificada a la lista de control de acceso discrecional (DACL) asociada a este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>Es true si se modifica correctamente la lista DACL; en caso contrario, es false.</returns>
      <param name="modification">La modificación que se va a aplicar a la DACL.</param>
      <param name="rule">La regla de acceso que se va a modificar.</param>
      <param name="modified">Es true si se modifica correctamente la lista DACL; en caso contrario, es false.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ModifyAccessRule(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AccessRule,System.Boolean@)">
      <summary>Aplica la modificación especificada a la lista de control de acceso discrecional (DACL) asociada a este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>Es true si se modifica correctamente la lista DACL; en caso contrario, es false.</returns>
      <param name="modification">La modificación que se va a aplicar a la DACL.</param>
      <param name="rule">La regla de acceso que se va a modificar.</param>
      <param name="modified">Es true si se modifica correctamente la lista DACL; en caso contrario, es false.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ModifyAudit(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AuditRule,System.Boolean@)">
      <summary>Aplica la modificación especificada a la lista de control de acceso del sistema (SACL) asociada a este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>Es true si se modifica correctamente la lista SACL; en caso contrario, es false.</returns>
      <param name="modification">Modificación que se va a aplicar a la SACL.</param>
      <param name="rule">La regla de auditoría que se va a modificar.</param>
      <param name="modified">Es true si se modifica correctamente la lista SACL; en caso contrario, es false.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ModifyAuditRule(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AuditRule,System.Boolean@)">
      <summary>Aplica la modificación especificada a la lista de control de acceso del sistema (SACL) asociada a este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>Es true si se modifica correctamente la lista SACL; en caso contrario, es false.</returns>
      <param name="modification">Modificación que se va a aplicar a la SACL.</param>
      <param name="rule">La regla de auditoría que se va a modificar.</param>
      <param name="modified">Es true si se modifica correctamente la lista SACL; en caso contrario, es false.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.OwnerModified">
      <summary>Obtiene o establece un valor booleano que especifica si se ha modificado el propietario del objeto que se puede proteger.</summary>
      <returns>Es true si se ha modificado el propietario del objeto que se puede proteger; de lo contrario es false.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.Persist(System.Boolean,System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>Guarda las secciones especificadas del descriptor de seguridad asociado a este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> en una ubicación de almacenamiento permanente.Es recomendable pasar los valores de los parámetros <paramref name="includeSections" /> al constructor y que los métodos Persist sean idénticos.Para obtener más información, vea la sección Comentarios.</summary>
      <param name="enableOwnershipPrivilege">true para habilitar el privilegio que permite al llamador hacerse con la propiedad del objeto.</param>
      <param name="name">Nombre utilizado para recuperar la información almacenada.</param>
      <param name="includeSections">Uno de los valores de la enumeración <see cref="T:System.Security.AccessControl.AccessControlSections" /> que especifica las secciones del descriptor de seguridad (reglas de acceso, reglas de auditoría, propietario y grupo principal) del objeto que se puede proteger y que se va a guardar.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.Persist(System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections)">
      <summary>Guarda las secciones especificadas del descriptor de seguridad asociado a este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> en una ubicación de almacenamiento permanente.Es recomendable pasar los valores de los parámetros <paramref name="includeSections" /> al constructor y que los métodos Persist sean idénticos.Para obtener más información, vea la sección Comentarios.</summary>
      <param name="handle">Identificador utilizado para recuperar la información almacenada.</param>
      <param name="includeSections">Uno de los valores de la enumeración <see cref="T:System.Security.AccessControl.AccessControlSections" /> que especifica las secciones del descriptor de seguridad (reglas de acceso, reglas de auditoría, propietario y grupo principal) del objeto que se puede proteger y que se va a guardar.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.Persist(System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>Guarda las secciones especificadas del descriptor de seguridad asociado a este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> en una ubicación de almacenamiento permanente.Es recomendable pasar los valores de los parámetros <paramref name="includeSections" /> al constructor y que los métodos Persist sean idénticos.Para obtener más información, vea la sección Comentarios.</summary>
      <param name="name">Nombre utilizado para recuperar la información almacenada.</param>
      <param name="includeSections">Uno de los valores de la enumeración <see cref="T:System.Security.AccessControl.AccessControlSections" /> que especifica las secciones del descriptor de seguridad (reglas de acceso, reglas de auditoría, propietario y grupo principal) del objeto que se puede proteger y que se va a guardar.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.PurgeAccessRules(System.Security.Principal.IdentityReference)">
      <summary>Quita todas las reglas de acceso asociadas al objeto <see cref="T:System.Security.Principal.IdentityReference" /> especificado.</summary>
      <param name="identity">Objeto <see cref="T:System.Security.Principal.IdentityReference" /> para el que se van a quitar todas las reglas de acceso.</param>
      <exception cref="T:System.InvalidOperationException">No todas las reglas de acceso están en orden canónico.</exception>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.PurgeAuditRules(System.Security.Principal.IdentityReference)">
      <summary>Quita todas las reglas de auditoría asociadas al objeto <see cref="T:System.Security.Principal.IdentityReference" /> especificado.</summary>
      <param name="identity">Objeto <see cref="T:System.Security.Principal.IdentityReference" /> para el que se van a quitar todas las reglas de auditoría.</param>
      <exception cref="T:System.InvalidOperationException">No todas las reglas de acceso están en orden canónico.</exception>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ReadLock">
      <summary>Bloquea este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> para el acceso de lectura.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ReadUnlock">
      <summary>Desbloquea este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> para el acceso de lectura.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetAccessRuleProtection(System.Boolean,System.Boolean)">
      <summary>Establece o quita la protección de las reglas de acceso asociadas a este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.Los objetos primarios no pueden modificar mediante herencia las reglas de acceso protegidas.</summary>
      <param name="isProtected">Es true para proteger las reglas de acceso asociadas a este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> contra la herencia; es false para permitir la herencia.</param>
      <param name="preserveInheritance">Es true para conservar las reglas de acceso heredadas; es false para quitar las reglas de acceso heredadas.Se omite este parámetro si <paramref name="isProtected" /> es false.</param>
      <exception cref="T:System.InvalidOperationException">Este método intenta quitar las reglas heredadas de una lista de control de acceso discrecional (DACL) no canónica.</exception>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetAuditRuleProtection(System.Boolean,System.Boolean)">
      <summary>Establece o quita la protección de las reglas de auditoría asociadas a este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.Los objetos primarios no pueden modificar mediante herencia las reglas de auditoría protegidas.</summary>
      <param name="isProtected">Es true para proteger las reglas de auditoría asociadas a este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> contra la herencia; es false para permitir la herencia.</param>
      <param name="preserveInheritance">Es true para conservar las reglas de auditoría heredadas; es false para quitar las reglas de auditoría heredadas.Se omite este parámetro si <paramref name="isProtected" /> es false.</param>
      <exception cref="T:System.InvalidOperationException">Este método intenta quitar las reglas heredadas de una lista de control de acceso de sistema (SACL) no canónica.</exception>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetGroup(System.Security.Principal.IdentityReference)">
      <summary>Establece el grupo primario para el descriptor de seguridad asociado a este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <param name="identity">Grupo primario que se va a establecer.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetOwner(System.Security.Principal.IdentityReference)">
      <summary>Establece el propietario para el descriptor de seguridad asociado a este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <param name="identity">Propietario que se va a establecer.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetSecurityDescriptorBinaryForm(System.Byte[])">
      <summary>Establece el descriptor de seguridad para este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> a partir de la matriz de valores de tipo byte especificada.</summary>
      <param name="binaryForm">Matriz de bytes a partir de la cual se va a establecer el descriptor de seguridad.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetSecurityDescriptorBinaryForm(System.Byte[],System.Security.AccessControl.AccessControlSections)">
      <summary>Establece las secciones especificadas del descriptor de seguridad para este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> a partir de la matriz de valores de tipo byte especificada.</summary>
      <param name="binaryForm">Matriz de bytes a partir de la cual se va a establecer el descriptor de seguridad.</param>
      <param name="includeSections">Las secciones (reglas de acceso, reglas de auditoría, propietario, grupo primario) del descriptor de seguridad que se van a establecer.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetSecurityDescriptorSddlForm(System.String)">
      <summary>Establece el descriptor de seguridad para este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> a partir de la cadena de lenguaje de definición de descriptores de seguridad (SDDL) especificada.</summary>
      <param name="sddlForm">Cadena SDDL a partir de la que se va a establecer el descriptor de seguridad.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetSecurityDescriptorSddlForm(System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>Establece las secciones especificadas del descriptor de seguridad para este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> a partir de la cadena de lenguaje de definición de descriptores de seguridad (SDDL) especificada.</summary>
      <param name="sddlForm">Cadena SDDL a partir de la que se va a establecer el descriptor de seguridad.</param>
      <param name="includeSections">Las secciones (reglas de acceso, reglas de auditoría, propietario, grupo primario) del descriptor de seguridad que se van a establecer.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.WriteLock">
      <summary>Bloquea este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> para el acceso de escritura.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.WriteUnlock">
      <summary>Desbloquea este objeto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> para el acceso de escritura.</summary>
    </member>
    <member name="T:System.Security.AccessControl.ObjectSecurity`1">
      <summary>Proporciona la capacidad de controlar el acceso a objetos sin la manipulación directa de las listas de control de acceso (ACL); también concede la capacidad para derechos de acceso de la conversión de tipo. </summary>
      <typeparam name="T">Derechos de acceso para el objeto.</typeparam>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType)">
      <summary>Inicializa una nueva instancia de la clase ObjectSecurity’ 1.</summary>
      <param name="isContainer">Es true si el nuevo objeto <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> es un objeto contenedor.</param>
      <param name="resourceType">Tipo de recurso.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections)">
      <summary>Inicializa una nueva instancia de la clase ObjectSecurity’ 1.</summary>
      <param name="isContainer">Es true si el nuevo objeto <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> es un objeto contenedor.</param>
      <param name="resourceType">Tipo de recurso.</param>
      <param name="safeHandle">Un identificador.</param>
      <param name="includeSections">Secciones que se van a incluir.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>Inicializa una nueva instancia de la clase ObjectSecurity’ 1.</summary>
      <param name="isContainer">Es true si el nuevo objeto <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> es un objeto contenedor.</param>
      <param name="resourceType">Tipo de recurso.</param>
      <param name="safeHandle">Un identificador.</param>
      <param name="includeSections">Secciones que se van a incluir.</param>
      <param name="exceptionFromErrorCode">Delegado implementado por integradores que proporciona las excepciones personalizadas.</param>
      <param name="exceptionContext">Objeto que contiene información contextual sobre el origen o el destino de la excepción.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>Inicializa una nueva instancia de la clase ObjectSecurity’ 1.</summary>
      <param name="isContainer">Es true si el nuevo objeto <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> es un objeto contenedor.</param>
      <param name="resourceType">Tipo de recurso.</param>
      <param name="name">El nombre del objeto que se puede proteger al que se asocia el nuevo objeto <see cref="T:System.Security.AccessControl.ObjectSecurity`1" />.</param>
      <param name="includeSections">Secciones que se van a incluir.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.String,System.Security.AccessControl.AccessControlSections,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>Inicializa una nueva instancia de la clase ObjectSecurity’ 1.</summary>
      <param name="isContainer">Es true si el nuevo objeto <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> es un objeto contenedor.</param>
      <param name="resourceType">Tipo de recurso.</param>
      <param name="name">El nombre del objeto que se puede proteger al que se asocia el nuevo objeto <see cref="T:System.Security.AccessControl.ObjectSecurity`1" />.</param>
      <param name="includeSections">Secciones que se van a incluir. </param>
      <param name="exceptionFromErrorCode">Delegado implementado por integradores que proporciona las excepciones personalizadas.</param>
      <param name="exceptionContext">Objeto que contiene información contextual sobre el origen o el destino de la excepción.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity`1.AccessRightType">
      <summary>Obtiene el tipo del objeto protegible asociado a este objeto ObjectSecurity ’1.</summary>
      <returns>El tipo del objeto protegible asociado con las reglas de auditoría de la instancia actual.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.AccessRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>Inicializa una nueva instancia de la clase ObjectAccessRule que representa una nueva regla de control de acceso para el objeto de seguridad asociado.</summary>
      <returns>Representa una nueva regla de control de acceso para el usuario indicado con los derechos de acceso, el control de acceso y los marcadores especificados.</returns>
      <param name="identityReference">Representa una cuenta de usuario.</param>
      <param name="accessMask">Tipo de acceso.</param>
      <param name="isInherited">true si se hereda la regla de acceso; de lo contrario, false.</param>
      <param name="inheritanceFlags">Especifica cómo se propagan las máscaras de acceso a los objetos secundarios.</param>
      <param name="propagationFlags">Especifica la forma de propagar las entradas de control de acceso (ACE) a los objetos secundarios.</param>
      <param name="type">Especifica si el acceso se concede o se deniega.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity`1.AccessRuleType">
      <summary>Obtiene el tipo del objeto asociado a las reglas de acceso de este objeto ObjectSecurity ’1. </summary>
      <returns>El tipo del objeto que está asociado con las reglas de acceso de la instancia actual.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.AddAccessRule(System.Security.AccessControl.AccessRule{`0})">
      <summary>Agrega la regla de acceso especificada a la lista de control de acceso discrecional (DACL) asociada a este objeto ObjectSecurity`1.</summary>
      <param name="rule">La regla que se va a agregar.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.AddAuditRule(System.Security.AccessControl.AuditRule{`0})">
      <summary>Agrega la regla de auditoría especificada a la lista de control de acceso de sistema (SACL) asociada a este objeto ObjectSecurity`1.</summary>
      <param name="rule">La regla de auditoría que se va a agregar.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.AuditRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.AuditRule" /> que representa la regla de auditoría especificada para el usuario especificado.</summary>
      <returns>Devuelve la regla especificada de auditoría para el usuario especificado.</returns>
      <param name="identityReference">Representa una cuenta de usuario. </param>
      <param name="accessMask">Entero que especifica un tipo de acceso.</param>
      <param name="isInherited">true si se hereda la regla de acceso; de lo contrario, false.</param>
      <param name="inheritanceFlags">Especifica cómo se propagan las máscaras de acceso a los objetos secundarios.</param>
      <param name="propagationFlags">Especifica la forma de propagar las entradas de control de acceso (ACE) a los objetos secundarios.</param>
      <param name="flags">Describe el tipo de auditoría que se debe realizar.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity`1.AuditRuleType">
      <summary>Obtiene el tipo del objeto asociado a las reglas de auditoría de este objeto ObjectSecurity ’1.</summary>
      <returns>El objeto Type asociado con las reglas de auditoría de la instancia actual.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.Persist(System.Runtime.InteropServices.SafeHandle)">
      <summary>Guarda el descriptor de seguridad asociado a este objeto ObjectSecurity`1 en una ubicación de almacenamiento permanente, mediante el identificador especificado.</summary>
      <param name="handle">El identificador del objeto que se puede proteger al que se asocia este objeto ObjectSecurity`1.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.Persist(System.String)">
      <summary>Guarda el descriptor de seguridad asociado a este objeto ObjectSecurity`1 en una ubicación de almacenamiento permanente, mediante el nombre especificado.</summary>
      <param name="name">El nombre del objeto que se puede proteger al que se asocia este objeto ObjectSecurity`1.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAccessRule(System.Security.AccessControl.AccessRule{`0})">
      <summary>Quita las reglas de acceso que contienen el mismo identificador de seguridad y máscara de acceso que la regla de acceso especificada de la lista de control de acceso discrecional (DACL) asociada a este objeto ObjectSecurity`1.</summary>
      <returns>Devuelve true si la regla se quitó satisfactoriamente; en caso contrario, false.</returns>
      <param name="rule">La regla que se va a quitar.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAccessRuleAll(System.Security.AccessControl.AccessRule{`0})">
      <summary>Quita todas las reglas de acceso que tienen el mismo identificador de seguridad que la regla de acceso especificada de la lista de control de acceso discrecional (DACL) asociada a este objeto ObjectSecurity`1.</summary>
      <param name="rule">La regla de acceso que se va a quitar.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAccessRuleSpecific(System.Security.AccessControl.AccessRule{`0})">
      <summary>Quita todas las reglas de acceso que coinciden exactamente con la regla de acceso especificada de la lista de control de acceso discrecional (DACL) asociada a este objeto ObjectSecurity`1.</summary>
      <param name="rule">La regla de acceso que se va a quitar.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAuditRule(System.Security.AccessControl.AuditRule{`0})">
      <summary>Quita las reglas de auditoría que contienen el mismo identificador de seguridad y máscara de acceso que la regla de auditoría especificada de la lista de control de acceso de sistema (SACL) asociada a este objeto ObjectSecurity`1.</summary>
      <returns>Devuelve true si el objeto se quitó; de lo contrario, es false.</returns>
      <param name="rule">La regla de auditoría que se va a quitar</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAuditRuleAll(System.Security.AccessControl.AuditRule{`0})">
      <summary>Quita todas las reglas de auditoría que tienen el mismo identificador de seguridad que la regla de auditoría especificada de la lista de control de acceso de sistema (SACL) asociada a este objeto ObjectSecurity`1.</summary>
      <param name="rule">La regla de auditoría que se va a quitar.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAuditRuleSpecific(System.Security.AccessControl.AuditRule{`0})">
      <summary>Quita todas las reglas de auditoría que coinciden exactamente con la regla de auditoría especificada de la lista de control de acceso de sistema (SACL) asociada a este objeto ObjectSecurity`1.</summary>
      <param name="rule">La regla de auditoría que se va a quitar.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.ResetAccessRule(System.Security.AccessControl.AccessRule{`0})">
      <summary>Quita todas las reglas de acceso de la lista de control de acceso discrecional (DACL) asociada a este objeto ObjectSecurity`1 y, a continuación, agrega la regla de acceso especificada.</summary>
      <param name="rule">La regla de acceso que se va a restablecer.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.SetAccessRule(System.Security.AccessControl.AccessRule{`0})">
      <summary>Quita todas las reglas de acceso que contienen el mismo identificador de seguridad y calificador que la regla de acceso especificada en la lista de control de acceso discrecional (DACL) asociada a ese objeto ObjectSecurity`1 y, a continuación, agrega la regla de acceso especificada.</summary>
      <param name="rule">La regla de acceso que se va a establecer.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.SetAuditRule(System.Security.AccessControl.AuditRule{`0})">
      <summary>Quita todas las reglas de auditoría que contienen el mismo identificador de seguridad y calificador que la regla de auditoría especificada en la lista de control de acceso de sistema (SACL) asociada a ese objeto ObjectSecurity`1 y, a continuación, agrega la regla de auditoría especificada.</summary>
      <param name="rule">La regla de auditoría que se va a establecer.</param>
    </member>
    <member name="T:System.Security.AccessControl.PrivilegeNotHeldException">
      <summary>Excepción que se produce cuando un método del espacio de nombres <see cref="N:System.Security.AccessControl" /> intenta habilitar un privilegio que no tiene.</summary>
    </member>
    <member name="M:System.Security.AccessControl.PrivilegeNotHeldException.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.PrivilegeNotHeldException" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.PrivilegeNotHeldException.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.PrivilegeNotHeldException" /> utilizando el privilegio especificado.</summary>
      <param name="privilege">El privilegio que no está habilitado.</param>
    </member>
    <member name="M:System.Security.AccessControl.PrivilegeNotHeldException.#ctor(System.String,System.Exception)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.PrivilegeNotHeldException" /> utilizando la excepción especificada.</summary>
      <param name="privilege">El privilegio que no está habilitado.</param>
      <param name="inner">La excepción que es la causa de la excepción actual.Si el parámetro <paramref name="innerException" /> no es una referencia nula (Nothing en Visual Basic), la excepción actual se produce en un bloque catch que controla la excepción interna.</param>
    </member>
    <member name="P:System.Security.AccessControl.PrivilegeNotHeldException.PrivilegeName">
      <summary>Obtiene el nombre del privilegio que no está habilitado.</summary>
      <returns>El nombre del privilegio que el método no ha podido habilitar.</returns>
    </member>
    <member name="T:System.Security.AccessControl.PropagationFlags">
      <summary>Especifica la forma en que las entradas de control de acceso (ACE) se propagan a los objetos secundarios.  Estos marcadores sólo son significativos si hay marcadores de herencia. </summary>
    </member>
    <member name="F:System.Security.AccessControl.PropagationFlags.InheritOnly">
      <summary>Especifica que la ACE sólo se propaga a los objetos secundarios,donde se incluyen tanto el contenedor como los objetos hoja secundarios.</summary>
    </member>
    <member name="F:System.Security.AccessControl.PropagationFlags.None">
      <summary>Especifica que no se han establecido marcadores de herencia.</summary>
    </member>
    <member name="F:System.Security.AccessControl.PropagationFlags.NoPropagateInherit">
      <summary>Especifica que la ACE no se propaga a los objetos secundarios.</summary>
    </member>
    <member name="T:System.Security.AccessControl.QualifiedAce">
      <summary>Representa una entrada de control de acceso (ACE) que contiene un calificador.El calificador, representado por un objeto <see cref="T:System.Security.AccessControl.AceQualifier" />, especifica si la ACE concede o deniega el acceso, produce auditorías del sistema o alarmas del sistema.La clase <see cref="T:System.Security.AccessControl.QualifiedAce" /> es la clase base abstracta de las clases <see cref="T:System.Security.AccessControl.CommonAce" /> y <see cref="T:System.Security.AccessControl.ObjectAce" />.</summary>
    </member>
    <member name="P:System.Security.AccessControl.QualifiedAce.AceQualifier">
      <summary>Obtiene un valor que especifica si la ACE concede o deniega el acceso, o produce auditorías o alarmas del sistema.</summary>
      <returns>Valor que especifica si la ACE concede o deniega el acceso, o produce auditorías o alarmas del sistema.</returns>
    </member>
    <member name="M:System.Security.AccessControl.QualifiedAce.GetOpaque">
      <summary>Devuelve los datos de devolución de llamada opacos asociados a este objeto <see cref="T:System.Security.AccessControl.QualifiedAce" />. </summary>
      <returns>Matriz de valores de tipo byte que representa los datos de devolución de llamada opacos asociados a este objeto <see cref="T:System.Security.AccessControl.QualifiedAce" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.QualifiedAce.IsCallback">
      <summary>Especifica si este objeto <see cref="T:System.Security.AccessControl.QualifiedAce" /> contiene los datos de devolución de llamada.</summary>
      <returns>Es true si este objeto <see cref="T:System.Security.AccessControl.QualifiedAce" /> contiene los datos de devolución de llamada; de lo contrario, es false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.QualifiedAce.OpaqueLength">
      <summary>Obtiene la longitud de los datos de devolución de llamada opacos asociados al objeto <see cref="T:System.Security.AccessControl.QualifiedAce" />.Esta propiedad sólo es válida para entradas de control de acceso (ACE) de devolución de llamada.</summary>
      <returns>La longitud de los datos de devolución de llamada opacos.</returns>
    </member>
    <member name="M:System.Security.AccessControl.QualifiedAce.SetOpaque(System.Byte[])">
      <summary>Establece los datos de devolución de llamada opacos asociados a este objeto <see cref="T:System.Security.AccessControl.QualifiedAce" />.</summary>
      <param name="opaque">Matriz de valores de tipo byte que representa los datos de devolución de llamada opacos para este objeto <see cref="T:System.Security.AccessControl.QualifiedAce" />.</param>
    </member>
    <member name="T:System.Security.AccessControl.RawAcl">
      <summary>Representa una lista de control de acceso (ACL).</summary>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.#ctor(System.Byte,System.Int32)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.RawAcl" /> con el nivel de revisión especificado.</summary>
      <param name="revision">El nivel de revisión de la nueva lista de control de acceso (ACL).</param>
      <param name="capacity">Número de entradas de control de acceso (ACE) que este objeto <see cref="T:System.Security.AccessControl.RawAcl" /> puede contener.Este número solamente será utilizado como una sugerencia.</param>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.#ctor(System.Byte[],System.Int32)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.RawAcl" /> a partir del tipo binario especificado.</summary>
      <param name="binaryForm">Una matriz de valores de tipo byte que representa una lista de control de acceso (ACL).</param>
      <param name="offset">El desplazamiento del parámetro <paramref name="binaryForm" /> desde donde se empiezan a resolver los datos.</param>
    </member>
    <member name="P:System.Security.AccessControl.RawAcl.BinaryLength">
      <summary>Obtiene la longitud, en bytes, de la representación binaria del objeto <see cref="T:System.Security.AccessControl.RawAcl" /> actual.Debe utilizarse este valor de longitud antes de hacer el cálculo de referencias entre la lista ACL y una matriz binaria utilizando el método <see cref="M:System.Security.AccessControl.RawAcl.GetBinaryForm" />.</summary>
      <returns>La longitud, en bytes, de la representación binaria del objeto <see cref="T:System.Security.AccessControl.RawAcl" /> actual.</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawAcl.Count">
      <summary>Obtiene el número de entradas de control de acceso (ACE) del objeto <see cref="T:System.Security.AccessControl.RawAcl" /> actual.</summary>
      <returns>El número de ACE del objeto <see cref="T:System.Security.AccessControl.RawAcl" /> actual.</returns>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Realiza el cálculo de referencias entre el contenido del objeto <see cref="T:System.Security.AccessControl.RawAcl" /> y la matriz de bytes especificada a partir de la posición de desplazamiento indicada.</summary>
      <param name="binaryForm">La matriz de bytes para la cual se va a realizar el cálculo de referencias del contenido del objeto <see cref="T:System.Security.AccessControl.RawAcl" />.</param>
      <param name="offset">El desplazamiento desde el cual comenzará el cálculo de referencias.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> es negativo o un valor demasiado alto para que se pueda copiar todo el contenido de <see cref="T:System.Security.AccessControl.RawAcl" /> en <paramref name="array" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.InsertAce(System.Int32,System.Security.AccessControl.GenericAce)">
      <summary>Inserta la entrada de control de acceso indicada (ACE) en el índice especificado.</summary>
      <param name="index">Posición en la que va a agregarse la nueva ACE.Especifique el valor de la propiedad <see cref="P:System.Security.AccessControl.RawAcl.Count" /> para insertar una ACE al final del objeto <see cref="T:System.Security.AccessControl.RawAcl" />.</param>
      <param name="ace">La ACE que se va a insertar.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> es negativo o un valor demasiado alto para que se pueda copiar todo el contenido de <see cref="T:System.Security.AccessControl.GenericAcl" /> en <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Security.AccessControl.RawAcl.Item(System.Int32)">
      <summary>Obtiene o establece la entrada de control de acceso (ACE) en el índice especificado.</summary>
      <returns>La ACE que se encuentra en el índice especificado.</returns>
      <param name="index">Índice de base cero de la ACE que se va a obtener o establecer.</param>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.RemoveAce(System.Int32)">
      <summary>Quita la entrada de control de acceso (ACE) en la ubicación especificada.</summary>
      <param name="index">Índice de base cero de la ACE que se va a quitar.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">El valor del parámetro <paramref name="index" /> es más alto que el valor de la propiedad <see cref="P:System.Security.AccessControl.RawAcl.Count" /> menos uno o es negativo.</exception>
    </member>
    <member name="P:System.Security.AccessControl.RawAcl.Revision">
      <summary>Obtiene el nivel de revisión del objeto <see cref="T:System.Security.AccessControl.RawAcl" />.</summary>
      <returns>Valor de tipo byte que indica el nivel de revisión del objeto <see cref="T:System.Security.AccessControl.RawAcl" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.RawSecurityDescriptor">
      <summary>Representa un descriptor de seguridad.Un descriptor de seguridad incluye un propietario, un grupo primario, una lista de control de acceso discrecional (DACL) y una lista de control de acceso de sistema (SACL).</summary>
    </member>
    <member name="M:System.Security.AccessControl.RawSecurityDescriptor.#ctor(System.Byte[],System.Int32)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> a partir de la matriz de valores de tipo byte especificada.</summary>
      <param name="binaryForm">La matriz de valores de tipo byte a partir de la que se va a crear el nuevo objeto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</param>
      <param name="offset">El desplazamiento en la matriz <paramref name="binaryForm" /> desde donde se empieza a copiar.</param>
    </member>
    <member name="M:System.Security.AccessControl.RawSecurityDescriptor.#ctor(System.Security.AccessControl.ControlFlags,System.Security.Principal.SecurityIdentifier,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.RawAcl,System.Security.AccessControl.RawAcl)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> con los valores especificados.</summary>
      <param name="flags">Marcadores que especifican el comportamiento del nuevo objeto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</param>
      <param name="owner">El propietario del nuevo objeto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</param>
      <param name="group">Objeto primario para el nuevo objeto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</param>
      <param name="systemAcl">Lista de control de acceso de sistema (SACL) para el nuevo objeto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</param>
      <param name="discretionaryAcl">Lista de control de acceso discrecional (DACL) para el nuevo objeto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</param>
    </member>
    <member name="M:System.Security.AccessControl.RawSecurityDescriptor.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> a partir de la cadena de lenguaje de definición de descriptores de seguridad (SDDL) especificada.</summary>
      <param name="sddlForm">Cadena SDDL a partir de la cual se va a crear el nuevo objeto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</param>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.ControlFlags">
      <summary>Obtiene valores que especifican el comportamiento del objeto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</summary>
      <returns>Uno o más valores de la enumeración <see cref="T:System.Security.AccessControl.ControlFlags" /> combinados con la operación OR lógica.</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.DiscretionaryAcl">
      <summary>Obtiene o establece la lista de control de acceso discrecional (DACL) para este objeto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.La DACL contiene las reglas de acceso.</summary>
      <returns>DACL para este objeto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.Group">
      <summary>Obtiene o establece el grupo primario para este objeto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</summary>
      <returns>Grupo primario para este objeto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.Owner">
      <summary>Obtiene o establece el propietario del objeto asociado a este objeto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</summary>
      <returns>El propietario del objeto asociado a este objeto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.ResourceManagerControl">
      <summary>Obtiene o establece un valor de tipo byte que representa los bits de control del administrador de recursos asociado a este objeto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</summary>
      <returns>Un valor de tipo byte que representa los bits de control del administrador de recursos asociado a este objeto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.RawSecurityDescriptor.SetFlags(System.Security.AccessControl.ControlFlags)">
      <summary>Establece la propiedad <see cref="P:System.Security.AccessControl.RawSecurityDescriptor.ControlFlags" /> de este objeto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> en el valor especificado.</summary>
      <param name="flags">Uno o más valores de la enumeración <see cref="T:System.Security.AccessControl.ControlFlags" /> combinados con la operación OR lógica.</param>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.SystemAcl">
      <summary>Obtiene o establece la lista de control de acceso de sistema (SACL) para este objeto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.La SACL contiene reglas de auditoría.</summary>
      <returns>SACL para este objeto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.ResourceType">
      <summary>Especifica los tipos de objeto nativos definidos.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.DSObject">
      <summary>Objeto de servicio de directorio (DS, Directory Service) o conjunto de propiedades o propiedad de un objeto de servicio de directorio.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.DSObjectAll">
      <summary>Objeto de servicio de directorio y todos sus conjuntos de propiedades y propiedades.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.FileObject">
      <summary>Archivo o directorio.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.KernelObject">
      <summary>Objeto del kernel local.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.LMShare">
      <summary>Recurso compartido de red.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.Printer">
      <summary>Impresora.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.ProviderDefined">
      <summary>Objeto definido por un proveedor.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.RegistryKey">
      <summary>Clave del Registro.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.RegistryWow6432Key">
      <summary>Objeto para una entrada del Registro bajo WOW64.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.Service">
      <summary>Servicio de Windows.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.Unknown">
      <summary>Tipo de objeto desconocido.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.WindowObject">
      <summary>Estación de ventana u objeto de escritorio en el equipo local.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.WmiGuidObject">
      <summary>Objeto de Instrumental de administración de Windows (WMI).</summary>
    </member>
    <member name="T:System.Security.AccessControl.SecurityInfos">
      <summary>Especifica la sección de un descriptor de seguridad que se va a consultar o establecer.</summary>
    </member>
    <member name="F:System.Security.AccessControl.SecurityInfos.DiscretionaryAcl">
      <summary>Especifica la lista de control de acceso discrecional (DACL, Discretionary Access Control List).</summary>
    </member>
    <member name="F:System.Security.AccessControl.SecurityInfos.Group">
      <summary>Especifica el identificador de grupo principal.</summary>
    </member>
    <member name="F:System.Security.AccessControl.SecurityInfos.Owner">
      <summary>Especifica el identificador del propietario.</summary>
    </member>
    <member name="F:System.Security.AccessControl.SecurityInfos.SystemAcl">
      <summary>Especifica la lista de control de acceso al sistema (SACL, System Access Control List).</summary>
    </member>
    <member name="T:System.Security.AccessControl.SystemAcl">
      <summary>Representa una lista de control de acceso del sistema (SACL).</summary>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.#ctor(System.Boolean,System.Boolean,System.Byte,System.Int32)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.SystemAcl" /> con los valores especificados.</summary>
      <param name="isContainer">true si el nuevo objeto <see cref="T:System.Security.AccessControl.SystemAcl" /> es un contenedor.</param>
      <param name="isDS">true si el nuevo objeto <see cref="T:System.Security.AccessControl.SystemAcl" /> es una lista de control de acceso (ACL) de objetos de directorio.</param>
      <param name="revision">Nivel de revisión del nuevo objeto <see cref="T:System.Security.AccessControl.SystemAcl" />.</param>
      <param name="capacity">Número de entradas de control de acceso (ACE) que este objeto <see cref="T:System.Security.AccessControl.SystemAcl" /> puede contener.Este número solamente se usará como una sugerencia.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.#ctor(System.Boolean,System.Boolean,System.Int32)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.SystemAcl" /> con los valores especificados.</summary>
      <param name="isContainer">true si el nuevo objeto <see cref="T:System.Security.AccessControl.SystemAcl" /> es un contenedor.</param>
      <param name="isDS">true si el nuevo objeto <see cref="T:System.Security.AccessControl.SystemAcl" /> es una lista de control de acceso (ACL) de objetos de directorio.</param>
      <param name="capacity">Número de entradas de control de acceso (ACE) que este objeto <see cref="T:System.Security.AccessControl.SystemAcl" /> puede contener.Este número solamente se usará como una sugerencia.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.#ctor(System.Boolean,System.Boolean,System.Security.AccessControl.RawAcl)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.AccessControl.SystemAcl" /> con los valores indicados del objeto <see cref="T:System.Security.AccessControl.RawAcl" /> especificado.</summary>
      <param name="isContainer">true si el nuevo objeto <see cref="T:System.Security.AccessControl.SystemAcl" /> es un contenedor.</param>
      <param name="isDS">true si el nuevo objeto <see cref="T:System.Security.AccessControl.SystemAcl" /> es una lista de control de acceso (ACL) de objetos de directorio.</param>
      <param name="rawAcl">Objeto <see cref="T:System.Security.AccessControl.RawAcl" /> subyacente para el nuevo objeto <see cref="T:System.Security.AccessControl.SystemAcl" />.Especifique null para crear una ACL vacía.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.AddAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Agrega una regla de auditoría al objeto <see cref="T:System.Security.AccessControl.SystemAcl" /> actual.</summary>
      <param name="auditFlags">Tipo de regla de auditoría que se va a agregar.</param>
      <param name="sid">Identificador <see cref="T:System.Security.Principal.SecurityIdentifier" /> para el que se va a agregar una regla de auditoría.</param>
      <param name="accessMask">Máscara de acceso para la nueva regla de auditoría.</param>
      <param name="inheritanceFlags">Marcas que especifican las propiedades de herencia de la nueva regla de auditoría.</param>
      <param name="propagationFlags">Marcas que especifican las propiedades de propagación de herencia para la nueva regla de auditoría.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.AddAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Agrega una regla de auditoría con la configuración especificada al objeto <see cref="T:System.Security.AccessControl.SystemAcl" /> actual.Use este método para las listas de control de acceso (ACL) de objetos de directorio cuando especifique el tipo de objeto o el tipo de objeto heredado para la nueva regla de auditoría.</summary>
      <param name="auditFlags">Tipo de regla de auditoría que se va a agregar.</param>
      <param name="sid">Identificador <see cref="T:System.Security.Principal.SecurityIdentifier" /> para el que se va a agregar una regla de auditoría.</param>
      <param name="accessMask">Máscara de acceso para la nueva regla de auditoría.</param>
      <param name="inheritanceFlags">Marcas que especifican las propiedades de herencia de la nueva regla de auditoría.</param>
      <param name="propagationFlags">Marcas que especifican las propiedades de propagación de herencia para la nueva regla de auditoría.</param>
      <param name="objectFlags">Marcadores que especifican si los parámetros <paramref name="objectType" /> e <paramref name="inheritedObjectType" /> contienen valores distintos de null.</param>
      <param name="objectType">La identidad de la clase de objetos a los que se aplica la nueva regla de auditoría.</param>
      <param name="inheritedObjectType">La identidad de la clase de objetos secundarios que pueden heredar la nueva regla de auditoría.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.AddAudit(System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAuditRule)">
      <summary>Agrega una regla de auditoría al objeto <see cref="T:System.Security.AccessControl.SystemAcl" /> actual.</summary>
      <param name="sid">Identificador <see cref="T:System.Security.Principal.SecurityIdentifier" /> para el que se va a agregar una regla de auditoría.</param>
      <param name="rule">El <see cref="T:System.Security.AccessControl.ObjectAuditRule" />para la nueva regla de auditoría.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Quita la regla de auditoría especificada del objeto <see cref="T:System.Security.AccessControl.SystemAcl" /> actual.</summary>
      <returns>Es true si este método quita correctamente la regla de auditoría especificada; de lo contrario, es false.</returns>
      <param name="auditFlags">Tipo de regla de auditoría que se va a quitar.</param>
      <param name="sid">Identificador <see cref="T:System.Security.Principal.SecurityIdentifier" /> para el que se va a quitar una regla de auditoría.</param>
      <param name="accessMask">Máscara de acceso de la regla que se va a quitar.</param>
      <param name="inheritanceFlags">Marcas que especifican las propiedades de herencia para la regla que se va a quitar.</param>
      <param name="propagationFlags">Marcas que especifican las propiedades de propagación de herencia para la regla que se va a quitar.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Quita la regla de auditoría especificada del objeto <see cref="T:System.Security.AccessControl.SystemAcl" /> actual.Use este método para las listas de control de acceso (ACL) de objetos de directorio cuando especifique el tipo de objeto o el tipo de objeto heredado.</summary>
      <returns>Es true si este método quita correctamente la regla de auditoría especificada; de lo contrario, es false.</returns>
      <param name="auditFlags">Tipo de regla de auditoría que se va a quitar.</param>
      <param name="sid">Identificador <see cref="T:System.Security.Principal.SecurityIdentifier" /> para el que se va a quitar una regla de auditoría.</param>
      <param name="accessMask">Máscara de acceso de la regla que se va a quitar.</param>
      <param name="inheritanceFlags">Marcas que especifican las propiedades de herencia para la regla que se va a quitar.</param>
      <param name="propagationFlags">Marcas que especifican las propiedades de propagación de herencia para la regla que se va a quitar.</param>
      <param name="objectFlags">Marcadores que especifican si los parámetros <paramref name="objectType" /> e <paramref name="inheritedObjectType" /> contienen valores distintos de null.</param>
      <param name="objectType">Identidad de la clase de objetos a los que se aplica la regla de control de auditoría quitada.</param>
      <param name="inheritedObjectType">Identidad de la clase de objetos secundarios que pueden heredar la regla de auditoría quitada.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAudit(System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAuditRule)">
      <summary>Quita la regla de auditoría especificada del objeto <see cref="T:System.Security.AccessControl.SystemAcl" /> actual.</summary>
      <returns>Es true si este método quita correctamente la regla de auditoría especificada; de lo contrario, es false.</returns>
      <param name="sid">Identificador <see cref="T:System.Security.Principal.SecurityIdentifier" /> para el que se va a quitar una regla de auditoría.</param>
      <param name="rule">Identificador <see cref="T:System.Security.AccessControl.ObjectAuditRule" /> para el que se va a quitar una regla de auditoría.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAuditSpecific(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Quita la regla de auditoría especificada del objeto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> actual.</summary>
      <param name="auditFlags">Tipo de regla de auditoría que se va a quitar.</param>
      <param name="sid">Identificador <see cref="T:System.Security.Principal.SecurityIdentifier" /> para el que se va a quitar una regla de auditoría.</param>
      <param name="accessMask">Máscara de acceso de la regla que se va a quitar.</param>
      <param name="inheritanceFlags">Marcas que especifican las propiedades de herencia para la regla que se va a quitar.</param>
      <param name="propagationFlags">Marcas que especifican las propiedades de propagación de herencia para la regla que se va a quitar.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAuditSpecific(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Quita la regla de auditoría especificada del objeto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> actual.Use este método para las listas de control de acceso (ACL) de objetos de directorio cuando especifique el tipo de objeto o el tipo de objeto heredado.</summary>
      <param name="auditFlags">Tipo de regla de auditoría que se va a quitar.</param>
      <param name="sid">Identificador <see cref="T:System.Security.Principal.SecurityIdentifier" /> para el que se va a quitar una regla de auditoría.</param>
      <param name="accessMask">Máscara de acceso de la regla que se va a quitar.</param>
      <param name="inheritanceFlags">Marcas que especifican las propiedades de herencia para la regla que se va a quitar.</param>
      <param name="propagationFlags">Marcas que especifican las propiedades de propagación de herencia para la regla que se va a quitar.</param>
      <param name="objectFlags">Marcadores que especifican si los parámetros <paramref name="objectType" /> e <paramref name="inheritedObjectType" /> contienen valores distintos de null.</param>
      <param name="objectType">Identidad de la clase de objetos a los que se aplica la regla de control de auditoría quitada.</param>
      <param name="inheritedObjectType">Identidad de la clase de objetos secundarios que pueden heredar la regla de auditoría quitada.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAuditSpecific(System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAuditRule)">
      <summary>Quita la regla de auditoría especificada del objeto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> actual.</summary>
      <param name="sid">Identificador <see cref="T:System.Security.Principal.SecurityIdentifier" /> para el que se va a quitar una regla de auditoría.</param>
      <param name="rule">El <see cref="T:System.Security.AccessControl.ObjectAuditRule" /> de la regla que se va a quitar.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.SetAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Establece la regla de auditoría indicada para el objeto <see cref="T:System.Security.Principal.SecurityIdentifier" /> especificado.</summary>
      <param name="auditFlags">Condición de auditoría que se va a establecer.</param>
      <param name="sid">Identificador <see cref="T:System.Security.Principal.SecurityIdentifier" /> para el que se va a establecer una regla de auditoría.</param>
      <param name="accessMask">Máscara de acceso para la nueva regla de auditoría.</param>
      <param name="inheritanceFlags">Marcas que especifican las propiedades de herencia de la nueva regla de auditoría.</param>
      <param name="propagationFlags">Marcas que especifican las propiedades de propagación de herencia para la nueva regla de auditoría.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.SetAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Establece la regla de auditoría indicada para el objeto <see cref="T:System.Security.Principal.SecurityIdentifier" /> especificado.Use este método para las listas de control de acceso (ACL) de objetos de directorio cuando especifique el tipo de objeto o el tipo de objeto heredado.</summary>
      <param name="auditFlags">Condición de auditoría que se va a establecer.</param>
      <param name="sid">Identificador <see cref="T:System.Security.Principal.SecurityIdentifier" /> para el que se va a establecer una regla de auditoría.</param>
      <param name="accessMask">Máscara de acceso para la nueva regla de auditoría.</param>
      <param name="inheritanceFlags">Marcas que especifican las propiedades de herencia de la nueva regla de auditoría.</param>
      <param name="propagationFlags">Marcas que especifican las propiedades de propagación de herencia para la nueva regla de auditoría.</param>
      <param name="objectFlags">Marcadores que especifican si los parámetros <paramref name="objectType" /> e <paramref name="inheritedObjectType" /> contienen valores distintos de null.</param>
      <param name="objectType">La identidad de la clase de objetos a los que se aplica la nueva regla de auditoría.</param>
      <param name="inheritedObjectType">La identidad de la clase de objetos secundarios que pueden heredar la nueva regla de auditoría.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.SetAudit(System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAuditRule)">
      <summary>Establece la regla de auditoría indicada para el objeto <see cref="T:System.Security.Principal.SecurityIdentifier" /> especificado.</summary>
      <param name="sid">Identificador <see cref="T:System.Security.Principal.SecurityIdentifier" /> para el que se va a establecer una regla de auditoría.</param>
      <param name="rule">Identificador <see cref="T:System.Security.AccessControl.ObjectAuditRule" /> para el que se va a establecer una regla de auditoría.</param>
    </member>
  </members>
</doc>