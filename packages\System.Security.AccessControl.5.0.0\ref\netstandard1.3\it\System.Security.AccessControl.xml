﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Security.AccessControl</name>
  </assembly>
  <members>
    <member name="T:System.Security.AccessControl.AccessControlActions">
      <summary>Specifica le azioni consentite per gli oggetti che possono essere protetti.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlActions.Change">
      <summary>Specifica l'accesso di sola scrittura.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlActions.None">
      <summary>Non specifica alcun accesso.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlActions.View">
      <summary>Specifica l'accesso di sola lettura.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AccessControlModification">
      <summary>Specifica il tipo di modifica del controllo di accesso da eseguire.Questa enumerazione viene utilizzata dai metodi della classe <see cref="T:System.Security.AccessControl.ObjectSecurity" /> e dai relativi discendenti.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.Add">
      <summary>Aggiunge la regola di autorizzazione specificata all'elenco di controllo di accesso (ACL).</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.Remove">
      <summary>Rimuove le regole di autorizzazione che contengono lo stesso identificatore di sicurezza (SID) e la maschera di accesso come regola di autorizzazione specificata dall'ACL.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.RemoveAll">
      <summary>Rimuove le regole di autorizzazione che contengono lo stesso SID come regola di autorizzazione specificata dall'ACL.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.RemoveSpecific">
      <summary>Rimuove le regole di autorizzazione che corrispondono esattamente alla regola di autorizzazione specificata dall'ACL.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.Reset">
      <summary>Rimuove le regole di autorizzazione che contengono lo stesso SID della regola di autorizzazione specificata dall'ACL e quindi aggiunge la regola di autorizzazione specificata all'ACL.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.Set">
      <summary>Rimuove tutte le regole di autorizzazione dall'ACL, quindi aggiunge la regola di autorizzazione specificata all'ACL.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AccessControlSections">
      <summary>Specifica le sezione di un descrittore di sicurezza da salvare o caricare.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.Access">
      <summary>Elenco di controllo di accesso discrezionale (DACL).</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.All">
      <summary>Intero descrittore di sicurezza.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.Audit">
      <summary>Elenco di controllo di accesso di sistema (SACL).</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.Group">
      <summary>Gruppo primario.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.None">
      <summary>Nessuna sezione.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.Owner">
      <summary>Proprietario.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AccessControlType">
      <summary>Specifica se un oggetto <see cref="T:System.Security.AccessControl.AccessRule" /> viene utilizzato per consentire o negare l'accesso.Questi valori non sono flag e non possono essere combinati.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlType.Allow">
      <summary>L'oggetto <see cref="T:System.Security.AccessControl.AccessRule" /> viene utilizzato per consentire l'accesso a un oggetto protetto.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlType.Deny">
      <summary>L'oggetto <see cref="T:System.Security.AccessControl.AccessRule" /> viene utilizzato per negare l'accesso a un oggetto protetto.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AccessRule">
      <summary>Rappresenta una combinazione di identità di un utente, maschera di accesso e tipo di controllo di accesso (consenso o negazione).Un oggetto <see cref="T:System.Security.AccessControl.AccessRule" /> contiene inoltre informazioni su come la regola viene ereditata dagli oggetti figlio e come viene propagata l'ereditarietà.</summary>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.AccessRule" /> utilizzando i valori specificati.</summary>
      <param name="identity">Identità a cui viene applicata la regola di accesso.Questo parametro deve essere un oggetto di cui è possibile eseguire il cast in <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="accessMask">Maschera di accesso della regola.La maschera di accesso è un insieme di bit anonimi a 32 bit, il cui significato è definito dai singoli integratori.</param>
      <param name="isInherited">true se la regola è ereditata da un contenitore padre.</param>
      <param name="inheritanceFlags">Proprietà di ereditarietà della regola di accesso.</param>
      <param name="propagationFlags">Specifica se le regole di accesso ereditate vengono propagate automaticamente.I flag di propagazione vengono ignorati se <paramref name="inheritanceFlags" /> è impostato su <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="type">Tipo di controllo di accesso valido.</param>
      <exception cref="T:System.ArgumentException">Non è possibile effettuare il cast del valore del parametro <paramref name="identity" /> in <see cref="T:System.Security.Principal.SecurityIdentifier" /> oppure il parametro <paramref name="type" /> contiene un valore non valido.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Il valore del parametro <paramref name="accessMask" /> è zero oppure i parametri <paramref name="inheritanceFlags" /> o <paramref name="propagationFlags" /> contengono valori di flag non riconosciuti.</exception>
    </member>
    <member name="P:System.Security.AccessControl.AccessRule.AccessControlType">
      <summary>Ottiene il valore <see cref="T:System.Security.AccessControl.AccessControlType" /> associato all'oggetto <see cref="T:System.Security.AccessControl.AccessRule" />.</summary>
      <returns>Valore <see cref="T:System.Security.AccessControl.AccessControlType" /> associato all'oggetto <see cref="T:System.Security.AccessControl.AccessRule" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.AccessRule`1">
      <summary>Rappresenta una combinazione di identità di un utente, maschera di accesso e tipo di controllo di accesso (consenso o negazione).Un oggetto AccessRule`1 contiene inoltre informazioni su come la regola viene ereditata dagli oggetti figlio e come viene propagata l'ereditarietà.</summary>
      <typeparam name="T">Tipo di diritti di accesso per la regola di accesso.</typeparam>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule`1.#ctor(System.Security.Principal.IdentityReference,`0,System.Security.AccessControl.AccessControlType)">
      <summary>Inizializza una nuova istanza della classe AccessRule’1 utilizzando i valori specificati.</summary>
      <param name="identity">Identità a cui viene applicata la regola di accesso.</param>
      <param name="rights">Diritti della regola di accesso.</param>
      <param name="type">Tipo di controllo di accesso valido.</param>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule`1.#ctor(System.Security.Principal.IdentityReference,`0,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>Inizializza una nuova istanza della classe AccessRule’1 utilizzando i valori specificati.</summary>
      <param name="identity">Identità a cui viene applicata la regola di accesso.</param>
      <param name="rights">Diritti della regola di accesso.</param>
      <param name="inheritanceFlags">Proprietà di ereditarietà della regola di accesso.</param>
      <param name="propagationFlags">Specifica se le regole di accesso ereditate vengono propagate automaticamente.I flag di propagazione vengono ignorati se <paramref name="inheritanceFlags" /> è impostato su <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="type">Tipo di controllo di accesso valido.</param>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule`1.#ctor(System.String,`0,System.Security.AccessControl.AccessControlType)">
      <summary>Inizializza una nuova istanza della classe AccessRule’1 utilizzando i valori specificati.</summary>
      <param name="identity">Identità a cui viene applicata la regola di accesso.</param>
      <param name="rights">Diritti della regola di accesso.</param>
      <param name="type">Tipo di controllo di accesso valido.</param>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule`1.#ctor(System.String,`0,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>Inizializza una nuova istanza della classe AccessRule’1 utilizzando i valori specificati.</summary>
      <param name="identity">Identità a cui viene applicata la regola di accesso.</param>
      <param name="rights">Diritti della regola di accesso.</param>
      <param name="inheritanceFlags">Proprietà di ereditarietà della regola di accesso.</param>
      <param name="propagationFlags">Specifica se le regole di accesso ereditate vengono propagate automaticamente.I flag di propagazione vengono ignorati se <paramref name="inheritanceFlags" /> è impostato su <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="type">Tipo di controllo di accesso valido.</param>
    </member>
    <member name="P:System.Security.AccessControl.AccessRule`1.Rights">
      <summary>Ottiene i diritti dell'istanza corrente.</summary>
      <returns>Diritti, cast come tipo &lt;T&gt;, dell'istanza corrente.</returns>
    </member>
    <member name="T:System.Security.AccessControl.AceEnumerator">
      <summary>Consente di scorrere le voci di controllo di accesso (ACE, Access Control Entry) in un elenco di controllo di accesso (ACL; Access Control List). </summary>
    </member>
    <member name="P:System.Security.AccessControl.AceEnumerator.Current">
      <summary>Ottiene l'elemento corrente dell'insieme <see cref="T:System.Security.AccessControl.GenericAce" />.Questa proprietà consente di ottenere la versione descrittiva del tipo dell'oggetto.</summary>
      <returns>Elemento corrente nell'insieme <see cref="T:System.Security.AccessControl.GenericAce" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.AceEnumerator.MoveNext">
      <summary>Sposta l'enumeratore all'elemento successivo della raccolta <see cref="T:System.Security.AccessControl.GenericAce" />.</summary>
      <returns>true se l'enumeratore ha completato il passaggio all'elemento successivo; false se l'enumeratore ha raggiunto la fine della raccolta.</returns>
      <exception cref="T:System.InvalidOperationException">La raccolta è stata modificata dopo la creazione dell'enumeratore.</exception>
    </member>
    <member name="M:System.Security.AccessControl.AceEnumerator.Reset">
      <summary>Imposta l'enumeratore sulla propria posizione iniziale, ovvero prima del primo elemento nell'insieme <see cref="T:System.Security.AccessControl.GenericAce" />.</summary>
      <exception cref="T:System.InvalidOperationException">La raccolta è stata modificata dopo la creazione dell'enumeratore.</exception>
    </member>
    <member name="P:System.Security.AccessControl.AceEnumerator.System#Collections#IEnumerator#Current"></member>
    <member name="T:System.Security.AccessControl.AceFlags">
      <summary>Specifica il comportamento di una voce di controllo di accesso (ACE, Access Control Entry) in relazione all'ereditarietà e ai controlli.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.AuditFlags">
      <summary>Vengono controllati tutti i tentativi di accesso.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.ContainerInherit">
      <summary>La maschera di accesso viene propagata agli oggetti contenitore figlio.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.FailedAccess">
      <summary>Vengono controllati i tentativi di accesso non riusciti.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.InheritanceFlags">
      <summary>Operatore OR logico di <see cref="F:System.Security.AccessControl.AceFlags.ObjectInherit" />, <see cref="F:System.Security.AccessControl.AceFlags.ContainerInherit" />, <see cref="F:System.Security.AccessControl.AceFlags.NoPropagateInherit" /> e <see cref="F:System.Security.AccessControl.AceFlags.InheritOnly" />.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.Inherited">
      <summary>Una voce ACE viene ereditata da un contenitore padre invece di essere impostata esplicitamente per un oggetto.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.InheritOnly">
      <summary>La maschera di accesso viene propagata solo agli oggetti figlio.Sono inclusi gli oggetti contenitore e gli oggetti figlio foglia.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.None">
      <summary>Nessun flag ACE impostato.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.NoPropagateInherit">
      <summary>I controlli di accesso non vengono applicati all'oggetto, ma solo ai corrispondenti oggetti figlio.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.ObjectInherit">
      <summary>La maschera di accesso viene propagata negli oggetti figlio foglia.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.SuccessfulAccess">
      <summary>Vengono controllati i tentativi di accesso riusciti.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AceQualifier">
      <summary>Specifica la funzione di una voce di controllo di accesso (ACE, Access Control Entry).</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceQualifier.AccessAllowed">
      <summary>Consente l'accesso.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceQualifier.AccessDenied">
      <summary>Nega l'accesso.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceQualifier.SystemAlarm">
      <summary>Attiva un avviso di sistema.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceQualifier.SystemAudit">
      <summary>Attiva un controllo di sistema.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AceType">
      <summary>Definisce i tipi di voci di controllo di accesso (ACE, Access Control Entry) disponibili.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowed">
      <summary>Consente l'accesso a un oggetto per uno specifico trustee identificato da un oggetto <see cref="T:System.Security.Principal.IdentityReference" />.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowedCallback">
      <summary>Consente l'accesso a un oggetto per uno specifico trustee identificato da un oggetto <see cref="T:System.Security.Principal.IdentityReference" />.Questo tipo di voce ACE può contenere dati di callback facoltativi.I dati di callback sono BLOB specifici del manager delle risorse che non sono interpretati.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowedCallbackObject">
      <summary>Consente l'accesso a un oggetto, a un insieme di proprietà o a una proprietà.La voce ACE contiene un insieme di diritti di accesso, un GUID che identifica il tipo di oggetto e un oggetto <see cref="T:System.Security.Principal.IdentityReference" /> che identifica il trustee a cui il sistema consentirà l'accesso.La voce ACE contiene anche un GUID e un insieme di flag che controllano l'ereditarietà della voce ACE da parte degli oggetti figlio.Questo tipo di voce ACE può contenere dati di callback facoltativi.I dati di callback sono BLOB specifici del manager delle risorse che non sono interpretati.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowedCompound">
      <summary>Definito ma mai utilizzato.Incluso per motivi di completezza.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowedObject">
      <summary>Consente l'accesso a un oggetto, a un insieme di proprietà o a una proprietà.La voce ACE contiene un insieme di diritti di accesso, un GUID che identifica il tipo di oggetto e un oggetto <see cref="T:System.Security.Principal.IdentityReference" /> che identifica il trustee a cui il sistema consentirà l'accesso.La voce ACE contiene anche un GUID e un insieme di flag che controllano l'ereditarietà della voce ACE da parte degli oggetti figlio.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessDenied">
      <summary>Nega l'accesso a un oggetto per uno specifico trustee identificato da un oggetto <see cref="T:System.Security.Principal.IdentityReference" />.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessDeniedCallback">
      <summary>Nega l'accesso a un oggetto per uno specifico trustee identificato da un oggetto <see cref="T:System.Security.Principal.IdentityReference" />.Questo tipo di voce ACE può contenere dati di callback facoltativi.I dati di callback sono BLOB specifici del manager delle risorse che non sono interpretati.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessDeniedCallbackObject">
      <summary>Nega l'accesso a un oggetto, a un insieme di proprietà o a una proprietà.La voce ACE contiene un insieme di diritti di accesso, un GUID che identifica il tipo di oggetto e un oggetto <see cref="T:System.Security.Principal.IdentityReference" /> che identifica il trustee a cui il sistema consentirà l'accesso.La voce ACE contiene anche un GUID e un insieme di flag che controllano l'ereditarietà della voce ACE da parte degli oggetti figlio.Questo tipo di voce ACE può contenere dati di callback facoltativi.I dati di callback sono BLOB specifici del manager delle risorse che non sono interpretati.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessDeniedObject">
      <summary>Nega l'accesso a un oggetto, a un insieme di proprietà o a una proprietà.La voce ACE contiene un insieme di diritti di accesso, un GUID che identifica il tipo di oggetto e un oggetto <see cref="T:System.Security.Principal.IdentityReference" /> che identifica il trustee a cui il sistema consentirà l'accesso.La voce ACE contiene anche un GUID e un insieme di flag che controllano l'ereditarietà della voce ACE da parte degli oggetti figlio.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.MaxDefinedAceType">
      <summary>Rileva il tipo di voce ACE maggiormente definito nell'enumerazione.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAlarm">
      <summary>Riservato per un utilizzo futuro.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAlarmCallback">
      <summary>Riservato per un utilizzo futuro.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAlarmCallbackObject">
      <summary>Riservato per un utilizzo futuro.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAlarmObject">
      <summary>Riservato per un utilizzo futuro.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAudit">
      <summary>Provoca la registrazione di un messaggio di controllo quando un trustee specificato tenta di ottenere l'accesso a un oggetto.Il trustee è identificato da un oggetto <see cref="T:System.Security.Principal.IdentityReference" />.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAuditCallback">
      <summary>Provoca la registrazione di un messaggio di controllo quando un trustee specificato tenta di ottenere l'accesso a un oggetto.Il trustee è identificato da un oggetto <see cref="T:System.Security.Principal.IdentityReference" />.Questo tipo di voce ACE può contenere dati di callback facoltativi.I dati di callback sono BLOB specifici del manager delle risorse che non sono interpretati.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAuditCallbackObject">
      <summary>Provoca la registrazione di un messaggio di controllo quando un trustee specificato tenta di ottenere l'accesso a un oggetto o a oggetti secondari, quali insiemi di proprietà o proprietà.La voce ACE contiene un insieme di diritti di accesso, un GUID che identifica il tipo di oggetto od oggetto secondario e un oggetto <see cref="T:System.Security.Principal.IdentityReference" /> che identifica il trustee per il quale il sistema controllerà l'accesso.La voce ACE contiene anche un GUID e un insieme di flag che controllano l'ereditarietà della voce ACE da parte degli oggetti figlio.Questo tipo di voce ACE può contenere dati di callback facoltativi.I dati di callback sono BLOB specifici del manager delle risorse che non sono interpretati.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAuditObject">
      <summary>Provoca la registrazione di un messaggio di controllo quando un trustee specificato tenta di ottenere l'accesso a un oggetto o a oggetti secondari, quali insiemi di proprietà o proprietà.La voce ACE contiene un insieme di diritti di accesso, un GUID che identifica il tipo di oggetto od oggetto secondario e un oggetto <see cref="T:System.Security.Principal.IdentityReference" /> che identifica il trustee per il quale il sistema controllerà l'accesso.La voce ACE contiene anche un GUID e un insieme di flag che controllano l'ereditarietà della voce ACE da parte degli oggetti figlio.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AuditFlags">
      <summary>Specifica le condizioni per il controllo dei tentativi di accesso a un oggetto che può essere protetto.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AuditFlags.Failure">
      <summary>Vengono controllati i tentativi di accesso non riusciti.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AuditFlags.None">
      <summary>Nessun tentativo di accesso viene controllato.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AuditFlags.Success">
      <summary>Vengono controllati i tentativi di accesso riusciti.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AuditRule">
      <summary>Rappresenta la combinazione dell'identità di un utente e di una maschera di accesso.Un oggetto <see cref="T:System.Security.AccessControl.AuditRule" /> contiene anche informazioni su come la regola viene ereditata dagli oggetti figlio, su come l'ereditarietà viene propagata e sulle condizioni per le quali viene eseguito il controllo.</summary>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.AuditRule" /> utilizzando i valori specificati.</summary>
      <param name="identity">Identità a cui viene applicata la regola di controllo.Deve essere un oggetto di cui è possibile eseguire il cast in <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="accessMask">Maschera di accesso della regola.La maschera di accesso è un insieme di bit anonimi a 32 bit, il cui significato è definito dai singoli integratori.</param>
      <param name="isInherited">true per ereditare la regola da un contenitore padre.</param>
      <param name="inheritanceFlags">Proprietà di ereditarietà della regola di controllo.</param>
      <param name="propagationFlags">Indica se le regole di controllo sono propagate automaticamente.I flag di propagazione vengono ignorati se <paramref name="inheritanceFlags" /> è impostato su <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="auditFlags">Condizioni per cui la regola viene controllata.</param>
      <exception cref="T:System.ArgumentException">Non è possibile eseguire il cast del valore del parametro <paramref name="identity" /> come <see cref="T:System.Security.Principal.SecurityIdentifier" /> oppure il parametro <paramref name="auditFlags" /> contiene un valore non valido.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Il valore del parametro <paramref name="accessMask" /> è zero oppure i parametri <paramref name="inheritanceFlags" /> o <paramref name="propagationFlags" /> contengono valori di flag non riconosciuti.</exception>
    </member>
    <member name="P:System.Security.AccessControl.AuditRule.AuditFlags">
      <summary>Ottiene i flag di controllo per la regola di controllo.</summary>
      <returns>Combinazione bit per bit dei valori dell'enumerazione.Questa combinazione specifica le condizioni di controllo per la regola di controllo.</returns>
    </member>
    <member name="T:System.Security.AccessControl.AuditRule`1">
      <summary>Rappresenta una combinazione dell'identità di un utente e di una maschera di accesso.</summary>
      <typeparam name="T"></typeparam>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule`1.#ctor(System.Security.Principal.IdentityReference,`0,System.Security.AccessControl.AuditFlags)">
      <summary>Inizializza una nuova istanza della classe AuditRule’1 utilizzando i valori specificati.</summary>
      <param name="identity">Identità a cui viene applicata questa regola di controllo.</param>
      <param name="rights">Diritti della regola di controllo.</param>
      <param name="flags">Condizioni per cui la regola viene controllata.</param>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule`1.#ctor(System.Security.Principal.IdentityReference,`0,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>Inizializza una nuova istanza della classe AuditRule’1 utilizzando i valori specificati.</summary>
      <param name="identity">Identità a cui viene applicata la regola di controllo. </param>
      <param name="rights">Diritti della regola di controllo.</param>
      <param name="inheritanceFlags">Proprietà di ereditarietà della regola di controllo.</param>
      <param name="propagationFlags">Indica se le regole di controllo sono propagate automaticamente.</param>
      <param name="flags">Condizioni per cui la regola viene controllata.</param>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule`1.#ctor(System.String,`0,System.Security.AccessControl.AuditFlags)">
      <summary>Inizializza una nuova istanza della classe AuditRule’1 utilizzando i valori specificati.</summary>
      <param name="identity">Identità a cui viene applicata la regola di controllo.</param>
      <param name="rights">Diritti della regola di controllo.</param>
      <param name="flags">Proprietà della regola di controllo.</param>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule`1.#ctor(System.String,`0,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>Inizializza una nuova istanza della classe AuditRule’1 utilizzando i valori specificati.</summary>
      <param name="identity">Identità a cui viene applicata la regola di controllo.</param>
      <param name="rights">Diritti della regola di controllo.</param>
      <param name="inheritanceFlags">Proprietà di ereditarietà della regola di controllo.</param>
      <param name="propagationFlags">Indica se le regole di controllo sono propagate automaticamente.</param>
      <param name="flags">Condizioni per cui la regola viene controllata.</param>
    </member>
    <member name="P:System.Security.AccessControl.AuditRule`1.Rights">
      <summary>Diritti della regola di controllo.</summary>
      <returns>Restituisce <see cref="{0}" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.AuthorizationRule">
      <summary>Determina l'accesso agli oggetti da proteggere.Le classi derivate <see cref="T:System.Security.AccessControl.AccessRule" /> e <see cref="T:System.Security.AccessControl.AuditRule" /> offrono funzionalità speciali per l'accesso e il controllo.</summary>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AuthorizationControl.AccessRule" /> utilizzando i valori specificati.</summary>
      <param name="identity">Identità a cui viene applicata la regola di accesso.  Questo parametro deve essere un oggetto di cui è possibile eseguire il cast in <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="accessMask">Maschera di accesso della regola.La maschera di accesso è un insieme di bit anonimi a 32 bit, il cui significato è definito dai singoli integratori.</param>
      <param name="isInherited">true per ereditare la regola da un contenitore padre.</param>
      <param name="inheritanceFlags">Proprietà di ereditarietà della regola di accesso.</param>
      <param name="propagationFlags">Specifica se le regole di accesso ereditate vengono propagate automaticamente.I flag di propagazione vengono ignorati se <paramref name="inheritanceFlags" /> è impostato su <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <exception cref="T:System.ArgumentException">Non è possibile sottoporre a cast il valore del parametro <paramref name="identity" /> come classe <see cref="T:System.Security.Principal.SecurityIdentifier" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Il valore del parametro <paramref name="accessMask" /> è zero oppure i parametri <paramref name="inheritanceFlags" /> o <paramref name="propagationFlags" /> contengono valori di flag non riconosciuti.</exception>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.AccessMask">
      <summary>Ottiene la maschera di accesso per questa regola.</summary>
      <returns>Maschera di accesso per questa regola.</returns>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.IdentityReference">
      <summary>Ottiene la classe <see cref="T:System.Security.Principal.IdentityReference" /> a cui viene applicata la regola.</summary>
      <returns>Classe <see cref="T:System.Security.Principal.IdentityReference" /> a cui viene applicata la regola.</returns>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.InheritanceFlags">
      <summary>Ottiene il valore dei flag che determinano come la regola viene ereditata dagli oggetti figlio.</summary>
      <returns>Combinazione bit per bit dei valori dell'enumerazione.</returns>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.IsInherited">
      <summary>Ottiene un valore che indica se la regola viene impostata in modo esplicito oppure se è ereditata da un oggetto contenitore padre.</summary>
      <returns>true se la regola non è impostata in modo esplicito ma viene ereditata da un contenitore padre.</returns>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.PropagationFlags">
      <summary>Ottiene il valore dei flag di propagazione, che determinano come l'ereditarietà di questa regola viene propagata agli oggetti figlio.Questa proprietà è importante solo quando il valore dell'enumerazione <see cref="T:System.Security.AccessControl.InheritanceFlags" /> non è <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</summary>
      <returns>Combinazione bit per bit dei valori dell'enumerazione.</returns>
    </member>
    <member name="T:System.Security.AccessControl.AuthorizationRuleCollection">
      <summary>Rappresenta una raccolta di oggetti <see cref="T:System.Security.AccessControl.AuthorizationRule" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.AuthorizationRuleCollection" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.AddRule(System.Security.AccessControl.AuthorizationRule)">
      <summary>Aggiunge un oggetto <see cref="T:System.Web.Configuration.AuthorizationRule" /> alla raccolta.</summary>
      <param name="rule">Oggetto <see cref="T:System.Web.Configuration.AuthorizationRule" /> da aggiungere alla raccolta.</param>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.CopyTo(System.Security.AccessControl.AuthorizationRule[],System.Int32)">
      <summary>Copia il contenuto della raccolta in una matrice.</summary>
      <param name="rules">Matrice in cui copiare il contenuto della raccolta.</param>
      <param name="index">Indice in base zero da cui ha inizio la copia.</param>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRuleCollection.Count"></member>
    <member name="P:System.Security.AccessControl.AuthorizationRuleCollection.Item(System.Int32)">
      <summary>Ottiene l'oggetto <see cref="T:System.Security.AccessControl.AuthorizationRule" /> in corrispondenza dell'indice specificato della raccolta.</summary>
      <returns>Oggetto <see cref="T:System.Security.AccessControl.AuthorizationRule" /> in corrispondenza dell'indice specificato.</returns>
      <param name="index">Indice in base zero dell'oggetto <see cref="T:System.Security.AccessControl.AuthorizationRule" /> da ottenere.</param>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)"></member>
    <member name="P:System.Security.AccessControl.AuthorizationRuleCollection.System#Collections#ICollection#IsSynchronized"></member>
    <member name="P:System.Security.AccessControl.AuthorizationRuleCollection.System#Collections#ICollection#SyncRoot"></member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.System#Collections#IEnumerable#GetEnumerator"></member>
    <member name="T:System.Security.AccessControl.CommonAce">
      <summary>Rappresenta una voce di controllo di accesso (ACE, Access Control Entry).</summary>
    </member>
    <member name="M:System.Security.AccessControl.CommonAce.#ctor(System.Security.AccessControl.AceFlags,System.Security.AccessControl.AceQualifier,System.Int32,System.Security.Principal.SecurityIdentifier,System.Boolean,System.Byte[])">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.CommonAce" />.</summary>
      <param name="flags">Flag che specificano le informazioni relative a ereditarietà, propagazione dell'ereditarietà e condizioni di controllo per la nuova voce di controllo di accesso (ACE).</param>
      <param name="qualifier">Utilizzo della nuova voce ACE.</param>
      <param name="accessMask">Maschera di accesso della voce ACE.</param>
      <param name="sid">Classe <see cref="T:System.Security.Principal.SecurityIdentifier" /> associata alla nuova voce ACE.</param>
      <param name="isCallback">true per specificare che la nuova voce ACE è una voce ACE di tipo callback.</param>
      <param name="opaque">Dati opachi associati alla nuova voce ACE.I dati opachi sono consentiti solo per i tipi di voci ACE di callback.La lunghezza di questa matrice non deve essere superiore al valore restituito del metodo <see cref="M:System.Security.AccessControl.CommonAce.MaxOpaqueLength(System.Boolean)" />.</param>
    </member>
    <member name="P:System.Security.AccessControl.CommonAce.BinaryLength">
      <summary>Ottiene la lunghezza, in byte, della rappresentazione binaria dell'oggetto <see cref="T:System.Security.AccessControl.CommonAce" /> corrente.Usare questa lunghezza con il metodo <see cref="M:System.Security.AccessControl.CommonAce.GetBinaryForm(System.Byte[],System.Int32)" /> prima di effettuare il marshalling dell'elenco ACL in una matrice binaria.</summary>
      <returns>Lunghezza, in byte, della rappresentazione binaria dell'oggetto <see cref="T:System.Security.AccessControl.CommonAce" /> corrente.</returns>
    </member>
    <member name="M:System.Security.AccessControl.CommonAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Esegue il marshalling dei contenuti dell'oggetto <see cref="T:System.Security.AccessControl.CommonAce" /> nella matrice di byte specificata, in corrispondenza dell'offset specificato.</summary>
      <param name="binaryForm">Matrice di byte in cui viene effettuato il marshalling del contenuto dell'oggetto <see cref="T:System.Security.AccessControl.CommonAce" />.</param>
      <param name="offset">Offset in corrispondenza del quale viene avviato il marshalling.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> è negativo o troppo alto per consentire la copia dell'intero oggetto <see cref="T:System.Security.AccessControl.CommonAce" /> nella matrice <paramref name="binaryForm" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.CommonAce.MaxOpaqueLength(System.Boolean)">
      <summary>Ottiene la lunghezza massima consentita di un BLOB di dati opachi per le voci di controllo di accesso (ACE) di callback.</summary>
      <returns>Lunghezza consentita di un BLOB di dati opachi.</returns>
      <param name="isCallback">true per specificare che l'oggetto <see cref="T:System.Security.AccessControl.CommonAce" /> è una voce ACE di tipo callback.</param>
    </member>
    <member name="T:System.Security.AccessControl.CommonAcl">
      <summary>Rappresenta un elenco di controllo di accesso (ACL, Access Control List) e costituisce la classe base per le classi <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> e <see cref="T:System.Security.AccessControl.SystemAcl" />.</summary>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.BinaryLength">
      <summary>Ottiene la lunghezza, in byte, della rappresentazione binaria dell'oggetto <see cref="T:System.Security.AccessControl.CommonAcl" /> corrente.Tale lunghezza deve essere usata prima di effettuare il marshalling dell'elenco di controllo di accesso (ACL) in una matrice binaria mediante il metodo <see cref="M:System.Security.AccessControl.CommonAcl.GetBinaryForm(System.Byte[],System.Int32)" />.</summary>
      <returns>Lunghezza, in byte, della rappresentazione binaria dell'oggetto <see cref="T:System.Security.AccessControl.CommonAcl" /> corrente.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.Count">
      <summary>Ottiene il numero di voci di controllo di accesso (ACE) presenti nell'oggetto <see cref="T:System.Security.AccessControl.CommonAcl" /> corrente.</summary>
      <returns>Numero di voci ACE presenti nell'oggetto <see cref="T:System.Security.AccessControl.CommonAcl" /> corrente.</returns>
    </member>
    <member name="M:System.Security.AccessControl.CommonAcl.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Esegue il marshalling dei contenuti dell'oggetto <see cref="T:System.Security.AccessControl.CommonAcl" /> nella matrice di byte specificata, in corrispondenza dell'offset specificato.</summary>
      <param name="binaryForm">Matrice di byte in cui i contenuti dell'oggetto <see cref="T:System.Security.AccessControl.CommonAcl" /> vengono sottoposti a marshalling.</param>
      <param name="offset">Offset in corrispondenza del quale viene avviato il marshalling.</param>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.IsCanonical">
      <summary>Ottiene un valore booleano che specifica se le voci di controllo di accesso (ACE) nell'oggetto <see cref="T:System.Security.AccessControl.CommonAcl" /> corrente sono in ordine canonico.</summary>
      <returns>true se le voci ACE nell'oggetto <see cref="T:System.Security.AccessControl.CommonAcl" /> corrente sono in ordine canonico; in caso contrario, false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.IsContainer">
      <summary>Specifica se l'oggetto <see cref="T:System.Security.AccessControl.CommonAcl" /> è un contenitore. </summary>
      <returns>true se l'oggetto <see cref="T:System.Security.AccessControl.CommonAcl" /> corrente è un contenitore.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.IsDS">
      <summary>Specifica se l'oggetto <see cref="T:System.Security.AccessControl.CommonAcl" /> corrente è un elenco di controllo di accesso (ACL) di un oggetto directory.</summary>
      <returns>true se l'oggetto <see cref="T:System.Security.AccessControl.CommonAcl" /> corrente è un elenco ACL di un oggetto directory.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.Item(System.Int32)">
      <summary>Ottiene o imposta l'oggetto <see cref="T:System.Security.AccessControl.CommonAce" /> in corrispondenza dell'indice specificato.</summary>
      <returns>
        <see cref="T:System.Security.AccessControl.CommonAce" /> in corrispondenza dell'indice specificato.</returns>
      <param name="index">Indice in base zero dell'oggetto <see cref="T:System.Security.AccessControl.CommonAce" /> da ottenere o impostare.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonAcl.Purge(System.Security.Principal.SecurityIdentifier)">
      <summary>Rimuove tutte le voci di controllo di accesso (ACE) contenute dall'oggetto <see cref="T:System.Security.AccessControl.CommonAcl" /> e associate all'oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> specificato.</summary>
      <param name="sid">Oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> da verificare.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonAcl.RemoveInheritedAces">
      <summary>Rimuove tutte le voci di controllo di accesso (ACE) ereditate dall'oggetto <see cref="T:System.Security.AccessControl.CommonAcl" />.</summary>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.Revision">
      <summary>Ottiene il livello di revisione della classe <see cref="T:System.Security.AccessControl.CommonAcl" />.</summary>
      <returns>Valore di byte che specifica il livello di revisione della classe <see cref="T:System.Security.AccessControl.CommonAcl" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.CommonObjectSecurity">
      <summary>Controlla l'accesso a oggetti senza modifica diretta degli elenchi di controllo di accesso (ACL, Access Control List).Questa classe è la classe base astratta per la classe <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.#ctor(System.Boolean)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <param name="isContainer">true se il nuovo oggetto è un oggetto contenitore.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.AddAccessRule(System.Security.AccessControl.AccessRule)">
      <summary>Aggiunge la regola di accesso specificata all'elenco DACL (Discretionary Access Control List) associato all'oggetto <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <param name="rule">Regola di accesso da aggiungere.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.AddAuditRule(System.Security.AccessControl.AuditRule)">
      <summary>Aggiunge la regola di controllo specificata all'elenco SACL (System Access Control List) associato all'oggetto <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <param name="rule">Regola di controllo da aggiungere.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.GetAccessRules(System.Boolean,System.Boolean,System.Type)">
      <summary>Ottiene un insieme delle regole di accesso associate all'identificatore di sicurezza specificato.</summary>
      <returns>Insieme di regole di accesso associate all'oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> specificato.</returns>
      <param name="includeExplicit">true per includere le regole di accesso esplicitamente impostate per l'oggetto.</param>
      <param name="includeInherited">true per includere le regole di accesso ereditate.</param>
      <param name="targetType">Specifica se l'ID di sicurezza per cui recuperare le regole di accesso è di tipo T:System.Security.Principal.SecurityIdentifier o di tipo T:System.Security.Principal.NTAccount.Il valore di questo parametro deve essere un tipo convertibile nel tipo <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.GetAuditRules(System.Boolean,System.Boolean,System.Type)">
      <summary>Ottiene un insieme di regole di accesso associate all'identificatore di sicurezza specificato.</summary>
      <returns>Insieme di regole di controllo associate all'oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> specificato.</returns>
      <param name="includeExplicit">true per includere le regole di controllo esplicitamente impostate per l'oggetto.</param>
      <param name="includeInherited">true per includere le regole di controllo ereditate.</param>
      <param name="targetType">Identificatore di sicurezza per il quale recuperare le regole di controllo.Deve essere un oggetto di cui è possibile eseguire il cast in un oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.ModifyAccess(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AccessRule,System.Boolean@)">
      <summary>Applica la modifica specificata all'elenco di controllo di accesso discrezionale (DACL) associato all'oggetto <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <returns>true se la modifica dell'elenco DACL è riuscita. In caso contrario, false.</returns>
      <param name="modification">Modifica da applicare all'elenco DACL.</param>
      <param name="rule">Regola di accesso da modificare.</param>
      <param name="modified">true se la modifica dell'elenco DACL è riuscita. In caso contrario, false.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.ModifyAudit(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AuditRule,System.Boolean@)">
      <summary>Applica la modifica specificata all'elenco SACL (System Access Control List) associato all'oggetto <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <returns>true se la modifica dell'elenco SACL è riuscita; in caso contrario, false.</returns>
      <param name="modification">Modifica da applicare all'elenco SACL.</param>
      <param name="rule">Regola di controllo da modificare.</param>
      <param name="modified">true se la modifica dell'elenco SACL è riuscita; in caso contrario, false.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAccessRule(System.Security.AccessControl.AccessRule)">
      <summary>Rimuove le regole di accesso contenenti lo stesso ID di sicurezza e la stessa maschera di accesso della regola di accesso specificata dall'elenco di controllo di accesso discrezionale (DACL) associato all'oggetto <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <returns>true se la rimozione della regola di accesso è riuscita; in caso contrario, false.</returns>
      <param name="rule">Regola di accesso da rimuovere.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAccessRuleAll(System.Security.AccessControl.AccessRule)">
      <summary>Rimuove tutte le regole di accesso che dispongono dello stesso identificatore di sicurezza e della stessa regola di accesso specificata dall'elenco DACL (Discretionary Access Control List) associato all'oggetto <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <param name="rule">Regola di accesso da rimuovere.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAccessRuleSpecific(System.Security.AccessControl.AccessRule)">
      <summary>Rimuove tutte le regole di accesso che corrispondono esattamente alla regola di accesso specificata dall'elenco DACL (Discretionary Access Control List) associato all'oggetto <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <param name="rule">Regola di accesso da rimuovere.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAuditRule(System.Security.AccessControl.AuditRule)">
      <summary>Rimuove le regole di controllo contenenti lo stesso identificatore di sicurezza e la stessa maschera di accesso della regola di controllo specificata dall'elenco SACL (System Access Control List) associato all'oggetto <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <returns>true se la rimozione della regola di controllo è riuscita; in caso contrario, false.</returns>
      <param name="rule">Regola di controllo da rimuovere.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAuditRuleAll(System.Security.AccessControl.AuditRule)">
      <summary>Rimuove tutte le regole di controllo che dispongono dello stesso ID di sicurezza e della stessa regola di controllo specificata dall'elenco di controllo di accesso di sistema (SACL) associato all'oggetto <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <param name="rule">Regola di controllo da rimuovere.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAuditRuleSpecific(System.Security.AccessControl.AuditRule)">
      <summary>Rimuove tutte le regole di controllo che corrispondono esattamente alla regola di controllo specificata dall'elenco di controllo di accesso di sistema (SACL) associato all'oggetto <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <param name="rule">Regola di controllo da rimuovere.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.ResetAccessRule(System.Security.AccessControl.AccessRule)">
      <summary>Rimuove tutte le regole di accesso nell'elenco di controllo di accesso discrezionale (DACL) associato all'oggetto <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />, quindi aggiunge la regola di accesso specificata.</summary>
      <param name="rule">Regola di accesso da ripristinare.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.SetAccessRule(System.Security.AccessControl.AccessRule)">
      <summary>Rimuove tutte le regole di accesso contenenti lo stesso identificatore e qualificatore di sicurezza della regola di accesso specificata nell'elenco DACL (Discretionary Access Control List) associato all'oggetto <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />, quindi aggiunge la regola di accesso specificata.</summary>
      <param name="rule">Regola di accesso da impostare.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.SetAuditRule(System.Security.AccessControl.AuditRule)">
      <summary>Rimuove tutte le regole di controllo contenenti lo stesso identificatore e qualificatore di sicurezza della regola di controllo specificata nell'elenco SACL (System Access Control List) associato all'oggetto <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />, quindi aggiunge la regola di controllo specificata.</summary>
      <param name="rule">Regola di controllo da impostare.</param>
    </member>
    <member name="T:System.Security.AccessControl.CommonSecurityDescriptor">
      <summary>Rappresenta un descrittore di sicurezza.Un descrittore di sicurezza include un proprietario, un gruppo primario, un elenco di controllo di accesso discrezionale (DACL, Discretionary Access Control List) e un elenco di controllo di accesso di sistema (SACL, System Access Control List).</summary>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.#ctor(System.Boolean,System.Boolean,System.Byte[],System.Int32)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> dalla matrice di valori di byte specificata.</summary>
      <param name="isContainer">true se il nuovo descrittore di sicurezza è associato a un oggetto contenitore.</param>
      <param name="isDS">true se il nuovo descrittore di sicurezza è associato a un oggetto directory.</param>
      <param name="binaryForm">Matrice di valori di byte da cui creare il nuovo oggetto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</param>
      <param name="offset">Offset nella matrice <paramref name="binaryForm" /> in corrispondenza del quale verrà iniziata la copia.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.#ctor(System.Boolean,System.Boolean,System.Security.AccessControl.ControlFlags,System.Security.Principal.SecurityIdentifier,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.SystemAcl,System.Security.AccessControl.DiscretionaryAcl)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> dalle informazioni specificate.</summary>
      <param name="isContainer">true se il nuovo descrittore di sicurezza è associato a un oggetto contenitore.</param>
      <param name="isDS">true se il nuovo descrittore di sicurezza è associato a un oggetto directory.</param>
      <param name="flags">Flag che specificano il comportamento del nuovo oggetto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</param>
      <param name="owner">Proprietario del nuovo oggetto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</param>
      <param name="group">Gruppo primario del nuovo oggetto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</param>
      <param name="systemAcl">Elenco SACL (System Access Control List) del nuovo oggetto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</param>
      <param name="discretionaryAcl">Elenco DACL (Discretionary Access Control List) del nuovo oggetto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.#ctor(System.Boolean,System.Boolean,System.Security.AccessControl.RawSecurityDescriptor)">
      <summary>Consente di inizializzare una nuova istanza della classe <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> dall'oggetto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> specificato.</summary>
      <param name="isContainer">true se il nuovo descrittore di sicurezza è associato a un oggetto contenitore.</param>
      <param name="isDS">true se il nuovo descrittore di sicurezza è associato a un oggetto directory.</param>
      <param name="rawSecurityDescriptor">Oggetto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> da cui creare il nuovo oggetto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.#ctor(System.Boolean,System.Boolean,System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> dalla stringa SDDL (Security Descriptor Definition Language) specificata.</summary>
      <param name="isContainer">true se il nuovo descrittore di sicurezza è associato a un oggetto contenitore.</param>
      <param name="isDS">true se il nuovo descrittore di sicurezza è associato a un oggetto directory.</param>
      <param name="sddlForm">Stringa SDDL da cui creare il nuovo oggetto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.AddDiscretionaryAcl(System.Byte,System.Int32)">
      <summary>Imposta la proprietà <see cref="P:System.Security.AccessControl.CommonSecurityDescriptor.DiscretionaryAcl" /> per questa istanza di <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> e imposta il flag <see cref="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclPresent" />.</summary>
      <param name="revision">Livello di revisione del nuovo oggetto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />.</param>
      <param name="trusted">Numero di ACE (Access Control Entries, voci di controllo di accesso) che l'oggetto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> può contenere.Questo numero deve essere usato solo come suggerimento.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.AddSystemAcl(System.Byte,System.Int32)">
      <summary>Imposta la proprietà <see cref="P:System.Security.AccessControl.CommonSecurityDescriptor.SystemAcl" /> per questa istanza di <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> e imposta il flag <see cref="F:System.Security.AccessControl.ControlFlags.SystemAclPresent" />.</summary>
      <param name="revision">Livello di revisione del nuovo oggetto <see cref="T:System.Security.AccessControl.SystemAcl" />.</param>
      <param name="trusted">Numero di ACE (Access Control Entries, voci di controllo di accesso) che l'oggetto <see cref="T:System.Security.AccessControl.SystemAcl" /> può contenere.Questo numero deve essere usato solo come suggerimento.</param>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.ControlFlags">
      <summary>Ottiene i valori che specificano il comportamento dell'oggetto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</summary>
      <returns>Uno o più valori dell'enumerazione <see cref="T:System.Security.AccessControl.ControlFlags" /> combinati con un'operazione OR logica.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.DiscretionaryAcl">
      <summary>Ottiene o imposta l'elenco di controllo di accesso discrezionale dell'oggetto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.L'elenco DACL contiene le regole di accesso.</summary>
      <returns>Elenco DACL dell'oggetto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.Group">
      <summary>Ottiene o imposta il gruppo primario dell'oggetto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</summary>
      <returns>Gruppo primario dell'oggetto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.IsContainer">
      <summary>Ottiene un valore booleano che specifica se l'oggetto associato a questo oggetto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> è un oggetto contenitore.</summary>
      <returns>true se l'oggetto associato a questo oggetto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> è un oggetto contenitore; in caso contrario, false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.IsDiscretionaryAclCanonical">
      <summary>Ottiene un valore booleano che specifica se l'elenco di controllo di accesso discrezionale associato a questo oggetto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> è in ordine canonico.</summary>
      <returns>true se l'elenco DACL associato a questo oggetto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> è in ordine canonico; in caso contrario, false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.IsDS">
      <summary>Ottiene un valore booleano che specifica se l'oggetto associato a questo oggetto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> è un oggetto directory.</summary>
      <returns>true se l'oggetto associato a questo oggetto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> è un oggetto directory; in caso contrario, false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.IsSystemAclCanonical">
      <summary>Ottiene un valore booleano che specifica se l'elenco di controllo di accesso di sistema associato a questo oggetto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> è in ordine canonico.</summary>
      <returns>true se l'elenco SACL associato a questo oggetto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> è in ordine canonico; in caso contrario, false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.Owner">
      <summary>Ottiene o imposta il proprietario dell'oggetto associato all'oggetto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</summary>
      <returns>Proprietario dell'oggetto associato all'oggetto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.PurgeAccessControl(System.Security.Principal.SecurityIdentifier)">
      <summary>Rimuove tutte le regole di accesso per l'identificatore di sicurezza specificato dall'elenco DACL (Discretionary Access Control List) associato all'oggetto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</summary>
      <param name="sid">Identificatore di sicurezza per il quale rimuovere le regole di accesso.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.PurgeAudit(System.Security.Principal.SecurityIdentifier)">
      <summary>Rimuove tutte le regole di controllo per l'identificatore di sicurezza specificato dall'elenco SACL (System Access Control List) associato all'oggetto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</summary>
      <param name="sid">Identificatore di sicurezza per il quale rimuovere le regole di controllo.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.SetDiscretionaryAclProtection(System.Boolean,System.Boolean)">
      <summary>Imposta la protezione dall'ereditarietà per l'elenco DACL (Discretionary Access Control List) associato all'oggetto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.Gli elenchi DACL che sono protetti non ereditano regole di accesso dai contenitori padre.</summary>
      <param name="isProtected">true per proteggere l'elenco DACL dall'ereditarietà.</param>
      <param name="preserveInheritance">true per conservare le regole di accesso ereditate nell'elenco DACL; false per rimuovere le regole di accesso ereditate dall'elenco DACL.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.SetSystemAclProtection(System.Boolean,System.Boolean)">
      <summary>Imposta la protezione dall'ereditarietà per l'elenco SACL (System Access Control List) associato all'oggetto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.Gli elenchi SACL che sono protetti non ereditano regole di controllo dai contenitori padre.</summary>
      <param name="isProtected">true per proteggere l'elenco SACL dall'ereditarietà.</param>
      <param name="preserveInheritance">true per conservare le regole di controllo ereditate nell'elenco SACL; false per rimuovere le regole di controllo ereditate dall'elenco SACL.</param>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.SystemAcl">
      <summary>Ottiene o imposta l'elenco SACL (System Access Control List) dell'oggetto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.L'elenco SACL contiene le regole di controllo.</summary>
      <returns>Elenco SACL dell'oggetto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.CompoundAce">
      <summary>Rappresenta una ACE (Access Control Entry, voce di controllo di accesso) composta.</summary>
    </member>
    <member name="M:System.Security.AccessControl.CompoundAce.#ctor(System.Security.AccessControl.AceFlags,System.Int32,System.Security.AccessControl.CompoundAceType,System.Security.Principal.SecurityIdentifier)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.CompoundAce" />.</summary>
      <param name="flags">Contiene flag che specificano informazioni sulle condizioni di ereditarietà, propagazione dell'ereditarietà e controllo per la nuova ACE.</param>
      <param name="accessMask">Maschera di accesso della voce ACE.</param>
      <param name="compoundAceType">Valore ottenuto dall'enumerazione <see cref="T:System.Security.AccessControl.CompoundAceType" />.</param>
      <param name="sid">Classe <see cref="T:System.Security.Principal.SecurityIdentifier" /> associata alla nuova voce ACE.</param>
    </member>
    <member name="P:System.Security.AccessControl.CompoundAce.BinaryLength">
      <summary>Ottiene la lunghezza, in byte, della rappresentazione binaria dell'oggetto <see cref="T:System.Security.AccessControl.CompoundAce" /> corrente.Questa lunghezza deve essere utilizzata prima di eseguire il marshaling dell'ACL in una matrice binaria con il metodo <see cref="M:System.Security.AccessControl.CompoundAce.GetBinaryForm" />.</summary>
      <returns>Lunghezza, in byte, della rappresentazione binaria dell'oggetto <see cref="T:System.Security.AccessControl.CompoundAce" /> corrente.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CompoundAce.CompoundAceType">
      <summary>Ottiene o imposta il tipo di questo oggetto <see cref="T:System.Security.AccessControl.CompoundAce" />.</summary>
      <returns>Tipo di questo oggetto <see cref="T:System.Security.AccessControl.CompoundAce" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.CompoundAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Esegue il marshalling dei contenuti dell'oggetto <see cref="T:System.Security.AccessControl.CompoundAce" /> nella matrice di byte specificata nella posizione di offset specificata.</summary>
      <param name="binaryForm">Matrice di byte in cui i contenuti dell'oggetto <see cref="T:System.Security.AccessControl.CompoundAce" /> vengono sottoposti a marshaling.</param>
      <param name="offset">Offset in corrispondenza del quale viene avviato il marshalling.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> è negativo o troppo elevato per consentire la copia di tutto l'oggetto <see cref="T:System.Security.AccessControl.CompoundAce" /> in <paramref name="array" />.</exception>
    </member>
    <member name="T:System.Security.AccessControl.CompoundAceType">
      <summary>Specifica il tipo di un oggetto <see cref="T:System.Security.AccessControl.CompoundAce" />.</summary>
    </member>
    <member name="F:System.Security.AccessControl.CompoundAceType.Impersonation">
      <summary>L'oggetto <see cref="T:System.Security.AccessControl.CompoundAce" /> è utilizzato per la rappresentazione.</summary>
    </member>
    <member name="T:System.Security.AccessControl.ControlFlags">
      <summary>Questi flag hanno effetto sul comportamento del descrittore di sicurezza.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclAutoInherited">
      <summary>Specifica che l'elenco DACL (Discretionary Access Control List, elenco di controllo di accesso discrezionale) è stato ereditato automaticamente dall'elemento padre.Impostato solo dai gestori delle risorse.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclAutoInheritRequired">
      <summary>Ignorato.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclDefaulted">
      <summary>Specifica che il DACL è stato ottenuto da un meccanismo di impostazione.Impostato solo dai gestori delle risorse.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclPresent">
      <summary>Specifica che il DACL non è null.Impostato dai gestori delle risorse o dagli utenti.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclProtected">
      <summary>Specifica che il gestore delle risorse impedisce l'ereditarietà automatica.Impostato dai gestori delle risorse o dagli utenti.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclUntrusted">
      <summary>Ignorato.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.GroupDefaulted">
      <summary>Specifica che il gruppo <see cref="T:System.Security.Principal.SecurityIdentifier" /> è stato ottenuto da un meccanismo di impostazione.Impostato solo dai gestori delle risorse; non deve essere impostato dai chiamanti.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.None">
      <summary>Nessun flag di controllo.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.OwnerDefaulted">
      <summary>Specifica che il proprietario <see cref="T:System.Security.Principal.SecurityIdentifier" /> è stato ottenuto da un meccanismo di impostazione.Impostato solo dai gestori delle risorse; non deve essere impostato dai chiamanti.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.RMControlValid">
      <summary>Specifica che i contenuti del campo riservato sono validi.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SelfRelative">
      <summary>Specifica che la rappresentazione binaria del descrittore di sicurezza è in formato relativo.  Questo flag è sempre impostato.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.ServerSecurity">
      <summary>Ignorato.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclAutoInherited">
      <summary>Specifica che l'elenco SACL (System Access Control List, elenco di controllo di accesso di sistema) è stato ereditato automaticamente dall'elemento padre.Impostato solo dai gestori delle risorse.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclAutoInheritRequired">
      <summary>Ignorato.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclDefaulted">
      <summary>Specifica che il SACL è stato ottenuto da un meccanismo di impostazione.Impostato solo dai gestori delle risorse.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclPresent">
      <summary>Specifica che il SACL non è null.Impostato dai gestori delle risorse o dagli utenti.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclProtected">
      <summary>Specifica che il gestore delle risorse impedisce l'ereditarietà automatica.Impostato dai gestori delle risorse o dagli utenti.</summary>
    </member>
    <member name="T:System.Security.AccessControl.CustomAce">
      <summary>Rappresenta una ACE (Access Control Entry, voce di controllo di accesso) non definita da uno dei membri dell'enumerazione <see cref="T:System.Security.AccessControl.AceType" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.CustomAce.#ctor(System.Security.AccessControl.AceType,System.Security.AccessControl.AceFlags,System.Byte[])">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.CustomAce" />.</summary>
      <param name="type">Tipo della nuova ACE.Questo valore deve essere maggiore di <see cref="F:System.Security.AccessControl.AceType.MaxDefinedAceType" />.</param>
      <param name="flags">Flag che specificano informazioni sulle condizioni di ereditarietà, propagazione dell'ereditarietà e controllo per la nuova ACE.</param>
      <param name="opaque">Matrice di valori di byte che contiene i dati per la nuova ACE.Il valore può essere null.La lunghezza di questa matrice non deve essere superiore al valore del campo <see cref="F:System.Security.AccessControl.CustomAce.MaxOpaqueLength" /> e deve essere un multiplo di quattro.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Il valore del parametro <paramref name="type" /> non è maggiore di <see cref="F:System.Security.AccessControl.AceType.MaxDefinedAceType" /> oppure la lunghezza della matrice <paramref name="opaque" /> è maggiore del valore del campo <see cref="F:System.Security.AccessControl.CustomAce.MaxOpaqueLength" /> o non è un multiplo di quattro.</exception>
    </member>
    <member name="P:System.Security.AccessControl.CustomAce.BinaryLength">
      <summary>Ottiene la lunghezza, in byte, della rappresentazione binaria dell'oggetto <see cref="T:System.Security.AccessControl.CustomAce" /> corrente.Questa lunghezza deve essere utilizzata prima di eseguire il marshaling dell'ACL in una matrice binaria con il metodo <see cref="M:System.Security.AccessControl.CustomAce.GetBinaryForm" />.</summary>
      <returns>Lunghezza, in byte, della rappresentazione binaria dell'oggetto <see cref="T:System.Security.AccessControl.CustomAce" /> corrente.</returns>
    </member>
    <member name="M:System.Security.AccessControl.CustomAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Esegue il marshalling dei contenuti dell'oggetto <see cref="T:System.Security.AccessControl.CustomAce" /> nella matrice di byte specificata nella posizione di offset specificata.</summary>
      <param name="binaryForm">Matrice di byte in cui i contenuti dell'oggetto <see cref="T:System.Security.AccessControl.CustomAce" /> vengono sottoposti a marshaling.</param>
      <param name="offset">Offset in corrispondenza del quale viene avviato il marshalling.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> è negativo o troppo elevato per consentire la copia di tutto l'oggetto <see cref="T:System.Security.AccessControl.CustomAce" /> in <paramref name="array" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.CustomAce.GetOpaque">
      <summary>Restituisce i dati opachi associati a questo oggetto <see cref="T:System.Security.AccessControl.CustomAce" />. </summary>
      <returns>Matrice di valori di byte che rappresenta i dati opachi associati a questo oggetto <see cref="T:System.Security.AccessControl.CustomAce" />.</returns>
    </member>
    <member name="F:System.Security.AccessControl.CustomAce.MaxOpaqueLength">
      <summary>Restituisce la lunghezza massima consentita per un blob di dati opachi per l'oggetto <see cref="T:System.Security.AccessControl.CustomAce" />.</summary>
    </member>
    <member name="P:System.Security.AccessControl.CustomAce.OpaqueLength">
      <summary>Ottiene la lunghezza dei dati opachi associati a questo oggetto <see cref="T:System.Security.AccessControl.CustomAce" />.</summary>
      <returns>Lunghezza dei dati di callback opachi.</returns>
    </member>
    <member name="M:System.Security.AccessControl.CustomAce.SetOpaque(System.Byte[])">
      <summary>Imposta i dati di callback opachi associati a questo oggetto <see cref="T:System.Security.AccessControl.CustomAce" />.</summary>
      <param name="opaque">Matrice di valori di byte che rappresenta i dati di callback opachi per questo oggetto <see cref="T:System.Security.AccessControl.CustomAce" />.</param>
    </member>
    <member name="T:System.Security.AccessControl.DiscretionaryAcl">
      <summary>Rappresenta un elenco di controllo di accesso discrezionale (DACL, Discretionary Access Control List).</summary>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.#ctor(System.Boolean,System.Boolean,System.Byte,System.Int32)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> con i valori specificati.</summary>
      <param name="isContainer">true se il nuovo oggetto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> è un contenitore.</param>
      <param name="isDS">true se il nuovo oggetto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> è un oggetto di directory ACL (Access Control List, elenco di controllo di accesso).</param>
      <param name="revision">Livello di revisione del nuovo oggetto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />.</param>
      <param name="capacity">Numero di ACE (Access Control Entries, voci di controllo di accesso) che l'oggetto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> può contenere.Questo numero deve essere usato solo come suggerimento.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.#ctor(System.Boolean,System.Boolean,System.Int32)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> con i valori specificati.</summary>
      <param name="isContainer">true se il nuovo oggetto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> è un contenitore.</param>
      <param name="isDS">true se il nuovo oggetto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> è un oggetto di directory ACL (Access Control List, elenco di controllo di accesso).</param>
      <param name="capacity">Numero di ACE (Access Control Entries, voci di controllo di accesso) che l'oggetto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> può contenere.Questo numero deve essere usato solo come suggerimento.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.#ctor(System.Boolean,System.Boolean,System.Security.AccessControl.RawAcl)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> con i valori specificati dell'oggetto <see cref="T:System.Security.AccessControl.RawAcl" /> specificato.</summary>
      <param name="isContainer">true se il nuovo oggetto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> è un contenitore.</param>
      <param name="isDS">true se il nuovo oggetto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> è un oggetto di directory ACL (Access Control List, elenco di controllo di accesso).</param>
      <param name="rawAcl">Oggetto <see cref="T:System.Security.AccessControl.RawAcl" /> sottostante per il nuovo oggetto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />.Specificare null per creare un elenco ACL vuoto.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.AddAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Aggiunge una voce di controllo di accesso (ACE) con le impostazioni specificate all'oggetto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> corrente.</summary>
      <param name="accessType">Tipo di controllo di accesso (consenso o negazione) da aggiungere.</param>
      <param name="sid">Classe <see cref="T:System.Security.Principal.SecurityIdentifier" /> per la quale aggiungere una voce ACE.</param>
      <param name="accessMask">Regola di accesso della nuova voce ACE.</param>
      <param name="inheritanceFlags">Flag che specificano le proprietà di ereditarietà della nuova voce ACE.</param>
      <param name="propagationFlags">Flag che specificano le proprietà di propagazione dell'ereditarietà della nuova voce ACE.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.AddAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Aggiunge una voce di controllo di accesso (ACE) con le impostazioni specificate all'oggetto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> corrente.Usare questo metodo per gli elenchi di controllo di accesso (ACL) di oggetti directory quando si specifica il tipo di oggetto o il tipo di oggetto ereditato della nuova voce ACE.</summary>
      <param name="accessType">Tipo di controllo di accesso (consenso o negazione) da aggiungere.</param>
      <param name="sid">Classe <see cref="T:System.Security.Principal.SecurityIdentifier" /> per la quale aggiungere una voce ACE.</param>
      <param name="accessMask">Regola di accesso della nuova voce ACE.</param>
      <param name="inheritanceFlags">Flag che specificano le proprietà di ereditarietà della nuova voce ACE.</param>
      <param name="propagationFlags">Flag che specificano le proprietà di propagazione dell'ereditarietà della nuova voce ACE.</param>
      <param name="objectFlags">Flag che specificano se i parametri <paramref name="objectType" /> e <paramref name="inheritedObjectType" /> contengono valori non null.</param>
      <param name="objectType">Identità della classe di oggetti a cui viene applicata la nuova voce ACE.</param>
      <param name="inheritedObjectType">Identità della classe di oggetti figlio che possono ereditare la nuova voce ACE.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.AddAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAccessRule)">
      <summary>Aggiunge una voce di controllo di accesso (ACE) con le impostazioni specificate all'oggetto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> corrente.</summary>
      <param name="accessType">Tipo di controllo di accesso (consenso o negazione) da aggiungere.</param>
      <param name="sid">Classe <see cref="T:System.Security.Principal.SecurityIdentifier" /> per la quale aggiungere una voce ACE.</param>
      <param name="rule">Oggetto <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> per il nuovo accesso.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Rimuove la regola di controllo di accesso specificata dall'oggetto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> corrente.</summary>
      <returns>true se l'accesso specificato viene rimosso dal metodo; in caso contrario, false.</returns>
      <param name="accessType">Tipo di controllo di accesso (consenso o negazione) da rimuovere.</param>
      <param name="sid">Classe <see cref="T:System.Security.Principal.SecurityIdentifier" /> per la quale rimuovere una regola di controllo di accesso.</param>
      <param name="accessMask">Maschera di accesso per la regola da rimuovere.</param>
      <param name="inheritanceFlags">Flag che specificano le proprietà di ereditarietà della regola da rimuovere.</param>
      <param name="propagationFlags">Flag che specificano le proprietà di propagazione dell'ereditarietà per la regola da rimuovere.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Rimuove la regola di controllo di accesso specificata dall'oggetto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> corrente.Usare questo metodo per gli elenchi di controllo di accesso (ACL) di oggetti directory quando si specifica il tipo di oggetto o il tipo di oggetto ereditato.</summary>
      <returns>true se l'accesso specificato viene rimosso dal metodo; in caso contrario, false.</returns>
      <param name="accessType">Tipo di controllo di accesso (consenso o negazione) da rimuovere.</param>
      <param name="sid">Classe <see cref="T:System.Security.Principal.SecurityIdentifier" /> per la quale rimuovere una regola di controllo di accesso.</param>
      <param name="accessMask">Maschera di accesso per la regola di controllo di accesso da rimuovere.</param>
      <param name="inheritanceFlags">Flag che specificano le proprietà di ereditarietà della regola di controllo di accesso da rimuovere.</param>
      <param name="propagationFlags">Flag che specificano le proprietà di propagazione dell'ereditarietà per la regola di controllo di accesso da rimuovere.</param>
      <param name="objectFlags">Flag che specificano se i parametri <paramref name="objectType" /> e <paramref name="inheritedObjectType" /> contengono valori non null.</param>
      <param name="objectType">Identità della classe di oggetti a cui viene applicata la regola di controllo di accesso rimossa.</param>
      <param name="inheritedObjectType">identità della classe di oggetti figlio che possono ereditare la regola di controllo di accesso rimossa.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAccessRule)">
      <summary>Rimuove la regola di controllo di accesso specificata dall'oggetto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> corrente.</summary>
      <returns>Restituisce <see cref="T:System.Boolean" />.</returns>
      <param name="accessType">Tipo di controllo di accesso (consenso o negazione) da rimuovere.</param>
      <param name="sid">Classe <see cref="T:System.Security.Principal.SecurityIdentifier" /> per la quale rimuovere una regola di controllo di accesso.</param>
      <param name="rule">Oggetto <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> per il quale rimuovere l'accesso.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccessSpecific(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Rimuove la voce di controllo di accesso (ACE) specificata dall'oggetto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> corrente.</summary>
      <param name="accessType">Tipo di controllo di accesso (consenso o negazione) da rimuovere.</param>
      <param name="sid">Oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> per il quale rimuovere una voce ACE.</param>
      <param name="accessMask">Maschera di accesso per la voce ACE da rimuovere.</param>
      <param name="inheritanceFlags">Flag che specificano le proprietà di ereditarietà della voce ACE da rimuovere.</param>
      <param name="propagationFlags">Flag che specificano le proprietà di propagazione dell'ereditarietà per la voce ACE da rimuovere.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccessSpecific(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Rimuove la voce di controllo di accesso (ACE) specificata dall'oggetto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> corrente.Usare questo metodo per gli elenchi di controllo di accesso (ACL) di oggetti directory quando si specifica il tipo di oggetto per la voce ACE da rimuovere.</summary>
      <param name="accessType">Tipo di controllo di accesso (consenso o negazione) da rimuovere.</param>
      <param name="sid">Oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> per il quale rimuovere una voce ACE.</param>
      <param name="accessMask">Maschera di accesso per la voce ACE da rimuovere.</param>
      <param name="inheritanceFlags">Flag che specificano le proprietà di ereditarietà della voce ACE da rimuovere.</param>
      <param name="propagationFlags">Flag che specificano le proprietà di propagazione dell'ereditarietà per la voce ACE da rimuovere.</param>
      <param name="objectFlags">Flag che specificano se i parametri <paramref name="objectType" /> e <paramref name="inheritedObjectType" /> contengono valori non null.</param>
      <param name="objectType">Identità della classe di oggetti a cui viene applicata la voce ACE rimossa.</param>
      <param name="inheritedObjectType">Identità della classe di oggetti figlio che possono ereditare la voce ACE rimossa.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccessSpecific(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAccessRule)">
      <summary>Rimuove la voce di controllo di accesso (ACE) specificata dall'oggetto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> corrente.</summary>
      <param name="accessType">Tipo di controllo di accesso (consenso o negazione) da rimuovere.</param>
      <param name="sid">Oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> per il quale rimuovere una voce ACE.</param>
      <param name="rule">Oggetto <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> per il quale rimuovere l'accesso.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.SetAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Imposta il controllo di accesso specificato per l'oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> specificato.</summary>
      <param name="accessType">Tipo di controllo di accesso (consenso o negazione) da impostare.</param>
      <param name="sid">Oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> per il quale impostare una voce ACE.</param>
      <param name="accessMask">Regola di accesso della nuova voce ACE.</param>
      <param name="inheritanceFlags">Flag che specificano le proprietà di ereditarietà della nuova voce ACE.</param>
      <param name="propagationFlags">Flag che specificano le proprietà di propagazione dell'ereditarietà della nuova voce ACE.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.SetAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Imposta il controllo di accesso specificato per l'oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> specificato.</summary>
      <param name="accessType">Tipo di controllo di accesso (consenso o negazione) da impostare.</param>
      <param name="sid">Oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> per il quale impostare una voce ACE.</param>
      <param name="accessMask">Regola di accesso della nuova voce ACE.</param>
      <param name="inheritanceFlags">Flag che specificano le proprietà di ereditarietà della nuova voce ACE.</param>
      <param name="propagationFlags">Flag che specificano le proprietà di propagazione dell'ereditarietà della nuova voce ACE.</param>
      <param name="objectFlags">Flag che specificano se i parametri <paramref name="objectType" /> e <paramref name="inheritedObjectType" /> contengono valori non null.</param>
      <param name="objectType">Identità della classe di oggetti a cui viene applicata la nuova voce ACE.</param>
      <param name="inheritedObjectType">Identità della classe di oggetti figlio che possono ereditare la nuova voce ACE.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.SetAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAccessRule)">
      <summary>Imposta il controllo di accesso specificato per l'oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> specificato.</summary>
      <param name="accessType">Tipo di controllo di accesso (consenso o negazione) da impostare.</param>
      <param name="sid">Oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> per il quale impostare una voce ACE.</param>
      <param name="rule">Oggetto <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> per il quale impostare l'accesso.</param>
    </member>
    <member name="T:System.Security.AccessControl.GenericAce">
      <summary>Rappresenta una ACE (Access Control Entry, voce del controllo di accesso) ed è la classe di base per tutte le altre classi ACE.</summary>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.AceFlags">
      <summary>Ottiene o imposta l'enumerazione <see cref="T:System.Security.AccessControl.AceFlags" /> associata a questo oggetto <see cref="T:System.Security.AccessControl.GenericAce" />.</summary>
      <returns>Enumerazione <see cref="T:System.Security.AccessControl.AceFlags" /> associata a questo oggetto <see cref="T:System.Security.AccessControl.GenericAce" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.AceType">
      <summary>Ottiene il tipo di questa ACE.</summary>
      <returns>Tipo di questa ACE.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.AuditFlags">
      <summary>Ottiene le informazioni di controllo associate a questa ACE.</summary>
      <returns>Informazioni di controllo associate a questa ACE.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.BinaryLength">
      <summary>Ottiene la lunghezza, in byte, della rappresentazione binaria dell'oggetto <see cref="T:System.Security.AccessControl.GenericAce" /> corrente.Questa lunghezza deve essere utilizzata prima di eseguire il marshaling dell'ACL in una matrice binaria con il metodo <see cref="M:System.Security.AccessControl.GenericAce.GetBinaryForm" />.</summary>
      <returns>Lunghezza, in byte, della rappresentazione binaria dell'oggetto <see cref="T:System.Security.AccessControl.GenericAce" /> corrente.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.Copy">
      <summary>Crea una copia completa di questa ACE.</summary>
      <returns>Oggetto <see cref="T:System.Security.AccessControl.GenericAce" /> creato da questo metodo.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.CreateFromBinaryForm(System.Byte[],System.Int32)">
      <summary>Crea un oggetto <see cref="T:System.Security.AccessControl.GenericAce" /> dai dati binari specificati.</summary>
      <returns>Oggetto <see cref="T:System.Security.AccessControl.GenericAce" /> creato da questo metodo.</returns>
      <param name="binaryForm">Dati binari da cui creare il nuovo oggetto <see cref="T:System.Security.AccessControl.GenericAce" />.</param>
      <param name="offset">Offset da cui iniziare l'unmarshaling.</param>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.Equals(System.Object)">
      <summary>Determina se l'oggetto <see cref="T:System.Security.AccessControl.GenericAce" /> specificato equivale all'oggetto <see cref="T:System.Security.AccessControl.GenericAce" /> corrente.</summary>
      <returns>true se l'oggetto <see cref="T:System.Security.AccessControl.GenericAce" /> specificato equivale all'oggetto <see cref="T:System.Security.AccessControl.GenericAce" /> corrente. In caso contrario, false.</returns>
      <param name="o">Oggetto <see cref="T:System.Security.AccessControl.GenericAce" /> da confrontare con l'oggetto <see cref="T:System.Security.AccessControl.GenericAce" /> corrente.</param>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Esegue il marshalling dei contenuti dell'oggetto <see cref="T:System.Security.AccessControl.GenericAce" /> nella matrice di byte specificata, in corrispondenza dell'offset specificato.</summary>
      <param name="binaryForm">Matrice di byte in cui i contenuti dell'oggetto <see cref="T:System.Security.AccessControl.GenericAce" /> vengono sottoposti a marshaling.</param>
      <param name="offset">Offset in corrispondenza del quale viene avviato il marshalling.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> è negativo o troppo alto per consentire la copia dell'intero oggetto <see cref="T:System.Security.AccessControl.GenericAcl" /> in <paramref name="array" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.GetHashCode">
      <summary>Viene utilizzato come funzione hash per la classe <see cref="T:System.Security.AccessControl.GenericAce" />.Il metodo <see cref="M:System.Security.AccessControl.GenericAce.GetHashCode" /> può essere utilizzato in algoritmi di hash e strutture di dati, ad esempio una tabella hash.</summary>
      <returns>Codice hash per l'oggetto <see cref="T:System.Security.AccessControl.GenericAce" /> corrente.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.InheritanceFlags">
      <summary>Ottiene flag che specificano le proprietà di ereditarietà di questa ACE.</summary>
      <returns>Flag che specificano le proprietà di ereditarietà di questa ACE.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.IsInherited">
      <summary>Ottiene un valore Boolean che specifica se questa ACE viene ereditata oppure viene impostata in modo esplicito.</summary>
      <returns>true se l'ACE è ereditata. In caso contrario, false.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.op_Equality(System.Security.AccessControl.GenericAce,System.Security.AccessControl.GenericAce)">
      <summary>Determina se gli oggetti <see cref="T:System.Security.AccessControl.GenericAce" /> specificati sono considerati uguali.</summary>
      <returns>true se i due oggetti <see cref="T:System.Security.AccessControl.GenericAce" /> sono uguali; in caso contrario, false.</returns>
      <param name="left">Primo oggetto <see cref="T:System.Security.AccessControl.GenericAce" /> da confrontare.</param>
      <param name="right">Secondo oggetto <see cref="T:System.Security.AccessControl.GenericAce" /> da confrontare.</param>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.op_Inequality(System.Security.AccessControl.GenericAce,System.Security.AccessControl.GenericAce)">
      <summary>Determina se gli oggetti <see cref="T:System.Security.AccessControl.GenericAce" /> specificati sono considerati disuguali.</summary>
      <returns>true se i due oggetti <see cref="T:System.Security.AccessControl.GenericAce" /> sono disuguali. In caso contrario, false.</returns>
      <param name="left">Primo oggetto <see cref="T:System.Security.AccessControl.GenericAce" /> da confrontare.</param>
      <param name="right">Secondo oggetto <see cref="T:System.Security.AccessControl.GenericAce" /> da confrontare.</param>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.PropagationFlags">
      <summary>Ottiene flag che specificano le proprietà di propagazione dell'ereditarietà di questa ACE.</summary>
      <returns>Flag che specificano le proprietà di propagazione dell'ereditarietà di questa ACE.</returns>
    </member>
    <member name="T:System.Security.AccessControl.GenericAcl">
      <summary>Rappresenta un elenco di controllo di accesso (ACL) ed è la classe di base per le classi <see cref="T:System.Security.AccessControl.CommonAcl" />, <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />, <see cref="T:System.Security.AccessControl.RawAcl" /> e <see cref="T:System.Security.AccessControl.SystemAcl" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.GenericAcl" />.</summary>
    </member>
    <member name="F:System.Security.AccessControl.GenericAcl.AclRevision">
      <summary>Livello di revisione della classe <see cref="T:System.Security.AccessControl.GenericAcl" /> corrente.Questo valore viene restituito dalla proprietà <see cref="P:System.Security.AccessControl.GenericAcl.Revision" /> per le ACL non associate a oggetti di Servizi di directory.</summary>
    </member>
    <member name="F:System.Security.AccessControl.GenericAcl.AclRevisionDS">
      <summary>Livello di revisione della classe <see cref="T:System.Security.AccessControl.GenericAcl" /> corrente.Questo valore viene restituito dalla proprietà <see cref="P:System.Security.AccessControl.GenericAcl.Revision" /> per le ACL associate a oggetti di Servizi di directory.</summary>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.BinaryLength">
      <summary>Ottiene la lunghezza, in byte, della rappresentazione binaria dell'oggetto <see cref="T:System.Security.AccessControl.GenericAcl" /> corrente.Tale lunghezza deve essere utilizzata prima di eseguire il marshaling dell'ACL in una matrice binaria con il metodo <see cref="M:System.Security.AccessControl.GenericAcl.GetBinaryForm" />.</summary>
      <returns>Lunghezza, in byte, della rappresentazione binaria dell'oggetto <see cref="T:System.Security.AccessControl.GenericAcl" /> corrente.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.CopyTo(System.Security.AccessControl.GenericAce[],System.Int32)">
      <summary>Copia ciascuna classe <see cref="T:System.Security.AccessControl.GenericAce" /> della classe <see cref="T:System.Security.AccessControl.GenericAcl" /> nella matrice specificata.</summary>
      <param name="array">Matrice che contiene copie degli oggetti <see cref="T:System.Security.AccessControl.GenericAce" /> inclusi nella classe <see cref="T:System.Security.AccessControl.GenericAcl" /> corrente.</param>
      <param name="index">Indice in base zero di <paramref name="array" /> in cui ha inizio la copia.</param>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.Count">
      <summary>Ottiene il numero di voci di controllo di accesso (ACE) presenti nell'oggetto <see cref="T:System.Security.AccessControl.GenericAcl" /> corrente.</summary>
      <returns>Numero di voci ACE presenti nell'oggetto <see cref="T:System.Security.AccessControl.GenericAcl" /> corrente.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Esegue il marshalling dei contenuti dell'oggetto <see cref="T:System.Security.AccessControl.GenericAcl" /> nella matrice di byte specificata, in corrispondenza dell'offset specificato.</summary>
      <param name="binaryForm">Matrice di byte in cui i contenuti dell'oggetto <see cref="T:System.Security.AccessControl.GenericAcl" /> vengono sottoposti a marshaling.</param>
      <param name="offset">Offset in corrispondenza del quale viene avviato il marshalling.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> è negativo o troppo alto per consentire la copia dell'intero oggetto <see cref="T:System.Security.AccessControl.GenericAcl" /> in <paramref name="array" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.GetEnumerator">
      <summary>Restituisce una nuova istanza della classe <see cref="T:System.Security.AccessControl.AceEnumerator" />.</summary>
      <returns>Classe <see cref="T:Security.AccessControl.AceEnumerator" /> restituita da questo metodo.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.IsSynchronized">
      <summary>Questa proprietà è sempre impostata su false.È implementata solo perché è necessaria per l'implementazione dell'interfaccia <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Sempre false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.Item(System.Int32)">
      <summary>Ottiene o imposta <see cref="T:System.Security.AccessControl.GenericAce" /> in corrispondenza dell'indice specificato.</summary>
      <returns>
        <see cref="T:System.Security.AccessControl.GenericAce" /> in corrispondenza dell'indice specificato.</returns>
      <param name="index">Indice a base zero della classe <see cref="T:System.Security.AccessControl.GenericAce" /> da ottenere o impostare.</param>
    </member>
    <member name="F:System.Security.AccessControl.GenericAcl.MaxBinaryLength">
      <summary>Lunghezza binaria massima consentita di un oggetto <see cref="T:System.Security.AccessControl.GenericAcl" />.</summary>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.Revision">
      <summary>Ottiene il livello di revisione della classe <see cref="T:System.Security.AccessControl.GenericAcl" />.</summary>
      <returns>Valore di byte che specifica il livello di revisione della classe <see cref="T:System.Security.AccessControl.GenericAcl" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.SyncRoot">
      <summary>Questa proprietà restituisce sempre null.È implementata solo perché è necessaria per l'implementazione dell'interfaccia <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Restituisce sempre null.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copia ciascuna classe <see cref="T:System.Security.AccessControl.GenericAce" /> della classe <see cref="T:System.Security.AccessControl.GenericAcl" /> nella matrice specificata.</summary>
      <param name="array">Matrice che contiene copie degli oggetti <see cref="T:System.Security.AccessControl.GenericAce" /> inclusi nella classe <see cref="T:System.Security.AccessControl.GenericAcl" /> corrente.</param>
      <param name="index">Indice in base zero di <paramref name="array" /> in cui ha inizio la copia.</param>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.System#Collections#IEnumerable#GetEnumerator">
      <summary>Restituisce una nuova istanza della classe <see cref="T:System.Security.AccessControl.AceEnumerator" /> sottoposta a cast come istanza dell'interfaccia <see cref="T:System.Collections.IEnumerator" />.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Security.AccessControl.AceEnumerator" />, sottoposto a cast come istanza dell'interfaccia <see cref="T:System.Collections.IEnumerator" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.GenericSecurityDescriptor">
      <summary>Rappresenta un descrittore di sicurezza.Un descrittore di sicurezza include un proprietario, un gruppo primario, un elenco di controllo di accesso discrezionale (DACL, Discretionary Access Control List) e un elenco di controllo di accesso di sistema (SACL, System Access Control List).</summary>
    </member>
    <member name="M:System.Security.AccessControl.GenericSecurityDescriptor.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.GenericSecurity" />.</summary>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.BinaryLength">
      <summary>Ottiene la lunghezza in byte della rappresentazione binaria dell'oggetto <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> corrente.Tale lunghezza deve essere utilizzata prima di effettuare il marshalling dell'elenco ACL in una matrice binaria con il metodo <see cref="M:System.Security.AccessControl.GenericSecurityDescriptor.GetBinaryForm" />.</summary>
      <returns>Lunghezza in byte della rappresentazione binaria dell'oggetto <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> corrente.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.ControlFlags">
      <summary>Ottiene i valori che specificano il comportamento dell'oggetto <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</summary>
      <returns>Uno o più valori dell'enumerazione <see cref="T:System.Security.AccessControl.ControlFlags" /> combinati con un'operazione OR logica.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericSecurityDescriptor.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Restituisce una matrice di valori di byte che rappresenta le informazioni contenute nell'oggetto <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</summary>
      <param name="binaryForm">Matrice di byte in cui viene effettuato il marshalling del contenuto dell'oggetto <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</param>
      <param name="offset">Offset in corrispondenza del quale viene avviato il marshalling.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> è negativo o troppo alto per consentire la copia dell'intero oggetto <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> in <paramref name="array" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.GenericSecurityDescriptor.GetSddlForm(System.Security.AccessControl.AccessControlSections)">
      <summary>Restituisce la rappresentazione in formato SDDL (Security Descriptor Definition Language) delle sezioni specificate del descrittore di sicurezza rappresentato dall'oggetto <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</summary>
      <returns>Rappresentazione in formato SDDL delle sezioni specificate del descrittore di sicurezza associato all'oggetto <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</returns>
      <param name="includeSections">Specifica le sezioni (regole di accesso, regole di controllo, gruppo primario, proprietà) del descrittore di sicurezza da ottenere.</param>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.Group">
      <summary>Ottiene o imposta il gruppo primario dell'oggetto <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</summary>
      <returns>Gruppo primario dell'oggetto <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericSecurityDescriptor.IsSddlConversionSupported">
      <summary>Restituisce un valore Boolean che specifica se il descrittore di sicurezza associato all'oggetto <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> può essere convertito nel formato SDDL (Security Descriptor Definition Language).</summary>
      <returns>true se il descrittore di sicurezza associato all'oggetto <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> può essere convertito nel formato SDDL (Security Descriptor Definition Language); in caso contrario, false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.Owner">
      <summary>Ottiene o imposta il proprietario dell'oggetto associato all'oggetto <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</summary>
      <returns>Proprietario dell'oggetto associato all'oggetto <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.Revision">
      <summary>Ottiene il livello di revisione dell'oggetto <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</summary>
      <returns>Valore di byte che specifica il livello di revisione dell'oggetto <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.InheritanceFlags">
      <summary>I flag di ereditarietà specificano la semantica per le voci di controllo di accesso (ACE).</summary>
    </member>
    <member name="F:System.Security.AccessControl.InheritanceFlags.ContainerInherit">
      <summary>L'ACE viene ereditata dagli oggetti contenitore figlio.</summary>
    </member>
    <member name="F:System.Security.AccessControl.InheritanceFlags.None">
      <summary>L'ACE non viene ereditata dagli oggetti figlio.</summary>
    </member>
    <member name="F:System.Security.AccessControl.InheritanceFlags.ObjectInherit">
      <summary>L'ACE viene ereditata dagli oggetti foglia figlio.</summary>
    </member>
    <member name="T:System.Security.AccessControl.KnownAce">
      <summary>Incapsula tutti i tipi di voci di controllo di accesso (ACE, Access Control Entry) definiti da Microsoft.Tutti gli oggetti <see cref="T:System.Security.AccessControl.KnownAce" /> contengono una maschera di accesso a 32 bit e un oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" />.</summary>
    </member>
    <member name="P:System.Security.AccessControl.KnownAce.AccessMask">
      <summary>Ottiene o imposta la maschera di accesso dell'oggetto <see cref="T:System.Security.AccessControl.KnownAce" />.</summary>
      <returns>Maschera di accesso dell'oggetto <see cref="T:System.Security.AccessControl.KnownAce" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.KnownAce.SecurityIdentifier">
      <summary>Ottiene o imposta l'oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> associato all'oggetto <see cref="T:System.Security.AccessControl.KnownAce" />.</summary>
      <returns>Oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> associato a questo oggetto <see cref="T:System.Security.AccessControl.KnownAce" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.NativeObjectSecurity">
      <summary>Consente di controllare l'accesso agli oggetti nativi senza modifica diretta degli elenchi di controllo di accesso (ACL, Access Control List).I tipi di oggetti nativi sono definiti dall'enumerazione <see cref="T:System.Security.AccessControl.ResourceType" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> con i valori specificati.</summary>
      <param name="isContainer">true se il nuovo oggetto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> è un oggetto contenitore.</param>
      <param name="resourceType">Tipo di oggetto che può essere protetto al quale è associato il nuovo oggetto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> con i valori specificati.Si consiglia di utilizzare valori identici per i parametri <paramref name="includeSections" /> passati al costruttore e per i metodi Persist.Per ulteriori informazioni, vedere la sezione Osservazioni.</summary>
      <param name="isContainer">true se il nuovo oggetto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> è un oggetto contenitore.</param>
      <param name="resourceType">Tipo di oggetto che può essere protetto al quale è associato il nuovo oggetto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="handle">Handle dell'oggetto che può essere protetto al quale è associato il nuovo oggetto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="includeSections">Uno dei valori dell'enumerazione <see cref="T:System.Security.AccessControl.AccessControlSections" /> che specifica le sezioni del descrittore di sicurezza (regole di accesso, regole di controllo, proprietario, gruppo primario) dell'oggetto che può essere protetto da includere in questo oggetto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> con i valori specificati.Si consiglia di utilizzare valori identici per i parametri <paramref name="includeSections" /> passati al costruttore e per i metodi Persist.Per ulteriori informazioni, vedere la sezione Osservazioni.</summary>
      <param name="isContainer">true se il nuovo oggetto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> è un oggetto contenitore.</param>
      <param name="resourceType">Tipo di oggetto che può essere protetto al quale è associato il nuovo oggetto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="handle">Handle dell'oggetto che può essere protetto al quale è associato il nuovo oggetto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="includeSections">Uno dei valori dell'enumerazione <see cref="T:System.Security.AccessControl.AccessControlSections" /> che specifica le sezioni del descrittore di sicurezza (regole di accesso, regole di controllo, proprietario, gruppo primario) dell'oggetto che può essere protetto da includere in questo oggetto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="exceptionFromErrorCode">Delegato implementato da integratori che fornisce eccezioni personalizzate. </param>
      <param name="exceptionContext">Oggetto contenente informazioni contestuali sull'origine o sulla destinazione dell'eccezione.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> utilizzando i valori specificati.</summary>
      <param name="isContainer">true se il nuovo oggetto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> è un oggetto contenitore.</param>
      <param name="resourceType">Tipo di oggetto che può essere protetto al quale è associato il nuovo oggetto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="exceptionFromErrorCode">Delegato implementato da integratori che fornisce eccezioni personalizzate. </param>
      <param name="exceptionContext">Oggetto contenente informazioni contestuali sull'origine o sulla destinazione dell'eccezione.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> con i valori specificati.Si consiglia di utilizzare valori identici per i parametri <paramref name="includeSections" /> passati al costruttore e per i metodi Persist.Per ulteriori informazioni, vedere la sezione Osservazioni.</summary>
      <param name="isContainer">true se il nuovo oggetto <see cref="T:System.Security.AccessControl.NativObjectSecurity" /> è un oggetto contenitore.</param>
      <param name="resourceType">Tipo di oggetto che può essere protetto al quale è associato il nuovo oggetto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="name">Nome dell'oggetto che può essere protetto al quale è associato il nuovo oggetto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="includeSections">Uno dei valori dell'enumerazione <see cref="T:System.Security.AccessControl.AccessControlSections" /> che specifica le sezioni del descrittore di sicurezza (regole di accesso, regole di controllo, proprietario, gruppo primario) dell'oggetto che può essere protetto da includere in questo oggetto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.String,System.Security.AccessControl.AccessControlSections,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> con i valori specificati.Si consiglia di utilizzare valori identici per i parametri <paramref name="includeSections" /> passati al costruttore e per i metodi Persist.Per ulteriori informazioni, vedere la sezione Osservazioni.</summary>
      <param name="isContainer">true se il nuovo oggetto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> è un oggetto contenitore.</param>
      <param name="resourceType">Tipo di oggetto che può essere protetto al quale è associato il nuovo oggetto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="name">Nome dell'oggetto che può essere protetto al quale è associato il nuovo oggetto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="includeSections">Uno dei valori dell'enumerazione <see cref="T:System.Security.AccessControl.AccessControlSections" /> che specifica le sezioni del descrittore di sicurezza (regole di accesso, regole di controllo, proprietario, gruppo primario) dell'oggetto che può essere protetto da includere in questo oggetto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="exceptionFromErrorCode">Delegato implementato da integratori che fornisce eccezioni personalizzate. </param>
      <param name="exceptionContext">Oggetto contenente informazioni contestuali sull'origine o sulla destinazione dell'eccezione.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.Persist(System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections)">
      <summary>Consente di salvare in un archivio permanente le sezioni specificate del descrittore di sicurezza associato all'oggetto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.Si consiglia di utilizzare valori identici per i parametri <paramref name="includeSections" /> passati al costruttore e per i metodi Persist.Per ulteriori informazioni, vedere la sezione Osservazioni.</summary>
      <param name="handle">Handle dell'oggetto che può essere protetto al quale è associato l'oggetto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="includeSections">Uno dei valori dell'enumerazione <see cref="T:System.Security.AccessControl.AccessControlSections" /> che specifica le sezioni del descrittore di sicurezza (regole di accesso, regole di controllo, proprietario, gruppo primario) dell'oggetto che può essere protetto da salvare.</param>
      <exception cref="T:System.IO.FileNotFoundException">L'oggetto che può essere protetto e al quale è associato l'oggetto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> è una directory o un file che non è possibile trovare.</exception>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.Persist(System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections,System.Object)">
      <summary>Consente di salvare in un archivio permanente le sezioni specificate del descrittore di sicurezza associato all'oggetto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.Si consiglia di utilizzare valori identici per i parametri <paramref name="includeSections" /> passati al costruttore e per i metodi Persist.Per ulteriori informazioni, vedere la sezione Osservazioni.</summary>
      <param name="handle">Handle dell'oggetto che può essere protetto al quale è associato l'oggetto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="includeSections">Uno dei valori dell'enumerazione <see cref="T:System.Security.AccessControl.AccessControlSections" /> che specifica le sezioni del descrittore di sicurezza (regole di accesso, regole di controllo, proprietario, gruppo primario) dell'oggetto che può essere protetto da salvare.</param>
      <param name="exceptionContext">Oggetto contenente informazioni contestuali sull'origine o sulla destinazione dell'eccezione.</param>
      <exception cref="T:System.IO.FileNotFoundException">L'oggetto che può essere protetto e al quale è associato l'oggetto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> è una directory o un file che non è possibile trovare.</exception>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.Persist(System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>Consente di salvare in un archivio permanente le sezioni specificate del descrittore di sicurezza associato all'oggetto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.Si consiglia di utilizzare valori identici per i parametri <paramref name="includeSections" /> passati al costruttore e per i metodi Persist.Per ulteriori informazioni, vedere la sezione Osservazioni.</summary>
      <param name="name">Nome dell'oggetto che può essere protetto al quale è associato l'oggetto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="includeSections">Uno dei valori dell'enumerazione <see cref="T:System.Security.AccessControl.AccessControlSections" /> che specifica le sezioni del descrittore di sicurezza (regole di accesso, regole di controllo, proprietario, gruppo primario) dell'oggetto che può essere protetto da salvare.</param>
      <exception cref="T:System.IO.FileNotFoundException">L'oggetto che può essere protetto e al quale è associato l'oggetto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> è una directory o un file che non è possibile trovare.</exception>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.Persist(System.String,System.Security.AccessControl.AccessControlSections,System.Object)">
      <summary>Consente di salvare in un archivio permanente le sezioni specificate del descrittore di sicurezza associato all'oggetto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.Si consiglia di utilizzare valori identici per i parametri <paramref name="includeSections" /> passati al costruttore e per i metodi Persist.Per ulteriori informazioni, vedere la sezione Osservazioni.</summary>
      <param name="name">Nome dell'oggetto che può essere protetto al quale è associato l'oggetto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="includeSections">Uno dei valori dell'enumerazione <see cref="T:System.Security.AccessControl.AccessControlSections" /> che specifica le sezioni del descrittore di sicurezza (regole di accesso, regole di controllo, proprietario, gruppo primario) dell'oggetto che può essere protetto da salvare.</param>
      <param name="exceptionContext">Oggetto contenente informazioni contestuali sull'origine o sulla destinazione dell'eccezione.</param>
      <exception cref="T:System.IO.FileNotFoundException">L'oggetto che può essere protetto e al quale è associato l'oggetto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> è una directory o un file che non è possibile trovare.</exception>
    </member>
    <member name="T:System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode">
      <summary>Fornisce un metodo per gli integratori per il mapping dei codici di errore numerici a specifiche eccezioni create dagli integratori stessi.</summary>
      <returns>Classe <see cref="T:System.Exception" /> creata da questo delegato.</returns>
      <param name="errorCode">Codice di errore numerico.</param>
      <param name="name">Nome dell'oggetto da proteggere a cui è associato l'oggetto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="handle">Handle dell'oggetto da proteggere a cui è associato l'oggetto <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="context">Oggetto contenente informazioni contestuali sull'origine o sulla destinazione dell'eccezione.</param>
    </member>
    <member name="T:System.Security.AccessControl.ObjectAccessRule">
      <summary>Rappresenta una combinazione di identità di un utente, maschera di accesso e tipo di controllo di accesso (consenso o negazione).Un oggetto <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> contiene anche informazioni sul tipo di oggetto a cui la regola viene applicata, sul tipo di oggetto figlio che può ereditare la regola, su come la regola viene ereditata dagli oggetti figlio e su come l'ereditarietà viene propagata.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAccessRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Guid,System.Guid,System.Security.AccessControl.AccessControlType)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> con i valori specificati.</summary>
      <param name="identity">Identità a cui viene applicata la regola di accesso.  Deve essere un oggetto di cui è possibile eseguire il cast in <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="accessMask">Maschera di accesso della regola.La maschera di accesso è un insieme di bit anonimi a 32 bit, il cui significato è definito dai singoli integratori.</param>
      <param name="isInherited">true se la regola è ereditata da un contenitore padre.</param>
      <param name="inheritanceFlags">Specifica le proprietà di ereditarietà della regola di accesso.</param>
      <param name="propagationFlags">Specifica se le regole di accesso ereditate vengono propagate automaticamente.I flag di propagazione vengono ignorati se <paramref name="inheritanceFlags" /> è impostato su <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="objectType">Tipo di oggetto a cui viene applicata la regola.</param>
      <param name="inheritedObjectType">Tipo di oggetto figlio che può ereditare la regola.</param>
      <param name="type">Specifica se la regola concede o nega l'accesso.</param>
      <exception cref="T:System.ArgumentException">Non è possibile effettuare il cast del valore del parametro <paramref name="identity" /> in <see cref="T:System.Security.Principal.SecurityIdentifier" /> oppure il parametro <paramref name="type" /> contiene un valore non valido.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Il valore del parametro <paramref name="accessMask" /> è 0 oppure i parametri <paramref name="inheritanceFlags" /> o <paramref name="propagationFlags" /> contengono valori di flag non riconosciuti.</exception>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAccessRule.InheritedObjectType">
      <summary>Ottiene il tipo dell'oggetto figlio che può ereditare l'oggetto <see cref="System.Security.AccessControl.ObjectAccessRule" />.</summary>
      <returns>Tipo dell'oggetto figlio che può ereditare l'oggetto <see cref="System.Security.AccessControl.ObjectAccessRule" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAccessRule.ObjectFlags">
      <summary>Ottiene flag che specificano se le proprietà <see cref="P:System.Security.AccessControl.ObjectAccessRule.ObjectType" /> e <see cref="P:System.Security.AccessControl.ObjectAccessRule.InheritedObjectType" /> dell'oggetto <see cref="System.Security.AccessControl.ObjectAccessRule" /> contengono valori validi.</summary>
      <returns>
        <see cref="F:System.Security.AccessControl.ObjectAceFlags.ObjectAceTypePresent" /> specifica che la proprietà <see cref="P:System.Security.AccessControl.ObjectAccessRule.ObjectType" /> contiene un valore valido.<see cref="F:System.Security.AccessControl.ObjectAceFlags.InheritedObjectAceTypePresent" /> specifica che la proprietà <see cref="P:System.Security.AccessControl.ObjectAccessRule.InheritedObjectType" /> contiene un valore valido.Tali valori possono essere combinati con un'operazione OR logica.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAccessRule.ObjectType">
      <summary>Ottiene il tipo di oggetto a cui viene applicata la classe <see cref="System.Security.AccessControl.ObjectAccessRule" />.</summary>
      <returns>Tipo di oggetto a cui viene applicata la classe <see cref="System.Security.AccessControl.ObjectAccessRule" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.ObjectAce">
      <summary>Controlla l'accesso agli oggetti di Servizi di directory.Questa classe rappresenta una voce ACE (Access Control Entry, voce del controllo di accesso) associata a un oggetto di directory.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAce.#ctor(System.Security.AccessControl.AceFlags,System.Security.AccessControl.AceQualifier,System.Int32,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid,System.Boolean,System.Byte[])">
      <summary>Avvia una nuova istanza della classe <see cref="T:System.Security.AccessControl.ObjectAce" />.</summary>
      <param name="aceFlags">Condizioni per l'ereditarietà, la propagazione dell'ereditarietà e per il controllo relative alla nuova voce ACE.</param>
      <param name="qualifier">Utilizzo della nuova voce ACE.</param>
      <param name="accessMask">Maschera di accesso della voce ACE.</param>
      <param name="sid">Classe <see cref="T:System.Security.Principal.SecurityIdentifier" /> associata alla nuova voce ACE.</param>
      <param name="flags">Indica se i parametri <paramref name="type" /> e <paramref name="inheritedType" /> contengono GUID oggetto validi.</param>
      <param name="type">GUID che identifica il tipo di oggetto a cui la nuova ACE viene applicata.</param>
      <param name="inheritedType">GUID che identifica il tipo di oggetto che può ereditare la nuova ACE.</param>
      <param name="isCallback">true se la nuova ACE è un'ACE di tipo callback.</param>
      <param name="opaque">Dati opachi associati alla nuova voce ACE.Questi dati sono consentiti solo per ACE di tipo callback.La lunghezza di questa matrice non deve essere superiore al valore restituito del metodo <see cref="M:System.Security.AccessControl.ObjectAceMaxOpaqueLength" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Il parametro qualificatore contiene un valore non valido oppure la lunghezza del valore del parametro opaco è maggiore del valore restituito del metodo <see cref="M:System.Security.AccessControl.ObjectAceMaxOpaqueLength" />.</exception>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAce.BinaryLength">
      <summary>Ottiene la lunghezza, in byte, della rappresentazione binaria dell'oggetto <see cref="T:System.Security.AccessControl.ObjectAce" /> corrente.Questa lunghezza deve essere utilizzata prima di eseguire il marshaling dell'ACL in una matrice binaria con il metodo <see cref="M:System.Security.AccessControl.ObjectAce.GetBinaryForm" />.</summary>
      <returns>Lunghezza, in byte, della rappresentazione binaria dell'oggetto <see cref="T:System.Security.AccessControl.ObjectAce" /> corrente.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Esegue il marshaling dei contenuti dell'oggetto <see cref="T:System.Security.AccessControl.ObjectAce" /> nella matrice di byte specificata, in corrispondenza dell'offset specificato.</summary>
      <param name="binaryForm">Matrice di byte in cui i contenuti dell'oggetto <see cref="T:System.Security.AccessControl.ObjectAce" /> vengono sottoposti a marshaling.</param>
      <param name="offset">Offset in corrispondenza del quale viene avviato il marshalling.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> è negativo o troppo elevato per consentire la copia di tutto l'oggetto <see cref="T:System.Security.AccessControl.ObjectAce" /> in <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAce.InheritedObjectAceType">
      <summary>Ottiene o imposta il GUID del tipo di oggetto che può ereditare l'ACE rappresentata da questo oggetto <see cref="T:System.Security.AccessControl.ObjectAce" />.</summary>
      <returns>GUID del tipo di oggetto che può ereditare l'ACE rappresentata da questo oggetto <see cref="T:System.Security.AccessControl.ObjectAce" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAce.MaxOpaqueLength(System.Boolean)">
      <summary>Restituisce la lunghezza massima consentita, in byte, di un BLOB di dati opachi per ACE di callback.</summary>
      <returns>Lunghezza massima consentita, in byte, di un BLOB di dati opachi per ACE di callback.</returns>
      <param name="isCallback">True se l'oggetto <see cref="T:System.Security.AccessControl.ObjectAce" /> è un'ACE di callback.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAce.ObjectAceFlags">
      <summary>Ottiene o imposta flag che specificano se le proprietà <see cref="P:System.Security.AccessControl.ObjectAce.ObjectAceType" /> e <see cref="P:System.Security.AccessControl.ObjectAce.InheritedObjectAceType" /> contengono valori che identificano tipi di oggetti validi.</summary>
      <returns>Uno o più membri dell'enumerazione <see cref="T:System.Security.AccessControl.ObjectAceFlags" /> combinati con un'operazione OR logica.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAce.ObjectAceType">
      <summary>Ottiene o imposta il GUID del tipo di oggetto associato a questo oggetto <see cref="T:System.Security.AccessControl.ObjectAce" />.</summary>
      <returns>GUID del tipo di oggetto associato a questo oggetto <see cref="T:System.Security.AccessControl.ObjectAce" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.ObjectAceFlags">
      <summary>Indica la presenza di tipi di oggetti per le voci di controllo di accesso (ACE, Access Control Entry).</summary>
    </member>
    <member name="F:System.Security.AccessControl.ObjectAceFlags.InheritedObjectAceTypePresent">
      <summary>Tipo di oggetto che può ereditare la voce ACE.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ObjectAceFlags.None">
      <summary>Nessun tipo di oggetto presente.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ObjectAceFlags.ObjectAceTypePresent">
      <summary>È presente il tipo di oggetto associato alla voce ACE.</summary>
    </member>
    <member name="T:System.Security.AccessControl.ObjectAuditRule">
      <summary>Rappresenta una combinazione di identità di un utente, maschera di accesso e condizioni di controllo.Un oggetto <see cref="T:System.Security.AccessControl.ObjectAuditRule" /> contiene inoltre informazioni sul tipo di oggetto a cui viene applicata la regola, sul tipo di oggetto figlio che può ereditare la regola, su come la regola viene ereditata dagli oggetti figlio e su come viene propagata l'ereditarietà.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAuditRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Guid,System.Guid,System.Security.AccessControl.AuditFlags)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.ObjectAuditRule" />.</summary>
      <param name="identity">Identità a cui viene applicata la regola di accesso.  Deve essere un oggetto di cui è possibile eseguire il cast in <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="accessMask">Maschera di accesso della regola.La maschera di accesso è un insieme di bit anonimi a 32 bit, il cui significato è definito dai singoli integratori.</param>
      <param name="isInherited">true se la regola è ereditata da un contenitore padre.</param>
      <param name="inheritanceFlags">Specifica le proprietà di ereditarietà della regola di accesso.</param>
      <param name="propagationFlags">Specifica se le regole di accesso ereditate vengono propagate automaticamente.I flag di propagazione vengono ignorati se <paramref name="inheritanceFlags" /> è impostato su <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="objectType">Tipo di oggetto a cui viene applicata la regola.</param>
      <param name="inheritedObjectType">Tipo di oggetto figlio che può ereditare la regola.</param>
      <param name="auditFlags">Condizioni di controllo.</param>
      <exception cref="T:System.ArgumentException">Non è possibile effettuare il cast del valore del parametro <paramref name="identity" /> in <see cref="T:System.Security.Principal.SecurityIdentifier" /> oppure il parametro <paramref name="type" /> contiene un valore non valido.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Il valore del parametro <paramref name="accessMask" /> è 0 oppure i parametri <paramref name="inheritanceFlags" /> o <paramref name="propagationFlags" /> contengono valori di flag non riconosciuti.</exception>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAuditRule.InheritedObjectType">
      <summary>Ottiene il tipo di oggetto figlio che può ereditare l'oggetto <see cref="System.Security.AccessControl.ObjectAuditRule" />.</summary>
      <returns>Tipo di oggetto figlio che può ereditare l'oggetto <see cref="System.Security.AccessControl.ObjectAuditRule" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAuditRule.ObjectFlags">
      <summary>Le proprietà <see cref="P:System.Security.AccessControl.ObjectAuditRule.ObjectType" /> e <see cref="P:System.Security.AccessControl.ObjectAuditRule.InheritedObjectType" /> dell'oggetto <see cref="System.Security.AccessControl.ObjectAuditRule" /> contengono valori validi.</summary>
      <returns>
        <see cref="F:System.Security.AccessControl.ObjectAceFlags.ObjectAceTypePresent" /> specifica che la proprietà <see cref="P:System.Security.AccessControl.ObjectAuditRule.ObjectType" /> contiene un valore valido.<see cref="F:System.Security.AccessControl.ObjectAceFlags.InheritedObjectAceTypePresent" /> specifica che la proprietà <see cref="P:System.Security.AccessControl.ObjectAuditRule.InheritedObjectType" /> contiene un valore valido.Tali valori possono essere combinati con un'operazione OR logica.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAuditRule.ObjectType">
      <summary>Ottiene il tipo di oggetto a cui viene applicata la classe <see cref="System.Security.AccessControl.ObjectAuditRule" />.</summary>
      <returns>Tipo di oggetto a cui viene applicata la classe <see cref="System.Security.AccessControl.ObjectAuditRule" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.ObjectSecurity">
      <summary>Consente di controllare l'accesso agli oggetti senza modificare direttamente le ACL (Access Control List, elenco di controllo di accesso).Questa è la classe base astratta per le classi <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> e <see cref="T:System.Security.AccessControl.DirectoryObjectSecurity" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.#ctor(System.Boolean,System.Boolean)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <param name="isContainer">true se il nuovo oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> è un oggetto contenitore.</param>
      <param name="isDS">True se il nuovo oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> è un oggetto directory.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.#ctor(System.Security.AccessControl.CommonSecurityDescriptor)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <param name="securityDescriptor">Oggetto <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> della nuova istanza di <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AccessRightType">
      <summary>Ottiene la classe <see cref="T:System.Type" /> dell'oggetto a protezione diretta associato all'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>Tipo dell'oggetto a protezione diretta associato all'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.AccessRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.AccessRule" /> con i valori specificati.</summary>
      <returns>Oggetto <see cref="T:System.Security.AccessControl.AccessRule" /> creato da questo metodo.</returns>
      <param name="identityReference">Identità a cui viene applicata la regola di accesso.Deve essere un oggetto di cui è possibile eseguire il cast in <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="accessMask">Maschera di accesso della regola.La maschera di accesso è una raccolta di bit anonimi a 32 bit, il cui significato è definito dai singoli integratori.</param>
      <param name="isInherited">true se la regola è ereditata da un contenitore padre.</param>
      <param name="inheritanceFlags">Specifica le proprietà di ereditarietà della regola di accesso.</param>
      <param name="propagationFlags">Specifica se le regole di accesso ereditate vengono propagate automaticamente.I flag di propagazione vengono ignorati se <paramref name="inheritanceFlags" /> è impostato su <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="type">Specifica il tipo di controllo di accesso valido.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AccessRulesModified">
      <summary>Ottiene o imposta un valore booleano che specifica se le regole di accesso associate a questo oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> sono state modificate.</summary>
      <returns>true se le regole di accesso associate a questo oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> sono state modificate. In caso contrario, false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AccessRuleType">
      <summary>Ottiene la classe <see cref="T:System.Type" /> dell'oggetto associato alle regole di accesso dell'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.L'oggetto <see cref="T:System.Type" /> deve essere un oggetto di cui sia possibile eseguire il cast come oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" />.</summary>
      <returns>Tipo dell'oggetto associato alle regole di accesso dell'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AreAccessRulesCanonical">
      <summary>Ottiene un valore booleano che specifica se le regole di accesso associate a questo oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> sono in ordine canonico.</summary>
      <returns>true se le regole di accesso sono in ordine canonico. In caso contrario, false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AreAccessRulesProtected">
      <summary>Ottiene un valore booleano che specifica se l'elenco di controllo di accesso discrezionale (DACL, Discretionary Access Control List) associato all'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> è protetto.</summary>
      <returns>true se il DACL è protetto. In caso contrario, false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AreAuditRulesCanonical">
      <summary>Ottiene un valore booleano che specifica se le regole di controllo associate all'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> sono in ordine canonico.</summary>
      <returns>true se le regole di controllo sono in ordine canonico. In caso contrario, false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AreAuditRulesProtected">
      <summary>Ottiene un valore booleano che specifica se l'elenco di controllo di accesso di sistema (SACL, System Access Control List) associato all'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> è protetto.</summary>
      <returns>true se il SACL è protetto. In caso contrario, false.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.AuditRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.AuditRule" /> con i valori specificati.</summary>
      <returns>Oggetto <see cref="T:System.Security.AccessControl.AuditRule" /> creato da questo metodo.</returns>
      <param name="identityReference">Identità a cui viene applicata la regola di controllo.Deve essere un oggetto di cui è possibile eseguire il cast in <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="accessMask">Maschera di accesso della regola.La maschera di accesso è una raccolta di bit anonimi a 32 bit, il cui significato è definito dai singoli integratori.</param>
      <param name="isInherited">true se la regola è ereditata da un contenitore padre.</param>
      <param name="inheritanceFlags">Specifica le proprietà di ereditarietà della regola di controllo.</param>
      <param name="propagationFlags">Specifica se le regole di controllo ereditate vengono propagate automaticamente.I flag di propagazione vengono ignorati se <paramref name="inheritanceFlags" /> è impostato su <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="flags">Specifica le condizioni in base alle quali viene controllata la regola.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AuditRulesModified">
      <summary>Ottiene o imposta un valore booleano che specifica se le regole di controllo associate all'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> sono state modificate.</summary>
      <returns>true se le regole di controllo associate all'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> sono state modificate. In caso contrario, false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AuditRuleType">
      <summary>Ottiene l'oggetto <see cref="T:System.Type" /> associato alle regole di controllo dell'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.L'oggetto <see cref="T:System.Type" /> deve essere un oggetto di cui sia possibile eseguire il cast come oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" />.</summary>
      <returns>Tipo dell'oggetto associato alle regole di controllo dell'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.GetGroup(System.Type)">
      <summary>Ottiene il gruppo primario associato al proprietario specificato.</summary>
      <returns>Gruppo primario associato al proprietario specificato.</returns>
      <param name="targetType">Proprietario per cui ottenere il gruppo primario. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.GetOwner(System.Type)">
      <summary>Ottiene il proprietario associato al gruppo primario specificato.</summary>
      <returns>Proprietario associato al gruppo specificato.</returns>
      <param name="targetType">Gruppo primario per cui ottenere il proprietario.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.GetSecurityDescriptorBinaryForm">
      <summary>Restituisce una matrice di valori di byte che rappresenta le informazioni sul descrittore di sicurezza per l'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>Matrice di valori di byte che rappresenta le informazioni sul descrittore di sicurezza per l'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.Questo metodo restituisce null se nell'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> non sono presenti informazioni di sicurezza.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.GetSecurityDescriptorSddlForm(System.Security.AccessControl.AccessControlSections)">
      <summary>Restituisce la rappresentazione SDDL (Security Descriptor Definition Language) delle sezioni specificate del descrittore di sicurezza associato all'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>Rappresentazione SDDL delle sezioni specificate del descrittore di sicurezza associato all'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</returns>
      <param name="includeSections">Specifica le sezioni (regole di accesso, regole di controllo, gruppo primario, proprietà) del descrittore di sicurezza da ottenere.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.GroupModified">
      <summary>Ottiene o imposta un valore booleano che specifica se il gruppo associato all'oggetto a protezione diretta è stato modificato. </summary>
      <returns>true se il gruppo associato all'oggetto da proteggere è stato modificato. In caso contrario, false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.IsContainer">
      <summary>Ottiene un valore booleano che specifica se l'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> è un oggetto contenitore.</summary>
      <returns>true se l'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> è un oggetto contenitore. In caso contrario, false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.IsDS">
      <summary>Ottiene un valore booleano che specifica se l'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> è un oggetto directory.</summary>
      <returns>true se l'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> è un oggetto directory. In caso contrario, false.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.IsSddlConversionSupported">
      <summary>Restituisce un valore booleano che specifica se il descrittore di sicurezza associato all'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> può essere convertito nel formato SDDL (Security Descriptor Definition Language).</summary>
      <returns>true se il descrittore di sicurezza associato all'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> può essere convertito nel formato SDDL (Security Descriptor Definition Language); in caso contrario, false.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ModifyAccess(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AccessRule,System.Boolean@)">
      <summary>Applica la modifica specificata al DACL (Discretionary Access Control List, elenco di controllo di accesso discrezionale) associato all'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>true se la modifica dell'elenco DACL è riuscita. In caso contrario, false.</returns>
      <param name="modification">Modifica da applicare all'elenco DACL.</param>
      <param name="rule">Regola di accesso da modificare.</param>
      <param name="modified">true se la modifica dell'elenco DACL è riuscita. In caso contrario, false.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ModifyAccessRule(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AccessRule,System.Boolean@)">
      <summary>Applica la modifica specificata al DACL (Discretionary Access Control List, elenco di controllo di accesso discrezionale) associato all'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>true se la modifica dell'elenco DACL è riuscita. In caso contrario, false.</returns>
      <param name="modification">Modifica da applicare all'elenco DACL.</param>
      <param name="rule">Regola di accesso da modificare.</param>
      <param name="modified">true se la modifica dell'elenco DACL è riuscita. In caso contrario, false.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ModifyAudit(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AuditRule,System.Boolean@)">
      <summary>Applica la modifica specificata al SACL (System Access Control List, elenco di controllo di accesso di sistema) associato all'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>true se la modifica dell'elenco SACL è riuscita; in caso contrario, false.</returns>
      <param name="modification">Modifica da applicare all'elenco SACL.</param>
      <param name="rule">Regola di controllo da modificare.</param>
      <param name="modified">true se la modifica dell'elenco SACL è riuscita; in caso contrario, false.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ModifyAuditRule(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AuditRule,System.Boolean@)">
      <summary>Applica la modifica specificata al SACL (System Access Control List, elenco di controllo di accesso di sistema) associato all'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>true se la modifica dell'elenco SACL è riuscita; in caso contrario, false.</returns>
      <param name="modification">Modifica da applicare all'elenco SACL.</param>
      <param name="rule">Regola di controllo da modificare.</param>
      <param name="modified">true se la modifica dell'elenco SACL è riuscita; in caso contrario, false.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.OwnerModified">
      <summary>Ottiene o imposta un valore booleano che specifica se il proprietario dell'oggetto a protezione diretta è stato modificato.</summary>
      <returns>true se il proprietario dell'oggetto da proteggere è stato modificato. In caso contrario, false.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.Persist(System.Boolean,System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>Salva in un archivio permanente le sezioni specificate del descrittore di sicurezza associato all'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.Si consiglia di usare valori identici per i parametri <paramref name="includeSections" /> passati al costruttore e per i metodi Persist.Per altre informazioni, vedere la sezione Osservazioni.</summary>
      <param name="enableOwnershipPrivilege">true per abilitare il privilegio che consente al chiamante di acquisire la proprietà dell'oggetto.</param>
      <param name="name">Nome usato per recuperare le informazioni persistenti.</param>
      <param name="includeSections">Uno dei valori dell'enumerazione <see cref="T:System.Security.AccessControl.AccessControlSections" /> che specifica le sezioni del descrittore di sicurezza (regole di accesso, regole di controllo, proprietario, gruppo primario) dell'oggetto a protezione diretta da salvare.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.Persist(System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections)">
      <summary>Salva in un archivio permanente le sezioni specificate del descrittore di sicurezza associato all'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.Si consiglia di usare valori identici per i parametri <paramref name="includeSections" /> passati al costruttore e per i metodi Persist.Per altre informazioni, vedere la sezione Osservazioni.</summary>
      <param name="handle">Handle usato per recuperare le informazioni persistenti.</param>
      <param name="includeSections">Uno dei valori dell'enumerazione <see cref="T:System.Security.AccessControl.AccessControlSections" /> che specifica le sezioni del descrittore di sicurezza (regole di accesso, regole di controllo, proprietario, gruppo primario) dell'oggetto a protezione diretta da salvare.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.Persist(System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>Salva in un archivio permanente le sezioni specificate del descrittore di sicurezza associato all'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.Si consiglia di usare valori identici per i parametri <paramref name="includeSections" /> passati al costruttore e per i metodi Persist.Per altre informazioni, vedere la sezione Osservazioni.</summary>
      <param name="name">Nome usato per recuperare le informazioni persistenti.</param>
      <param name="includeSections">Uno dei valori dell'enumerazione <see cref="T:System.Security.AccessControl.AccessControlSections" /> che specifica le sezioni del descrittore di sicurezza (regole di accesso, regole di controllo, proprietario, gruppo primario) dell'oggetto a protezione diretta da salvare.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.PurgeAccessRules(System.Security.Principal.IdentityReference)">
      <summary>Rimuove tutte le regole di accesso associate all'oggetto <see cref="T:System.Security.Principal.IdentityReference" /> specificat0.</summary>
      <param name="identity">Oggetto <see cref="T:System.Security.Principal.IdentityReference" /> per cui rimuovere tutte le regole di accesso.</param>
      <exception cref="T:System.InvalidOperationException">Le regole di accesso non sono in ordine canonico.</exception>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.PurgeAuditRules(System.Security.Principal.IdentityReference)">
      <summary>Rimuove tutte le regole di controllo associate all'oggetto <see cref="T:System.Security.Principal.IdentityReference" /> specificato.</summary>
      <param name="identity">Oggetto <see cref="T:System.Security.Principal.IdentityReference" /> per cui rimuovere tutte le regole di controllo.</param>
      <exception cref="T:System.InvalidOperationException">Le regole di controllo non sono in ordine canonico.</exception>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ReadLock">
      <summary>Blocca l'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> per l'accesso in lettura.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ReadUnlock">
      <summary>Sblocca l'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> per l'accesso in lettura.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetAccessRuleProtection(System.Boolean,System.Boolean)">
      <summary>Imposta o rimuove la protezione per le regole di accesso associate all'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.Le regole di accesso protette non possono essere modificate da oggetti padre mediante l'ereditarietà.</summary>
      <param name="isProtected">true per proteggere le regole di accesso associate all'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> mediante l'ereditarietà; false per consentire l'ereditarietà.</param>
      <param name="preserveInheritance">true per conservare le regole di accesso ereditate; false per rimuovere le regole di accesso ereditate.Questo parametro viene ignorato se <paramref name="isProtected" /> è false.</param>
      <exception cref="T:System.InvalidOperationException">Questo metodo tenta di rimuovere le regole ereditate da un DACL (Discretionary Access Control List, elenco di controllo di accesso discrezionale) non canonico.</exception>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetAuditRuleProtection(System.Boolean,System.Boolean)">
      <summary>Imposta o rimuove la protezione per le regole di controllo associate all'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.Le regole di controllo protette non possono essere modificate da oggetti padre mediante l'ereditarietà.</summary>
      <param name="isProtected">true per proteggere le regole di controllo associate all'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> mediante l'ereditarietà; false per consentire l'ereditarietà.</param>
      <param name="preserveInheritance">true per conservare le regole di controllo ereditate; false per rimuovere le regole di controllo ereditate.Questo parametro viene ignorato se <paramref name="isProtected" /> è false.</param>
      <exception cref="T:System.InvalidOperationException">Questo metodo tenta di rimuovere le regole ereditate da un SACL (System Access Control List, elenco di controllo di accesso di sistema) non canonico.</exception>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetGroup(System.Security.Principal.IdentityReference)">
      <summary>Imposta il gruppo primario per il descrittore di sicurezza associato all'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <param name="identity">Gruppo primario da impostare.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetOwner(System.Security.Principal.IdentityReference)">
      <summary>Imposta il proprietario per il descrittore di sicurezza associato all'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <param name="identity">Proprietario da impostare.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetSecurityDescriptorBinaryForm(System.Byte[])">
      <summary>Imposta il descrittore di sicurezza per l'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> dalla matrice specificata di valori di byte.</summary>
      <param name="binaryForm">Matrice di byte da cui impostare il descrittore di sicurezza.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetSecurityDescriptorBinaryForm(System.Byte[],System.Security.AccessControl.AccessControlSections)">
      <summary>Imposta le sezioni specificate del descrittore di sicurezza per l'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> dalla matrice specificata di valori di byte.</summary>
      <param name="binaryForm">Matrice di byte da cui impostare il descrittore di sicurezza.</param>
      <param name="includeSections">Sezioni del descrittore di sicurezza da impostare (regole di accesso, regole di controllo, gruppo primario, proprietario).</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetSecurityDescriptorSddlForm(System.String)">
      <summary>Imposta il descrittore di sicurezza per l'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> dalla stringa SDDL (Security Descriptor Definition Language).</summary>
      <param name="sddlForm">Stringa SDDL da cui impostare il descrittore di sicurezza.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetSecurityDescriptorSddlForm(System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>Imposta le sezioni specificate del descrittore di sicurezza per l'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> dalla stringa SDDL (Security Descriptor Definition Language) specificata.</summary>
      <param name="sddlForm">Stringa SDDL da cui impostare il descrittore di sicurezza.</param>
      <param name="includeSections">Sezioni del descrittore di sicurezza da impostare (regole di accesso, regole di controllo, gruppo primario, proprietario).</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.WriteLock">
      <summary>Blocca l'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> per l'accesso in scrittura.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.WriteUnlock">
      <summary>Sblocca l'oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity" /> per l'accesso in scrittura.</summary>
    </member>
    <member name="T:System.Security.AccessControl.ObjectSecurity`1">
      <summary>Consente di controllare l'accesso agli oggetti senza modifica diretta degli elenchi di controllo di accesso (ACL, Access Control List); garantisce inoltre i diritti di accesso tipo-cast. </summary>
      <typeparam name="T">Diritti di accesso per l'oggetto.</typeparam>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType)">
      <summary>Inizializza una nuova istanza della classe ObjectSecurity`1.</summary>
      <param name="isContainer">true se il nuovo oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> è un oggetto contenitore.</param>
      <param name="resourceType">Tipo di risorsa.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections)">
      <summary>Inizializza una nuova istanza della classe ObjectSecurity`1.</summary>
      <param name="isContainer">true se il nuovo oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> è un oggetto contenitore.</param>
      <param name="resourceType">Tipo di risorsa.</param>
      <param name="safeHandle">Handle.</param>
      <param name="includeSections">Sezioni da includere.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>Inizializza una nuova istanza della classe ObjectSecurity`1.</summary>
      <param name="isContainer">true se il nuovo oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> è un oggetto contenitore.</param>
      <param name="resourceType">Tipo di risorsa.</param>
      <param name="safeHandle">Handle.</param>
      <param name="includeSections">Sezioni da includere.</param>
      <param name="exceptionFromErrorCode">Delegato implementato da integratori che fornisce eccezioni personalizzate.</param>
      <param name="exceptionContext">Oggetto contenente informazioni contestuali sull'origine o sulla destinazione dell'eccezione.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>Inizializza una nuova istanza della classe ObjectSecurity`1.</summary>
      <param name="isContainer">true se il nuovo oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> è un oggetto contenitore.</param>
      <param name="resourceType">Tipo di risorsa.</param>
      <param name="name">Nome dell'oggetto che può essere protetto al quale è associato il nuovo oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity`1" />.</param>
      <param name="includeSections">Sezioni da includere.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.String,System.Security.AccessControl.AccessControlSections,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>Inizializza una nuova istanza della classe ObjectSecurity`1.</summary>
      <param name="isContainer">true se il nuovo oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> è un oggetto contenitore.</param>
      <param name="resourceType">Tipo di risorsa.</param>
      <param name="name">Nome dell'oggetto che può essere protetto al quale è associato il nuovo oggetto <see cref="T:System.Security.AccessControl.ObjectSecurity`1" />.</param>
      <param name="includeSections">Sezioni da includere. </param>
      <param name="exceptionFromErrorCode">Delegato implementato da integratori che fornisce eccezioni personalizzate.</param>
      <param name="exceptionContext">Oggetto contenente informazioni contestuali sull'origine o sulla destinazione dell'eccezione.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity`1.AccessRightType">
      <summary>Ottiene il tipo dell'oggetto a protezione diretta associato all'oggetto ObjectSecurity`1.</summary>
      <returns>Tipo dell'oggetto da proteggere associato all'istanza corrente.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.AccessRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>Inizializza una nuova istanza della classe ObjectAccessRule che rappresenta una nuova regola di controllo di accesso per l'oggetto di sicurezza associato.</summary>
      <returns>Rappresenta una nuova regola di controllo di accesso per l'utente specificato, con i diritti di accesso, il controllo di accesso e i flag specificati.</returns>
      <param name="identityReference">Rappresenta un account utente.</param>
      <param name="accessMask">Tipo di accesso.</param>
      <param name="isInherited">true se la regola di accesso è ereditata; in caso contrario false.</param>
      <param name="inheritanceFlags">Specifica come propagare le maschere di accesso agli oggetti figlio.</param>
      <param name="propagationFlags">Specifica il modo in cui le voci di controllo di accesso (ACE, Access Control Entry) vengono propagate agli oggetti figlio.</param>
      <param name="type">Specifica se l'accesso è consentito o negato.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity`1.AccessRuleType">
      <summary>Ottiene il tipo dell'oggetto associato alle regole di accesso dell'oggetto ObjectSecurity`1. </summary>
      <returns>Tipo dell'oggetto associato alle regole di accesso dell'istanza corrente.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.AddAccessRule(System.Security.AccessControl.AccessRule{`0})">
      <summary>Aggiunge la regola di accesso specificata all'elenco DACL (Discretionary Access Control List) associato all'oggetto ObjectSecurity`1.</summary>
      <param name="rule">Le regole da aggiungere.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.AddAuditRule(System.Security.AccessControl.AuditRule{`0})">
      <summary>Aggiunge la regola di controllo specificata all'elenco SACL (System Access Control List) associato all'oggetto ObjectSecurity`1.</summary>
      <param name="rule">Regola di controllo da aggiungere.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.AuditRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.AuditRule" /> che rappresenta la regola di controllo specificata per l'utente specificato.</summary>
      <returns>Restituisce la regola di controllo specificata per l'utente specificato.</returns>
      <param name="identityReference">Rappresenta un account utente. </param>
      <param name="accessMask">Numero intero che specifica un tipo di accesso.</param>
      <param name="isInherited">true se la regola di accesso è ereditata; in caso contrario false.</param>
      <param name="inheritanceFlags">Specifica come propagare le maschere di accesso agli oggetti figlio.</param>
      <param name="propagationFlags">Specifica il modo in cui le voci di controllo di accesso (ACE, Access Control Entry) vengono propagate agli oggetti figlio.</param>
      <param name="flags">Descrive il tipo di controllo da eseguire.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity`1.AuditRuleType">
      <summary>Ottiene l'oggetto Type associato alle regole di controllo dell'oggetto ObjectSecurity`1.</summary>
      <returns>Oggetto Type associato alle regole di controllo dell'istanza corrente.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.Persist(System.Runtime.InteropServices.SafeHandle)">
      <summary>Consente di salvare in un archivio permanente il descrittore di sicurezza associato all'oggetto ObjectSecurity`1 utilizzando l'handle specificato.</summary>
      <param name="handle">Handle dell'oggetto che può essere protetto al quale è associato l'oggetto ObjectSecurity`1.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.Persist(System.String)">
      <summary>Consente di salvare in un archivio permanente il descrittore di sicurezza associato all'oggetto ObjectSecurity`1 utilizzando il nome specificato.</summary>
      <param name="name">Nome dell'oggetto che può essere protetto al quale è associato l'oggetto ObjectSecurity`1.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAccessRule(System.Security.AccessControl.AccessRule{`0})">
      <summary>Rimuove le regole di accesso contenenti lo stesso ID di sicurezza e la stessa maschera di accesso della regola di accesso specificata dall'elenco di controllo di accesso discrezionale (DACL) associato all'oggetto ObjectSecurity`1.</summary>
      <returns>Restituisce true se la rimozione della regola di accesso è riuscita; in caso contrario, false.</returns>
      <param name="rule">Il ruolo da rimuovere.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAccessRuleAll(System.Security.AccessControl.AccessRule{`0})">
      <summary>Rimuove tutte le regole di accesso che dispongono dello stesso identificatore di sicurezza e della stessa regola di accesso specificata dall'elenco DACL (Discretionary Access Control List) associato all'oggetto ObjectSecurity`1.</summary>
      <param name="rule">Regola di accesso da rimuovere.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAccessRuleSpecific(System.Security.AccessControl.AccessRule{`0})">
      <summary>Rimuove tutte le regole di accesso che corrispondono esattamente alla regola di accesso specificata dall'elenco DACL (Discretionary Access Control List) associato all'oggetto ObjectSecurity`1.</summary>
      <param name="rule">Regola di accesso da rimuovere.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAuditRule(System.Security.AccessControl.AuditRule{`0})">
      <summary>Rimuove le regole di controllo contenenti lo stesso identificatore di sicurezza e la stessa maschera di accesso della regola di controllo specificata dall'elenco SACL (System Access Control List) associato all'oggetto ObjectSecurity`1.</summary>
      <returns>Restituisce true se l'oggetto è stato rimosso; in caso contrario, false.</returns>
      <param name="rule">Regola di controllo da rimuovere</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAuditRuleAll(System.Security.AccessControl.AuditRule{`0})">
      <summary>Rimuove tutte le regole di controllo che dispongono dello stesso ID di sicurezza e della stessa regola di controllo specificata dall'elenco di controllo di accesso di sistema (SACL) associato all'oggetto ObjectSecurity`1.</summary>
      <param name="rule">Regola di controllo da rimuovere.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAuditRuleSpecific(System.Security.AccessControl.AuditRule{`0})">
      <summary>Rimuove tutte le regole di controllo che corrispondono esattamente alla regola di controllo specificata dall'elenco di controllo di accesso di sistema (SACL) associato all'oggetto ObjectSecurity`1.</summary>
      <param name="rule">Regola di controllo da rimuovere.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.ResetAccessRule(System.Security.AccessControl.AccessRule{`0})">
      <summary>Rimuove tutte le regole di accesso nell'elenco di controllo di accesso discrezionale (DACL) associato all'oggetto ObjectSecurity`1, quindi aggiunge la regola di accesso specificata.</summary>
      <param name="rule">Regola di accesso da ripristinare.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.SetAccessRule(System.Security.AccessControl.AccessRule{`0})">
      <summary>Rimuove tutte le regole di accesso contenenti lo stesso identificatore e qualificatore di sicurezza della regola di accesso specificata nell'elenco DACL (Discretionary Access Control List) associato all'oggetto ObjectSecurity`1, quindi aggiunge la regola di accesso specificata.</summary>
      <param name="rule">Regola di accesso da impostare.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.SetAuditRule(System.Security.AccessControl.AuditRule{`0})">
      <summary>Rimuove tutte le regole di controllo contenenti lo stesso identificatore e qualificatore di sicurezza della regola di controllo specificata nell'elenco SACL (System Access Control List) associato all'oggetto ObjectSecurity`1, quindi aggiunge la regola di controllo specificata.</summary>
      <param name="rule">Regola di controllo da impostare.</param>
    </member>
    <member name="T:System.Security.AccessControl.PrivilegeNotHeldException">
      <summary>Eccezione generata quando un metodo nello spazio dei nomi <see cref="N:System.Security.AccessControl" /> tenta di abilitare un privilegio di cui non dispone.</summary>
    </member>
    <member name="M:System.Security.AccessControl.PrivilegeNotHeldException.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.PrivilegeNotHeldException" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.PrivilegeNotHeldException.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.PrivilegeNotHeldException" /> con il privilegio specificato.</summary>
      <param name="privilege">Privilegio che non è stato abilitato.</param>
    </member>
    <member name="M:System.Security.AccessControl.PrivilegeNotHeldException.#ctor(System.String,System.Exception)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.PrivilegeNotHeldException" /> con l'eccezione specificata.</summary>
      <param name="privilege">Privilegio che non è stato abilitato.</param>
      <param name="inner">Eccezione causa dell'eccezione corrente.Se il parametro <paramref name="innerException" /> non è un riferimento null, Nothing in Visual Basic, l'eccezione corrente viene generata in un blocco catch che gestisce l'eccezione interna.</param>
    </member>
    <member name="P:System.Security.AccessControl.PrivilegeNotHeldException.PrivilegeName">
      <summary>Ottiene il nome del privilegio che non è stato abilitato.</summary>
      <returns>Nome del privilegio che il metodo non è riuscito ad abilitare.</returns>
    </member>
    <member name="T:System.Security.AccessControl.PropagationFlags">
      <summary>Specifica il modo in cui le voci di controllo di accesso (ACE, Access Control Entry) vengono propagate agli oggetti figlio.  Questi flag sono significativi solo se sono presenti flag di ereditarietà. </summary>
    </member>
    <member name="F:System.Security.AccessControl.PropagationFlags.InheritOnly">
      <summary>Specifica che la voce ACE viene propagata solo agli oggetti figlio.Sono inclusi gli oggetti contenitore e gli oggetti figlio foglia.</summary>
    </member>
    <member name="F:System.Security.AccessControl.PropagationFlags.None">
      <summary>Specifica che non è impostato alcun flag di ereditarietà.</summary>
    </member>
    <member name="F:System.Security.AccessControl.PropagationFlags.NoPropagateInherit">
      <summary>Specifica che la voce ACE non viene propagata agli oggetti figlio.</summary>
    </member>
    <member name="T:System.Security.AccessControl.QualifiedAce">
      <summary>Rappresenta una ACE (Access Control Entry, voce di controllo di accesso) che contiene un qualificatore.Il qualificatore, rappresentato da un oggetto <see cref="T:System.Security.AccessControl.AceQualifier" />, specifica se l'ACE concede l'accesso, nega l'accesso, determina controlli di sistema o provoca allarmi di sistema.La classe <see cref="T:System.Security.AccessControl.QualifiedAce" /> è la classe di base astratta per le classi <see cref="T:System.Security.AccessControl.CommonAce" /> e <see cref="T:System.Security.AccessControl.ObjectAce" />.</summary>
    </member>
    <member name="P:System.Security.AccessControl.QualifiedAce.AceQualifier">
      <summary>Ottiene un valore che specifica se l'ACE concede l'accesso, nega l'accesso, determina controlli di sistema o provoca allarmi di sistema.</summary>
      <returns>Valore che specifica se l'ACE concede l'accesso, nega l'accesso, determina controlli di sistema o provoca allarmi di sistema.</returns>
    </member>
    <member name="M:System.Security.AccessControl.QualifiedAce.GetOpaque">
      <summary>Restituisce i dati di callback opachi associati a questo oggetto <see cref="T:System.Security.AccessControl.QualifiedAce" />. </summary>
      <returns>Matrice di valori di byte che rappresenta i dati di callback opachi associati a questo oggetto <see cref="T:System.Security.AccessControl.QualifiedAce" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.QualifiedAce.IsCallback">
      <summary>Specifica se questo oggetto <see cref="T:System.Security.AccessControl.QualifiedAce" /> contiene dati di callback.</summary>
      <returns>true se l'oggetto <see cref="T:System.Security.AccessControl.QualifiedAce" /> contiene dati di callback. In caso contrario, false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.QualifiedAce.OpaqueLength">
      <summary>Ottiene la lunghezza dei dati di callback opachi associati a questo oggetto <see cref="T:System.Security.AccessControl.QualifiedAce" />.Questa proprietà è valida solo per ACE di callback.</summary>
      <returns>Lunghezza dei dati di callback opachi.</returns>
    </member>
    <member name="M:System.Security.AccessControl.QualifiedAce.SetOpaque(System.Byte[])">
      <summary>Imposta i dati di callback opachi associati a questo oggetto <see cref="T:System.Security.AccessControl.QualifiedAce" />.</summary>
      <param name="opaque">Matrice di valori di byte che rappresenta i dati di callback opachi per questo oggetto <see cref="T:System.Security.AccessControl.QualifiedAce" />.</param>
    </member>
    <member name="T:System.Security.AccessControl.RawAcl">
      <summary>Rappresenta un elenco di controllo di accesso (ACL, Access Control List).</summary>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.#ctor(System.Byte,System.Int32)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.RawAcl" /> con il livello di revisione specificato.</summary>
      <param name="revision">Livello di revisione del nuovo elenco ACL.</param>
      <param name="capacity">Numero di voci ACE che l'oggetto <see cref="T:System.Security.AccessControl.RawAcl" /> è in grado di contenere.Questo numero deve essere utilizzato solo come suggerimento.</param>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.#ctor(System.Byte[],System.Int32)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.RawAcl" /> dal formato binario specificato.</summary>
      <param name="binaryForm">Matrice di valori di byte che rappresentano un elenco ACL.</param>
      <param name="offset">Offset nel parametro <paramref name="binaryForm" /> in corrispondenza del quale verrà effettuato l'unmarshalling dei dati.</param>
    </member>
    <member name="P:System.Security.AccessControl.RawAcl.BinaryLength">
      <summary>Ottiene la lunghezza in byte della rappresentazione binaria dell'oggetto <see cref="T:System.Security.AccessControl.RawAcl" /> corrente.Tale lunghezza deve essere utilizzata prima di effettuare il marshalling dell'elenco ACL in una matrice binaria con il metodo <see cref="M:System.Security.AccessControl.RawAcl.GetBinaryForm" />.</summary>
      <returns>Lunghezza in byte della rappresentazione binaria dell'oggetto <see cref="T:System.Security.AccessControl.RawAcl" /> corrente.</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawAcl.Count">
      <summary>Ottiene il numero di voci di controllo di accesso (ACE) presenti nell'oggetto <see cref="T:System.Security.AccessControl.RawAcl" /> corrente.</summary>
      <returns>Numero di voci ACE presenti nell'oggetto <see cref="T:System.Security.AccessControl.RawAcl" /> corrente.</returns>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Effettua il marshalling del contenuto dell'oggetto <see cref="T:System.Security.AccessControl.RawAcl" /> nella matrice di byte specificata, in corrispondenza dell'offset specificato.</summary>
      <param name="binaryForm">Matrice di byte in cui viene effettuato il marshalling del contenuto dell'oggetto <see cref="T:System.Security.AccessControl.RawAcl" />.</param>
      <param name="offset">Offset in corrispondenza del quale viene avviato il marshalling.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> è negativo o troppo alto per consentire la copia dell'intero oggetto <see cref="T:System.Security.AccessControl.RawAcl" /> in <paramref name="array" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.InsertAce(System.Int32,System.Security.AccessControl.GenericAce)">
      <summary>Inserisce la voce ACE specificata in corrispondenza dell'indice specificato.</summary>
      <param name="index">Posizione in cui verrà aggiunta la nuova voce ACE.Specificare il valore della proprietà <see cref="P:System.Security.AccessControl.RawAcl.Count" /> per inserire una voce ACE alla fine dell'oggetto <see cref="T:System.Security.AccessControl.RawAcl" />.</param>
      <param name="ace">Voce ACE da inserire.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> è negativo o troppo alto per consentire la copia dell'intero oggetto <see cref="T:System.Security.AccessControl.GenericAcl" /> in <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Security.AccessControl.RawAcl.Item(System.Int32)">
      <summary>Ottiene o imposta la voce ACE (Access Control Entry) in corrispondenza dell'indice specificato.</summary>
      <returns>Voce ACE in corrispondenza dell'indice specificato.</returns>
      <param name="index">Indice in base zero della voce ACE da ottenere o impostare.</param>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.RemoveAce(System.Int32)">
      <summary>Rimuove la voce ACE in corrispondenza dell'indice specificato.</summary>
      <param name="index">Indice in base zero della voce ACE da rimuovere.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Il valore del parametro <paramref name="index" /> è più elevato del valore della proprietà <see cref="P:System.Security.AccessControl.RawAcl.Count" /> meno uno oppure è negativo.</exception>
    </member>
    <member name="P:System.Security.AccessControl.RawAcl.Revision">
      <summary>Ottiene il livello di revisione dell'oggetto <see cref="T:System.Security.AccessControl.RawAcl" />.</summary>
      <returns>Valore di byte che specifica il livello di revisione dell'oggetto <see cref="T:System.Security.AccessControl.RawAcl" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.RawSecurityDescriptor">
      <summary>Rappresenta un descrittore di sicurezza.Un descrittore di sicurezza include un proprietario, un gruppo primario, un elenco di controllo di accesso discrezionale (DACL, Discretionary Access Control List) e un elenco di controllo di accesso di sistema (SACL, System Access Control List).</summary>
    </member>
    <member name="M:System.Security.AccessControl.RawSecurityDescriptor.#ctor(System.Byte[],System.Int32)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> dalla matrice di valori di byte specificata.</summary>
      <param name="binaryForm">Matrice di valori di byte da cui creare il nuovo oggetto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</param>
      <param name="offset">Offset nella matrice <paramref name="binaryForm" /> in corrispondenza del quale verrà iniziata la copia.</param>
    </member>
    <member name="M:System.Security.AccessControl.RawSecurityDescriptor.#ctor(System.Security.AccessControl.ControlFlags,System.Security.Principal.SecurityIdentifier,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.RawAcl,System.Security.AccessControl.RawAcl)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> con i valori specificati.</summary>
      <param name="flags">Flag che specificano il comportamento del nuovo oggetto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</param>
      <param name="owner">Proprietario del nuovo oggetto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</param>
      <param name="group">Gruppo primario del nuovo oggetto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</param>
      <param name="systemAcl">Elenco SACL (System Access Control List) del nuovo oggetto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</param>
      <param name="discretionaryAcl">Elenco di controllo di accesso discrezionale (DACL) del nuovo oggetto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</param>
    </member>
    <member name="M:System.Security.AccessControl.RawSecurityDescriptor.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> dalla stringa SDDL (Security Descriptor Definition Language) specificata.</summary>
      <param name="sddlForm">Stringa SDDL da cui creare il nuovo oggetto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</param>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.ControlFlags">
      <summary>Ottiene i valori che specificano il comportamento dell'oggetto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</summary>
      <returns>Uno o più valori dell'enumerazione <see cref="T:System.Security.AccessControl.ControlFlags" /> combinati con un'operazione OR logica.</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.DiscretionaryAcl">
      <summary>Ottiene o imposta l'elenco DACL (Discretionary Access Control List) dell'oggetto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.L'elenco DACL contiene le regole di accesso.</summary>
      <returns>Elenco DACL dell'oggetto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.Group">
      <summary>Ottiene o imposta il gruppo primario dell'oggetto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</summary>
      <returns>Gruppo primario dell'oggetto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.Owner">
      <summary>Ottiene o imposta il proprietario dell'oggetto associato all'oggetto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</summary>
      <returns>Proprietario dell'oggetto associato all'oggetto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.ResourceManagerControl">
      <summary>Ottiene o imposta un valore di byte che rappresenta i bit di controllo del gestore delle risorse associati all'oggetto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</summary>
      <returns>Valore di byte che rappresenta i bit di controllo del gestore delle risorse associati all'oggetto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.RawSecurityDescriptor.SetFlags(System.Security.AccessControl.ControlFlags)">
      <summary>Imposta la proprietà <see cref="P:System.Security.AccessControl.RawSecurityDescriptor.ControlFlags" /> dell'oggetto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> sul valore specificato.</summary>
      <param name="flags">Uno o più valori dell'enumerazione <see cref="T:System.Security.AccessControl.ControlFlags" /> combinati con un'operazione OR logica.</param>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.SystemAcl">
      <summary>Ottiene o imposta l'elenco SACL (System Access Control List) dell'oggetto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.L'elenco SACL contiene le regole di controllo.</summary>
      <returns>Elenco SACL dell'oggetto <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.ResourceType">
      <summary>Specifica i tipi di oggetti nativi definiti.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.DSObject">
      <summary>Oggetto di Servizi di directory (DS) o set di proprietà o proprietà di un oggetto di Servizi di directory.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.DSObjectAll">
      <summary>Oggetto di Servizi di directory e tutti i relativi gruppi di proprietà e tutte le proprietà.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.FileObject">
      <summary>File o directory.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.KernelObject">
      <summary>Oggetto kernel locale.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.LMShare">
      <summary>Condivisione di rete.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.Printer">
      <summary>Stampante.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.ProviderDefined">
      <summary>Oggetto definito da un provider.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.RegistryKey">
      <summary>Chiave del Registro di sistema.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.RegistryWow6432Key">
      <summary>Oggetto relativo a una voce del Registro di sistema in WOW64.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.Service">
      <summary>Servizio Windows.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.Unknown">
      <summary>Tipo di oggetto sconosciuto.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.WindowObject">
      <summary>Oggetto finestra o desktop sul computer locale.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.WmiGuidObject">
      <summary>Oggetto WMI (Windows Management Instrumentation).</summary>
    </member>
    <member name="T:System.Security.AccessControl.SecurityInfos">
      <summary>Specifica la sezione di un descrittore di sicurezza su cui eseguire una query o da impostare.</summary>
    </member>
    <member name="F:System.Security.AccessControl.SecurityInfos.DiscretionaryAcl">
      <summary>Specifica l'elenco di controllo di accesso discrezionale (DACL).</summary>
    </member>
    <member name="F:System.Security.AccessControl.SecurityInfos.Group">
      <summary>Specifica l'identificatore di gruppo primario.</summary>
    </member>
    <member name="F:System.Security.AccessControl.SecurityInfos.Owner">
      <summary>Specifica l'identificatore di proprietario.</summary>
    </member>
    <member name="F:System.Security.AccessControl.SecurityInfos.SystemAcl">
      <summary>Specifica l'elenco di controllo di accesso di sistema (SACL).</summary>
    </member>
    <member name="T:System.Security.AccessControl.SystemAcl">
      <summary>Rappresenta un SACL (System Access Control List, elenco di controllo di accesso di sistema).</summary>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.#ctor(System.Boolean,System.Boolean,System.Byte,System.Int32)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.SystemAcl" /> con i valori specificati.</summary>
      <param name="isContainer">true se il nuovo oggetto <see cref="T:System.Security.AccessControl.SystemAcl" /> è un contenitore.</param>
      <param name="isDS">true se il nuovo oggetto <see cref="T:System.Security.AccessControl.SystemAcl" /> è un oggetto di directory ACL (Access Control List, elenco di controllo di accesso).</param>
      <param name="revision">Livello di revisione del nuovo oggetto <see cref="T:System.Security.AccessControl.SystemAcl" />.</param>
      <param name="capacity">Numero di ACE (Access Control Entries, voci di controllo di accesso) che l'oggetto <see cref="T:System.Security.AccessControl.SystemAcl" /> può contenere.Questo numero deve essere usato solo come suggerimento.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.#ctor(System.Boolean,System.Boolean,System.Int32)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.SystemAcl" /> con i valori specificati.</summary>
      <param name="isContainer">true se il nuovo oggetto <see cref="T:System.Security.AccessControl.SystemAcl" /> è un contenitore.</param>
      <param name="isDS">true se il nuovo oggetto <see cref="T:System.Security.AccessControl.SystemAcl" /> è un oggetto di directory ACL (Access Control List, elenco di controllo di accesso).</param>
      <param name="capacity">Numero di ACE (Access Control Entries, voci di controllo di accesso) che l'oggetto <see cref="T:System.Security.AccessControl.SystemAcl" /> può contenere.Questo numero deve essere usato solo come suggerimento.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.#ctor(System.Boolean,System.Boolean,System.Security.AccessControl.RawAcl)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.AccessControl.SystemAcl" /> con i valori specificati dell'oggetto <see cref="T:System.Security.AccessControl.RawAcl" /> specificato.</summary>
      <param name="isContainer">true se il nuovo oggetto <see cref="T:System.Security.AccessControl.SystemAcl" /> è un contenitore.</param>
      <param name="isDS">true se il nuovo oggetto <see cref="T:System.Security.AccessControl.SystemAcl" /> è un oggetto di directory ACL (Access Control List, elenco di controllo di accesso).</param>
      <param name="rawAcl">Oggetto <see cref="T:System.Security.AccessControl.RawAcl" /> sottostante per il nuovo oggetto <see cref="T:System.Security.AccessControl.SystemAcl" />.Specificare null per creare un elenco ACL vuoto.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.AddAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Aggiunge una regola di controllo all'oggetto <see cref="T:System.Security.AccessControl.SystemAcl" /> corrente.</summary>
      <param name="auditFlags">Tipo di regola di controllo da aggiungere.</param>
      <param name="sid">Oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> per cui aggiungere una regola di controllo.</param>
      <param name="accessMask">Maschera di accesso per la nuova regola di controllo.</param>
      <param name="inheritanceFlags">Flag che specificano le proprietà di ereditarietà della nuova regola di controllo.</param>
      <param name="propagationFlags">Flag che specificano le proprietà di propagazione dell'ereditarietà della nuova regola di controllo.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.AddAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Aggiunge una regola di controllo con le impostazioni specificate all'oggetto <see cref="T:System.Security.AccessControl.SystemAcl" /> corrente.Usare questo metodo per l'oggetto di directory ACL quando si specifica il tipo di oggetto o il tipo di oggetto ereditato per la nuova regola di controllo.</summary>
      <param name="auditFlags">Tipo di regola di controllo da aggiungere.</param>
      <param name="sid">Oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> per cui aggiungere una regola di controllo.</param>
      <param name="accessMask">Maschera di accesso per la nuova regola di controllo.</param>
      <param name="inheritanceFlags">Flag che specificano le proprietà di ereditarietà della nuova regola di controllo.</param>
      <param name="propagationFlags">Flag che specificano le proprietà di propagazione dell'ereditarietà della nuova regola di controllo.</param>
      <param name="objectFlags">Flag che specificano se i parametri <paramref name="objectType" /> e <paramref name="inheritedObjectType" /> contengono valori non null.</param>
      <param name="objectType">Identità della classe di oggetti a cui viene applicata la nuova regola di controllo.</param>
      <param name="inheritedObjectType">Identità della classe di oggetti figlio che possono ereditare la nuova regola di controllo.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.AddAudit(System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAuditRule)">
      <summary>Aggiunge una regola di controllo all'oggetto <see cref="T:System.Security.AccessControl.SystemAcl" /> corrente.</summary>
      <param name="sid">Oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> per cui aggiungere una regola di controllo.</param>
      <param name="rule">Oggetto <see cref="T:System.Security.AccessControl.ObjectAuditRule" /> per la nuova regola di controllo.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Rimuove la regola di controllo specificata dall'oggetto <see cref="T:System.Security.AccessControl.SystemAcl" /> corrente.</summary>
      <returns>true se il metodo rimuove la regola di controllo specificata. In caso contrario, false.</returns>
      <param name="auditFlags">Tipo di regola di controllo da rimuovere.</param>
      <param name="sid">Classe <see cref="T:System.Security.Principal.SecurityIdentifier" /> per cui rimuovere una regola di controllo.</param>
      <param name="accessMask">Maschera di accesso per la regola da rimuovere.</param>
      <param name="inheritanceFlags">Flag che specificano le proprietà di ereditarietà della regola da rimuovere.</param>
      <param name="propagationFlags">Flag che specificano le proprietà di propagazione dell'ereditarietà per la regola da rimuovere.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Rimuove la regola di controllo specificata dall'oggetto <see cref="T:System.Security.AccessControl.SystemAcl" /> corrente.Usare questo metodo per gli elenchi di controllo di accesso (ACL) di oggetti directory quando si specifica il tipo di oggetto o il tipo di oggetto ereditato.</summary>
      <returns>true se il metodo rimuove la regola di controllo specificata. In caso contrario, false.</returns>
      <param name="auditFlags">Tipo di regola di controllo da rimuovere.</param>
      <param name="sid">Classe <see cref="T:System.Security.Principal.SecurityIdentifier" /> per cui rimuovere una regola di controllo.</param>
      <param name="accessMask">Maschera di accesso per la regola da rimuovere.</param>
      <param name="inheritanceFlags">Flag che specificano le proprietà di ereditarietà della regola da rimuovere.</param>
      <param name="propagationFlags">Flag che specificano le proprietà di propagazione dell'ereditarietà per la regola da rimuovere.</param>
      <param name="objectFlags">Flag che specificano se i parametri <paramref name="objectType" /> e <paramref name="inheritedObjectType" /> contengono valori non null.</param>
      <param name="objectType">Identità della classe di oggetti a cui la regola di controllo rimossa viene applicata.</param>
      <param name="inheritedObjectType">Identità della classe di oggetti figlio che può ereditare la regola di controllo rimossa.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAudit(System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAuditRule)">
      <summary>Rimuove la regola di controllo specificata dall'oggetto <see cref="T:System.Security.AccessControl.SystemAcl" /> corrente.</summary>
      <returns>true se il metodo rimuove la regola di controllo specificata. In caso contrario, false.</returns>
      <param name="sid">Classe <see cref="T:System.Security.Principal.SecurityIdentifier" /> per cui rimuovere una regola di controllo.</param>
      <param name="rule">Classe <see cref="T:System.Security.AccessControl.ObjectAuditRule" /> per cui rimuovere una regola di controllo.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAuditSpecific(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Rimuove la regola di controllo specificata dall'oggetto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> corrente.</summary>
      <param name="auditFlags">Tipo di regola di controllo da rimuovere.</param>
      <param name="sid">Classe <see cref="T:System.Security.Principal.SecurityIdentifier" /> per cui rimuovere una regola di controllo.</param>
      <param name="accessMask">Maschera di accesso per la regola da rimuovere.</param>
      <param name="inheritanceFlags">Flag che specificano le proprietà di ereditarietà della regola da rimuovere.</param>
      <param name="propagationFlags">Flag che specificano le proprietà di propagazione dell'ereditarietà per la regola da rimuovere.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAuditSpecific(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Rimuove la regola di controllo specificata dall'oggetto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> corrente.Usare questo metodo per gli elenchi di controllo di accesso (ACL) di oggetti directory quando si specifica il tipo di oggetto o il tipo di oggetto ereditato.</summary>
      <param name="auditFlags">Tipo di regola di controllo da rimuovere.</param>
      <param name="sid">Classe <see cref="T:System.Security.Principal.SecurityIdentifier" /> per cui rimuovere una regola di controllo.</param>
      <param name="accessMask">Maschera di accesso per la regola da rimuovere.</param>
      <param name="inheritanceFlags">Flag che specificano le proprietà di ereditarietà della regola da rimuovere.</param>
      <param name="propagationFlags">Flag che specificano le proprietà di propagazione dell'ereditarietà per la regola da rimuovere.</param>
      <param name="objectFlags">Flag che specificano se i parametri <paramref name="objectType" /> e <paramref name="inheritedObjectType" /> contengono valori non null.</param>
      <param name="objectType">Identità della classe di oggetti a cui la regola di controllo rimossa viene applicata.</param>
      <param name="inheritedObjectType">Identità della classe di oggetti figlio che può ereditare la regola di controllo rimossa.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAuditSpecific(System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAuditRule)">
      <summary>Rimuove la regola di controllo specificata dall'oggetto <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> corrente.</summary>
      <param name="sid">Classe <see cref="T:System.Security.Principal.SecurityIdentifier" /> per cui rimuovere una regola di controllo.</param>
      <param name="rule">Oggetto <see cref="T:System.Security.AccessControl.ObjectAuditRule" /> per la regola da rimuovere.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.SetAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Imposta la regola di controllo specificata per l'oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> specificato.</summary>
      <param name="auditFlags">Condizione di controllo da impostare.</param>
      <param name="sid">Oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> per cui impostare una regola di controllo.</param>
      <param name="accessMask">Maschera di accesso per la nuova regola di controllo.</param>
      <param name="inheritanceFlags">Flag che specificano le proprietà di ereditarietà della nuova regola di controllo.</param>
      <param name="propagationFlags">Flag che specificano le proprietà di propagazione dell'ereditarietà della nuova regola di controllo.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.SetAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Imposta la regola di controllo specificata per l'oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> specificato.Usare questo metodo per gli elenchi di controllo di accesso (ACL) di oggetti directory quando si specifica il tipo di oggetto o il tipo di oggetto ereditato.</summary>
      <param name="auditFlags">Condizione di controllo da impostare.</param>
      <param name="sid">Oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> per cui impostare una regola di controllo.</param>
      <param name="accessMask">Maschera di accesso per la nuova regola di controllo.</param>
      <param name="inheritanceFlags">Flag che specificano le proprietà di ereditarietà della nuova regola di controllo.</param>
      <param name="propagationFlags">Flag che specificano le proprietà di propagazione dell'ereditarietà della nuova regola di controllo.</param>
      <param name="objectFlags">Flag che specificano se i parametri <paramref name="objectType" /> e <paramref name="inheritedObjectType" /> contengono valori non null.</param>
      <param name="objectType">Identità della classe di oggetti a cui viene applicata la nuova regola di controllo.</param>
      <param name="inheritedObjectType">Identità della classe di oggetti figlio che possono ereditare la nuova regola di controllo.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.SetAudit(System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAuditRule)">
      <summary>Imposta la regola di controllo specificata per l'oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> specificato.</summary>
      <param name="sid">Oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> per cui impostare una regola di controllo.</param>
      <param name="rule">Oggetto <see cref="T:System.Security.AccessControl.ObjectAuditRule" /> per cui impostare una regola di controllo.</param>
    </member>
  </members>
</doc>