﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Security.AccessControl</name>
  </assembly>
  <members>
    <member name="T:System.Security.AccessControl.AccessControlActions">
      <summary>Определяет действия, разрешенные для защищаемых объектов.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlActions.Change">
      <summary>Разрешен доступ только для записи.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlActions.None">
      <summary>Доступ запрещен.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlActions.View">
      <summary>Разрешен доступ только для чтения.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AccessControlModification">
      <summary>Определяет тип выполняемого изменения управления доступом.Это перечисление используется методами класса <see cref="T:System.Security.AccessControl.ObjectSecurity" /> и производных классов.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.Add">
      <summary>Добавить указанное правило авторизации к списку управления доступом.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.Remove">
      <summary>Удалить правила авторизации, содержащие ИД безопасности и маску доступа, как у указанного правила авторизации, из списка управления доступом.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.RemoveAll">
      <summary>Удалить правила авторизации, содержащие ИД безопасности, как у указанного правила авторизации, из списка управления доступом.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.RemoveSpecific">
      <summary>Удалить правила авторизации, в точности совпадающие с указанным правилом авторизации, из списка управления доступом.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.Reset">
      <summary>Удалить правила авторизации, содержащие ИД безопасности, как у указанного правила авторизации, из списка управления доступом, после чего добавить в список управления доступом указанное правило авторизации.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.Set">
      <summary>Удалить все правила авторизации из списка управления доступом, после чего добавить в список управления доступом указанное правило авторизации.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AccessControlSections">
      <summary>Определяет разделы дескриптора безопасности для сохранения и загрузки.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.Access">
      <summary>Список управления доступом на уровне пользователей.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.All">
      <summary>Весь дескриптор безопасности.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.Audit">
      <summary>Системный список управления доступом.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.Group">
      <summary>Основная группа.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.None">
      <summary>Ни один из разделов.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.Owner">
      <summary>Владелец.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AccessControlType">
      <summary>Определяет тип правила <see cref="T:System.Security.AccessControl.AccessRule" /> — разрешающее или запрещающее доступ.Эти значения не являются флагами и их невозможно объединять.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlType.Allow">
      <summary>Объект <see cref="T:System.Security.AccessControl.AccessRule" />, который используется для разрешения доступа к защищаемому объекту.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlType.Deny">
      <summary>Объект <see cref="T:System.Security.AccessControl.AccessRule" />, который используется для запрета доступа к защищаемому объекту.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AccessRule">
      <summary>Представляет сочетание идентификатора пользователя, маски доступа и типа управления доступом ("разрешить" или "запретить").Кроме того, в объекте <see cref="T:System.Security.AccessControl.AccessRule" /> содержатся сведения о том, как правило наследуется дочерними объектами и как это наследование распространяется.</summary>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>Инициализирует новый экземпляр <see cref="T:System.Security.AccessControl.AccessRule" />, используя указанные значения.</summary>
      <param name="identity">Идентификатор, к которому применяется правило доступа.В качестве этого параметра должен выступать объект, который можно привести к типу <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="accessMask">Маска доступа данного правила.Маска доступа представляет собой группу из 32 анонимных битов, значение каждого из которых определяется отдельными интеграторами.</param>
      <param name="isInherited">Значение true, если данное правило наследуется у родительского контейнера.</param>
      <param name="inheritanceFlags">Свойства наследования правила доступа.</param>
      <param name="propagationFlags">Определяет возможность автоматического распространения наследуемых правил доступа.Флаги распространения игнорируются, если параметр <paramref name="inheritanceFlags" /> имеет значение <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="type">Допустимый тип управления доступом.</param>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="identity" /> нельзя привести к типу <see cref="T:System.Security.Principal.SecurityIdentifier" /> или параметр <paramref name="type" /> содержит недопустимое значение.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="accessMask" /> равно нулю или параметр <paramref name="inheritanceFlags" /> или <paramref name="propagationFlags" /> содержит неизвестное значение.</exception>
    </member>
    <member name="P:System.Security.AccessControl.AccessRule.AccessControlType">
      <summary>Получает значение <see cref="T:System.Security.AccessControl.AccessControlType" />, связанное с данным объектом <see cref="T:System.Security.AccessControl.AccessRule" />.</summary>
      <returns>Значение <see cref="T:System.Security.AccessControl.AccessControlType" />, связанное с данным объектом <see cref="T:System.Security.AccessControl.AccessRule" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.AccessRule`1">
      <summary>Представляет сочетание идентификатора пользователя, маски доступа и типа управления доступом ("разрешить" или "запретить").Кроме того, в объекте AccessRule`1 содержатся сведения о том, как правило наследуется дочерними объектами и как это наследование распространяется.</summary>
      <typeparam name="T">Тип прав доступа для правила доступа.</typeparam>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule`1.#ctor(System.Security.Principal.IdentityReference,`0,System.Security.AccessControl.AccessControlType)">
      <summary>Инициализирует новый экземпляр класса AccessRule’1, используя указанные значения.</summary>
      <param name="identity">Идентификатор, к которому применяется правило доступа.</param>
      <param name="rights">Права правила доступа.</param>
      <param name="type">Допустимый тип управления доступом.</param>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule`1.#ctor(System.Security.Principal.IdentityReference,`0,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>Инициализирует новый экземпляр класса AccessRule’1, используя указанные значения.</summary>
      <param name="identity">Идентификатор, к которому применяется правило доступа.</param>
      <param name="rights">Права правила доступа.</param>
      <param name="inheritanceFlags">Свойства наследования правила доступа.</param>
      <param name="propagationFlags">Определяет возможность автоматического распространения наследуемых правил доступа.Флаги распространения игнорируются, если параметр <paramref name="inheritanceFlags" /> имеет значение <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="type">Допустимый тип управления доступом.</param>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule`1.#ctor(System.String,`0,System.Security.AccessControl.AccessControlType)">
      <summary>Инициализирует новый экземпляр класса AccessRule’1, используя указанные значения.</summary>
      <param name="identity">Идентификатор, к которому применяется правило доступа.</param>
      <param name="rights">Права правила доступа.</param>
      <param name="type">Допустимый тип управления доступом.</param>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule`1.#ctor(System.String,`0,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>Инициализирует новый экземпляр класса AccessRule’1, используя указанные значения.</summary>
      <param name="identity">Идентификатор, к которому применяется правило доступа.</param>
      <param name="rights">Права правила доступа.</param>
      <param name="inheritanceFlags">Свойства наследования правила доступа.</param>
      <param name="propagationFlags">Определяет возможность автоматического распространения наследуемых правил доступа.Флаги распространения игнорируются, если параметр <paramref name="inheritanceFlags" /> имеет значение <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="type">Допустимый тип управления доступом.</param>
    </member>
    <member name="P:System.Security.AccessControl.AccessRule`1.Rights">
      <summary>Возвращает права текущего экземпляра.</summary>
      <returns>Права текущего экземпляра приведенные к типу &lt;T&gt;.</returns>
    </member>
    <member name="T:System.Security.AccessControl.AceEnumerator">
      <summary>Обеспечивает возможность перебора элементов управления доступом в списке управления доступом. </summary>
    </member>
    <member name="P:System.Security.AccessControl.AceEnumerator.Current">
      <summary>Получает текущий элемент в коллекции <see cref="T:System.Security.AccessControl.GenericAce" />.Это свойство получает версию объекта удобного типа.</summary>
      <returns>Текущий элемент коллекции <see cref="T:System.Security.AccessControl.GenericAce" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.AceEnumerator.MoveNext">
      <summary>Перемещает перечислитель к следующему элементу коллекции <see cref="T:System.Security.AccessControl.GenericAce" />.</summary>
      <returns>Значение true, если перечислитель был успешно перемещен к следующему элементу; значение false, если перечислитель достиг конца коллекции.</returns>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя.</exception>
    </member>
    <member name="M:System.Security.AccessControl.AceEnumerator.Reset">
      <summary>Устанавливает перечислитель в исходное положение перед первым элементом коллекции <see cref="T:System.Security.AccessControl.GenericAce" />.</summary>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя.</exception>
    </member>
    <member name="P:System.Security.AccessControl.AceEnumerator.System#Collections#IEnumerator#Current"></member>
    <member name="T:System.Security.AccessControl.AceFlags">
      <summary>Определяет порядок наследования и аудита элемента управления доступом.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.AuditFlags">
      <summary>Все попытки доступа подлежат аудиту.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.ContainerInherit">
      <summary>Маска доступа распространяется на дочерние объекты-контейнеры.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.FailedAccess">
      <summary>Неудачные попытки доступа подлежат аудиту.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.InheritanceFlags">
      <summary>Операция логического OR, примененная к флагам <see cref="F:System.Security.AccessControl.AceFlags.ObjectInherit" />, <see cref="F:System.Security.AccessControl.AceFlags.ContainerInherit" />, <see cref="F:System.Security.AccessControl.AceFlags.NoPropagateInherit" /> и <see cref="F:System.Security.AccessControl.AceFlags.InheritOnly" />.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.Inherited">
      <summary>Элемент управления доступом наследуется у родительского контейнера, а на задается для объекта явным образом.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.InheritOnly">
      <summary>Маска доступа распространяется только на дочерние объекты.К ним относятся контейнеры и конечные дочерние объекты.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.None">
      <summary>Флаги элементов управления доступом не установлены.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.NoPropagateInherit">
      <summary>Проверка прав доступа не применяется к данному объекту. Она применяется только к его дочерним объектам.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.ObjectInherit">
      <summary>Маска доступа распространяется на дочерние конечные объекты.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.SuccessfulAccess">
      <summary>Удачные попытки доступа подлежат аудиту.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AceQualifier">
      <summary>Задает функцию элемента управления доступом.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceQualifier.AccessAllowed">
      <summary>Разрешить доступ.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceQualifier.AccessDenied">
      <summary>Запретить доступ.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceQualifier.SystemAlarm">
      <summary>Вызвать системное оповещение.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceQualifier.SystemAudit">
      <summary>Вызвать системный аудит.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AceType">
      <summary>Определяет доступные типы элементов управления доступом.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowed">
      <summary>Разрешает доступ к объекту для конкретного доверенного лица, определяемого объектом <see cref="T:System.Security.Principal.IdentityReference" />.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowedCallback">
      <summary>Разрешает доступ к объекту для конкретного доверенного лица, определяемого объектом <see cref="T:System.Security.Principal.IdentityReference" />.Элемент управления доступом этого типа может содержать необязательные данные обратного вызова.Данные обратного вызова представляют собой определяемый диспетчером ресурсов и неинтерпретируемый большой двоичный объект.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowedCallbackObject">
      <summary>Разрешает доступ к объекту, набору свойств или свойству.Такой элемент управления доступом содержит набор прав доступа, идентификатор GUID, определяющий тип объекта, а также объект <see cref="T:System.Security.Principal.IdentityReference" />, который определяет доверенное лицо, получающее доступ.Кроме того, этот элемент содержит GUID и набор флагов, определяющих параметры наследования элемента управления доступом дочерними объектами.Элемент управления доступом этого типа может содержать необязательные данные обратного вызова.Данные обратного вызова представляют собой определяемый диспетчером ресурсов и неинтерпретируемый большой двоичный объект.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowedCompound">
      <summary>Определено, но никогда не используется.Указано для полноты представления информации.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowedObject">
      <summary>Разрешает доступ к объекту, набору свойств или свойству.Такой элемент управления доступом содержит набор прав доступа, идентификатор GUID, определяющий тип объекта, а также объект <see cref="T:System.Security.Principal.IdentityReference" />, который определяет доверенное лицо, получающее доступ.Кроме того, этот элемент содержит GUID и набор флагов, определяющих параметры наследования элемента управления доступом дочерними объектами.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessDenied">
      <summary>Запрещает доступ к объекту для конкретного доверенного лица, определяемого объектом <see cref="T:System.Security.Principal.IdentityReference" />.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessDeniedCallback">
      <summary>Запрещает доступ к объекту для конкретного доверенного лица, определяемого объектом <see cref="T:System.Security.Principal.IdentityReference" />.Элемент управления доступом этого типа может содержать необязательные данные обратного вызова.Данные обратного вызова представляют собой определяемый диспетчером ресурсов и неинтерпретируемый большой двоичный объект.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessDeniedCallbackObject">
      <summary>Запрещает доступ к объекту, набору свойств или свойству.Такой элемент управления доступом содержит набор прав доступа, идентификатор GUID, определяющий тип объекта, а также объект <see cref="T:System.Security.Principal.IdentityReference" />, который определяет доверенное лицо, получающее доступ.Кроме того, этот элемент содержит GUID и набор флагов, определяющих параметры наследования элемента управления доступом дочерними объектами.Элемент управления доступом этого типа может содержать необязательные данные обратного вызова.Данные обратного вызова представляют собой определяемый диспетчером ресурсов и неинтерпретируемый большой двоичный объект.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessDeniedObject">
      <summary>Запрещает доступ к объекту, набору свойств или свойству.Такой элемент управления доступом содержит набор прав доступа, идентификатор GUID, определяющий тип объекта, а также объект <see cref="T:System.Security.Principal.IdentityReference" />, который определяет доверенное лицо, получающее доступ.Кроме того, этот элемент содержит GUID и набор флагов, определяющих параметры наследования элемента управления доступом дочерними объектами.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.MaxDefinedAceType">
      <summary>Максимальный определенный тип элемента управления доступом в перечислении.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAlarm">
      <summary>Зарезервировано для использования в будущем.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAlarmCallback">
      <summary>Зарезервировано для использования в будущем.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAlarmCallbackObject">
      <summary>Зарезервировано для использования в будущем.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAlarmObject">
      <summary>Зарезервировано для использования в будущем.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAudit">
      <summary>Вызывает регистрацию сообщения аудита, когда указанное доверенное лицо пытается получить доступ к объекту.Доверенное лицо определяется объектом <see cref="T:System.Security.Principal.IdentityReference" />.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAuditCallback">
      <summary>Вызывает регистрацию сообщения аудита, когда указанное доверенное лицо пытается получить доступ к объекту.Доверенное лицо определяется объектом <see cref="T:System.Security.Principal.IdentityReference" />.Элемент управления доступом этого типа может содержать необязательные данные обратного вызова.Данные обратного вызова представляют собой определяемый диспетчером ресурсов и неинтерпретируемый большой двоичный объект.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAuditCallbackObject">
      <summary>Вызывает регистрацию сообщения аудита, когда указанное доверенное лицо пытается получить доступ к объекту или его составляющим, например к свойству или набору свойств.Такой элемент управления доступом содержит набор прав доступа, идентификатор GUID, определяющий тип объекта или его составляющих, а также объект <see cref="T:System.Security.Principal.IdentityReference" />, который определяет доверенное лицо, доступ которого подлежит аудиту.Кроме того, этот элемент содержит GUID и набор флагов, определяющих параметры наследования элемента управления доступом дочерними объектами.Элемент управления доступом этого типа может содержать необязательные данные обратного вызова.Данные обратного вызова представляют собой определяемый диспетчером ресурсов и неинтерпретируемый большой двоичный объект.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAuditObject">
      <summary>Вызывает регистрацию сообщения аудита, когда указанное доверенное лицо пытается получить доступ к объекту или его составляющим, например к свойству или набору свойств.Такой элемент управления доступом содержит набор прав доступа, идентификатор GUID, определяющий тип объекта или его составляющих, а также объект <see cref="T:System.Security.Principal.IdentityReference" />, который определяет доверенное лицо, доступ которого подлежит аудиту.Кроме того, этот элемент содержит GUID и набор флагов, определяющих параметры наследования элемента управления доступом дочерними объектами.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AuditFlags">
      <summary>Задает условия аудита попыток доступа к защищаемому объекту.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AuditFlags.Failure">
      <summary>Аудиту подлежат неудачные попытки доступа.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AuditFlags.None">
      <summary>Аудиту не подлежат никакие попытки доступа.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AuditFlags.Success">
      <summary>Аудиту подлежат успешные попытки доступа.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AuditRule">
      <summary>Представляет сочетание идентификатора пользователя и маски доступа.Кроме того, в объекте <see cref="T:System.Security.AccessControl.AuditRule" /> содержатся сведения о том, как правило наследуется дочерними объектами, как это наследование распространяется, а также об условиях, при которых выполняется аудит.</summary>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>Инициализирует новый экземпляр <see cref="T:System.Security.AccessControl.AuditRule" />, используя указанные значения.</summary>
      <param name="identity">Идентификатор, к которому применяется правило аудита.Это должен быть объект, который можно привести к типу <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="accessMask">Маска доступа данного правила.Маска доступа представляет собой группу из 32 анонимных битов, значение каждого из которых определяется отдельными интеграторами.</param>
      <param name="isInherited">Значение true, если данное правило наследуется у родительского контейнера.</param>
      <param name="inheritanceFlags">Свойства наследования правила аудита.</param>
      <param name="propagationFlags">Определяет возможность автоматического распространения наследуемых правил аудита.Флаги распространения игнорируются, если параметр <paramref name="inheritanceFlags" /> имеет значение <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="auditFlags">Условия, при которых применяется правило аудита.</param>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="identity" /> нельзя привести к типу <see cref="T:System.Security.Principal.SecurityIdentifier" /> или параметр <paramref name="auditFlags" /> содержит недопустимое значение.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="accessMask" /> равно нулю или параметр <paramref name="inheritanceFlags" /> или <paramref name="propagationFlags" /> содержит неизвестное значение.</exception>
    </member>
    <member name="P:System.Security.AccessControl.AuditRule.AuditFlags">
      <summary>Получает флаги аудита для данного правила аудита.</summary>
      <returns>Побитовое сочетание значений перечисления.Это сочетание определяет условия аудита для данного правила аудита.</returns>
    </member>
    <member name="T:System.Security.AccessControl.AuditRule`1">
      <summary>Представляет сочетание идентификатора пользователя и маски доступа.</summary>
      <typeparam name="T"></typeparam>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule`1.#ctor(System.Security.Principal.IdentityReference,`0,System.Security.AccessControl.AuditFlags)">
      <summary>Инициализирует новый экземпляр класса AuditRule’1, используя указанные значения.</summary>
      <param name="identity">Удостоверение, к которому применяется данное правило аудита.</param>
      <param name="rights">Права правила аудита.</param>
      <param name="flags">Условия, при которых применяется правило аудита.</param>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule`1.#ctor(System.Security.Principal.IdentityReference,`0,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>Инициализирует новый экземпляр класса AuditRule’1, используя указанные значения.</summary>
      <param name="identity">Идентификатор, к которому применяется правило аудита. </param>
      <param name="rights">Права правила аудита.</param>
      <param name="inheritanceFlags">Свойства наследования правила аудита.</param>
      <param name="propagationFlags">Определяет возможность автоматического распространения наследуемых правил аудита.</param>
      <param name="flags">Условия, при которых применяется правило аудита.</param>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule`1.#ctor(System.String,`0,System.Security.AccessControl.AuditFlags)">
      <summary>Инициализирует новый экземпляр класса AuditRule’1, используя указанные значения.</summary>
      <param name="identity">Идентификатор, к которому применяется правило аудита.</param>
      <param name="rights">Права правила аудита.</param>
      <param name="flags">Свойства правила аудита.</param>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule`1.#ctor(System.String,`0,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>Инициализирует новый экземпляр класса AuditRule’1, используя указанные значения.</summary>
      <param name="identity">Идентификатор, к которому применяется правило аудита.</param>
      <param name="rights">Права правила аудита.</param>
      <param name="inheritanceFlags">Свойства наследования правила аудита.</param>
      <param name="propagationFlags">Определяет возможность автоматического распространения наследуемых правил аудита.</param>
      <param name="flags">Условия, при которых применяется правило аудита.</param>
    </member>
    <member name="P:System.Security.AccessControl.AuditRule`1.Rights">
      <summary>Права правила аудита.</summary>
      <returns>Возвращает <see cref="{0}" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.AuthorizationRule">
      <summary>Определяет доступ к защищаемым объектам.В производных классах <see cref="T:System.Security.AccessControl.AccessRule" /> и <see cref="T:System.Security.AccessControl.AuditRule" /> детализируются функции доступа и аудита.</summary>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Инициализирует новый экземпляр <see cref="T:System.Security.AuthorizationControl.AccessRule" />, используя указанные значения.</summary>
      <param name="identity">Идентификатор, к которому применяется правило доступа.  В качестве этого параметра должен выступать объект, который можно привести к типу <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="accessMask">Маска доступа данного правила.Маска доступа представляет собой группу из 32 анонимных битов, значение каждого из которых определяется отдельными интеграторами.</param>
      <param name="isInherited">Значение true, если данное правило наследуется у родительского контейнера.</param>
      <param name="inheritanceFlags">Свойства наследования правила доступа.</param>
      <param name="propagationFlags">Определяет возможность автоматического распространения наследуемых правил доступа.Флаги распространения игнорируются, если параметр <paramref name="inheritanceFlags" /> имеет значение <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="identity" /> нельзя привести к типу <see cref="T:System.Security.Principal.SecurityIdentifier" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="accessMask" /> равно нулю или параметр <paramref name="inheritanceFlags" /> или <paramref name="propagationFlags" /> содержит неизвестное значение.</exception>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.AccessMask">
      <summary>Получает маску доступа для данного правила.</summary>
      <returns>Маска доступа для данного правила.</returns>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.IdentityReference">
      <summary>Получает объект <see cref="T:System.Security.Principal.IdentityReference" />, к которому применяется данное правило.</summary>
      <returns>Объект <see cref="T:System.Security.Principal.IdentityReference" />, к которому применяется данное правило.</returns>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.InheritanceFlags">
      <summary>Получает значение флагов, определяющих порядок наследования правила дочерними объектами.</summary>
      <returns>Побитовое сочетание значений перечисления.</returns>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.IsInherited">
      <summary>Получает значение, которое определяет, наследуется правило от родительского объекта-контейнера или задается явным образом.</summary>
      <returns>Значение true, если правило наследуется от родительского контейнера, а не задается явным образом.</returns>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.PropagationFlags">
      <summary>Получает значение флагов распространения, определяющих порядок распространения наследования правила среди дочерних объектов.Это свойство имеет смысл лишь в том случае, когда значение перечисления <see cref="T:System.Security.AccessControl.InheritanceFlags" /> не равно <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</summary>
      <returns>Побитовое сочетание значений перечисления.</returns>
    </member>
    <member name="T:System.Security.AccessControl.AuthorizationRuleCollection">
      <summary>Представляет коллекцию объектов <see cref="T:System.Security.AccessControl.AuthorizationRule" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.AuthorizationRuleCollection" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.AddRule(System.Security.AccessControl.AuthorizationRule)">
      <summary>Добавление в коллекцию объекта <see cref="T:System.Web.Configuration.AuthorizationRule" />.</summary>
      <param name="rule">Объект <see cref="T:System.Web.Configuration.AuthorizationRule" /> для добавления в коллекцию.</param>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.CopyTo(System.Security.AccessControl.AuthorizationRule[],System.Int32)">
      <summary>Копирует содержимое коллекции в массив.</summary>
      <param name="rules">Массив, в который нужно скопировать содержимое коллекции.</param>
      <param name="index">Индекс (с нуля), с которого начинается копирование.</param>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRuleCollection.Count"></member>
    <member name="P:System.Security.AccessControl.AuthorizationRuleCollection.Item(System.Int32)">
      <summary>Получает объект <see cref="T:System.Security.AccessControl.AuthorizationRule" /> по указанному индексу в коллекции.</summary>
      <returns>Объект <see cref="T:System.Security.AccessControl.AuthorizationRule" /> по указанному индексу.</returns>
      <param name="index">Отсчитываемый с нуля индекс получаемого объекта <see cref="T:System.Security.AccessControl.AuthorizationRule" />.</param>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)"></member>
    <member name="P:System.Security.AccessControl.AuthorizationRuleCollection.System#Collections#ICollection#IsSynchronized"></member>
    <member name="P:System.Security.AccessControl.AuthorizationRuleCollection.System#Collections#ICollection#SyncRoot"></member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.System#Collections#IEnumerable#GetEnumerator"></member>
    <member name="T:System.Security.AccessControl.CommonAce">
      <summary>Представляет элемент управления доступом.</summary>
    </member>
    <member name="M:System.Security.AccessControl.CommonAce.#ctor(System.Security.AccessControl.AceFlags,System.Security.AccessControl.AceQualifier,System.Int32,System.Security.Principal.SecurityIdentifier,System.Boolean,System.Byte[])">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.CommonAce" />.</summary>
      <param name="flags">Флаги, определяющие параметры наследования и распространения наследования, а также условия аудита нового элемента управления доступом.</param>
      <param name="qualifier">Функция нового элемента управления доступом.</param>
      <param name="accessMask">Маска доступа элемента управления доступом.</param>
      <param name="sid">Объект <see cref="T:System.Security.Principal.SecurityIdentifier" />, связанный с новым элементом управления доступом.</param>
      <param name="isCallback">Значение true, если новый элемент управления доступом является элементом управления доступом обратного вызова.</param>
      <param name="opaque">Непрозрачные данные, связанные с новым элементом управления доступом.Непрозрачные данные разрешены только для элементов управления доступом обратного вызова.Длина этого массива не должна превышать значения, возвращаемого методом <see cref="M:System.Security.AccessControl.CommonAce.MaxOpaqueLength(System.Boolean)" />.</param>
    </member>
    <member name="P:System.Security.AccessControl.CommonAce.BinaryLength">
      <summary>Получает длину в байтах двоичного представления текущего объекта <see cref="T:System.Security.AccessControl.CommonAce" />.Это длину необходимо передать методу <see cref="M:System.Security.AccessControl.CommonAce.GetBinaryForm(System.Byte[],System.Int32)" /> перед маршалингом списка управления доступом в двоичный массив.</summary>
      <returns>Длина в байтах двоичного представления текущего объекта <see cref="T:System.Security.AccessControl.CommonAce" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.CommonAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Маршалирует содержимое объекта <see cref="T:System.Security.AccessControl.CommonAce" /> в указанный массив байтов, начиная с указанной позиции.</summary>
      <param name="binaryForm">Массив байтов, в который маршалируется содержимое объекта <see cref="T:System.Security.AccessControl.CommonAce" />.</param>
      <param name="offset">Позиция, с которой начинается маршалинг.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение <paramref name="offset" /> отрицательно или слишком велико, чтобы можно было скопировать весь объект <see cref="T:System.Security.AccessControl.CommonAce" /> в массив <paramref name="binaryForm" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.CommonAce.MaxOpaqueLength(System.Boolean)">
      <summary>Возвращает максимально допустимую длину большого двоичного объекта непрозрачных данных для элементов управления доступом обратного вызова.</summary>
      <returns>Допустимая длина большого двоичного объекта непрозрачных данных.</returns>
      <param name="isCallback">Значение true, если объект <see cref="T:System.Security.AccessControl.CommonAce" /> является элементом управления доступом обратного вызова.</param>
    </member>
    <member name="T:System.Security.AccessControl.CommonAcl">
      <summary>Представляет список управления доступом и является базовым классом для классов <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> и <see cref="T:System.Security.AccessControl.SystemAcl" />.</summary>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.BinaryLength">
      <summary>Возвращает длину в байтах двоичного представления текущего объекта <see cref="T:System.Security.AccessControl.CommonAcl" />.Эта длина должна использоваться перед маршалингом списка управления доступом в двоичный массив с помощью метода <see cref="M:System.Security.AccessControl.CommonAcl.GetBinaryForm(System.Byte[],System.Int32)" />.</summary>
      <returns>Длина в байтах двоичного представления текущего объекта <see cref="T:System.Security.AccessControl.CommonAcl" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.Count">
      <summary>Возвращает количество элементов управления доступом в текущем объекте <see cref="T:System.Security.AccessControl.CommonAcl" />.</summary>
      <returns>Количество элементов управления доступом в текущем объекте <see cref="T:System.Security.AccessControl.CommonAcl" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.CommonAcl.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Маршалирует содержимое объекта <see cref="T:System.Security.AccessControl.CommonAcl" /> в указанный массив байтов, начиная с указанной позиции.</summary>
      <param name="binaryForm">Массив байтов, в который маршалируется содержимое объекта <see cref="T:System.Security.AccessControl.CommonAcl" />.</param>
      <param name="offset">Позиция, с которой начинается маршалинг.</param>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.IsCanonical">
      <summary>Возвращает логическое значение, которое указывает, расположены ли элементы управления доступом в текущем объекте <see cref="T:System.Security.AccessControl.CommonAcl" /> в каноническом порядке.</summary>
      <returns>Значение true, если элементы управления доступом в текущем текущем объекте <see cref="T:System.Security.AccessControl.CommonAcl" /> расположены в каноническом порядке; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.IsContainer">
      <summary>Определяет, является ли объект <see cref="T:System.Security.AccessControl.CommonAcl" /> контейнером. </summary>
      <returns>Значение true, если текущий объект <see cref="T:System.Security.AccessControl.CommonAcl" /> является контейнером.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.IsDS">
      <summary>Определяет, является ли текущий объект <see cref="T:System.Security.AccessControl.CommonAcl" /> списком управления доступом объекта каталога.</summary>
      <returns>Значение true, если текущий объект <see cref="T:System.Security.AccessControl.CommonAcl" /> является списком управления доступом объекта каталога.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.Item(System.Int32)">
      <summary>Возвращает или задает объект <see cref="T:System.Security.AccessControl.CommonAce" /> с заданным индексом.</summary>
      <returns>Параметр <see cref="T:System.Security.AccessControl.CommonAce" /> по указанному индексу.</returns>
      <param name="index">Индекс (с нуля) возвращаемого или задаваемого <see cref="T:System.Security.AccessControl.CommonAce" />.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonAcl.Purge(System.Security.Principal.SecurityIdentifier)">
      <summary>Удаляет все элементы управления доступом, содержащиеся в данном объекте <see cref="T:System.Security.AccessControl.CommonAcl" /> и связанные с объектом <see cref="T:System.Security.Principal.SecurityIdentifier" />.</summary>
      <param name="sid">Объект <see cref="T:System.Security.Principal.SecurityIdentifier" /> для проверки.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonAcl.RemoveInheritedAces">
      <summary>Удаляет все наследуемые элементы управления доступом из данного объекта <see cref="T:System.Security.AccessControl.CommonAcl" />.</summary>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.Revision">
      <summary>Возвращает уровень редакции объекта <see cref="T:System.Security.AccessControl.CommonAcl" />.</summary>
      <returns>Байтовое значение, определяющее уровень редакции объекта <see cref="T:System.Security.AccessControl.CommonAcl" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.CommonObjectSecurity">
      <summary>Обеспечивает возможность управления доступом к объектам без непосредственной работы со списками управления доступом.Этот класс является абстрактным базовым классом для класса <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.#ctor(System.Boolean)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <param name="isContainer">Значение true, если новый объект является контейнером.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.AddAccessRule(System.Security.AccessControl.AccessRule)">
      <summary>Добавляет указанное правило доступа в список управления доступом на уровне пользователей, связанный с данным объектом <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <param name="rule">Добавляемое правило доступа.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.AddAuditRule(System.Security.AccessControl.AuditRule)">
      <summary>Добавляет указанное правило аудита в системный список управления доступом, связанный с данным объектом <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <param name="rule">Добавляемое правило аудита.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.GetAccessRules(System.Boolean,System.Boolean,System.Type)">
      <summary>Получает коллекцию правил доступа, связанных с указанным идентификатором безопасности.</summary>
      <returns>Коллекция правил доступа, связанных с указанным объектом <see cref="T:System.Security.Principal.SecurityIdentifier" />.</returns>
      <param name="includeExplicit">Значение true, чтобы включить правила доступа, явным образом указанные для данного объекта.</param>
      <param name="includeInherited">Значение true, чтобы включить унаследованные правила доступа.</param>
      <param name="targetType">Определяет, относится ли идентификатор безопасности, для которого извлекаются правила доступа, к типу T:System.Security.Principal.SecurityIdentifier или к типу T:System.Security.Principal.NTAccount.Значение этого параметра должно иметь тип, который можно преобразовать в тип <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.GetAuditRules(System.Boolean,System.Boolean,System.Type)">
      <summary>Получает коллекцию правил аудита, связанных с указанным идентификатором безопасности.</summary>
      <returns>Коллекция правил аудита, связанных с указанным объектом <see cref="T:System.Security.Principal.SecurityIdentifier" />.</returns>
      <param name="includeExplicit">Значение true, чтобы включить правила аудита, явным образом указанные для данного объекта.</param>
      <param name="includeInherited">Значение true, чтобы включить унаследованные правила аудита.</param>
      <param name="targetType">Идентификатор безопасности, для которого извлекаются правила аудита.Это должен быть объект, который можно привести к типу <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.ModifyAccess(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AccessRule,System.Boolean@)">
      <summary>Применяет указанное изменение к списку управления доступом на уровне пользователей, связанному с данным объектом <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <returns>Значение true, если список управления доступом на уровне пользователей успешно изменен; в противном случае — значение false.</returns>
      <param name="modification">Применяемое изменение списка управления доступом на уровне пользователей.</param>
      <param name="rule">Изменяемое правило доступа.</param>
      <param name="modified">Значение true, если список управления доступом на уровне пользователей успешно изменен; в противном случае — значение false.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.ModifyAudit(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AuditRule,System.Boolean@)">
      <summary>Применяет указанное изменение к системному списку управления доступом, связанному с данным объектом <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <returns>Значение true, если системный список управления доступом успешно изменен; в противном случае — значение false.</returns>
      <param name="modification">Применяемое изменение системного списка управления доступом.</param>
      <param name="rule">Изменяемое правило аудита.</param>
      <param name="modified">Значение true, если системный список управления доступом успешно изменен; в противном случае — значение false.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAccessRule(System.Security.AccessControl.AccessRule)">
      <summary>Удаляет правила доступа с тем же идентификатором безопасности и маской доступа, что и у указанного правила доступа, из списка управления доступом на уровне пользователей, связанного с текущим объектом <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <returns>Значение true, если правило доступа успешно удалено; в противном случае — значение false.</returns>
      <param name="rule">Удаляемое правило доступа.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAccessRuleAll(System.Security.AccessControl.AccessRule)">
      <summary>Удаляет все правила доступа с тем же идентификатором безопасности, что и у указанного правила доступа, из списка управления доступом на уровне пользователей, связанного с текущим объектом <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <param name="rule">Удаляемое правило доступа.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAccessRuleSpecific(System.Security.AccessControl.AccessRule)">
      <summary>Удаляет все правила доступа, в точности совпадающие с указанным правилом доступа, из списка управления доступом на уровне пользователей, связанного с текущим объектом <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <param name="rule">Удаляемое правило доступа.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAuditRule(System.Security.AccessControl.AuditRule)">
      <summary>Удаляет правила аудита с тем же идентификатором безопасности и маской доступа, что и у указанного правила аудита, из системного списка управления доступом, связанного с текущим объектом <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <returns>Значение true, если правило аудита успешно удалено; в противном случае — значение false.</returns>
      <param name="rule">Удаляемое правило аудита.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAuditRuleAll(System.Security.AccessControl.AuditRule)">
      <summary>Удаляет все правила аудита с тем же идентификатором безопасности, что и у указанного правила аудита, из системного списка управления доступом, связанного с текущим объектом <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <param name="rule">Удаляемое правило аудита.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAuditRuleSpecific(System.Security.AccessControl.AuditRule)">
      <summary>Удаляет все правила аудита, в точности совпадающие с указанным правилом аудита, из системного списка управления доступом, связанного с текущим объектом <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <param name="rule">Удаляемое правило аудита.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.ResetAccessRule(System.Security.AccessControl.AccessRule)">
      <summary>Удаляет все правила доступа из списка управления доступом на уровне пользователей (DACL), связанного с текущим объектом <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />, а затем добавляет указанное правило доступа.</summary>
      <param name="rule">Сбрасываемое правило доступа.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.SetAccessRule(System.Security.AccessControl.AccessRule)">
      <summary>Удаляет все правила доступа с тем же идентификатором и квалификатором безопасности, что и у указанного правила доступа, из списка управления доступом на уровне пользователей, связанного с текущим объектом <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />, а затем добавляет указанное правило доступа.</summary>
      <param name="rule">Устанавливаемое правило доступа.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.SetAuditRule(System.Security.AccessControl.AuditRule)">
      <summary>Удаляет все правила аудита с тем же идентификатором и квалификатором безопасности, что и у указанного правила аудита, из системного списка управления доступом, связанного с текущим объектом <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />, а затем добавляет указанное правило аудита.</summary>
      <param name="rule">Устанавливаемое правило аудита.</param>
    </member>
    <member name="T:System.Security.AccessControl.CommonSecurityDescriptor">
      <summary>Представляет дескриптор безопасности.Дескриптор безопасности включает владельца, основную группу, список управления доступом на уровне пользователей и системный список управления доступом.</summary>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.#ctor(System.Boolean,System.Boolean,System.Byte[],System.Int32)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> на основании указанного массива байтовых значений.</summary>
      <param name="isContainer">Значение true, если новый дескриптор безопасности связан с объектом-контейнером.</param>
      <param name="isDS">Значение true, если новый дескриптор безопасности связан с объектом каталога.</param>
      <param name="binaryForm">Массив байтовых значений, на основании которого необходимо создать новый объект <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</param>
      <param name="offset">Позиция в массиве <paramref name="binaryForm" />, с которой начинается копирование.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.#ctor(System.Boolean,System.Boolean,System.Security.AccessControl.ControlFlags,System.Security.Principal.SecurityIdentifier,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.SystemAcl,System.Security.AccessControl.DiscretionaryAcl)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> на основании указанных сведений.</summary>
      <param name="isContainer">Значение true, если новый дескриптор безопасности связан с объектом-контейнером.</param>
      <param name="isDS">Значение true, если новый дескриптор безопасности связан с объектом каталога.</param>
      <param name="flags">Флаги, определяющие поведение нового объекта <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</param>
      <param name="owner">Владелец нового объекта <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</param>
      <param name="group">Основная группа для нового объекта <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</param>
      <param name="systemAcl">Системный список управления доступом нового объекта <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</param>
      <param name="discretionaryAcl">Список управления доступом на уровне пользователей нового объекта <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.#ctor(System.Boolean,System.Boolean,System.Security.AccessControl.RawSecurityDescriptor)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> на основании указанного объекта <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</summary>
      <param name="isContainer">Значение true, если новый дескриптор безопасности связан с объектом-контейнером.</param>
      <param name="isDS">Значение true, если новый дескриптор безопасности связан с объектом каталога.</param>
      <param name="rawSecurityDescriptor">Объект <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />, из которого создается новый объект <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.#ctor(System.Boolean,System.Boolean,System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> на основании указанной строки SDDL (Security Descriptor Definition Language).</summary>
      <param name="isContainer">Значение true, если новый дескриптор безопасности связан с объектом-контейнером.</param>
      <param name="isDS">Значение true, если новый дескриптор безопасности связан с объектом каталога.</param>
      <param name="sddlForm">Строка SDDL, на основании которой создается новый объект <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.AddDiscretionaryAcl(System.Byte,System.Int32)">
      <summary>Задает <see cref="P:System.Security.AccessControl.CommonSecurityDescriptor.DiscretionaryAcl" /> свойство для данного <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> экземпляра и наборы <see cref="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclPresent" /> флаг.</summary>
      <param name="revision">Номер редакции нового объекта <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />.</param>
      <param name="trusted">Количество элементов управления доступом, которые могут содержаться в данном объекте <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />.Это число будет использоваться только для справки.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.AddSystemAcl(System.Byte,System.Int32)">
      <summary>Задает <see cref="P:System.Security.AccessControl.CommonSecurityDescriptor.SystemAcl" /> свойство для данного <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> экземпляра и наборы <see cref="F:System.Security.AccessControl.ControlFlags.SystemAclPresent" /> флаг.</summary>
      <param name="revision">Номер редакции нового объекта <see cref="T:System.Security.AccessControl.SystemAcl" />.</param>
      <param name="trusted">Количество элементов управления доступом, которые могут содержаться в данном объекте <see cref="T:System.Security.AccessControl.SystemAcl" />.Это число будет использоваться только для справки.</param>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.ControlFlags">
      <summary>Получает значения, определяющие поведение объекта <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</summary>
      <returns>Один или несколько членов перечисления <see cref="T:System.Security.AccessControl.ControlFlags" />, объединенных с помощью операции логического ИЛИ.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.DiscretionaryAcl">
      <summary>Получает или задает список управления доступом на уровне пользователей для данного объекта <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.Список управления доступом на уровне пользователей содержит правила доступа.</summary>
      <returns>Список управления доступом на уровне пользователей для данного объекта <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.Group">
      <summary>Получает или задает основную группу для данного объекта <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</summary>
      <returns>Основная группа для данного объекта <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.IsContainer">
      <summary>Получает логическое значение, которое указывает, связан ли данный объект <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> с объектом-контейнером.</summary>
      <returns>Значение true, если объект связан с объектом <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />, являющимся контейнером; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.IsDiscretionaryAclCanonical">
      <summary>Получает логическое значение, указывающее, размещены ли элементы связанного с этим объектом <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> списка управления доступом на уровне пользователей в каноническом порядке.</summary>
      <returns>Значение true, если элементы связанного с этим объектом <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> списка управления доступом на уровне пользователей размещены в каноническом порядке; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.IsDS">
      <summary>Получает логическое значение, которое указывает, связан ли данный объект <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> с объектом каталога.</summary>
      <returns>Значение true, если объект, связанный с этим объектом <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />, является объектом каталога; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.IsSystemAclCanonical">
      <summary>Получает логическое значение, указывающее, размещены ли элементы связанного с этим объектом <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> системного списка управления доступом в каноническом порядке.</summary>
      <returns>Значение true, если элементы связанного с этим объектом <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> системного списка управления доступом размещены в каноническом порядке; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.Owner">
      <summary>Получает или задает владельца объекта, связанного с данным объектом <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</summary>
      <returns>Владелец объекта, связанного с данным объектом <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.PurgeAccessControl(System.Security.Principal.SecurityIdentifier)">
      <summary>Удаляет все правила доступа для указанного идентификатора безопасности из списка управления доступом на уровне пользователей, связанного с текущим объектом <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</summary>
      <param name="sid">Идентификатор безопасности, для которого удаляются правила доступа.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.PurgeAudit(System.Security.Principal.SecurityIdentifier)">
      <summary>Удаляет все правила аудита для указанного идентификатора безопасности из системного списка управления доступом, связанного с текущим объектом <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</summary>
      <param name="sid">Идентификатор безопасности, для которого удаляются правила аудита.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.SetDiscretionaryAclProtection(System.Boolean,System.Boolean)">
      <summary>Задает защиту от наследования для списка управления доступом на уровне пользователей, связанного с данным объектом <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.Защищенные списки управления доступом на уровне пользователей не наследуют правила доступа у родительских контейнеров.</summary>
      <param name="isProtected">Значение true, если список управления доступом на уровне пользователей защищается от наследования.</param>
      <param name="preserveInheritance">Значение true для сохранения наследуемых правил доступа в списке управления доступом на уровне пользователей; значение false для удаления наследуемых правил доступа из списка управления доступом на уровне пользователей.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.SetSystemAclProtection(System.Boolean,System.Boolean)">
      <summary>Задает защиту от наследования для системного списка управления доступом, связанного с данным объектом <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.Защищенные системные списки управления доступом не наследуют правила аудита у родительских контейнеров.</summary>
      <param name="isProtected">Значение true, если системный список управления доступом защищается от наследования.</param>
      <param name="preserveInheritance">Значение true для сохранения наследуемых правил аудита в системном списке управления доступом; значение false для удаления наследуемых правил аудита из системного списка управления доступом.</param>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.SystemAcl">
      <summary>Получает или задает системный список управления доступом для данного объекта <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.Системный список управления доступом содержит правила аудита.</summary>
      <returns>Системный список управления доступом для данного объекта <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.CompoundAce">
      <summary>Представляет составной элемент управления доступом.</summary>
    </member>
    <member name="M:System.Security.AccessControl.CompoundAce.#ctor(System.Security.AccessControl.AceFlags,System.Int32,System.Security.AccessControl.CompoundAceType,System.Security.Principal.SecurityIdentifier)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.CompoundAce" />.</summary>
      <param name="flags">Содержит флаги, определяющие параметры наследования и распространения наследования, а также условия аудита нового элемента управления доступом.</param>
      <param name="accessMask">Маска доступа элемента управления доступом.</param>
      <param name="compoundAceType">Значение из перечисления <see cref="T:System.Security.AccessControl.CompoundAceType" />.</param>
      <param name="sid">Объект <see cref="T:System.Security.Principal.SecurityIdentifier" />, связанный с новым элементом управления доступом.</param>
    </member>
    <member name="P:System.Security.AccessControl.CompoundAce.BinaryLength">
      <summary>Получает длину в байтах двоичного представления текущего объекта <see cref="T:System.Security.AccessControl.CompoundAce" />.Эта длина должна использоваться перед маршалингом списка управления доступом в двоичный массив с помощью метода <see cref="M:System.Security.AccessControl.CompoundAce.GetBinaryForm" />.</summary>
      <returns>Длина в байтах двоичного представления текущего объекта <see cref="T:System.Security.AccessControl.CompoundAce" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CompoundAce.CompoundAceType">
      <summary>Получает или задает тип этого объекта <see cref="T:System.Security.AccessControl.CompoundAce" />.</summary>
      <returns>Тип данного объекта <see cref="T:System.Security.AccessControl.CompoundAce" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.CompoundAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Маршалирует содержимое объекта <see cref="T:System.Security.AccessControl.CompoundAce" /> в указанный массив байтов, начиная с указанного смещения.</summary>
      <param name="binaryForm">Массив байтов, в который упаковывается содержимое объекта <see cref="T:System.Security.AccessControl.CompoundAce" />.</param>
      <param name="offset">Позиция, с которой начинается маршалинг.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение <paramref name="offset" /> отрицательно или слишком велико, чтобы можно было скопировать весь объект <see cref="T:System.Security.AccessControl.CompoundAce" /> в массив <paramref name="array" />.</exception>
    </member>
    <member name="T:System.Security.AccessControl.CompoundAceType">
      <summary>Определяет тип объекта <see cref="T:System.Security.AccessControl.CompoundAce" />.</summary>
    </member>
    <member name="F:System.Security.AccessControl.CompoundAceType.Impersonation">
      <summary>Объект <see cref="T:System.Security.AccessControl.CompoundAce" /> используется для олицетворения.</summary>
    </member>
    <member name="T:System.Security.AccessControl.ControlFlags">
      <summary>Эти флаги влияют на поведение дескриптора безопасности.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclAutoInherited">
      <summary>Указывает, что список управления доступом на уровне пользователей автоматически наследуется у родительского элемента.Задается только диспетчером ресурсов.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclAutoInheritRequired">
      <summary>Игнорируется.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclDefaulted">
      <summary>Указывает, что список управления доступом на уровне пользователей был получен с помощью механизма установки значений по умолчанию.Задается только диспетчером ресурсов.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclPresent">
      <summary>Указывает, что список управления доступом на уровне пользователей не равен null.Задается диспетчером ресурсов или пользователями.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclProtected">
      <summary>Указывает, что диспетчер ресурсов не допускает автоматического наследования.Задается диспетчером ресурсов или пользователями.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclUntrusted">
      <summary>Игнорируется.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.GroupDefaulted">
      <summary>Указывает, что группа <see cref="T:System.Security.Principal.SecurityIdentifier" /> была получена с помощью механизма установки значений по умолчанию.Задается только диспетчером ресурсов; не должен задаваться вызывающими объектами.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.None">
      <summary>Нет флагов управления.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.OwnerDefaulted">
      <summary>Указывает, что владелец <see cref="T:System.Security.Principal.SecurityIdentifier" /> был получен с помощью механизма установки значений по умолчанию.Задается только диспетчером ресурсов; не должен задаваться вызывающими объектами.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.RMControlValid">
      <summary>Указывает, что содержимое поля Reserved действительно.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SelfRelative">
      <summary>Указывает, что двоичное представление дескриптора безопасности не относительное.  Этот флаг всегда установлен.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.ServerSecurity">
      <summary>Игнорируется.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclAutoInherited">
      <summary>Указывает, что системный список управления доступом автоматически наследуется у родительского элемента.Задается только диспетчером ресурсов.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclAutoInheritRequired">
      <summary>Игнорируется.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclDefaulted">
      <summary>Указывает, что системный список управления доступом был получен с помощью механизма установки значений по умолчанию.Задается только диспетчером ресурсов.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclPresent">
      <summary>Указывает, что системный список управления доступом не равен null.Задается диспетчером ресурсов или пользователями.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclProtected">
      <summary>Указывает, что диспетчер ресурсов не допускает автоматического наследования.Задается диспетчером ресурсов или пользователями.</summary>
    </member>
    <member name="T:System.Security.AccessControl.CustomAce">
      <summary>Представляет элемент управления доступом, тип которого не является членом перечисления <see cref="T:System.Security.AccessControl.AceType" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.CustomAce.#ctor(System.Security.AccessControl.AceType,System.Security.AccessControl.AceFlags,System.Byte[])">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.CustomAce" />.</summary>
      <param name="type">Тип нового элемента управления доступом.Значение должно быть больше, чем <see cref="F:System.Security.AccessControl.AceType.MaxDefinedAceType" />.</param>
      <param name="flags">Флаги, определяющие параметры наследования и распространения наследования, а также условия аудита нового элемента управления доступом.</param>
      <param name="opaque">Массив байтовых значений, содержащий данные нового элемента управления доступом.Это значение может быть равно null.Длина этого массива не должна превышать значения поля <see cref="F:System.Security.AccessControl.CustomAce.MaxOpaqueLength" /> и должно быть кратным четырем.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="type" /> не превышает <see cref="F:System.Security.AccessControl.AceType.MaxDefinedAceType" /> или длина массива <paramref name="opaque" /> превышает значение поля <see cref="F:System.Security.AccessControl.CustomAce.MaxOpaqueLength" /> или не кратна четырем.</exception>
    </member>
    <member name="P:System.Security.AccessControl.CustomAce.BinaryLength">
      <summary>Получает длину в байтах двоичного представления текущего объекта <see cref="T:System.Security.AccessControl.CustomAce" />.Эта длина должна использоваться перед маршалингом списка управления доступом в двоичный массив с помощью метода <see cref="M:System.Security.AccessControl.CustomAce.GetBinaryForm" />.</summary>
      <returns>Длина в байтах двоичного представления текущего объекта <see cref="T:System.Security.AccessControl.CustomAce" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.CustomAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Маршалирует содержимое объекта <see cref="T:System.Security.AccessControl.CustomAce" /> в указанный массив байтов, начиная с указанного смещения.</summary>
      <param name="binaryForm">Массив байтов, в который маршалируется содержимое объекта <see cref="T:System.Security.AccessControl.CustomAce" />.</param>
      <param name="offset">Позиция, с которой начинается маршалинг.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="offset" /> имеет отрицательное или слишком большое значение, чтобы можно было скопировать весь объект <see cref="T:System.Security.AccessControl.CustomAce" /> в параметр <paramref name="array" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.CustomAce.GetOpaque">
      <summary>Возвращает длину непрозрачных данных, связанных с этим объектом <see cref="T:System.Security.AccessControl.CustomAce" />. </summary>
      <returns>Массив байтов, представляющий непрозрачные данные, связанные с данным объектом <see cref="T:System.Security.AccessControl.CustomAce" />.</returns>
    </member>
    <member name="F:System.Security.AccessControl.CustomAce.MaxOpaqueLength">
      <summary>Возвращает максимально допустимую длину большого двоичного объекта непрозрачных данных для данного объекта <see cref="T:System.Security.AccessControl.CustomAce" />.</summary>
    </member>
    <member name="P:System.Security.AccessControl.CustomAce.OpaqueLength">
      <summary>Получает длину непрозрачных данных, связанных с этим объектом <see cref="T:System.Security.AccessControl.CustomAce" />.</summary>
      <returns>Длина непрозрачных данных обратного вызова в байтах.</returns>
    </member>
    <member name="M:System.Security.AccessControl.CustomAce.SetOpaque(System.Byte[])">
      <summary>Задает непрозрачные данные обратного вызова, связанные с этим объектом <see cref="T:System.Security.AccessControl.CustomAce" />.</summary>
      <param name="opaque">Массив байтов, представляющий непрозрачные данные обратного вызова для данного объекта <see cref="T:System.Security.AccessControl.CustomAce" />.</param>
    </member>
    <member name="T:System.Security.AccessControl.DiscretionaryAcl">
      <summary>Представляет список управления доступом на уровне пользователей.</summary>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.#ctor(System.Boolean,System.Boolean,System.Byte,System.Int32)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> с использованием указанных значений.</summary>
      <param name="isContainer">Значение true, если новый объект <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> является контейнером.</param>
      <param name="isDS">Значение true, если новый объект <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> является объектом каталога списка управления доступом.</param>
      <param name="revision">Номер редакции нового объекта <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />.</param>
      <param name="capacity">Количество элементов управления доступом, которые могут содержаться в данном объекте <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />.Это число будет использоваться только для справки.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.#ctor(System.Boolean,System.Boolean,System.Int32)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> с использованием указанных значений.</summary>
      <param name="isContainer">Значение true, если новый объект <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> является контейнером.</param>
      <param name="isDS">Значение true, если новый объект <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> является объектом каталога списка управления доступом.</param>
      <param name="capacity">Количество элементов управления доступом, которые могут содержаться в данном объекте <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />.Это число будет использоваться только для справки.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.#ctor(System.Boolean,System.Boolean,System.Security.AccessControl.RawAcl)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> с указанными значениями из указанного объекта <see cref="T:System.Security.AccessControl.RawAcl" />.</summary>
      <param name="isContainer">Значение true, если новый объект <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> является контейнером.</param>
      <param name="isDS">Значение true, если новый объект <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> является объектом каталога списка управления доступом.</param>
      <param name="rawAcl">Базовый объект <see cref="T:System.Security.AccessControl.RawAcl" />, соответствующий новому объекту <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />.Чтобы создать пустой список управления доступом, укажите значение null.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.AddAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Добавляет элемент управления доступом с указанными параметрами к текущему объекту <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />.</summary>
      <param name="accessType">Тип добавляемого элемента управления доступом («разрешить» или «запретить»).</param>
      <param name="sid">Объект <see cref="T:System.Security.Principal.SecurityIdentifier" />, для которого добавляется элемент управления доступом.</param>
      <param name="accessMask">Правило доступа для нового элемента управления доступом.</param>
      <param name="inheritanceFlags">Флаги, определяющие свойства наследования нового элемента управления доступом.</param>
      <param name="propagationFlags">Флаги, определяющие свойства распространения наследования нового элемента управления доступом.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.AddAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Добавляет элемент управления доступом с указанными параметрами к текущему объекту <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />.Используйте этот метод для списков управления доступом объектов каталогов при указании типа объекта или типа наследуемого объекта для нового элемента управления доступом.</summary>
      <param name="accessType">Тип добавляемого элемента управления доступом («разрешить» или «запретить»).</param>
      <param name="sid">Объект <see cref="T:System.Security.Principal.SecurityIdentifier" />, для которого добавляется элемент управления доступом.</param>
      <param name="accessMask">Правило доступа для нового элемента управления доступом.</param>
      <param name="inheritanceFlags">Флаги, определяющие свойства наследования нового элемента управления доступом.</param>
      <param name="propagationFlags">Флаги, определяющие свойства распространения наследования нового элемента управления доступом.</param>
      <param name="objectFlags">Флаги, определяющие, содержат ли параметры <paramref name="objectType" /> и <paramref name="inheritedObjectType" /> значения, отличные от null.</param>
      <param name="objectType">Идентификатор класса объектов, к которым применяется новый элемент управления доступом.</param>
      <param name="inheritedObjectType">Идентификатор класса дочерних объектов, которые могут наследовать новый элемент управления доступом.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.AddAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAccessRule)">
      <summary>Добавляет элемент управления доступом с указанными параметрами к текущему объекту <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />.</summary>
      <param name="accessType">Тип добавляемого элемента управления доступом («разрешить» или «запретить»).</param>
      <param name="sid">Объект <see cref="T:System.Security.Principal.SecurityIdentifier" />, для которого добавляется элемент управления доступом.</param>
      <param name="rule">
        <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> Для новых доступа.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Удаляет указанное правило управления доступом из текущего объекта <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />.</summary>
      <returns>Значение true, если метод успешно удаляет указанное правило доступа; в противном случае — значение false.</returns>
      <param name="accessType">Тип удаляемого элемента управления доступом («разрешить» или «запретить»).</param>
      <param name="sid">Объект <see cref="T:System.Security.Principal.SecurityIdentifier" />, для которого удаляется правило управления доступом.</param>
      <param name="accessMask">Маска доступа для удаляемого правила.</param>
      <param name="inheritanceFlags">Флаги, определяющие свойства наследования удаляемого правила.</param>
      <param name="propagationFlags">Флаги, определяющие свойства распространения наследования удаляемого правила.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Удаляет указанное правило управления доступом из текущего объекта <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />.Используйте этот метод для списков управления доступом объектов каталогов при указании типа объекта или типа наследуемого объекта.</summary>
      <returns>Значение true, если метод успешно удаляет указанное правило доступа; в противном случае — значение false.</returns>
      <param name="accessType">Тип удаляемого элемента управления доступом («разрешить» или «запретить»).</param>
      <param name="sid">Объект <see cref="T:System.Security.Principal.SecurityIdentifier" />, для которого удаляется правило управления доступом.</param>
      <param name="accessMask">Маска доступа для удаляемого правила управления доступом.</param>
      <param name="inheritanceFlags">Флаги, определяющие свойства наследования удаляемого правила управления доступом.</param>
      <param name="propagationFlags">Флаги, определяющие свойства распространения наследования удаляемого правила управления доступом.</param>
      <param name="objectFlags">Флаги, определяющие, содержат ли параметры <paramref name="objectType" /> и <paramref name="inheritedObjectType" /> значения, отличные от null.</param>
      <param name="objectType">Идентификатор класса объектов, к которым применяется удаляемое правило управления доступом.</param>
      <param name="inheritedObjectType">Идентификатор класса дочерних объектов, которые могут наследовать удаляемое правило управления доступом.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAccessRule)">
      <summary>Удаляет указанное правило управления доступом из текущего объекта <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.</returns>
      <param name="accessType">Тип удаляемого элемента управления доступом («разрешить» или «запретить»).</param>
      <param name="sid">Объект <see cref="T:System.Security.Principal.SecurityIdentifier" />, для которого удаляется правило управления доступом.</param>
      <param name="rule">
        <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> Для которого необходимо удалить доступ.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccessSpecific(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Удаляет указанный элемент управления доступом из текущего объекта <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />.</summary>
      <param name="accessType">Тип удаляемого элемента управления доступом («разрешить» или «запретить»).</param>
      <param name="sid">Объект <see cref="T:System.Security.Principal.SecurityIdentifier" />, для которого удаляется элемент управления доступом.</param>
      <param name="accessMask">Маска доступа для удаляемого элемента управления доступом.</param>
      <param name="inheritanceFlags">Флаги, определяющие свойства наследования удаляемого элемента управления доступом.</param>
      <param name="propagationFlags">Флаги, определяющие свойства распространения наследования удаляемого элемента управления доступом.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccessSpecific(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Удаляет указанный элемент управления доступом из текущего объекта <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />.Используйте этот метод для списков управления доступом объектов каталогов при указании типа объекта или типа наследуемого объекта для удаляемого элемента управления доступом.</summary>
      <param name="accessType">Тип удаляемого элемента управления доступом («разрешить» или «запретить»).</param>
      <param name="sid">Объект <see cref="T:System.Security.Principal.SecurityIdentifier" />, для которого удаляется элемент управления доступом.</param>
      <param name="accessMask">Маска доступа для удаляемого элемента управления доступом.</param>
      <param name="inheritanceFlags">Флаги, определяющие свойства наследования удаляемого элемента управления доступом.</param>
      <param name="propagationFlags">Флаги, определяющие свойства распространения наследования удаляемого элемента управления доступом.</param>
      <param name="objectFlags">Флаги, определяющие, содержат ли параметры <paramref name="objectType" /> и <paramref name="inheritedObjectType" /> значения, отличные от null.</param>
      <param name="objectType">Идентификатор класса объектов, к которым применяется удаляемый элемент управления доступом.</param>
      <param name="inheritedObjectType">Идентификатор класса дочерних объектов, которые могут наследовать удаляемый элемент управления доступом.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccessSpecific(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAccessRule)">
      <summary>Удаляет указанный элемент управления доступом из текущего объекта <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />.</summary>
      <param name="accessType">Тип удаляемого элемента управления доступом («разрешить» или «запретить»).</param>
      <param name="sid">Объект <see cref="T:System.Security.Principal.SecurityIdentifier" />, для которого удаляется элемент управления доступом.</param>
      <param name="rule">
        <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> Для которого необходимо удалить доступ.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.SetAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Задает указанный элемент управления доступом для указанного объекта <see cref="T:System.Security.Principal.SecurityIdentifier" />.</summary>
      <param name="accessType">Тип устанавливаемого элемента управления доступом («разрешить» или «запретить»).</param>
      <param name="sid">Объект <see cref="T:System.Security.Principal.SecurityIdentifier" />, для которого нужно установить элемент управления доступом.</param>
      <param name="accessMask">Правило доступа для нового элемента управления доступом.</param>
      <param name="inheritanceFlags">Флаги, определяющие свойства наследования нового элемента управления доступом.</param>
      <param name="propagationFlags">Флаги, определяющие свойства распространения наследования нового элемента управления доступом.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.SetAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Задает указанный элемент управления доступом для указанного объекта <see cref="T:System.Security.Principal.SecurityIdentifier" />.</summary>
      <param name="accessType">Тип устанавливаемого элемента управления доступом («разрешить» или «запретить»).</param>
      <param name="sid">Объект <see cref="T:System.Security.Principal.SecurityIdentifier" />, для которого нужно установить элемент управления доступом.</param>
      <param name="accessMask">Правило доступа для нового элемента управления доступом.</param>
      <param name="inheritanceFlags">Флаги, определяющие свойства наследования нового элемента управления доступом.</param>
      <param name="propagationFlags">Флаги, определяющие свойства распространения наследования нового элемента управления доступом.</param>
      <param name="objectFlags">Флаги, определяющие, содержат ли параметры <paramref name="objectType" /> и <paramref name="inheritedObjectType" /> значения, отличные от null.</param>
      <param name="objectType">Идентификатор класса объектов, к которым применяется новый элемент управления доступом.</param>
      <param name="inheritedObjectType">Идентификатор класса дочерних объектов, которые могут наследовать новый элемент управления доступом.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.SetAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAccessRule)">
      <summary>Задает указанный элемент управления доступом для указанного объекта <see cref="T:System.Security.Principal.SecurityIdentifier" />.</summary>
      <param name="accessType">Тип устанавливаемого элемента управления доступом («разрешить» или «запретить»).</param>
      <param name="sid">Объект <see cref="T:System.Security.Principal.SecurityIdentifier" />, для которого нужно установить элемент управления доступом.</param>
      <param name="rule">
        <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> Для которого требуется задать доступ.</param>
    </member>
    <member name="T:System.Security.AccessControl.GenericAce">
      <summary>Представляет элемент управления доступом и является базовым классом для всех остальных классов элементов управления доступом.</summary>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.AceFlags">
      <summary>Получает или задает объект <see cref="T:System.Security.AccessControl.AceFlags" />, связанный с объектом <see cref="T:System.Security.AccessControl.GenericAce" />.</summary>
      <returns>Объект <see cref="T:System.Security.AccessControl.AceFlags" />, связанный с данным объектом <see cref="T:System.Security.AccessControl.GenericAce" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.AceType">
      <summary>Получает тип данного элемента управления доступом.</summary>
      <returns>Тип данного элемента управления доступом.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.AuditFlags">
      <summary>Получает сведения об аудите, связанные с данным элементом управления доступом.</summary>
      <returns>Сведения об аудите, связанные с данным элементом управления доступом.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.BinaryLength">
      <summary>Получает длину в байтах двоичного представления текущего объекта <see cref="T:System.Security.AccessControl.GenericAce" />.Эта длина должна использоваться перед маршалингом списка управления доступом в двоичный массив с помощью метода <see cref="M:System.Security.AccessControl.GenericAce.GetBinaryForm" />.</summary>
      <returns>Длина в байтах двоичного представления текущего объекта <see cref="T:System.Security.AccessControl.GenericAce" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.Copy">
      <summary>Создает глубокую копию данного элемента управления доступом.</summary>
      <returns>Объект <see cref="T:System.Security.AccessControl.GenericAce" />, создаваемый с помощью данного метода.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.CreateFromBinaryForm(System.Byte[],System.Int32)">
      <summary>Создает объект <see cref="T:System.Security.AccessControl.GenericAce" /> из указанных двоичных данных.</summary>
      <returns>Объект <see cref="T:System.Security.AccessControl.GenericAce" />, созданный с помощью данного метода.</returns>
      <param name="binaryForm">Двоичные данные, из которых создается новый объект <see cref="T:System.Security.AccessControl.GenericAce" />.</param>
      <param name="offset">Позиция, с которой начинается распаковка.</param>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.Equals(System.Object)">
      <summary>Определяет, равен ли заданный объект <see cref="T:System.Security.AccessControl.GenericAce" /> текущему объекту <see cref="T:System.Security.AccessControl.GenericAce" />.</summary>
      <returns>Значение true, если заданный объект <see cref="T:System.Security.AccessControl.GenericAce" /> равен текущему объекту <see cref="T:System.Security.AccessControl.GenericAce" />; в противном случае — значение false.</returns>
      <param name="o">Объект <see cref="T:System.Security.AccessControl.GenericAce" />, который требуется сравнить с текущим объектом <see cref="T:System.Security.AccessControl.GenericAce" />.</param>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Маршалирует содержимое объекта <see cref="T:System.Security.AccessControl.GenericAce" /> в указанный массив байтов, начиная с указанного смещения.</summary>
      <param name="binaryForm">Массив байтов, в который маршалируется содержимое объекта <see cref="T:System.Security.AccessControl.GenericAce" />.</param>
      <param name="offset">Позиция, с которой начинается маршалинг.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="offset" /> имеет отрицательное или слишком большое значение, чтобы можно было скопировать весь объект <see cref="T:System.Security.AccessControl.GenericAcl" /> в параметр <paramref name="array" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.GetHashCode">
      <summary>Служит хэш-функцией для класса <see cref="T:System.Security.AccessControl.GenericAce" />.Метод <see cref="M:System.Security.AccessControl.GenericAce.GetHashCode" /> можно использовать в алгоритмах хэширования и структурах данных, подобных хэш-таблицам.</summary>
      <returns>Хэш-код для текущего объекта <see cref="T:System.Security.AccessControl.GenericAce" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.InheritanceFlags">
      <summary>Получает флаги, определяющие свойства наследования данного элемента управления доступом.</summary>
      <returns>Флаги, определяющие свойства наследования данного элемента управления доступом.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.IsInherited">
      <summary>Получает логическое значение, определяющее, наследуется данный элемент управления доступом или задается явным образом.</summary>
      <returns>Значение true, если элемент управления доступом наследуется; в противном случае — false.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.op_Equality(System.Security.AccessControl.GenericAce,System.Security.AccessControl.GenericAce)">
      <summary>Определяет, считаются ли равными заданные объекты <see cref="T:System.Security.AccessControl.GenericAce" />.</summary>
      <returns>Значение true, если два объекта <see cref="T:System.Security.AccessControl.GenericAce" /> равны; в противном случае — значение false.</returns>
      <param name="left">Первый сравниваемый объект <see cref="T:System.Security.AccessControl.GenericAce" />.</param>
      <param name="right">Второй объект <see cref="T:System.Security.AccessControl.GenericAce" /> для сравнения.</param>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.op_Inequality(System.Security.AccessControl.GenericAce,System.Security.AccessControl.GenericAce)">
      <summary>Определяет, считаются ли заданные объекты <see cref="T:System.Security.AccessControl.GenericAce" /> неравными.</summary>
      <returns>Значение true, если два объекта <see cref="T:System.Security.AccessControl.GenericAce" /> не равны; в противном случае — значение false.</returns>
      <param name="left">Первый сравниваемый объект <see cref="T:System.Security.AccessControl.GenericAce" />.</param>
      <param name="right">Второй объект <see cref="T:System.Security.AccessControl.GenericAce" /> для сравнения.</param>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.PropagationFlags">
      <summary>Получает флаги, определяющие свойства распространения наследования данного элемента управления доступом.</summary>
      <returns>Флаги, определяющие свойства распространения наследования данного элемента управления доступом.</returns>
    </member>
    <member name="T:System.Security.AccessControl.GenericAcl">
      <summary>Представляет список управления доступом и является базовым классом для классов <see cref="T:System.Security.AccessControl.CommonAcl" />, <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />, <see cref="T:System.Security.AccessControl.RawAcl" /> и <see cref="T:System.Security.AccessControl.SystemAcl" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.GenericAcl" />.</summary>
    </member>
    <member name="F:System.Security.AccessControl.GenericAcl.AclRevision">
      <summary>Номер редакции текущего объекта <see cref="T:System.Security.AccessControl.GenericAcl" />.Это значение возвращается свойством <see cref="P:System.Security.AccessControl.GenericAcl.Revision" /> списков управления доступом, не связанных с объектами служб каталогов.</summary>
    </member>
    <member name="F:System.Security.AccessControl.GenericAcl.AclRevisionDS">
      <summary>Номер редакции текущего объекта <see cref="T:System.Security.AccessControl.GenericAcl" />.Это значение возвращается свойством <see cref="P:System.Security.AccessControl.GenericAcl.Revision" /> списков управления доступом, связанных с объектами служб каталогов.</summary>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.BinaryLength">
      <summary>Получает длину в байтах двоичного представления текущего объекта <see cref="T:System.Security.AccessControl.GenericAcl" />.Эта длина должна использоваться перед маршалингом списка управления доступом в двоичный массив с помощью метода <see cref="M:System.Security.AccessControl.GenericAcl.GetBinaryForm" />.</summary>
      <returns>Длина в байтах двоичного представления текущего объекта <see cref="T:System.Security.AccessControl.GenericAcl" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.CopyTo(System.Security.AccessControl.GenericAce[],System.Int32)">
      <summary>Копирует все элементы <see cref="T:System.Security.AccessControl.GenericAce" /> текущего объекта <see cref="T:System.Security.AccessControl.GenericAcl" /> в указанный массив.</summary>
      <param name="array">Массив, в который помещаются копии элементов <see cref="T:System.Security.AccessControl.GenericAce" /> текущего объекта <see cref="T:System.Security.AccessControl.GenericAcl" />.</param>
      <param name="index">Отсчитываемый от нуля индекс позиции в массиве <paramref name="array" />, с которой начинается копирование.</param>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.Count">
      <summary>Получает количество элементов управления доступом в текущем объекте <see cref="T:System.Security.AccessControl.GenericAcl" />.</summary>
      <returns>Количество элементов управления доступом в текущем объекте <see cref="T:System.Security.AccessControl.GenericAcl" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Маршалирует содержимое объекта <see cref="T:System.Security.AccessControl.GenericAcl" /> в указанный массив байтов, начиная с указанного смещения.</summary>
      <param name="binaryForm">Массив байтов, в который маршалируется содержимое объекта <see cref="T:System.Security.AccessControl.GenericAcl" />.</param>
      <param name="offset">Позиция, с которой начинается маршалинг.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="offset" /> имеет отрицательное или слишком большое значение, чтобы можно было скопировать весь объект <see cref="T:System.Security.AccessControl.GenericAcl" /> в параметр <paramref name="array" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.GetEnumerator">
      <summary>Возвращает новый экземпляр класса <see cref="T:System.Security.AccessControl.AceEnumerator" />.</summary>
      <returns>Возвращаемое данным методом значение <see cref="T:Security.AccessControl.AceEnumerator" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.IsSynchronized">
      <summary>Этому свойству всегда присваивается значение false.Оно реализуется только потому, что оно требуется для реализации интерфейса <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Всегда имеет значение false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.Item(System.Int32)">
      <summary>Получает или задает объект <see cref="T:System.Security.AccessControl.GenericAce" /> с указанным индексом.</summary>
      <returns>Объект <see cref="T:System.Security.AccessControl.GenericAce" /> с указанным индексом.</returns>
      <param name="index">Начинающийся с нуля индекс получаемого или задаваемого объекта <see cref="T:System.Security.AccessControl.GenericAce" />.</param>
    </member>
    <member name="F:System.Security.AccessControl.GenericAcl.MaxBinaryLength">
      <summary>Максимально допустимая двоичная длина объекта <see cref="T:System.Security.AccessControl.GenericAcl" />.</summary>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.Revision">
      <summary>Получает уровень редакции объекта <see cref="T:System.Security.AccessControl.GenericAcl" />.</summary>
      <returns>Байтовое значение, определяющее уровень редакции объекта <see cref="T:System.Security.AccessControl.GenericAcl" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.SyncRoot">
      <summary>Данное свойство всегда возвращает значение null.Оно реализуется только потому, что оно требуется для реализации интерфейса <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Всегда возвращает значение null.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Копирует все элементы <see cref="T:System.Security.AccessControl.GenericAce" /> текущего объекта <see cref="T:System.Security.AccessControl.GenericAcl" /> в указанный массив.</summary>
      <param name="array">Массив, в который помещаются копии элементов <see cref="T:System.Security.AccessControl.GenericAce" /> текущего объекта <see cref="T:System.Security.AccessControl.GenericAcl" />.</param>
      <param name="index">Отсчитываемый от нуля индекс позиции в массиве <paramref name="array" />, с которой начинается копирование.</param>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.System#Collections#IEnumerable#GetEnumerator">
      <summary>Возвращает новый экземпляр класса <see cref="T:System.Security.AccessControl.AceEnumerator" />, являющийся реализацией интерфейса <see cref="T:System.Collections.IEnumerator" />.</summary>
      <returns>Новый объект <see cref="T:System.Security.AccessControl.AceEnumerator" />, являющийся реализацией интерфейса <see cref="T:System.Collections.IEnumerator" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.GenericSecurityDescriptor">
      <summary>Представляет дескриптор безопасности.Дескриптор безопасности включает владельца, основную группу, список управления доступом на уровне пользователей и системный список управления доступом.</summary>
    </member>
    <member name="M:System.Security.AccessControl.GenericSecurityDescriptor.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.GenericSecurity" />.</summary>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.BinaryLength">
      <summary>Получает длину в байтах двоичного представления текущего объекта <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.Эта длина должна использоваться перед маршалингом списка управления доступом в двоичный массив с помощью метода <see cref="M:System.Security.AccessControl.GenericSecurityDescriptor.GetBinaryForm" />.</summary>
      <returns>Длина в байтах двоичного представления текущего объекта <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.ControlFlags">
      <summary>Получает значения, определяющие поведение объекта <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</summary>
      <returns>Один или несколько членов перечисления <see cref="T:System.Security.AccessControl.ControlFlags" />, объединенных с помощью операции логического ИЛИ.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericSecurityDescriptor.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Возвращает массив байтовых значений, представляющих информацию, которая содержится в объекте <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</summary>
      <param name="binaryForm">Массив байтов, в который маршалируется содержимое объекта <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</param>
      <param name="offset">Позиция, с которой начинается маршалинг.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение <paramref name="offset" /> отрицательно или слишком велико, чтобы можно было скопировать весь объект <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> в массив <paramref name="array" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.GenericSecurityDescriptor.GetSddlForm(System.Security.AccessControl.AccessControlSections)">
      <summary>Возвращает представление указанных разделов дескриптора безопасности, представляемого данным объектом <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />, в формате SDDL (Security Descriptor Definition Language).</summary>
      <returns>Представление указанных разделов связанного с данным объектом <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> дескриптора безопасности в формате SDDL.</returns>
      <param name="includeSections">Определяет получаемые разделы дескриптора безопасности (правила доступа, правила аудита, основная группа, владелец).</param>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.Group">
      <summary>Получает или задает основную группу для данного объекта <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</summary>
      <returns>Основная группа для данного объекта <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericSecurityDescriptor.IsSddlConversionSupported">
      <summary>Возвращает логическое значение, которое указывает, можно ли преобразовать связанный с данным объектом <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> дескриптор безопасности в формат SDDL (Security Descriptor Definition Language).</summary>
      <returns>Значение true, если связанный с данным объектом <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> дескриптор безопасности можно преобразовать в формат SDDL; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.Owner">
      <summary>Получает или задает владельца объекта, связанного с данным объектом <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</summary>
      <returns>Владелец объекта, связанного с данным объектом <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.Revision">
      <summary>Получает номер редакции объекта <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</summary>
      <returns>Байтовое значение, определяющее уровень редакции объекта <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.InheritanceFlags">
      <summary>Флаги наследования определяют семантику наследования элементов управления доступом.</summary>
    </member>
    <member name="F:System.Security.AccessControl.InheritanceFlags.ContainerInherit">
      <summary>Элемент управления доступом наследуется дочерними объектами-контейнерами.</summary>
    </member>
    <member name="F:System.Security.AccessControl.InheritanceFlags.None">
      <summary>Элемент управления доступом не наследуется дочерними объектами.</summary>
    </member>
    <member name="F:System.Security.AccessControl.InheritanceFlags.ObjectInherit">
      <summary>Элемент управления доступом наследуется дочерними конечными объектами.</summary>
    </member>
    <member name="T:System.Security.AccessControl.KnownAce">
      <summary>Инкапсулирует все типы элементов управления доступом, которые в настоящее время определены корпорацией Майкрософт.Все объекты <see cref="T:System.Security.AccessControl.KnownAce" /> содержат 32-битные маски доступа и объект <see cref="T:System.Security.Principal.SecurityIdentifier" />.</summary>
    </member>
    <member name="P:System.Security.AccessControl.KnownAce.AccessMask">
      <summary>Получает или задает маску доступа для данного объекта <see cref="T:System.Security.AccessControl.KnownAce" />.</summary>
      <returns>Маска доступа для данного объекта <see cref="T:System.Security.AccessControl.KnownAce" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.KnownAce.SecurityIdentifier">
      <summary>Получает или задает объект <see cref="T:System.Security.Principal.SecurityIdentifier" />, связанный с данным объектом <see cref="T:System.Security.AccessControl.KnownAce" />.</summary>
      <returns>Объект <see cref="T:System.Security.Principal.SecurityIdentifier" />, связанный с данным объектом <see cref="T:System.Security.AccessControl.KnownAce" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.NativeObjectSecurity">
      <summary>Обеспечивает возможность управления доступом к основным объектам без непосредственной работы со списками управления доступом.Основные объекты определены в перечислении <see cref="T:System.Security.AccessControl.ResourceType" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> с использованием указанных значений.</summary>
      <param name="isContainer">Значение true, если новый объект <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> является контейнером.</param>
      <param name="resourceType">Тип защищаемого объекта, с которым связан новый объект <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> с использованием указанных значений.Рекомендуется, чтобы значения параметров <paramref name="includeSections" />, передаваемых конструктору и методам сохранения, были одинаковыми.Дополнительные сведения см. в разделе "Примечания".</summary>
      <param name="isContainer">Значение true, если новый объект <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> является контейнером.</param>
      <param name="resourceType">Тип защищаемого объекта, с которым связан новый объект <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="handle">Дескриптор защищаемого объекта, с которым связан новый объект <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="includeSections">Одно из значений перечисления <see cref="T:System.Security.AccessControl.AccessControlSections" />, определяющее разделы дескриптора безопасности защищаемого объекта (правила доступа, правила аудита, владелец, основная группа), которые необходимо включить в данный объект <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> с использованием указанных значений.Рекомендуется, чтобы значения параметров <paramref name="includeSections" />, передаваемых конструктору и методам сохранения, были одинаковыми.Дополнительные сведения см. в разделе "Примечания".</summary>
      <param name="isContainer">Значение true, если новый объект <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> является контейнером.</param>
      <param name="resourceType">Тип защищаемого объекта, с которым связан новый объект <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="handle">Дескриптор защищаемого объекта, с которым связан новый объект <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="includeSections">Одно из значений перечисления <see cref="T:System.Security.AccessControl.AccessControlSections" />, определяющее разделы дескриптора безопасности защищаемого объекта (правила доступа, правила аудита, владелец, основная группа), которые необходимо включить в данный объект <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="exceptionFromErrorCode">Делегат, реализованный интеграторами и обеспечивающий дополнительные исключения. </param>
      <param name="exceptionContext">Объект, содержащий контекстные сведения об источнике или назначении исключения.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>Инициализирует новый экземпляр <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />, используя указанные значения.</summary>
      <param name="isContainer">Значение true, если новый объект <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> является контейнером.</param>
      <param name="resourceType">Тип защищаемого объекта, с которым связан новый объект <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="exceptionFromErrorCode">Делегат, реализованный интеграторами и обеспечивающий дополнительные исключения. </param>
      <param name="exceptionContext">Объект, содержащий контекстные сведения об источнике или назначении исключения.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> с использованием указанных значений.Рекомендуется, чтобы значения параметров <paramref name="includeSections" />, передаваемых конструктору и методам сохранения, были одинаковыми.Дополнительные сведения см. в разделе "Примечания".</summary>
      <param name="isContainer">Значение true, если новый объект <see cref="T:System.Security.AccessControl.NativObjectSecurity" /> является контейнером.</param>
      <param name="resourceType">Тип защищаемого объекта, с которым связан новый объект <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="name">Имя защищаемого объекта, с которым связан новый объект <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="includeSections">Одно из значений перечисления <see cref="T:System.Security.AccessControl.AccessControlSections" />, определяющее разделы дескриптора безопасности защищаемого объекта (правила доступа, правила аудита, владелец, основная группа), которые необходимо включить в данный объект <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.String,System.Security.AccessControl.AccessControlSections,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> с использованием указанных значений.Рекомендуется, чтобы значения параметров <paramref name="includeSections" />, передаваемых конструктору и методам сохранения, были одинаковыми.Дополнительные сведения см. в разделе "Примечания".</summary>
      <param name="isContainer">Значение true, если новый объект <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> является контейнером.</param>
      <param name="resourceType">Тип защищаемого объекта, с которым связан новый объект <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="name">Имя защищаемого объекта, с которым связан новый объект <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="includeSections">Одно из значений перечисления <see cref="T:System.Security.AccessControl.AccessControlSections" />, определяющее разделы дескриптора безопасности защищаемого объекта (правила доступа, правила аудита, владелец, основная группа), которые необходимо включить в данный объект <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="exceptionFromErrorCode">Делегат, реализованный интеграторами и обеспечивающий дополнительные исключения. </param>
      <param name="exceptionContext">Объект, содержащий контекстные сведения об источнике или назначении исключения.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.Persist(System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections)">
      <summary>Сохраняет указанные разделы дескриптора безопасности, связанного с данным объектом <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />, в постоянном хранилище.Рекомендуется, чтобы значения параметров <paramref name="includeSections" />, передаваемых конструктору и методам сохранения, были одинаковыми.Дополнительные сведения см. в разделе "Примечания".</summary>
      <param name="handle">Дескриптор защищаемого объекта, с которым связан данный объект <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="includeSections">Одно из значений перечисления <see cref="T:System.Security.AccessControl.AccessControlSections" />, определяющее разделы дескриптора безопасности защищаемого объекта (правила доступа, правила аудита, владелец, основная группа), которые необходимо сохранить.</param>
      <exception cref="T:System.IO.FileNotFoundException">Защищаемый объект, с которым связан данный объект <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />, является каталогом или файлом, который не удается найти.</exception>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.Persist(System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections,System.Object)">
      <summary>Сохраняет указанные разделы дескриптора безопасности, связанного с данным объектом <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />, в постоянном хранилище.Рекомендуется, чтобы значения параметров <paramref name="includeSections" />, передаваемых конструктору и методам сохранения, были одинаковыми.Дополнительные сведения см. в разделе "Примечания".</summary>
      <param name="handle">Дескриптор защищаемого объекта, с которым связан данный объект <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="includeSections">Одно из значений перечисления <see cref="T:System.Security.AccessControl.AccessControlSections" />, определяющее разделы дескриптора безопасности защищаемого объекта (правила доступа, правила аудита, владелец, основная группа), которые необходимо сохранить.</param>
      <param name="exceptionContext">Объект, содержащий контекстные сведения об источнике или назначении исключения.</param>
      <exception cref="T:System.IO.FileNotFoundException">Защищаемый объект, с которым связан данный объект <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />, является каталогом или файлом, который не удается найти.</exception>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.Persist(System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>Сохраняет указанные разделы дескриптора безопасности, связанного с данным объектом <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />, в постоянном хранилище.Рекомендуется, чтобы значения параметров <paramref name="includeSections" />, передаваемых конструктору и методам сохранения, были одинаковыми.Дополнительные сведения см. в разделе "Примечания".</summary>
      <param name="name">Имя защищаемого объекта, с которым связан данный объект <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="includeSections">Одно из значений перечисления <see cref="T:System.Security.AccessControl.AccessControlSections" />, определяющее разделы дескриптора безопасности защищаемого объекта (правила доступа, правила аудита, владелец, основная группа), которые необходимо сохранить.</param>
      <exception cref="T:System.IO.FileNotFoundException">Защищаемый объект, с которым связан данный объект <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />, является каталогом или файлом, который не удается найти.</exception>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.Persist(System.String,System.Security.AccessControl.AccessControlSections,System.Object)">
      <summary>Сохраняет указанные разделы дескриптора безопасности, связанного с данным объектом <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />, в постоянном хранилище.Рекомендуется, чтобы значения параметров <paramref name="includeSections" />, передаваемых конструктору и методам сохранения, были одинаковыми.Дополнительные сведения см. в разделе "Примечания".</summary>
      <param name="name">Имя защищаемого объекта, с которым связан данный объект <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="includeSections">Одно из значений перечисления <see cref="T:System.Security.AccessControl.AccessControlSections" />, определяющее разделы дескриптора безопасности защищаемого объекта (правила доступа, правила аудита, владелец, основная группа), которые необходимо сохранить.</param>
      <param name="exceptionContext">Объект, содержащий контекстные сведения об источнике или назначении исключения.</param>
      <exception cref="T:System.IO.FileNotFoundException">Защищаемый объект, с которым связан данный объект <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />, является каталогом или файлом, который не удается найти.</exception>
    </member>
    <member name="T:System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode">
      <summary>Обеспечивает для интеграторов способ сопоставления числовых кодов ошибок с конкретными создаваемыми ими исключениями.</summary>
      <returns>Исключение <see cref="T:System.Exception" />, создаваемое этим делегатом.</returns>
      <param name="errorCode">Числовой код ошибки.</param>
      <param name="name">Имя защищаемого объекта, с которым связан объект <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="handle">Дескриптор защищаемого объекта, с которым связан объект <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="context">Объект, содержащий контекстные сведения об источнике или назначении исключения.</param>
    </member>
    <member name="T:System.Security.AccessControl.ObjectAccessRule">
      <summary>Представляет сочетание идентификатора пользователя, маски доступа и типа управления доступом ("разрешить" или "запретить").Кроме того, в объекте <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> содержатся сведения о типе объекта, к которому относится правило, типе дочернего объекта, который может наследовать правило, способе наследования правила дочерними объектами, а также способе распространения этого наследования.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAccessRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Guid,System.Guid,System.Security.AccessControl.AccessControlType)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> с использованием указанных значений.</summary>
      <param name="identity">Идентификатор, к которому применяется правило доступа.  Это должен быть объект, который можно привести к типу <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="accessMask">Маска доступа данного правила.Маска доступа представляет собой группу из 32 анонимных битов, значение каждого из которых определяется отдельными интеграторами.</param>
      <param name="isInherited">Значение true, если данное правило наследуется у родительского контейнера.</param>
      <param name="inheritanceFlags">Определяет свойства наследования правила доступа.</param>
      <param name="propagationFlags">Определяет возможность автоматического распространения наследуемых правил доступа.Флаги распространения игнорируются, если параметр <paramref name="inheritanceFlags" /> имеет значение <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="objectType">Тип объекта, к которому применяется правило доступа.</param>
      <param name="inheritedObjectType">Тип дочернего объекта, который может наследовать данное правило.</param>
      <param name="type">Определяет тип правила ("разрешить" или "запретить").</param>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="identity" /> нельзя привести к типу <see cref="T:System.Security.Principal.SecurityIdentifier" /> или параметр <paramref name="type" /> содержит недопустимое значение.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="accessMask" /> равно 0 или параметр <paramref name="inheritanceFlags" /> или <paramref name="propagationFlags" /> содержит неизвестное значение флага.</exception>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAccessRule.InheritedObjectType">
      <summary>Получает тип дочернего объекта, который может наследовать объект <see cref="System.Security.AccessControl.ObjectAccessRule" />.</summary>
      <returns>Тип дочернего объекта, который может наследовать объект <see cref="System.Security.AccessControl.ObjectAccessRule" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAccessRule.ObjectFlags">
      <summary>Получает флаги, определяющие, содержат ли свойства <see cref="P:System.Security.AccessControl.ObjectAccessRule.ObjectType" /> и <see cref="P:System.Security.AccessControl.ObjectAccessRule.InheritedObjectType" /> объекта <see cref="System.Security.AccessControl.ObjectAccessRule" /> допустимые значения.</summary>
      <returns>
        <see cref="F:System.Security.AccessControl.ObjectAceFlags.ObjectAceTypePresent" /> указывает на то, что свойство <see cref="P:System.Security.AccessControl.ObjectAccessRule.ObjectType" /> содержит допустимое значение.<see cref="F:System.Security.AccessControl.ObjectAceFlags.InheritedObjectAceTypePresent" /> указывает на то, что свойство <see cref="P:System.Security.AccessControl.ObjectAccessRule.InheritedObjectType" /> содержит допустимое значение.Эти значения можно объединять с помощью операции логического ИЛИ.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAccessRule.ObjectType">
      <summary>Получает тип объекта, к которому применяется <see cref="System.Security.AccessControl.ObjectAccessRule" />.</summary>
      <returns>Тип объекта, к которому применяется <see cref="System.Security.AccessControl.ObjectAccessRule" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.ObjectAce">
      <summary>Обеспечивает управление доступом к объектам служб каталогов.Этот класс представляет элемент управления доступом, связанный с объектом каталога.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAce.#ctor(System.Security.AccessControl.AceFlags,System.Security.AccessControl.AceQualifier,System.Int32,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid,System.Boolean,System.Byte[])">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.ObjectAce" />.</summary>
      <param name="aceFlags">Наследование, распространение наследования, условия аудита для нового элемента управления доступом.</param>
      <param name="qualifier">Функция нового элемента управления доступом.</param>
      <param name="accessMask">Маска доступа элемента управления доступом.</param>
      <param name="sid">Объект <see cref="T:System.Security.Principal.SecurityIdentifier" />, связанный с новым элементом управления доступом.</param>
      <param name="flags">Определяет, содержат ли параметры <paramref name="type" /> и <paramref name="inheritedType" /> допустимые значения GUID.</param>
      <param name="type">Идентификатор GUID, определяющий тип объекта, к которому относится новый элемент управления доступом.</param>
      <param name="inheritedType">Идентификатор GUID, определяющий тип объекта, который может наследовать новый элемент управления доступом.</param>
      <param name="isCallback">Значение true, если новый элемент управления доступом является элементом управления доступом типа обратного вызова.</param>
      <param name="opaque">Непрозрачные данные, связанные с новым элементом управления доступом.Допустимо только для элементов управления доступом обратного вызова.Длина этого массива не должна превышать значения, возвращаемого методом <see cref="M:System.Security.AccessControl.ObjectAceMaxOpaqueLength" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр qualifier содержит недопустимое значение или длина параметра opaque превышает значение, возвращаемое методом <see cref="M:System.Security.AccessControl.ObjectAceMaxOpaqueLength" />.</exception>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAce.BinaryLength">
      <summary>Получает длину в байтах двоичного представления текущего объекта <see cref="T:System.Security.AccessControl.ObjectAce" />.Эта длина должна использоваться перед маршалингом списка управления доступом в двоичный массив с помощью метода <see cref="M:System.Security.AccessControl.ObjectAce.GetBinaryForm" />.</summary>
      <returns>Длина в байтах двоичного представления текущего объекта <see cref="T:System.Security.AccessControl.ObjectAce" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Маршалирует содержимое объекта <see cref="T:System.Security.AccessControl.ObjectAce" /> в указанный массив байтов, начиная с указанной позиции.</summary>
      <param name="binaryForm">Массив байтов, в который маршалируется содержимое объекта <see cref="T:System.Security.AccessControl.ObjectAce" />.</param>
      <param name="offset">Позиция, с которой начинается маршалинг.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение <paramref name="offset" /> отрицательно или слишком велико, чтобы можно было скопировать весь объект <see cref="T:System.Security.AccessControl.ObjectAce" /> в массив <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAce.InheritedObjectAceType">
      <summary>Получает или задает GUID типа объекта, который может наследовать элемент управления доступом, представляемый данным объектом <see cref="T:System.Security.AccessControl.ObjectAce" />.</summary>
      <returns>GUID типа объекта, который может наследовать элемент управления доступом, представляемый данным объектом <see cref="T:System.Security.AccessControl.ObjectAce" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAce.MaxOpaqueLength(System.Boolean)">
      <summary>Возвращает максимально допустимую длину (в байтах) большого двоичного объекта непрозрачных данных для элементов управления доступом обратного вызова.</summary>
      <returns>Максимально допустимая длина (в байтах) большого двоичного объекта непрозрачных данных элементов управления доступом обратного вызова.</returns>
      <param name="isCallback">Значение true, если объект <see cref="T:System.Security.AccessControl.ObjectAce" /> является элементом управления доступом обратного вызова.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAce.ObjectAceFlags">
      <summary>Получает или задает флаги, определяющие, содержат ли свойства <see cref="P:System.Security.AccessControl.ObjectAce.ObjectAceType" /> и <see cref="P:System.Security.AccessControl.ObjectAce.InheritedObjectAceType" /> значения, соответствующие допустимым типам объектов.</summary>
      <returns>Один или несколько членов перечисления <see cref="T:System.Security.AccessControl.ObjectAceFlags" />, объединенные с помощью операции логического ИЛИ.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAce.ObjectAceType">
      <summary>Получает или задает GUID типа объекта, связанного с объектом <see cref="T:System.Security.AccessControl.ObjectAce" />.</summary>
      <returns>GUID типа объекта, связанного с объектом <see cref="T:System.Security.AccessControl.ObjectAce" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.ObjectAceFlags">
      <summary>Определяет наличие типов объектов для элементов управления доступом.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ObjectAceFlags.InheritedObjectAceTypePresent">
      <summary>Тип объекта, который может наследовать элемент управления доступом.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ObjectAceFlags.None">
      <summary>Тип объекта отсутствует.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ObjectAceFlags.ObjectAceTypePresent">
      <summary>Присутствует тип объекта, связанный с элементом управления доступом.</summary>
    </member>
    <member name="T:System.Security.AccessControl.ObjectAuditRule">
      <summary>Представляет сочетание идентификатора пользователя, маски доступа и условий аудита.Кроме того, в объекте <see cref="T:System.Security.AccessControl.ObjectAuditRule" /> содержатся сведения о типе объекта, к которому относится данное правило, типе дочернего объекта, который может наследовать правило, способе наследования правила дочерними объектами, а также о способе распространения этого наследования.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAuditRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Guid,System.Guid,System.Security.AccessControl.AuditFlags)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.ObjectAuditRule" />.</summary>
      <param name="identity">Идентификатор, к которому применяется правило доступа.  Это должен быть объект, который можно привести к типу <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="accessMask">Маска доступа данного правила.Маска доступа представляет собой группу из 32 анонимных битов, значение каждого из которых определяется отдельными интеграторами.</param>
      <param name="isInherited">Значение true, если данное правило наследуется у родительского контейнера.</param>
      <param name="inheritanceFlags">Определяет свойства наследования правила доступа.</param>
      <param name="propagationFlags">Определяет возможность автоматического распространения наследуемых правил доступа.Флаги распространения игнорируются, если параметр <paramref name="inheritanceFlags" /> имеет значение <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="objectType">Тип объекта, к которому применяется правило доступа.</param>
      <param name="inheritedObjectType">Тип дочернего объекта, который может наследовать данное правило.</param>
      <param name="auditFlags">Условия аудита.</param>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="identity" /> нельзя привести к типу <see cref="T:System.Security.Principal.SecurityIdentifier" /> или параметр <paramref name="type" /> содержит недопустимое значение.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="accessMask" /> равно 0 или параметр <paramref name="inheritanceFlags" /> или <paramref name="propagationFlags" /> содержит неизвестное значение флага.</exception>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAuditRule.InheritedObjectType">
      <summary>Получает тип дочернего объекта, который может наследовать объект <see cref="System.Security.AccessControl.ObjectAuditRule" />.</summary>
      <returns>Тип дочернего объекта, который может наследовать объект <see cref="System.Security.AccessControl.ObjectAuditRule" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAuditRule.ObjectFlags">
      <summary>Свойства <see cref="P:System.Security.AccessControl.ObjectAuditRule.ObjectType" /> и <see cref="P:System.Security.AccessControl.ObjectAuditRule.InheritedObjectType" /> объекта <see cref="System.Security.AccessControl.ObjectAuditRule" /> содержат недопустимые значения.</summary>
      <returns>
        <see cref="F:System.Security.AccessControl.ObjectAceFlags.ObjectAceTypePresent" /> указывает на то, что свойство <see cref="P:System.Security.AccessControl.ObjectAuditRule.ObjectType" /> содержит допустимое значение.<see cref="F:System.Security.AccessControl.ObjectAceFlags.InheritedObjectAceTypePresent" /> указывает на то, что свойство <see cref="P:System.Security.AccessControl.ObjectAuditRule.InheritedObjectType" /> содержит допустимое значение.Эти значения можно объединять с помощью операции логического ИЛИ.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAuditRule.ObjectType">
      <summary>Получает тип объекта, к которому применяется <see cref="System.Security.AccessControl.ObjectAuditRule" />.</summary>
      <returns>Тип объекта, к которому применяется <see cref="System.Security.AccessControl.ObjectAuditRule" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.ObjectSecurity">
      <summary>Обеспечивает возможность управлять доступом к объектам без непосредственной работы со списками управления доступом.Данный класс является абстрактным базовым классом для классов <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> и <see cref="T:System.Security.AccessControl.DirectoryObjectSecurity" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.#ctor(System.Boolean,System.Boolean)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <param name="isContainer">Значение true, если новый объект <see cref="T:System.Security.AccessControl.ObjectSecurity" /> является объектом контейнера.</param>
      <param name="isDS">Значение true, если новый объект <see cref="T:System.Security.AccessControl.ObjectSecurity" /> является объектом каталога.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.#ctor(System.Security.AccessControl.CommonSecurityDescriptor)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <param name="securityDescriptor">Значение <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> нового экземпляра класса <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AccessRightType">
      <summary>Получает значение <see cref="T:System.Type" /> защищаемого объекта, связанного с данным объектом <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>Тип защищаемого объекта, связанного с данным объектом <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.AccessRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.AccessRule" /> с использованием указанных значений.</summary>
      <returns>Объект <see cref="T:System.Security.AccessControl.AccessRule" />, созданный данным методом.</returns>
      <param name="identityReference">Идентификатор, к которому применяется правило доступа.Это должен быть объект, который можно привести к типу <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="accessMask">Маска доступа данного правила.Маска доступа представляет собой группу из 32 анонимных битов, значение каждого из которых определяется отдельными интеграторами.</param>
      <param name="isInherited">Значение true, если данное правило наследуется у родительского контейнера.</param>
      <param name="inheritanceFlags">Определяет свойства наследования правила доступа.</param>
      <param name="propagationFlags">Определяет возможность автоматического распространения наследуемых правил доступа.Флаги распространения игнорируются, если параметр <paramref name="inheritanceFlags" /> имеет значение <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="type">Указывает допустимый тип управления доступом.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AccessRulesModified">
      <summary>Получает или задает логическое значение, которое указывает, были ли изменены правила доступа, связанные с данным объектом <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>Значение true, если связанные с данным объектом <see cref="T:System.Security.AccessControl.ObjectSecurity" /> правила доступа были изменены; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AccessRuleType">
      <summary>Получает значение <see cref="T:System.Type" /> объекта, связанного с правилами доступа к данному объекту <see cref="T:System.Security.AccessControl.ObjectSecurity" />.Объект <see cref="T:System.Type" /> должен являться объектом, который можно привести к объекту <see cref="T:System.Security.Principal.SecurityIdentifier" />.</summary>
      <returns>Тип объекта, связанного с правилами доступа к данному объекту <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AreAccessRulesCanonical">
      <summary>Получает логическое значение, которое указывает, следуют ли правила доступа, связанные с данным объектом <see cref="T:System.Security.AccessControl.ObjectSecurity" />, в каноническом порядке</summary>
      <returns>Значение true, если правила доступа следуют в каноническом порядке; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AreAccessRulesProtected">
      <summary>Получает логическое значение, которое указывает, защищен ли список управления доступом на уровне пользователей, связанный с этим объектом <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>Значение true, если список управления доступом на уровне пользователей защищен; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AreAuditRulesCanonical">
      <summary>Получает логическое значение, которое указывает, следуют ли правила аудита, связанные с данным объектом <see cref="T:System.Security.AccessControl.ObjectSecurity" />, в каноническом порядке</summary>
      <returns>Значение true, если правила аудита следуют в каноническом порядке; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AreAuditRulesProtected">
      <summary>Получает логическое значение, которое указывает, защищен ли системный список управления доступом, связанный с этим объектом <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>Значение true, если системный список управления доступом защищен; в противном случае — значение false.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.AuditRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.AuditRule" /> с использованием указанных значений.</summary>
      <returns>Объект <see cref="T:System.Security.AccessControl.AuditRule" />, созданный данным методом.</returns>
      <param name="identityReference">Идентификатор, к которому применяется правило аудита.Это должен быть объект, который можно привести к типу <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="accessMask">Маска доступа данного правила.Маска доступа представляет собой группу из 32 анонимных битов, значение каждого из которых определяется отдельными интеграторами.</param>
      <param name="isInherited">Значение true, если данное правило наследуется у родительского контейнера.</param>
      <param name="inheritanceFlags">Определяет свойства наследования правила аудита.</param>
      <param name="propagationFlags">Определяет возможность автоматического распространения наследуемых правил аудита.Флаги распространения игнорируются, если параметр <paramref name="inheritanceFlags" /> имеет значение <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="flags">Определяет условия, при которых правило проходит аудит.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AuditRulesModified">
      <summary>Получает или задает логическое значение, которое указывает, были ли изменены правила аудита, связанные с данным объектом <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>Значение true, если связанные с данным объектом <see cref="T:System.Security.AccessControl.ObjectSecurity" /> правила аудита были изменены; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AuditRuleType">
      <summary>Получает значение <see cref="T:System.Type" /> объекта, связанного с правилами аудита для данного объекта <see cref="T:System.Security.AccessControl.ObjectSecurity" />.Объект <see cref="T:System.Type" /> должен являться объектом, который можно привести к объекту <see cref="T:System.Security.Principal.SecurityIdentifier" />.</summary>
      <returns>Тип объекта, связанного с правилами аудита для данного объекта <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.GetGroup(System.Type)">
      <summary>Получает основную группу, связанную с указанным владельцем.</summary>
      <returns>Основная группа, связанная с указанным владельцем.</returns>
      <param name="targetType">Владелец, для которого получается основная группа. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.GetOwner(System.Type)">
      <summary>Получает владельца, связанного с указанной основной группой.</summary>
      <returns>Владелец, связанный с указанной основной группой.</returns>
      <param name="targetType">Основная группа, для которой получается владелец.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.GetSecurityDescriptorBinaryForm">
      <summary>Возвращает массив байтовых значений, представляющих информацию дескриптора безопасности для данного объекта <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>Массив байтовых значений, представляющих дескриптор безопасности для данного объекта <see cref="T:System.Security.AccessControl.ObjectSecurity" />.Этот метод возвращает значение null, если информация о безопасности данного объекта <see cref="T:System.Security.AccessControl.ObjectSecurity" /> отсутствует.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.GetSecurityDescriptorSddlForm(System.Security.AccessControl.AccessControlSections)">
      <summary>Возвращает представление указанных разделов связанного с данным объектом <see cref="T:System.Security.AccessControl.ObjectSecurity" /> дескриптора безопасности в формате SDDL (Security Descriptor Definition Language).</summary>
      <returns>Представление указанных разделов связанного с данным объектом <see cref="T:System.Security.AccessControl.ObjectSecurity" /> дескриптора безопасности в формате SDDL.</returns>
      <param name="includeSections">Определяет получаемые разделы дескриптора безопасности (правила доступа, правила аудита, основная группа, владелец).</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.GroupModified">
      <summary>Получает или задает логическое значение, которое указывает, была ли изменена группа, связанная с защищаемым объектом. </summary>
      <returns>Значение true, если связанная с защищаемым объектом группа была изменена; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.IsContainer">
      <summary>Получает логическое значение, которое указывает, является ли данный объект <see cref="T:System.Security.AccessControl.ObjectSecurity" /> контейнером.</summary>
      <returns>Значение true, если объект <see cref="T:System.Security.AccessControl.ObjectSecurity" /> является контейнером; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.IsDS">
      <summary>Получает логическое значение, которое указывает, является ли данный объект <see cref="T:System.Security.AccessControl.ObjectSecurity" /> объектом каталога.</summary>
      <returns>Значение true, если объект <see cref="T:System.Security.AccessControl.ObjectSecurity" /> является объектом каталога; в противном случае — значение false.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.IsSddlConversionSupported">
      <summary>Возвращает логическое значение, которое указывает, можно ли преобразовать связанный с данным объектом <see cref="T:System.Security.AccessControl.ObjectSecurity" /> дескриптор безопасности в формат SDDL (Security Descriptor Definition Language).</summary>
      <returns>Значение true, если связанный с данным объектом <see cref="T:System.Security.AccessControl.ObjectSecurity" /> дескриптор безопасности можно преобразовать в формат SDDL; в противном случае — значение false.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ModifyAccess(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AccessRule,System.Boolean@)">
      <summary>Применяет указанное изменение к списку управления доступом на уровне пользователей, связанному с данным объектом <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>Значение true, если список управления доступом на уровне пользователей успешно изменен; в противном случае — значение false.</returns>
      <param name="modification">Применяемое изменение списка управления доступом на уровне пользователей.</param>
      <param name="rule">Изменяемое правило доступа.</param>
      <param name="modified">Значение true, если список управления доступом на уровне пользователей успешно изменен; в противном случае — значение false.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ModifyAccessRule(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AccessRule,System.Boolean@)">
      <summary>Применяет указанное изменение к списку управления доступом на уровне пользователей, связанному с данным объектом <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>Значение true, если список управления доступом на уровне пользователей успешно изменен; в противном случае — значение false.</returns>
      <param name="modification">Применяемое изменение списка управления доступом на уровне пользователей.</param>
      <param name="rule">Изменяемое правило доступа.</param>
      <param name="modified">Значение true, если список управления доступом на уровне пользователей успешно изменен; в противном случае — значение false.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ModifyAudit(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AuditRule,System.Boolean@)">
      <summary>Применяет указанное изменение к системному списку управления доступом, связанному с данным объектом <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>Значение true, если системный список управления доступом успешно изменен; в противном случае — значение false.</returns>
      <param name="modification">Применяемое изменение системного списка управления доступом.</param>
      <param name="rule">Изменяемое правило аудита.</param>
      <param name="modified">Значение true, если системный список управления доступом успешно изменен; в противном случае — значение false.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ModifyAuditRule(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AuditRule,System.Boolean@)">
      <summary>Применяет указанное изменение к системному списку управления доступом, связанному с данным объектом <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>Значение true, если системный список управления доступом успешно изменен; в противном случае — значение false.</returns>
      <param name="modification">Применяемое изменение системного списка управления доступом.</param>
      <param name="rule">Изменяемое правило аудита.</param>
      <param name="modified">Значение true, если системный список управления доступом успешно изменен; в противном случае — значение false.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.OwnerModified">
      <summary>Получает или задает логическое значение, которое указывает, был ли изменен владелец защищаемого объекта.</summary>
      <returns>Значение true, если владелец защищаемого объекта был изменен; в противном случае — значение false.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.Persist(System.Boolean,System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>Сохраняет указанные разделы дескриптора безопасности, связанного с данным объектом <see cref="T:System.Security.AccessControl.ObjectSecurity" />, в постоянном хранилище.Рекомендуется, чтобы значения параметров <paramref name="includeSections" />, передаваемых конструктору и методам сохранения, были одинаковыми.Дополнительные сведения см. в разделе "Замечания".</summary>
      <param name="enableOwnershipPrivilege">Значение true, чтобы включить разрешения, позволяющие вызывающему коду получать объект во владение.</param>
      <param name="name">Имя, необходимое для извлечения сохраненной информации.</param>
      <param name="includeSections">Одно из значений перечисления <see cref="T:System.Security.AccessControl.AccessControlSections" />, определяющее разделы дескриптора безопасности защищаемого объекта (правила доступа, правила аудита, владелец, основная группа), которые необходимо сохранить.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.Persist(System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections)">
      <summary>Сохраняет указанные разделы дескриптора безопасности, связанного с данным объектом <see cref="T:System.Security.AccessControl.ObjectSecurity" />, в постоянном хранилище.Рекомендуется, чтобы значения параметров <paramref name="includeSections" />, передаваемых конструктору и методам сохранения, были одинаковыми.Дополнительные сведения см. в разделе "Замечания".</summary>
      <param name="handle">Дескриптор, необходимый для извлечения сохраненной информации.</param>
      <param name="includeSections">Одно из значений перечисления <see cref="T:System.Security.AccessControl.AccessControlSections" />, определяющее разделы дескриптора безопасности защищаемого объекта (правила доступа, правила аудита, владелец, основная группа), которые необходимо сохранить.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.Persist(System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>Сохраняет указанные разделы дескриптора безопасности, связанного с данным объектом <see cref="T:System.Security.AccessControl.ObjectSecurity" />, в постоянном хранилище.Рекомендуется, чтобы значения параметров <paramref name="includeSections" />, передаваемых конструктору и методам сохранения, были одинаковыми.Дополнительные сведения см. в разделе "Замечания".</summary>
      <param name="name">Имя, необходимое для извлечения сохраненной информации.</param>
      <param name="includeSections">Одно из значений перечисления <see cref="T:System.Security.AccessControl.AccessControlSections" />, определяющее разделы дескриптора безопасности защищаемого объекта (правила доступа, правила аудита, владелец, основная группа), которые необходимо сохранить.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.PurgeAccessRules(System.Security.Principal.IdentityReference)">
      <summary>Удаляет все правила доступа, связанные с указанным объектом <see cref="T:System.Security.Principal.IdentityReference" />.</summary>
      <param name="identity">Объект <see cref="T:System.Security.Principal.IdentityReference" />, для которого удаляются все правила доступа.</param>
      <exception cref="T:System.InvalidOperationException">Все правила доступа указаны не в каноническом порядке.</exception>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.PurgeAuditRules(System.Security.Principal.IdentityReference)">
      <summary>Удаляет все правила аудита, связанные с указанным объектом <see cref="T:System.Security.Principal.IdentityReference" />.</summary>
      <param name="identity">Объект <see cref="T:System.Security.Principal.IdentityReference" />, для которого удаляются все правила аудита.</param>
      <exception cref="T:System.InvalidOperationException">Все правила аудита указаны не в каноническом порядке.</exception>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ReadLock">
      <summary>Блокирует доступ для чтения к объекту <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ReadUnlock">
      <summary>Разблокирует доступ для чтения к объекту <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetAccessRuleProtection(System.Boolean,System.Boolean)">
      <summary>Устанавливает или снимает защиту правил доступа, связанных с данным объектом <see cref="T:System.Security.AccessControl.ObjectSecurity" />.Защищенные правила доступа не могут изменяться родительскими объектами через наследование.</summary>
      <param name="isProtected">Значение true для защиты правила доступа, связанного с данным объектом <see cref="T:System.Security.AccessControl.ObjectSecurity" />, от наследования; значение false для разрешения наследования.</param>
      <param name="preserveInheritance">Значение true для сохранения наследуемых правил доступа; значение false для удаления наследуемых правил доступа.Данный параметр игнорируется, если параметр <paramref name="isProtected" /> имеет значение false.</param>
      <exception cref="T:System.InvalidOperationException">Этот метод пытается удалить наследуемые правила из списка управления доступом на уровне пользователей в неканонической форме.</exception>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetAuditRuleProtection(System.Boolean,System.Boolean)">
      <summary>Устанавливает или снимает защиту правил аудита, связанных с данным объектом <see cref="T:System.Security.AccessControl.ObjectSecurity" />.Защищенные правила аудита не могут изменяться родительскими объектами через наследование.</summary>
      <param name="isProtected">Значение true для защиты правила аудита, связанного с данным объектом <see cref="T:System.Security.AccessControl.ObjectSecurity" />, от наследования; значение false для разрешения наследования.</param>
      <param name="preserveInheritance">Значение true для сохранения наследуемых правил аудита; значение false для удаления наследуемых правил аудита.Данный параметр игнорируется, если параметр <paramref name="isProtected" /> имеет значение false.</param>
      <exception cref="T:System.InvalidOperationException">Этот метод пытается удалить наследуемые правила из системного списка управления доступом в неканонической форме.</exception>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetGroup(System.Security.Principal.IdentityReference)">
      <summary>Задает основную группу для дескриптора безопасности, связанного с данным объектом <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <param name="identity">Задаваемая основная группа.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetOwner(System.Security.Principal.IdentityReference)">
      <summary>Задает владельца для дескриптора безопасности, связанного с данным объектом <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <param name="identity">Задаваемый владелец.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetSecurityDescriptorBinaryForm(System.Byte[])">
      <summary>Задает дескриптор безопасности для данного объекта <see cref="T:System.Security.AccessControl.ObjectSecurity" /> на основании указанного массива байтовых значений.</summary>
      <param name="binaryForm">Массив байтов, на основании которого задается дескриптор безопасности.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetSecurityDescriptorBinaryForm(System.Byte[],System.Security.AccessControl.AccessControlSections)">
      <summary>Задает указанные разделы дескриптора безопасности для данного объекта <see cref="T:System.Security.AccessControl.ObjectSecurity" /> на основании указанного массива байтовых значений.</summary>
      <param name="binaryForm">Массив байтов, на основании которого задается дескриптор безопасности.</param>
      <param name="includeSections">Задаваемые разделы дескриптора безопасности (правила доступа, правила аудита, владелец, основная группа).</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetSecurityDescriptorSddlForm(System.String)">
      <summary>Задает дескриптор безопасности для данного объекта <see cref="T:System.Security.AccessControl.ObjectSecurity" /> на основании указанной строки SDDL (Security Descriptor Definition Language).</summary>
      <param name="sddlForm">Строка SDDL, на основании которой задается дескриптор безопасности.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetSecurityDescriptorSddlForm(System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>Задает указанные разделы дескриптора безопасности для данного объекта <see cref="T:System.Security.AccessControl.ObjectSecurity" /> на основании указанной строки SDDL (Security Descriptor Definition Language).</summary>
      <param name="sddlForm">Строка SDDL, на основании которой задается дескриптор безопасности.</param>
      <param name="includeSections">Задаваемые разделы дескриптора безопасности (правила доступа, правила аудита, владелец, основная группа).</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.WriteLock">
      <summary>Блокирует доступ для записи к объекту <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.WriteUnlock">
      <summary>Разблокирует доступ для записи к объекту <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
    </member>
    <member name="T:System.Security.AccessControl.ObjectSecurity`1">
      <summary>Обеспечивает возможность управления доступом к объектам каталога без непосредственной работы со списками управления доступом; также дает возможность приводить тип для прав доступа. </summary>
      <typeparam name="T">Права доступа для объекта.</typeparam>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType)">
      <summary>Инициализирует новый экземпляр класса ObjectSecurity`1.</summary>
      <param name="isContainer">Значение true, если новый объект <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> является объектом контейнера.</param>
      <param name="resourceType">Тип ресурса.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections)">
      <summary>Инициализирует новый экземпляр класса ObjectSecurity`1.</summary>
      <param name="isContainer">Значение true, если новый объект <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> является объектом контейнера.</param>
      <param name="resourceType">Тип ресурса.</param>
      <param name="safeHandle">Дескриптор.</param>
      <param name="includeSections">Разделы, которые необходимо включить.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>Инициализирует новый экземпляр класса ObjectSecurity`1.</summary>
      <param name="isContainer">Значение true, если новый объект <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> является объектом контейнера.</param>
      <param name="resourceType">Тип ресурса.</param>
      <param name="safeHandle">Дескриптор.</param>
      <param name="includeSections">Разделы, которые необходимо включить.</param>
      <param name="exceptionFromErrorCode">Делегат, реализованный интеграторами и обеспечивающий дополнительные исключения.</param>
      <param name="exceptionContext">Объект, содержащий контекстные сведения об источнике или назначении исключения.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>Инициализирует новый экземпляр класса ObjectSecurity`1.</summary>
      <param name="isContainer">Значение true, если новый объект <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> является объектом контейнера.</param>
      <param name="resourceType">Тип ресурса.</param>
      <param name="name">Имя защищаемого объекта, с которым связан новый объект <see cref="T:System.Security.AccessControl.ObjectSecurity`1" />.</param>
      <param name="includeSections">Разделы, которые необходимо включить.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.String,System.Security.AccessControl.AccessControlSections,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>Инициализирует новый экземпляр класса ObjectSecurity`1.</summary>
      <param name="isContainer">Значение true, если новый объект <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> является объектом контейнера.</param>
      <param name="resourceType">Тип ресурса.</param>
      <param name="name">Имя защищаемого объекта, с которым связан новый объект <see cref="T:System.Security.AccessControl.ObjectSecurity`1" />.</param>
      <param name="includeSections">Разделы, которые необходимо включить. </param>
      <param name="exceptionFromErrorCode">Делегат, реализованный интеграторами и обеспечивающий дополнительные исключения.</param>
      <param name="exceptionContext">Объект, содержащий контекстные сведения об источнике или назначении исключения.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity`1.AccessRightType">
      <summary>Получает тип защищаемого объекта, связанного с правилами аудита данного объекта ObjectSecurity`1.</summary>
      <returns>Тип защищаемого объекта, связанный с текущим экземпляром.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.AccessRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>Инициализирует новый экземпляр класса ObjectAccessRule, представляющий новое правило управления доступом для связанного объекта безопасности.</summary>
      <returns>Представляет новое правило управления доступом для указанного пользователя с указанными правами, типом управления доступом и флагами.</returns>
      <param name="identityReference">Представляет учетную запись пользователя.</param>
      <param name="accessMask">Тип доступа.</param>
      <param name="isInherited">Значение true, если правило доступа наследуется; в противном случае — значение false.</param>
      <param name="inheritanceFlags">Определяет, как выполнять распространение масок доступа на дочерние объектам.</param>
      <param name="propagationFlags">Определяет порядок распространения действия элементов управления доступом на дочерние объекты.</param>
      <param name="type">Определяет, разрешен или запрещен доступ.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity`1.AccessRuleType">
      <summary>Получает тип объекта, связанного с правилами доступа данного объекта ObjectSecurity`1. </summary>
      <returns>Тип объекта, связанного с правилами доступа для текущего экземпляра.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.AddAccessRule(System.Security.AccessControl.AccessRule{`0})">
      <summary>Добавляет указанное правило доступа в список управления доступом на уровне пользователей, связанный с данным объектом ObjectSecurity`1.</summary>
      <param name="rule">Добавляемое правило.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.AddAuditRule(System.Security.AccessControl.AuditRule{`0})">
      <summary>Добавляет указанное правило аудита в системный список управления доступом, связанный с данным объектом ObjectSecurity`1.</summary>
      <param name="rule">Добавляемое правило аудита.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.AuditRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.AuditRule" />, который представляет указанное правило аудита для указанного пользователя.</summary>
      <returns>Возвращает указанное правило аудита для указанного пользователя.</returns>
      <param name="identityReference">Представляет учетную запись пользователя. </param>
      <param name="accessMask">Целочисленное значение, задающее тип доступа.</param>
      <param name="isInherited">Значение true, если правило доступа наследуется; в противном случае — значение false.</param>
      <param name="inheritanceFlags">Определяет, как выполнять распространение масок доступа на дочерние объектам.</param>
      <param name="propagationFlags">Определяет порядок распространения действия элементов управления доступом на дочерние объекты.</param>
      <param name="flags">Описывает тип аудита для выполнения.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity`1.AuditRuleType">
      <summary>Получает объект Type, связанный с правилами аудита данного объекта ObjectSecurity`1.</summary>
      <returns>объект Type, связанный с правилами аудита для текущего экземпляра.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.Persist(System.Runtime.InteropServices.SafeHandle)">
      <summary>Сохраняет дескриптора безопасности, связанного с данным объектом ObjectSecurity`1, в постоянном хранилище, используя указанный дескриптор.</summary>
      <param name="handle">Дескриптор защищаемого объекта, с которым связан данный объект ObjectSecurity`1.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.Persist(System.String)">
      <summary>Сохраняет дескриптора безопасности, связанного с данным объектом ObjectSecurity`1, в постоянном хранилище, используя указанное имя.</summary>
      <param name="name">Имя защищаемого объекта, с которым связан данный объект ObjectSecurity`1.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAccessRule(System.Security.AccessControl.AccessRule{`0})">
      <summary>Удаляет правила доступа с тем же идентификатором безопасности и маской доступа, что и у указанного правила доступа, из списка управления доступом на уровне пользователей, связанного с текущим объектом ObjectSecurity`1.</summary>
      <returns>Возвращает Значение true, если правило доступа успешно удалено; в противном случае — значение false.</returns>
      <param name="rule">Удаляемая правило.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAccessRuleAll(System.Security.AccessControl.AccessRule{`0})">
      <summary>Удаляет все правила доступа с тем же идентификатором безопасности, что и у указанного правила доступа, из списка управления доступом на уровне пользователей, связанного с текущим объектом ObjectSecurity`1.</summary>
      <param name="rule">Удаляемое правило доступа.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAccessRuleSpecific(System.Security.AccessControl.AccessRule{`0})">
      <summary>Удаляет все правила доступа, в точности совпадающие с указанным правилом доступа, из списка управления доступом на уровне пользователей, связанного с текущим объектом ObjectSecurity`1.</summary>
      <param name="rule">Удаляемое правило доступа.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAuditRule(System.Security.AccessControl.AuditRule{`0})">
      <summary>Удаляет правила аудита с тем же идентификатором безопасности и маской доступа, что и у указанного правила аудита, из системного списка управления доступом, связанного с текущим объектом ObjectSecurity`1.</summary>
      <returns>Возвращает значение true, если объект был удален; в противном случае — значение false.</returns>
      <param name="rule">Удаляемое правило аудита.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAuditRuleAll(System.Security.AccessControl.AuditRule{`0})">
      <summary>Удаляет все правила аудита с тем же идентификатором безопасности, что и у указанного правила аудита, из системного списка управления доступом, связанного с текущим объектом ObjectSecurity`1.</summary>
      <param name="rule">Удаляемое правило аудита.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAuditRuleSpecific(System.Security.AccessControl.AuditRule{`0})">
      <summary>Удаляет все правила аудита, в точности совпадающие с указанным правилом аудита, из системного списка управления доступом, связанного с текущим объектом ObjectSecurity`1.</summary>
      <param name="rule">Удаляемое правило аудита.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.ResetAccessRule(System.Security.AccessControl.AccessRule{`0})">
      <summary>Удаляет все правила доступа из списка управления доступом на уровне пользователей (DACL), связанного с текущим объектом ObjectSecurity`1, а затем добавляет указанное правило доступа.</summary>
      <param name="rule">Сбрасываемое правило доступа.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.SetAccessRule(System.Security.AccessControl.AccessRule{`0})">
      <summary>Удаляет все правила доступа с тем же идентификатором и квалификатором безопасности, что и у указанного правила доступа, из списка управления доступом на уровне пользователей, связанного с текущим объектом ObjectSecurity`1, а затем добавляет указанное правило доступа.</summary>
      <param name="rule">Устанавливаемое правило доступа.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.SetAuditRule(System.Security.AccessControl.AuditRule{`0})">
      <summary>Удаляет все правила аудита с тем же идентификатором и квалификатором безопасности, что и у указанного правила аудита, из системного списка управления доступом, связанного с текущим объектом ObjectSecurity`1, а затем добавляет указанное правило аудита.</summary>
      <param name="rule">Устанавливаемое правило аудита.</param>
    </member>
    <member name="T:System.Security.AccessControl.PrivilegeNotHeldException">
      <summary>Исключение вызывается, когда метод в пространстве имен <see cref="N:System.Security.AccessControl" /> пытается использовать отсутствующую у него привилегию.</summary>
    </member>
    <member name="M:System.Security.AccessControl.PrivilegeNotHeldException.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.PrivilegeNotHeldException" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.PrivilegeNotHeldException.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.PrivilegeNotHeldException" /> с использованием заданного права.</summary>
      <param name="privilege">Отсутствующее право.</param>
    </member>
    <member name="M:System.Security.AccessControl.PrivilegeNotHeldException.#ctor(System.String,System.Exception)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.PrivilegeNotHeldException" /> с использованием указанного исключения.</summary>
      <param name="privilege">Отсутствующее право.</param>
      <param name="inner">Исключение, которое вызвало текущее исключение.Если параметр <paramref name="innerException" /> не является указателем null (Nothing в Visual Basic), то текущее исключение сгенерировано в блоке catch, обрабатывающем внутреннее исключение.</param>
    </member>
    <member name="P:System.Security.AccessControl.PrivilegeNotHeldException.PrivilegeName">
      <summary>Получает имя отсутствующего права.</summary>
      <returns>Имя права, которым не удалось воспользоваться методу.</returns>
    </member>
    <member name="T:System.Security.AccessControl.PropagationFlags">
      <summary>Определяет порядок распространения действия элементов управления доступом на дочерние объекты.  Эти флаги имеют значение лишь в том случае, когда имеются флаги наследования. </summary>
    </member>
    <member name="F:System.Security.AccessControl.PropagationFlags.InheritOnly">
      <summary>Указывает, что элемент управления доступом распространяется только на дочерние объекты.К ним относятся контейнеры и конечные дочерние объекты.</summary>
    </member>
    <member name="F:System.Security.AccessControl.PropagationFlags.None">
      <summary>Указывает, что флаги наследования не установлены.</summary>
    </member>
    <member name="F:System.Security.AccessControl.PropagationFlags.NoPropagateInherit">
      <summary>Указывает, что элемент управления доступом не распространяется на дочерние объекты.</summary>
    </member>
    <member name="T:System.Security.AccessControl.QualifiedAce">
      <summary>Представляет элемент управления доступом, содержащий квалификатор.Квалификатор, представляемый объектом <see cref="T:System.Security.AccessControl.AceQualifier" />, определяет функцию элемента управления доступом — разрешение доступа, запрет доступа, вызов системного аудита или системного оповещения.Класс <see cref="T:System.Security.AccessControl.QualifiedAce" /> является абстрактным базовым классом для классов <see cref="T:System.Security.AccessControl.CommonAce" /> и <see cref="T:System.Security.AccessControl.ObjectAce" />.</summary>
    </member>
    <member name="P:System.Security.AccessControl.QualifiedAce.AceQualifier">
      <summary>Получает значение, определяющее функцию элемента управления доступом — разрешение доступа, запрет доступа, вызов системного аудита или системного оповещения.</summary>
      <returns>Значение, определяющее функцию элемента управления доступом — разрешение доступа, запрет доступа, вызов системного аудита или системного оповещения.</returns>
    </member>
    <member name="M:System.Security.AccessControl.QualifiedAce.GetOpaque">
      <summary>Возвращает длину непрозрачных данных обратного вызова, связанных с этим объектом <see cref="T:System.Security.AccessControl.QualifiedAce" />. </summary>
      <returns>Массив байтов, представляющий непрозрачные данные обратного вызова, связанные с данным объектом <see cref="T:System.Security.AccessControl.QualifiedAce" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.QualifiedAce.IsCallback">
      <summary>Определяет, содержит ли этот объект <see cref="T:System.Security.AccessControl.QualifiedAce" /> данные обратного вызова.</summary>
      <returns>Значение true, если этот объект <see cref="T:System.Security.AccessControl.QualifiedAce" /> содержит данные обратного вызова; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.QualifiedAce.OpaqueLength">
      <summary>Получает длину непрозрачных данных обратного вызова, связанных с этим объектом <see cref="T:System.Security.AccessControl.QualifiedAce" />.Это свойство применимо только к элементам управления доступом обратного вызова.</summary>
      <returns>Длина непрозрачных данных обратного вызова в байтах.</returns>
    </member>
    <member name="M:System.Security.AccessControl.QualifiedAce.SetOpaque(System.Byte[])">
      <summary>Задает непрозрачные данные обратного вызова, связанные с этим объектом <see cref="T:System.Security.AccessControl.QualifiedAce" />.</summary>
      <param name="opaque">Массив байтов, представляющий непрозрачные данные обратного вызова для данного объекта <see cref="T:System.Security.AccessControl.QualifiedAce" />.</param>
    </member>
    <member name="T:System.Security.AccessControl.RawAcl">
      <summary>Представляет список управления доступом.</summary>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.#ctor(System.Byte,System.Int32)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.RawAcl" /> с заданным номером редакции.</summary>
      <param name="revision">Номер редакции нового списка управления доступом.</param>
      <param name="capacity">Количество элементов управления доступом, которые могут содержаться в данном объекте <see cref="T:System.Security.AccessControl.RawAcl" />.Это число будет использоваться только для справки.</param>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.#ctor(System.Byte[],System.Int32)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.RawAcl" /> из указанной двоичной формы.</summary>
      <param name="binaryForm">Массив байтовых значений, представляющий список управления доступом.</param>
      <param name="offset">Позиция в массиве <paramref name="binaryForm" />, с которой начинается распаковка данных.</param>
    </member>
    <member name="P:System.Security.AccessControl.RawAcl.BinaryLength">
      <summary>Получает длину в байтах двоичного представления текущего объекта <see cref="T:System.Security.AccessControl.RawAcl" />.Эта длина должна использоваться перед маршалингом списка управления доступом в двоичный массив с помощью метода <see cref="M:System.Security.AccessControl.RawAcl.GetBinaryForm" />.</summary>
      <returns>Длина в байтах двоичного представления текущего объекта <see cref="T:System.Security.AccessControl.RawAcl" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawAcl.Count">
      <summary>Получает количество элементов управления доступом в текущем объекте <see cref="T:System.Security.AccessControl.RawAcl" />.</summary>
      <returns>Количество элементов управления доступом в текущем объекте <see cref="T:System.Security.AccessControl.RawAcl" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Маршалирует содержимое объекта <see cref="T:System.Security.AccessControl.RawAcl" /> в указанный массив байтов, начиная с указанного смещения.</summary>
      <param name="binaryForm">Массив байтов, в который маршалируется содержимое объекта <see cref="T:System.Security.AccessControl.RawAcl" />.</param>
      <param name="offset">Позиция, с которой начинается маршалинг.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение <paramref name="offset" /> отрицательно или слишком велико, чтобы можно было скопировать весь объект <see cref="T:System.Security.AccessControl.RawAcl" /> в массив <paramref name="array" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.InsertAce(System.Int32,System.Security.AccessControl.GenericAce)">
      <summary>Вставляет указанный элемент управления доступом по указанному индексу.</summary>
      <param name="index">Позиция, куда добавляются новые элементы управления доступом.Укажите значение свойства <see cref="P:System.Security.AccessControl.RawAcl.Count" />, чтобы поместить элемент управления доступом в конец объекта <see cref="T:System.Security.AccessControl.RawAcl" />.</param>
      <param name="ace">Вставляемый элемент управления доступом.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="offset" /> имеет отрицательное или слишком большое значение, чтобы можно было скопировать весь объект <see cref="T:System.Security.AccessControl.GenericAcl" /> в параметр <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Security.AccessControl.RawAcl.Item(System.Int32)">
      <summary>Получает или задает элемент управления доступом по указанному индексу.</summary>
      <returns>Элемент управления доступом по указанному индексу.</returns>
      <param name="index">Индекс (с нуля) получаемого или задаваемого элемента управления доступом.</param>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.RemoveAce(System.Int32)">
      <summary>Удаляет элемент управления доступом по указанному индексу.</summary>
      <param name="index">Отсчитываемый с нуля индекс удаляемого элемента управления доступом.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> превышает значение свойства <see cref="P:System.Security.AccessControl.RawAcl.Count" /> минус один или отрицательно.</exception>
    </member>
    <member name="P:System.Security.AccessControl.RawAcl.Revision">
      <summary>Получает уровень редакции объекта <see cref="T:System.Security.AccessControl.RawAcl" />.</summary>
      <returns>Байтовое значение, определяющее уровень редакции объекта <see cref="T:System.Security.AccessControl.RawAcl" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.RawSecurityDescriptor">
      <summary>Представляет дескриптор безопасности.Дескриптор безопасности включает владельца, основную группу, список управления доступом на уровне пользователей и системный список управления доступом.</summary>
    </member>
    <member name="M:System.Security.AccessControl.RawSecurityDescriptor.#ctor(System.Byte[],System.Int32)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> на основании указанного массива байтовых значений.</summary>
      <param name="binaryForm">Массив байтовых значений, на основании которого необходимо создать новый объект <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</param>
      <param name="offset">Позиция в массиве <paramref name="binaryForm" />, с которой начинается копирование.</param>
    </member>
    <member name="M:System.Security.AccessControl.RawSecurityDescriptor.#ctor(System.Security.AccessControl.ControlFlags,System.Security.Principal.SecurityIdentifier,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.RawAcl,System.Security.AccessControl.RawAcl)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> с использованием указанных значений.</summary>
      <param name="flags">Флаги, определяющие поведение нового объекта <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</param>
      <param name="owner">Владелец нового объекта <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</param>
      <param name="group">Основная группа для нового объекта <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</param>
      <param name="systemAcl">Системный список управления доступом нового объекта <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</param>
      <param name="discretionaryAcl">Список управления доступом на уровне пользователей нового объекта <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</param>
    </member>
    <member name="M:System.Security.AccessControl.RawSecurityDescriptor.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> на основании указанной строки SDDL (Security Descriptor Definition Language).</summary>
      <param name="sddlForm">Строка SDDL, на основании которой создается новый объект <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</param>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.ControlFlags">
      <summary>Получает значения, определяющие поведение объекта <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</summary>
      <returns>Один или несколько членов перечисления <see cref="T:System.Security.AccessControl.ControlFlags" />, объединенных с помощью операции логического ИЛИ.</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.DiscretionaryAcl">
      <summary>Получает или задает список управления доступом на уровне пользователей для данного объекта <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.Список управления доступом на уровне пользователей содержит правила доступа.</summary>
      <returns>Список управления доступом на уровне пользователей для данного объекта <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.Group">
      <summary>Получает или задает основную группу для данного объекта <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</summary>
      <returns>Основная группа для данного объекта <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.Owner">
      <summary>Получает или задает владельца объекта, связанного с данным объектом <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</summary>
      <returns>Владелец объекта, связанного с данным объектом <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.ResourceManagerControl">
      <summary>Получает или задает байтовое значение, которое представляет управляющие биты диспетчера ресурсов, связанные с данным объектом <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</summary>
      <returns>Байтовое значение, которое представляет управляющие биты диспетчера ресурсов, связанные с данным объектом <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.RawSecurityDescriptor.SetFlags(System.Security.AccessControl.ControlFlags)">
      <summary>Устанавливает указанное значение для свойства <see cref="P:System.Security.AccessControl.RawSecurityDescriptor.ControlFlags" /> данного объекта <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</summary>
      <param name="flags">Один или несколько членов перечисления <see cref="T:System.Security.AccessControl.ControlFlags" />, объединенных с помощью операции логического ИЛИ.</param>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.SystemAcl">
      <summary>Получает или задает системный список управления доступом для данного объекта <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.Системный список управления доступом содержит правила аудита.</summary>
      <returns>Системный список управления доступом для данного объекта <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.ResourceType">
      <summary>Задает предварительно определенные собственные типы объектов.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.DSObject">
      <summary>Объект службы каталогов или набор свойств или свойство объекта службы каталогов.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.DSObjectAll">
      <summary>Объект службы каталогов и все его наборы свойств и свойства.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.FileObject">
      <summary>Файл или каталог.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.KernelObject">
      <summary>Локальный объект ядра.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.LMShare">
      <summary>Сетевая папка.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.Printer">
      <summary>Принтер.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.ProviderDefined">
      <summary>Объект, определенный поставщиком.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.RegistryKey">
      <summary>Раздел реестра.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.RegistryWow6432Key">
      <summary>Объект записи реестра в подсистеме WOW64.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.Service">
      <summary>Служба Windows.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.Unknown">
      <summary>Объект неизвестного типа.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.WindowObject">
      <summary>Объект рабочей станции Windows на локальном компьютере.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.WmiGuidObject">
      <summary>Объект инструментария управления Windows (WMI)</summary>
    </member>
    <member name="T:System.Security.AccessControl.SecurityInfos">
      <summary>Определяет раздел дескриптора безопасности для запроса или установки.</summary>
    </member>
    <member name="F:System.Security.AccessControl.SecurityInfos.DiscretionaryAcl">
      <summary>Список управления доступом на уровне пользователей.</summary>
    </member>
    <member name="F:System.Security.AccessControl.SecurityInfos.Group">
      <summary>Идентификатор основной группы.</summary>
    </member>
    <member name="F:System.Security.AccessControl.SecurityInfos.Owner">
      <summary>Идентификатор владельца.</summary>
    </member>
    <member name="F:System.Security.AccessControl.SecurityInfos.SystemAcl">
      <summary>Системный список управления доступом.</summary>
    </member>
    <member name="T:System.Security.AccessControl.SystemAcl">
      <summary>Представляет системный список управления доступом.</summary>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.#ctor(System.Boolean,System.Boolean,System.Byte,System.Int32)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.SystemAcl" /> с использованием указанных значений.</summary>
      <param name="isContainer">Значение true, если новый объект <see cref="T:System.Security.AccessControl.SystemAcl" /> является контейнером.</param>
      <param name="isDS">Значение true, если новый объект <see cref="T:System.Security.AccessControl.SystemAcl" /> является объектом каталога списка управления доступом.</param>
      <param name="revision">Номер редакции нового объекта <see cref="T:System.Security.AccessControl.SystemAcl" />.</param>
      <param name="capacity">Количество элементов управления доступом, которые могут содержаться в данном объекте <see cref="T:System.Security.AccessControl.SystemAcl" />.Это число будет использоваться только для справки.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.#ctor(System.Boolean,System.Boolean,System.Int32)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.SystemAcl" /> с использованием указанных значений.</summary>
      <param name="isContainer">Значение true, если новый объект <see cref="T:System.Security.AccessControl.SystemAcl" /> является контейнером.</param>
      <param name="isDS">Значение true, если новый объект <see cref="T:System.Security.AccessControl.SystemAcl" /> является объектом каталога списка управления доступом.</param>
      <param name="capacity">Количество элементов управления доступом, которые могут содержаться в данном объекте <see cref="T:System.Security.AccessControl.SystemAcl" />.Это число будет использоваться только для справки.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.#ctor(System.Boolean,System.Boolean,System.Security.AccessControl.RawAcl)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.AccessControl.SystemAcl" /> с указанными значениями из указанного объекта <see cref="T:System.Security.AccessControl.RawAcl" />.</summary>
      <param name="isContainer">Значение true, если новый объект <see cref="T:System.Security.AccessControl.SystemAcl" /> является контейнером.</param>
      <param name="isDS">Значение true, если новый объект <see cref="T:System.Security.AccessControl.SystemAcl" /> является объектом каталога списка управления доступом.</param>
      <param name="rawAcl">Базовый объект <see cref="T:System.Security.AccessControl.RawAcl" />, соответствующий новому объекту <see cref="T:System.Security.AccessControl.SystemAcl" />.Чтобы создать пустой список управления доступом, укажите значение null.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.AddAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Добавляет правило аудита к текущему объекту <see cref="T:System.Security.AccessControl.SystemAcl" />.</summary>
      <param name="auditFlags">Тип добавляемого правила аудита.</param>
      <param name="sid">Объект <see cref="T:System.Security.Principal.SecurityIdentifier" />, для которого добавляется правило аудита.</param>
      <param name="accessMask">Маска доступа для нового правила аудита.</param>
      <param name="inheritanceFlags">Флаги, определяющие свойства наследования нового правила аудита.</param>
      <param name="propagationFlags">Флаги, определяющие свойства распространения наследования нового правила аудита.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.AddAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Добавляет правило аудита с указанными параметрами к текущему объекту <see cref="T:System.Security.AccessControl.SystemAcl" />.Используйте этот метод для списков управления доступом объектов каталогов при указании типа объекта или типа наследуемого объекта для нового правила аудита.</summary>
      <param name="auditFlags">Тип добавляемого правила аудита.</param>
      <param name="sid">Объект <see cref="T:System.Security.Principal.SecurityIdentifier" />, для которого добавляется правило аудита.</param>
      <param name="accessMask">Маска доступа для нового правила аудита.</param>
      <param name="inheritanceFlags">Флаги, определяющие свойства наследования нового правила аудита.</param>
      <param name="propagationFlags">Флаги, определяющие свойства распространения наследования нового правила аудита.</param>
      <param name="objectFlags">Флаги, определяющие, содержат ли параметры <paramref name="objectType" /> и <paramref name="inheritedObjectType" /> значения, отличные от null.</param>
      <param name="objectType">Идентификатор класса объектов, к которым применяется новое правило аудита.</param>
      <param name="inheritedObjectType">Идентификатор класса дочерних объектов, которые могут наследовать новое правило аудита.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.AddAudit(System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAuditRule)">
      <summary>Добавляет правило аудита к текущему объекту <see cref="T:System.Security.AccessControl.SystemAcl" />.</summary>
      <param name="sid">Объект <see cref="T:System.Security.Principal.SecurityIdentifier" />, для которого добавляется правило аудита.</param>
      <param name="rule">
        <see cref="T:System.Security.AccessControl.ObjectAuditRule" />Для нового правила аудита.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Удаляет указанное правило аудита из текущего объекта <see cref="T:System.Security.AccessControl.SystemAcl" />.</summary>
      <returns>Значение true, если метод успешно удаляет указанное правило аудита; в противном случае — значение false.</returns>
      <param name="auditFlags">Тип удаляемого правила аудита.</param>
      <param name="sid">Объект <see cref="T:System.Security.Principal.SecurityIdentifier" />, для которого удаляется правило аудита.</param>
      <param name="accessMask">Маска доступа для удаляемого правила.</param>
      <param name="inheritanceFlags">Флаги, определяющие свойства наследования удаляемого правила.</param>
      <param name="propagationFlags">Флаги, определяющие свойства распространения наследования удаляемого правила.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Удаляет указанное правило аудита из текущего объекта <see cref="T:System.Security.AccessControl.SystemAcl" />.Используйте этот метод для списков управления доступом объектов каталогов при указании типа объекта или типа наследуемого объекта.</summary>
      <returns>Значение true, если метод успешно удаляет указанное правило аудита; в противном случае — значение false.</returns>
      <param name="auditFlags">Тип удаляемого правила аудита.</param>
      <param name="sid">Объект <see cref="T:System.Security.Principal.SecurityIdentifier" />, для которого удаляется правило аудита.</param>
      <param name="accessMask">Маска доступа для удаляемого правила.</param>
      <param name="inheritanceFlags">Флаги, определяющие свойства наследования удаляемого правила.</param>
      <param name="propagationFlags">Флаги, определяющие свойства распространения наследования удаляемого правила.</param>
      <param name="objectFlags">Флаги, определяющие, содержат ли параметры <paramref name="objectType" /> и <paramref name="inheritedObjectType" /> значения, отличные от null.</param>
      <param name="objectType">Идентификатор класса объектов, к которым применяется удаляемое правило аудита.</param>
      <param name="inheritedObjectType">Идентификатор класса дочерних объектов, которые могут наследовать удаляемое правило аудита.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAudit(System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAuditRule)">
      <summary>Удаляет указанное правило аудита из текущего объекта <see cref="T:System.Security.AccessControl.SystemAcl" />.</summary>
      <returns>Значение true, если метод успешно удаляет указанное правило аудита; в противном случае — значение false.</returns>
      <param name="sid">Объект <see cref="T:System.Security.Principal.SecurityIdentifier" />, для которого удаляется правило аудита.</param>
      <param name="rule">Объект <see cref="T:System.Security.AccessControl.ObjectAuditRule" />, для которого удаляется правило аудита.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAuditSpecific(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Удаляет указанное правило аудита из текущего объекта <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />.</summary>
      <param name="auditFlags">Тип удаляемого правила аудита.</param>
      <param name="sid">Объект <see cref="T:System.Security.Principal.SecurityIdentifier" />, для которого удаляется правило аудита.</param>
      <param name="accessMask">Маска доступа для удаляемого правила.</param>
      <param name="inheritanceFlags">Флаги, определяющие свойства наследования удаляемого правила.</param>
      <param name="propagationFlags">Флаги, определяющие свойства распространения наследования удаляемого правила.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAuditSpecific(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Удаляет указанное правило аудита из текущего объекта <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />.Используйте этот метод для списков управления доступом объектов каталогов при указании типа объекта или типа наследуемого объекта.</summary>
      <param name="auditFlags">Тип удаляемого правила аудита.</param>
      <param name="sid">Объект <see cref="T:System.Security.Principal.SecurityIdentifier" />, для которого удаляется правило аудита.</param>
      <param name="accessMask">Маска доступа для удаляемого правила.</param>
      <param name="inheritanceFlags">Флаги, определяющие свойства наследования удаляемого правила.</param>
      <param name="propagationFlags">Флаги, определяющие свойства распространения наследования удаляемого правила.</param>
      <param name="objectFlags">Флаги, определяющие, содержат ли параметры <paramref name="objectType" /> и <paramref name="inheritedObjectType" /> значения, отличные от null.</param>
      <param name="objectType">Идентификатор класса объектов, к которым применяется удаляемое правило аудита.</param>
      <param name="inheritedObjectType">Идентификатор класса дочерних объектов, которые могут наследовать удаляемое правило аудита.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAuditSpecific(System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAuditRule)">
      <summary>Удаляет указанное правило аудита из текущего объекта <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />.</summary>
      <param name="sid">Объект <see cref="T:System.Security.Principal.SecurityIdentifier" />, для которого удаляется правило аудита.</param>
      <param name="rule">
        <see cref="T:System.Security.AccessControl.ObjectAuditRule" /> Для удаляемого правила.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.SetAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Устанавливает указанное правило аудита для указанного объекта <see cref="T:System.Security.Principal.SecurityIdentifier" />.</summary>
      <param name="auditFlags">Устанавливаемое условие аудита.</param>
      <param name="sid">Объект <see cref="T:System.Security.Principal.SecurityIdentifier" />, для которого устанавливается правило аудита.</param>
      <param name="accessMask">Маска доступа для нового правила аудита.</param>
      <param name="inheritanceFlags">Флаги, определяющие свойства наследования нового правила аудита.</param>
      <param name="propagationFlags">Флаги, определяющие свойства распространения наследования нового правила аудита.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.SetAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Устанавливает указанное правило аудита для указанного объекта <see cref="T:System.Security.Principal.SecurityIdentifier" />.Используйте этот метод для списков управления доступом объектов каталогов при указании типа объекта или типа наследуемого объекта.</summary>
      <param name="auditFlags">Устанавливаемое условие аудита.</param>
      <param name="sid">Объект <see cref="T:System.Security.Principal.SecurityIdentifier" />, для которого устанавливается правило аудита.</param>
      <param name="accessMask">Маска доступа для нового правила аудита.</param>
      <param name="inheritanceFlags">Флаги, определяющие свойства наследования нового правила аудита.</param>
      <param name="propagationFlags">Флаги, определяющие свойства распространения наследования нового правила аудита.</param>
      <param name="objectFlags">Флаги, определяющие, содержат ли параметры <paramref name="objectType" /> и <paramref name="inheritedObjectType" /> значения, отличные от null.</param>
      <param name="objectType">Идентификатор класса объектов, к которым применяется новое правило аудита.</param>
      <param name="inheritedObjectType">Идентификатор класса дочерних объектов, которые могут наследовать новое правило аудита.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.SetAudit(System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAuditRule)">
      <summary>Устанавливает указанное правило аудита для указанного объекта <see cref="T:System.Security.Principal.SecurityIdentifier" />.</summary>
      <param name="sid">Объект <see cref="T:System.Security.Principal.SecurityIdentifier" />, для которого устанавливается правило аудита.</param>
      <param name="rule">Объект <see cref="T:System.Security.AccessControl.ObjectAuditRule" />, для которого устанавливается правило аудита.</param>
    </member>
  </members>
</doc>