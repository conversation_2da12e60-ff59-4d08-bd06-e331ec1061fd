﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Security.AccessControl</name>
  </assembly>
  <members>
    <member name="T:System.Security.AccessControl.AccessControlActions">
      <summary>指定允許對安全物件進行的動作。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlActions.Change">
      <summary>指定唯寫存取。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlActions.None">
      <summary>未指定存取。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlActions.View">
      <summary>指定唯讀存取。</summary>
    </member>
    <member name="T:System.Security.AccessControl.AccessControlModification">
      <summary>指定要執行的存取控制 (Access Control) 修改型別。這個列舉型別 (Enumeration) 由 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 類別及其子類別的方法所使用。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.Add">
      <summary>將指定的授權規則加入存取控制清單 (ACL)。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.Remove">
      <summary> 移除 ACL 中包含與指定授權規則相同之安全識別項 (SID) 和存取遮罩的授權規則。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.RemoveAll">
      <summary> 移除  ACL 中包含與指定驗證規則相同之 SID 的授權規則。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.RemoveSpecific">
      <summary> 移除  ACL 中與指定授權規則完全相符的授權規則。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.Reset">
      <summary> 移除  ACL 中包含與指定授權規則相同之 SID 的授權規則，然後將指定的授權規則加入 ACL。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.Set">
      <summary> 移除  ACL 中的所有授權規則，然後將指定的授權規則加入 ACL。</summary>
    </member>
    <member name="T:System.Security.AccessControl.AccessControlSections">
      <summary>指定要儲存或載入的安全性描述元 (Security Descriptor) 區段。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.Access">
      <summary>Discretionary 存取控制清單 (DACL)。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.All">
      <summary>整個安全性描述元。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.Audit">
      <summary>系統存取控制清單 (SACL)。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.Group">
      <summary>主要群組。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.None">
      <summary>沒有區段。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.Owner">
      <summary>擁有人。</summary>
    </member>
    <member name="T:System.Security.AccessControl.AccessControlType">
      <summary>指定 <see cref="T:System.Security.AccessControl.AccessRule" /> 物件是用於允許存取還是拒絕存取。這些值不是旗標，它們也不可組合。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlType.Allow">
      <summary>
        <see cref="T:System.Security.AccessControl.AccessRule" /> 物件是用於允許存取安全物件。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlType.Deny">
      <summary>
        <see cref="T:System.Security.AccessControl.AccessRule" /> 物件是用於拒絕存取安全物件。</summary>
    </member>
    <member name="T:System.Security.AccessControl.AccessRule">
      <summary>表示使用者的識別 (Identity)、存取遮罩和存取控制 (Access Control) 型別 (允許或拒絕) 的組合。<see cref="T:System.Security.AccessControl.AccessRule" /> 物件還包含子物件如何繼承規則，以及如何傳用該繼承的相關資訊。</summary>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>使用指定的值，初始化 <see cref="T:System.Security.AccessControl.AccessRule" /> 類別的新執行個體。</summary>
      <param name="identity">要套用存取規則的識別 (Identity)。這個參數必須是可轉型成 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 的物件。</param>
      <param name="accessMask">這個規則的存取遮罩。存取遮罩為 32 位元的匿名位元集合，其意義由個別的整合子定義。</param>
      <param name="isInherited">如果這個規則是從父容器繼承，則為 true。</param>
      <param name="inheritanceFlags">存取規則的繼承 (Inheritance) 屬性。</param>
      <param name="propagationFlags">是否要自動傳用繼承的存取規則。如果 <paramref name="inheritanceFlags" /> 設為 <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />，將略過傳用旗標。</param>
      <param name="type">有效的存取控制型別。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="identity" /> 參數的值不能轉型成 <see cref="T:System.Security.Principal.SecurityIdentifier" />，或是 <paramref name="type" /> 參數包含無效的值。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="accessMask" /> 參數的值為零，或是 <paramref name="inheritanceFlags" /> 或 <paramref name="propagationFlags" /> 參數包含無法辨認的旗標值。</exception>
    </member>
    <member name="P:System.Security.AccessControl.AccessRule.AccessControlType">
      <summary>取得與這個 <see cref="T:System.Security.AccessControl.AccessRule" /> 物件相關聯的 <see cref="T:System.Security.AccessControl.AccessControlType" /> 值。</summary>
      <returns>與這個 <see cref="T:System.Security.AccessControl.AccessRule" /> 物件相關聯的 <see cref="T:System.Security.AccessControl.AccessControlType" /> 值。</returns>
    </member>
    <member name="T:System.Security.AccessControl.AccessRule`1">
      <summary>表示使用者的識別 (Identity)、存取遮罩和存取控制 (Access Control) 型別 (允許或拒絕) 的組合。AccessRule`1 物件還包含子物件如何繼承規則，以及如何傳用該繼承的相關資訊。</summary>
      <typeparam name="T">存取規則的存取權限類型。</typeparam>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule`1.#ctor(System.Security.Principal.IdentityReference,`0,System.Security.AccessControl.AccessControlType)">
      <summary>使用指定的值，初始化 AccessRule’1 類別的新執行個體。</summary>
      <param name="identity">要套用存取規則的識別 (Identity)。</param>
      <param name="rights">存取規則的權限。</param>
      <param name="type">有效的存取控制型別。</param>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule`1.#ctor(System.Security.Principal.IdentityReference,`0,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>使用指定的值，初始化 AccessRule’1 類別的新執行個體。</summary>
      <param name="identity">要套用存取規則的識別 (Identity)。</param>
      <param name="rights">存取規則的權限。</param>
      <param name="inheritanceFlags">存取規則的繼承 (Inheritance) 屬性。</param>
      <param name="propagationFlags">是否要自動傳用繼承的存取規則。如果 <paramref name="inheritanceFlags" /> 設為 <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />，將略過傳用旗標。</param>
      <param name="type">有效的存取控制型別。</param>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule`1.#ctor(System.String,`0,System.Security.AccessControl.AccessControlType)">
      <summary>使用指定的值，初始化 AccessRule’1 類別的新執行個體。</summary>
      <param name="identity">要套用存取規則的識別 (Identity)。</param>
      <param name="rights">存取規則的權限。</param>
      <param name="type">有效的存取控制型別。</param>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule`1.#ctor(System.String,`0,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>使用指定的值，初始化 AccessRule’1 類別的新執行個體。</summary>
      <param name="identity">要套用存取規則的識別 (Identity)。</param>
      <param name="rights">存取規則的權限。</param>
      <param name="inheritanceFlags">存取規則的繼承 (Inheritance) 屬性。</param>
      <param name="propagationFlags">是否要自動傳用繼承的存取規則。如果 <paramref name="inheritanceFlags" /> 設為 <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />，將略過傳用旗標。</param>
      <param name="type">有效的存取控制型別。</param>
    </member>
    <member name="P:System.Security.AccessControl.AccessRule`1.Rights">
      <summary>取得目前執行個體的權限。</summary>
      <returns>目前執行個體的權限，轉型為類型 &lt;T&gt;。</returns>
    </member>
    <member name="T:System.Security.AccessControl.AceEnumerator">
      <summary>提供在存取控制清單 (ACL) 中逐一查看存取控制項目 (ACE) 的功能。</summary>
    </member>
    <member name="P:System.Security.AccessControl.AceEnumerator.Current">
      <summary>取得 <see cref="T:System.Security.AccessControl.GenericAce" /> 集合中的目前項目。這個屬性會取得物件的易記型別版本。</summary>
      <returns>
        <see cref="T:System.Security.AccessControl.GenericAce" /> 集合中的目前項目。</returns>
    </member>
    <member name="M:System.Security.AccessControl.AceEnumerator.MoveNext">
      <summary>讓列舉程式前進至 <see cref="T:System.Security.AccessControl.GenericAce" /> 集合中的下一個項目。</summary>
      <returns>如果列舉值成功地前移至下一個項目，則為 true，如果列舉值已超過集合的結尾，則為 false。</returns>
      <exception cref="T:System.InvalidOperationException">在建立列舉值之後，會修改集合。</exception>
    </member>
    <member name="M:System.Security.AccessControl.AceEnumerator.Reset">
      <summary>將列舉值設為它的初始位置，即 <see cref="T:System.Security.AccessControl.GenericAce" /> 集合中的第一個項目之前。</summary>
      <exception cref="T:System.InvalidOperationException">在建立列舉值之後，會修改集合。</exception>
    </member>
    <member name="P:System.Security.AccessControl.AceEnumerator.System#Collections#IEnumerator#Current"></member>
    <member name="T:System.Security.AccessControl.AceFlags">
      <summary>指定存取控制項目 (ACE) 的繼承 (Inheritance) 和稽核行為。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.AuditFlags">
      <summary>稽核所有的存取嘗試。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.ContainerInherit">
      <summary>存取遮罩會傳用至子容器 (Container) 物件。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.FailedAccess">
      <summary>稽核失敗的存取嘗試。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.InheritanceFlags">
      <summary>
        <see cref="F:System.Security.AccessControl.AceFlags.ObjectInherit" />、<see cref="F:System.Security.AccessControl.AceFlags.ContainerInherit" />、<see cref="F:System.Security.AccessControl.AceFlags.NoPropagateInherit" /> 和 <see cref="F:System.Security.AccessControl.AceFlags.InheritOnly" /> 的邏輯 OR。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.Inherited">
      <summary>ACE 是從父容器繼承而非明確設定用於物件。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.InheritOnly">
      <summary>存取遮罩只會傳用至子物件。這同時包含容器和分葉子物件。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.None">
      <summary>未設定 ACE 旗標。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.NoPropagateInherit">
      <summary>存取檢查不適用於物件，只適用於其子系。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.ObjectInherit">
      <summary>存取遮罩會傳用至子分葉物件。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.SuccessfulAccess">
      <summary>稽核成功的存取嘗試。</summary>
    </member>
    <member name="T:System.Security.AccessControl.AceQualifier">
      <summary>指定存取控制項目 (ACE) 的功能。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceQualifier.AccessAllowed">
      <summary>允許存取。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceQualifier.AccessDenied">
      <summary>拒絕存取。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceQualifier.SystemAlarm">
      <summary>引起系統警示。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceQualifier.SystemAudit">
      <summary>引起系統稽核。</summary>
    </member>
    <member name="T:System.Security.AccessControl.AceType">
      <summary>定義可用的存取控制項目 (ACE) 型別。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowed">
      <summary>允許對 <see cref="T:System.Security.Principal.IdentityReference" /> 物件識別之特定信任項物件的存取。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowedCallback">
      <summary>允許對 <see cref="T:System.Security.Principal.IdentityReference" /> 物件識別之特定信任項物件的存取。這個 ACE 型別可包含選擇性的回呼資料。回呼資料是未解譯的資源管理員特定 BLOB。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowedCallbackObject">
      <summary>允許對物件、屬性集或屬性的存取。ACE 包含一組存取權限、識別物件型別的 GUID，以及識別系統要授與存取權之信任項的 <see cref="T:System.Security.Principal.IdentityReference" /> 物件。ACE 也包含 GUID 和一組可依照子物件控制 ACE 繼承的旗標。這個 ACE 型別可包含選擇性的回呼資料。回呼資料是未解譯的資源管理員特定 BLOB。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowedCompound">
      <summary>已定義但從未使用。在這裡併入以求完整性。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowedObject">
      <summary>允許對物件、屬性集或屬性的存取。ACE 包含一組存取權限、識別物件型別的 GUID，以及識別系統要授與存取權之信任項的 <see cref="T:System.Security.Principal.IdentityReference" /> 物件。ACE 也包含 GUID 和一組可依照子物件控制 ACE 繼承的旗標。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessDenied">
      <summary>拒絕對 <see cref="T:System.Security.Principal.IdentityReference" /> 物件識別之特定信任項物件的存取。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessDeniedCallback">
      <summary>拒絕對 <see cref="T:System.Security.Principal.IdentityReference" /> 物件識別之特定信任項物件的存取。此 ACE 型別可包含選擇性的回呼資料。回呼資料是未解譯的資源管理員特定 BLOB。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessDeniedCallbackObject">
      <summary>拒絕對物件、屬性集或屬性的存取。ACE 包含一組存取權限、識別物件型別的 GUID，以及識別系統要授與存取權之信任項的 <see cref="T:System.Security.Principal.IdentityReference" /> 物件。ACE 也包含 GUID 和一組可依照子物件控制 ACE 繼承的旗標。此 ACE 型別可包含選擇性的回呼資料。回呼資料是未解譯的資源管理員特定 BLOB。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessDeniedObject">
      <summary>拒絕對物件、屬性集或屬性的存取。ACE 包含一組存取權限、識別物件型別的 GUID，以及識別系統要授與存取權之信任項的 <see cref="T:System.Security.Principal.IdentityReference" /> 物件。ACE 也包含 GUID 和一組可依照子物件控制 ACE 繼承的旗標。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.MaxDefinedAceType">
      <summary>追蹤列舉型別中定義的最大 ACE 型別。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAlarm">
      <summary>保留供將來使用。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAlarmCallback">
      <summary>保留供將來使用。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAlarmCallbackObject">
      <summary>保留供將來使用。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAlarmObject">
      <summary>保留供將來使用。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAudit">
      <summary>在指定的信任項嘗試取得物件的存取權時，記錄稽核訊息。此信任項是由 <see cref="T:System.Security.Principal.IdentityReference" /> 物件識別。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAuditCallback">
      <summary>在指定的信任項嘗試取得物件的存取權時，記錄稽核訊息。此信任項是由 <see cref="T:System.Security.Principal.IdentityReference" /> 物件識別。此 ACE 型別可包含選擇性的回呼資料。回呼資料是未解譯的資源管理員特定 BLOB。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAuditCallbackObject">
      <summary>在指定的信任項嘗試取得物件或子物件 (例如屬性集或屬性) 的存取權時，記錄稽核訊息。ACE 包含一組存取權限、識別物件或子物件型別的 GUID，以及識別系統要稽核存取之信任項的 <see cref="T:System.Security.Principal.IdentityReference" /> 物件。ACE 也包含 GUID 和一組可依照子物件控制 ACE 繼承的旗標。此 ACE 型別可包含選擇性的回呼資料。回呼資料是未解譯的資源管理員特定 BLOB。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAuditObject">
      <summary>在指定的信任項嘗試取得物件或子物件 (例如屬性集或屬性) 的存取權時，記錄稽核訊息。ACE 包含一組存取權限、識別物件或子物件型別的 GUID，以及識別系統要稽核存取之信任項的 <see cref="T:System.Security.Principal.IdentityReference" /> 物件。ACE 也包含 GUID 和一組可依照子物件控制 ACE 繼承的旗標。</summary>
    </member>
    <member name="T:System.Security.AccessControl.AuditFlags">
      <summary>指定稽核嘗試存取安全物件的條件。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AuditFlags.Failure">
      <summary>稽核失敗的存取嘗試。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AuditFlags.None">
      <summary>不稽核任何存取嘗試。</summary>
    </member>
    <member name="F:System.Security.AccessControl.AuditFlags.Success">
      <summary>稽核成功的存取嘗試。</summary>
    </member>
    <member name="T:System.Security.AccessControl.AuditRule">
      <summary>表示使用者識別 (Identity) 和存取遮罩的組合。<see cref="T:System.Security.AccessControl.AuditRule" /> 物件還包含子物件如何繼承規則、如何傳用繼承，以及稽核繼承條件的相關資訊。</summary>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>使用指定的值，初始化 <see cref="T:System.Security.AccessControl.AuditRule" /> 類別的新執行個體。</summary>
      <param name="identity">要套用稽核規則的識別。它必須是可以轉型為 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 的物件。</param>
      <param name="accessMask">這個規則的存取遮罩。存取遮罩為 32 位元的匿名位元集合，其意義由個別的整合子定義。</param>
      <param name="isInherited">true 表示從父容器繼承這個規則。</param>
      <param name="inheritanceFlags">稽核規則的繼承屬性。</param>
      <param name="propagationFlags">是否要自動傳用繼承的稽核規則。如果 <paramref name="inheritanceFlags" /> 設為 <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />，將略過傳用旗標。</param>
      <param name="auditFlags">稽核規則的條件。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="identity" /> 參數的值不能轉型成 <see cref="T:System.Security.Principal.SecurityIdentifier" />，或是 <paramref name="auditFlags" /> 參數包含無效的值。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="accessMask" /> 參數的值為零，或是 <paramref name="inheritanceFlags" /> 或 <paramref name="propagationFlags" /> 參數包含無法辨認的旗標值。</exception>
    </member>
    <member name="P:System.Security.AccessControl.AuditRule.AuditFlags">
      <summary>取得這個稽核規則的稽核旗標。</summary>
      <returns>列舉值的位元組合。這個組合指定此稽核規則的稽核條件。</returns>
    </member>
    <member name="T:System.Security.AccessControl.AuditRule`1">
      <summary>表示使用者識別和存取遮罩的組合。</summary>
      <typeparam name="T"></typeparam>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule`1.#ctor(System.Security.Principal.IdentityReference,`0,System.Security.AccessControl.AuditFlags)">
      <summary>使用指定的值，初始化 AuditRule’1 類別的新執行個體。</summary>
      <param name="identity">要套用這個稽核規則的識別。</param>
      <param name="rights">稽核規則的權限。</param>
      <param name="flags">要稽核其規則的條件。</param>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule`1.#ctor(System.Security.Principal.IdentityReference,`0,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>使用指定的值，初始化 AuditRule’1 類別的新執行個體。</summary>
      <param name="identity">要套用稽核規則的識別。</param>
      <param name="rights">稽核規則的權限。</param>
      <param name="inheritanceFlags">稽核規則的繼承屬性。</param>
      <param name="propagationFlags">是否要自動傳用繼承的稽核規則。</param>
      <param name="flags">稽核規則的條件。</param>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule`1.#ctor(System.String,`0,System.Security.AccessControl.AuditFlags)">
      <summary>使用指定的值，初始化 AuditRule’1 類別的新執行個體。</summary>
      <param name="identity">要套用稽核規則的識別。</param>
      <param name="rights">稽核規則的權限。</param>
      <param name="flags">稽核規則的屬性。</param>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule`1.#ctor(System.String,`0,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>使用指定的值，初始化 AuditRule’1 類別的新執行個體。</summary>
      <param name="identity">要套用稽核規則的識別。</param>
      <param name="rights">稽核規則的權限。</param>
      <param name="inheritanceFlags">稽核規則的繼承屬性。</param>
      <param name="propagationFlags">是否要自動傳用繼承的稽核規則。</param>
      <param name="flags">稽核規則的條件。</param>
    </member>
    <member name="P:System.Security.AccessControl.AuditRule`1.Rights">
      <summary>稽核規則的權限。</summary>
      <returns>傳回 <see cref="{0}" />。</returns>
    </member>
    <member name="T:System.Security.AccessControl.AuthorizationRule">
      <summary>決定對安全物件的存取。衍生類別 (Derived Class) <see cref="T:System.Security.AccessControl.AccessRule" /> 和 <see cref="T:System.Security.AccessControl.AuditRule" /> 會為存取和稽核功能提供特製化。</summary>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>使用指定的值，初始化 <see cref="T:System.Security.AuthorizationControl.AccessRule" /> 類別的新執行個體。</summary>
      <param name="identity">要套用存取規則的識別 (Identity)。這個參數必須是可轉型成 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 的物件。</param>
      <param name="accessMask">這個規則的存取遮罩。存取遮罩為 32 位元的匿名位元集合，其意義由個別的整合子定義。</param>
      <param name="isInherited">true 表示從父容器繼承這個規則。</param>
      <param name="inheritanceFlags">存取規則的繼承 (Inheritance) 屬性。</param>
      <param name="propagationFlags">是否要自動傳用繼承的存取規則。如果 <paramref name="inheritanceFlags" /> 設為 <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />，將略過傳用旗標。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="identity" /> 參數的值無法轉型成 <see cref="T:System.Security.Principal.SecurityIdentifier" />。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="accessMask" /> 參數的值為零，或是 <paramref name="inheritanceFlags" /> 或 <paramref name="propagationFlags" /> 參數包含無法辨認的旗標值。</exception>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.AccessMask">
      <summary>取得這個規則的存取遮罩。</summary>
      <returns>這個規則的存取遮罩。</returns>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.IdentityReference">
      <summary>取得套用這個規則的 <see cref="T:System.Security.Principal.IdentityReference" />。</summary>
      <returns>套用這個規則的 <see cref="T:System.Security.Principal.IdentityReference" />。</returns>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.InheritanceFlags">
      <summary>取得旗標值，判斷子物件如何繼承這個規則。</summary>
      <returns>列舉值的位元組合。</returns>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.IsInherited">
      <summary>取得值，指出這個規則是明確設定還是從父容器物件繼承。</summary>
      <returns>如果這個規則並非明確設定而是從父容器繼承，則為 true。</returns>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.PropagationFlags">
      <summary>取得傳用旗標值，決定這個規則的繼承如何傳用至子物件。只有在 <see cref="T:System.Security.AccessControl.InheritanceFlags" /> 列舉型別 (Enumeration) 的值不是 <see cref="F:System.Security.AccessControl.InheritanceFlags.None" /> 時，這個屬性才有意義。</summary>
      <returns>列舉值的位元組合。</returns>
    </member>
    <member name="T:System.Security.AccessControl.AuthorizationRuleCollection">
      <summary>表示 <see cref="T:System.Security.AccessControl.AuthorizationRule" /> 物件的集合。</summary>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.#ctor">
      <summary>初始化 <see cref="T:System.Security.AccessControl.AuthorizationRuleCollection" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.AddRule(System.Security.AccessControl.AuthorizationRule)">
      <summary>將 <see cref="T:System.Web.Configuration.AuthorizationRule" /> 物件加入集合。</summary>
      <param name="rule">要加入至集合中的 <see cref="T:System.Web.Configuration.AuthorizationRule" /> 物件。</param>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.CopyTo(System.Security.AccessControl.AuthorizationRule[],System.Int32)">
      <summary>複製集合的內容至陣列。</summary>
      <param name="rules">要複製集合內容至其中的陣列。</param>
      <param name="index">要開始複製之以零起始的索引。</param>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRuleCollection.Count"></member>
    <member name="P:System.Security.AccessControl.AuthorizationRuleCollection.Item(System.Int32)">
      <summary>取得集合中位於指定索引的 <see cref="T:System.Security.AccessControl.AuthorizationRule" /> 物件。</summary>
      <returns>在指定之索引處的 <see cref="T:System.Security.AccessControl.AuthorizationRule" /> 物件。</returns>
      <param name="index">要取得的 <see cref="T:System.Security.AccessControl.AuthorizationRule" /> 物件之以零起始的索引。</param>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)"></member>
    <member name="P:System.Security.AccessControl.AuthorizationRuleCollection.System#Collections#ICollection#IsSynchronized"></member>
    <member name="P:System.Security.AccessControl.AuthorizationRuleCollection.System#Collections#ICollection#SyncRoot"></member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.System#Collections#IEnumerable#GetEnumerator"></member>
    <member name="T:System.Security.AccessControl.CommonAce">
      <summary>表示存取控制項目 (ACE)。</summary>
    </member>
    <member name="M:System.Security.AccessControl.CommonAce.#ctor(System.Security.AccessControl.AceFlags,System.Security.AccessControl.AceQualifier,System.Int32,System.Security.Principal.SecurityIdentifier,System.Boolean,System.Byte[])">
      <summary>初始化 <see cref="T:System.Security.AccessControl.CommonAce" /> 類別的新執行個體。</summary>
      <param name="flags">旗標，指定新存取控制項目 (ACE) 之繼承 (Inheritance)、繼承傳用和稽核條件的相關資訊。</param>
      <param name="qualifier">新 ACE 的使用方式。</param>
      <param name="accessMask">ACE 的存取遮罩。</param>
      <param name="sid">與新 ACE 相關聯的 <see cref="T:System.Security.Principal.SecurityIdentifier" />。</param>
      <param name="isCallback">true 表示指定新 ACE 為回呼 (Callback) 型別 ACE。</param>
      <param name="opaque">與新 ACE 相關聯的不透明資料。不透明資料只適用於回呼 ACE 類型。這個陣列的長度不能大於 <see cref="M:System.Security.AccessControl.CommonAce.MaxOpaqueLength(System.Boolean)" /> 方法的傳回值。</param>
    </member>
    <member name="P:System.Security.AccessControl.CommonAce.BinaryLength">
      <summary>取得目前 <see cref="T:System.Security.AccessControl.CommonAce" /> 物件的二進位表示長度 (以位元組為單位)。將 ACL 封送處理至二進位陣列之前，以 <see cref="M:System.Security.AccessControl.CommonAce.GetBinaryForm(System.Byte[],System.Int32)" /> 方法使用這個長度。</summary>
      <returns>目前 <see cref="T:System.Security.AccessControl.CommonAce" /> 物件的二進位表示長度 (以位元組為單位)。</returns>
    </member>
    <member name="M:System.Security.AccessControl.CommonAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>將 <see cref="T:System.Security.AccessControl.CommonAce" /> 物件的內容封送處理到開始於指定位移的指定位元組陣列。</summary>
      <param name="binaryForm">位元組陣列，<see cref="T:System.Security.AccessControl.CommonAce" /> 物件的內容會封送處理至此陣列。</param>
      <param name="offset">要開始封送處理的位移。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 為負值，或是過大而無法將整個 <see cref="T:System.Security.AccessControl.CommonAce" /> 複製到 <paramref name="binaryForm" /> 陣列。</exception>
    </member>
    <member name="M:System.Security.AccessControl.CommonAce.MaxOpaqueLength(System.Boolean)">
      <summary>取得回呼存取控制項目 (ACE) 之不透明資料 BLOB (二進位大型物件) 的最大允許長度。</summary>
      <returns>不透明資料 BLOB 的允許長度。</returns>
      <param name="isCallback">true 表示指定 <see cref="T:System.Security.AccessControl.CommonAce" /> 物件為回呼 ACE 型別。</param>
    </member>
    <member name="T:System.Security.AccessControl.CommonAcl">
      <summary>代表存取控制清單 (ACL)，且為 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 與 <see cref="T:System.Security.AccessControl.SystemAcl" /> 類別的基底類別。</summary>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.BinaryLength">
      <summary>取得目前 <see cref="T:System.Security.AccessControl.CommonAcl" /> 物件的二進位表示長度 (以位元組為單位)。使用 <see cref="M:System.Security.AccessControl.CommonAcl.GetBinaryForm(System.Byte[],System.Int32)" /> 方法，將存取控制清單 (ACL) 封送處理至二進位陣列之前，應使用這個長度。</summary>
      <returns>目前 <see cref="T:System.Security.AccessControl.CommonAcl" /> 物件的二進位表示長度 (以位元組為單位)。</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.Count">
      <summary>取得目前 <see cref="T:System.Security.AccessControl.CommonAcl" /> 物件中的存取控制項目 (ACE) 數目。</summary>
      <returns>目前 <see cref="T:System.Security.AccessControl.CommonAcl" /> 物件中的 ACE 數目。</returns>
    </member>
    <member name="M:System.Security.AccessControl.CommonAcl.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>將 <see cref="T:System.Security.AccessControl.CommonAcl" /> 物件的內容封送處理到開始於指定位移的指定位元組陣列。</summary>
      <param name="binaryForm">位元組陣列，<see cref="T:System.Security.AccessControl.CommonAcl" /> 的內容會封送處理至此陣列。</param>
      <param name="offset">要開始封送處理的位移。</param>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.IsCanonical">
      <summary>取得布林值，指定目前 <see cref="T:System.Security.AccessControl.CommonAcl" /> 物件中的存取控制項目 (ACE) 是否為標準順序。</summary>
      <returns>如果目前 <see cref="T:System.Security.AccessControl.CommonAcl" /> 物件中的 ACE 為標準順序則為 true；否則為 false。</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.IsContainer">
      <summary>設定 <see cref="T:System.Security.AccessControl.CommonAcl" /> 物件是否為容器。</summary>
      <returns>如果目前 <see cref="T:System.Security.AccessControl.CommonAcl" /> 物件是容器，則為 true。</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.IsDS">
      <summary>設定目前 <see cref="T:System.Security.AccessControl.CommonAcl" /> 物件是否為目錄物件存取控制清單 (ACL)。</summary>
      <returns>如果目前 <see cref="T:System.Security.AccessControl.CommonAcl" /> 物件是目錄物件 ACL，則為 true。</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.Item(System.Int32)">
      <summary>取得或設定在指定索引處的 <see cref="T:System.Security.AccessControl.CommonAce" />。</summary>
      <returns>指定索引處的 <see cref="T:System.Security.AccessControl.CommonAce" />。</returns>
      <param name="index">要取得或設定的 <see cref="T:System.Security.AccessControl.CommonAce" /> 的以零起始的索引。</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonAcl.Purge(System.Security.Principal.SecurityIdentifier)">
      <summary> 移除與指定 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 物件相關聯之這個 <see cref="T:System.Security.AccessControl.CommonAcl" /> 物件中包含的所有存取控制項目 (ACE)。</summary>
      <param name="sid">要檢查的 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 物件。</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonAcl.RemoveInheritedAces">
      <summary>從這個 <see cref="T:System.Security.AccessControl.CommonAcl" /> 物件移除所有繼承的存取控制項目 (ACE)。</summary>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.Revision">
      <summary>取得 <see cref="T:System.Security.AccessControl.CommonAcl" /> 的修訂層級。</summary>
      <returns>位元組值，指定 <see cref="T:System.Security.AccessControl.CommonAcl" /> 的修訂層級。</returns>
    </member>
    <member name="T:System.Security.AccessControl.CommonObjectSecurity">
      <summary>無需直接管理存取控制清單 (ACL) 即可控制對物件的存取。這個類別是 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 類別的抽象基底類別。</summary>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.#ctor(System.Boolean)">
      <summary>初始化 <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> 類別的新執行個體。</summary>
      <param name="isContainer">如果新物件是容器物件，則為 true。</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.AddAccessRule(System.Security.AccessControl.AccessRule)">
      <summary>將指定的存取規則加入與這個 <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> 物件相關聯的 Discretionary 存取控制清單 (DACL)。</summary>
      <param name="rule">要加入的存取規則。</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.AddAuditRule(System.Security.AccessControl.AuditRule)">
      <summary>將指定的稽核規則加入與這個 <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> 物件相關聯的系統存取控制清單 (SACL)。</summary>
      <param name="rule">要加入的稽核規則。</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.GetAccessRules(System.Boolean,System.Boolean,System.Type)">
      <summary>取得與指定之安全識別項相關聯的存取規則集合。</summary>
      <returns>與指定之 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 物件相關聯的存取規則集合。</returns>
      <param name="includeExplicit">true 表示包括為物件明確設定的存取規則。</param>
      <param name="includeInherited">true 表示包括繼承的存取規則。</param>
      <param name="targetType">指定要擷取存取規則的安全識別項屬於 T:System.Security.Principal.SecurityIdentifier 型別或 T:System.Security.Principal.NTAccount 型別。這個參數值所屬的型別必須可以轉譯為 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 型別。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.GetAuditRules(System.Boolean,System.Boolean,System.Type)">
      <summary>取得與指定之安全識別項相關聯的稽核規則集合。</summary>
      <returns>與指定之 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 物件相關聯的稽核規則集合。</returns>
      <param name="includeExplicit">true 表示包括為物件明確設定的稽核規則。</param>
      <param name="includeInherited">true 表示包括繼承的稽核規則。</param>
      <param name="targetType">要擷取稽核規則的安全識別項。這必須是可轉型為 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 物件的物件。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.ModifyAccess(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AccessRule,System.Boolean@)">
      <summary>將指定的修改套用至與這個 <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> 物件相關聯的 Discretionary 存取控制清單 (DACL)。</summary>
      <returns>如果 DACL 修改成功，則為 true，否則為 false。</returns>
      <param name="modification">要套用至 DACL 的修改。</param>
      <param name="rule">要修改的存取規則。</param>
      <param name="modified">如果 DACL 修改成功，則為 true，否則為 false。</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.ModifyAudit(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AuditRule,System.Boolean@)">
      <summary>將指定的修改套用至與這個 <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> 物件相關聯的系統存取控制清單 (SACL)。</summary>
      <returns>如果 SACL 修改成功，則為 true，否則為 false。</returns>
      <param name="modification">要套用至 SACL 的修改。</param>
      <param name="rule">要修改的稽核規則。</param>
      <param name="modified">如果 SACL 修改成功，則為 true，否則為 false。</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAccessRule(System.Security.AccessControl.AccessRule)">
      <summary>從與這個 <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> 物件相關聯的 Discretionary 存取控制清單 (DACL)，移除與指定的存取規則包含相同安全識別項和存取遮罩的存取規則。</summary>
      <returns>如果成功移除存取規則，則為 true，否則為 false。</returns>
      <param name="rule">要移除的存取規則。</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAccessRuleAll(System.Security.AccessControl.AccessRule)">
      <summary>從與這個 <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> 物件相關聯的 Discretionary 存取控制清單 (DACL)，移除與指定的存取規則具有相同安全識別項的所有存取規則。</summary>
      <param name="rule">要移除的存取規則。</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAccessRuleSpecific(System.Security.AccessControl.AccessRule)">
      <summary>從與這個 <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> 物件相關聯的 Discretionary 存取控制清單 (DACL)，移除與指定的存取規則完全相符的所有存取規則。</summary>
      <param name="rule">要移除的存取規則。</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAuditRule(System.Security.AccessControl.AuditRule)">
      <summary>從與這個 <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> 物件相關聯的系統存取控制清單 (SACL)，移除與指定的稽核規則包含相同安全識別項和存取遮罩的稽核規則。</summary>
      <returns>如果成功移除稽核規則，則為 true，否則為 false。</returns>
      <param name="rule">要移除的稽核規則。</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAuditRuleAll(System.Security.AccessControl.AuditRule)">
      <summary>從與這個 <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> 物件相關聯的系統存取控制清單 (SACL)，移除與指定的稽核規則包含相同安全識別項的所有稽核規則。</summary>
      <param name="rule">要移除的稽核規則。</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAuditRuleSpecific(System.Security.AccessControl.AuditRule)">
      <summary>從與這個 <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> 物件相關聯的系統存取控制清單 (SACL)，移除與指定稽核規則完全相符的所有稽核規則。</summary>
      <param name="rule">要移除的稽核規則。</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.ResetAccessRule(System.Security.AccessControl.AccessRule)">
      <summary>移除與這個 <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> 物件相關聯之 Discretionary 存取控制清單 (DACL) 中的所有存取規則，然後加入指定的存取規則。</summary>
      <param name="rule">要重設的存取規則。</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.SetAccessRule(System.Security.AccessControl.AccessRule)">
      <summary>從與這個 <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> 物件相關聯之 Discretionary 存取控制清單 (DACL)，移除與指定的存取規則包含相同安全識別項和限定詞的所有存取規則，然後加入指定的存取規則。</summary>
      <param name="rule">要設定的存取規則。</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.SetAuditRule(System.Security.AccessControl.AuditRule)">
      <summary>從與這個 <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> 物件相關聯之系統存取控制清單 (SACL)，移除與指定的稽核規則包含相同安全識別項和限定詞的所有稽核規則，然後加入指定的稽核規則。</summary>
      <param name="rule">要設定的稽核規則。</param>
    </member>
    <member name="T:System.Security.AccessControl.CommonSecurityDescriptor">
      <summary>表示安全性描述元 (Security Descriptor)。安全性描述元包括擁有人、主要群組、Discretionary 存取控制清單 (DACL) 和系統存取控制清單 (SACL)。</summary>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.#ctor(System.Boolean,System.Boolean,System.Byte[],System.Int32)">
      <summary>從指定的位元組值陣列，初始化 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 類別的新執行個體。</summary>
      <param name="isContainer">如果新的安全性描述元與容器物件相關聯，則為 true。</param>
      <param name="isDS">如果新的安全性描述元與目錄物件相關聯，則為 true。</param>
      <param name="binaryForm">位元組值陣列，從其中建立新的 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 物件。</param>
      <param name="offset">
        <paramref name="binaryForm" /> 陣列中開始複製的位移。</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.#ctor(System.Boolean,System.Boolean,System.Security.AccessControl.ControlFlags,System.Security.Principal.SecurityIdentifier,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.SystemAcl,System.Security.AccessControl.DiscretionaryAcl)">
      <summary>從指定的資訊，初始化 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 類別的新執行個體。</summary>
      <param name="isContainer">如果新的安全性描述元與容器物件相關聯，則為 true。</param>
      <param name="isDS">如果新的安全性描述元與目錄物件相關聯，則為 true。</param>
      <param name="flags">旗標，指定新 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 物件的行為。</param>
      <param name="owner">新 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 物件擁有人。</param>
      <param name="group">新 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 物件的主要群組。</param>
      <param name="systemAcl">新 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 物件的系統存取控制清單 (SACL)。</param>
      <param name="discretionaryAcl">新 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 物件的 Discretionary 存取控制清單 (DACL)。</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.#ctor(System.Boolean,System.Boolean,System.Security.AccessControl.RawSecurityDescriptor)">
      <summary>從指定的 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 物件，初始化 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 類別的新執行個體。</summary>
      <param name="isContainer">如果新的安全性描述元與容器物件相關聯，則為 true。</param>
      <param name="isDS">如果新的安全性描述元與目錄物件相關聯，則為 true。</param>
      <param name="rawSecurityDescriptor">
        <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 物件，從其中建立新的 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 物件。</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.#ctor(System.Boolean,System.Boolean,System.String)">
      <summary>從指定的安全性描述元定義語言 (SDDL) 字串，初始化 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 類別的新執行個體。</summary>
      <param name="isContainer">如果新的安全性描述元與容器物件相關聯，則為 true。</param>
      <param name="isDS">如果新的安全性描述元與目錄物件相關聯，則為 true。</param>
      <param name="sddlForm">SDDL 字串，從其中建立新的 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 物件。</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.AddDiscretionaryAcl(System.Byte,System.Int32)">
      <summary>設定<see cref="P:System.Security.AccessControl.CommonSecurityDescriptor.DiscretionaryAcl" />屬性這<see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />執行個體和設定<see cref="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclPresent" />旗標。</summary>
      <param name="revision">新 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 物件的修訂層級。</param>
      <param name="trusted">此 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 物件可以容納的存取控制項目 (ACE) 數。這個數字只做為提示之用。</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.AddSystemAcl(System.Byte,System.Int32)">
      <summary>設定<see cref="P:System.Security.AccessControl.CommonSecurityDescriptor.SystemAcl" />屬性這<see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />執行個體和設定<see cref="F:System.Security.AccessControl.ControlFlags.SystemAclPresent" />旗標。</summary>
      <param name="revision">新 <see cref="T:System.Security.AccessControl.SystemAcl" /> 物件的修訂層級。</param>
      <param name="trusted">此 <see cref="T:System.Security.AccessControl.SystemAcl" /> 物件可以容納的存取控制項目 (ACE) 數。這個數字只做為提示之用。</param>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.ControlFlags">
      <summary>取得指定 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 物件之行為的值。</summary>
      <returns>與邏輯 OR 運算結合的一或多個 <see cref="T:System.Security.AccessControl.ControlFlags" /> 列舉型別的值。</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.DiscretionaryAcl">
      <summary>取得或設定這個 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 物件的 Discretionary 存取控制清單 (DACL)。DACL 包含存取規則。</summary>
      <returns>這個 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 物件的 DACL。</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.Group">
      <summary>取得或設定這個 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 物件的主要群組。</summary>
      <returns>這個 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 物件的主要群組。</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.IsContainer">
      <summary>取得布林值 (Boolean) ，指定與這個 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 物件相關聯的物件是否為容器物件。</summary>
      <returns>如果與這個 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 物件相關聯的物件是容器物件則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.IsDiscretionaryAclCanonical">
      <summary>取得布林值，指定與這個 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 物件相關聯的 Discretionary 存取控制清單 (DACL) 是否為標準順序。</summary>
      <returns>如果與這個 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 物件相關聯的 DACL 為標準順序則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.IsDS">
      <summary>取得布林值，指定與這個 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 物件相關聯的物件是否為目錄物件。</summary>
      <returns>如果與這個 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 物件相關聯的物件為目錄物件則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.IsSystemAclCanonical">
      <summary>取得布林值，指定與這個 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 物件相關聯的系統存取控制清單 (SACL) 是否為標準順序。</summary>
      <returns>如果與這個 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 物件相關聯的 SACL 為標準順序則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.Owner">
      <summary>取得或設定與這個 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 物件相關聯的物件擁有人。</summary>
      <returns>與這個 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 物件相關聯的物件擁有人。</returns>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.PurgeAccessControl(System.Security.Principal.SecurityIdentifier)">
      <summary>從與這個 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 物件相關聯的 Discretionary 存取控制清單 (DACL)，移除指定安全識別項 (Security Identifier) 的所有存取規則。</summary>
      <param name="sid">要移除存取規則的安全識別項。</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.PurgeAudit(System.Security.Principal.SecurityIdentifier)">
      <summary>從與這個 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 物件相關聯的系統存取控制清單 (SACL)，移除指定安全識別項的所有稽核規則。</summary>
      <param name="sid">要移除稽核規則的安全識別項。</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.SetDiscretionaryAclProtection(System.Boolean,System.Boolean)">
      <summary>設定與這個 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 物件相關聯之 Discretionary 存取控制清單 (DACL) 的繼承保護。保護的 DACL 不會從父容器繼承存取規則。</summary>
      <param name="isProtected">true 表示保護 DACL，使其不繼承。</param>
      <param name="preserveInheritance">true 表示在 DACL 中保留繼承存取規則，false 表示移除 DACL 中的繼承存取規則。</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.SetSystemAclProtection(System.Boolean,System.Boolean)">
      <summary>設定與這個 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 物件相關聯之系統存取控制清單 (SACL) 的繼承保護。保護的 SACL 不會從父容器繼承稽核規則。</summary>
      <param name="isProtected">true 表示保護 SACL，使其不繼承。</param>
      <param name="preserveInheritance">true 表示在 SACL 中保留繼承稽核規則，false 表示移除 SACL 中的繼承稽核規則。</param>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.SystemAcl">
      <summary>取得或設定這個 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 物件的系統存取控制清單 (SACL)。SACL 包含稽核規則。</summary>
      <returns>這個 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 物件的 SACL。</returns>
    </member>
    <member name="T:System.Security.AccessControl.CompoundAce">
      <summary>表示複合存取控制項目 (ACE)。</summary>
    </member>
    <member name="M:System.Security.AccessControl.CompoundAce.#ctor(System.Security.AccessControl.AceFlags,System.Int32,System.Security.AccessControl.CompoundAceType,System.Security.Principal.SecurityIdentifier)">
      <summary>初始化 <see cref="T:System.Security.AccessControl.CompoundAce" /> 類別的新執行個體。</summary>
      <param name="flags">包含旗標，這些旗標會指定新存取控制項目 (ACE) 之繼承 (Inheritance)、繼承傳用和稽核條件的相關資訊。</param>
      <param name="accessMask">ACE 的存取遮罩。</param>
      <param name="compoundAceType">
        <see cref="T:System.Security.AccessControl.CompoundAceType" /> 列舉中的值。</param>
      <param name="sid">與新 ACE 相關聯的 <see cref="T:System.Security.Principal.SecurityIdentifier" />。</param>
    </member>
    <member name="P:System.Security.AccessControl.CompoundAce.BinaryLength">
      <summary>取得目前 <see cref="T:System.Security.AccessControl.CompoundAce" /> 物件的二進位表示長度 (以位元組為單位)。使用 <see cref="M:System.Security.AccessControl.CompoundAce.GetBinaryForm" /> 方法，將 ACL 封送處理至二進位陣列之前，應使用這個長度。</summary>
      <returns>目前 <see cref="T:System.Security.AccessControl.CompoundAce" /> 物件的二進位表示長度 (以位元組為單位)。</returns>
    </member>
    <member name="P:System.Security.AccessControl.CompoundAce.CompoundAceType">
      <summary>取得或設定這個 <see cref="T:System.Security.AccessControl.CompoundAce" /> 物件的型別。</summary>
      <returns>這個 <see cref="T:System.Security.AccessControl.CompoundAce" /> 物件的型別。</returns>
    </member>
    <member name="M:System.Security.AccessControl.CompoundAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>將 <see cref="T:System.Security.AccessControl.CompoundAce" /> 物件的內容封送處理到開始於指定位移的指定位元組陣列。</summary>
      <param name="binaryForm">位元組陣列，<see cref="T:System.Security.AccessControl.CompoundAce" /> 的內容會封送處理至此陣列。</param>
      <param name="offset">要開始封送處理的位移。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 為負值，或是過大而無法將整個 <see cref="T:System.Security.AccessControl.CompoundAce" /> 複製到 <paramref name="array" />。</exception>
    </member>
    <member name="T:System.Security.AccessControl.CompoundAceType">
      <summary>指定 <see cref="T:System.Security.AccessControl.CompoundAce" /> 物件的型別。</summary>
    </member>
    <member name="F:System.Security.AccessControl.CompoundAceType.Impersonation">
      <summary>
        <see cref="T:System.Security.AccessControl.CompoundAce" /> 物件是用於模擬。</summary>
    </member>
    <member name="T:System.Security.AccessControl.ControlFlags">
      <summary>這些旗標會影響安全性描述元 (Security Descriptor) 行為。</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclAutoInherited">
      <summary>指定已自動從父代繼承 Discretionary 存取控制清單 (DACL)。僅由資源管理員設定。</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclAutoInheritRequired">
      <summary>已忽略。</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclDefaulted">
      <summary>指定 DACL 已由預設機制取得。僅由資源管理員設定。</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclPresent">
      <summary>指定 DACL 不為 null。由資源管理員或使用者設定。</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclProtected">
      <summary>指定資源管理員防止自動繼承。由資源管理員或使用者設定。</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclUntrusted">
      <summary>已忽略。</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.GroupDefaulted">
      <summary>指定群組 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 已由預設機制取得。只由資源管理員設定，不應由呼叫端設定。</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.None">
      <summary>沒有控制旗標。</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.OwnerDefaulted">
      <summary>指定擁有人 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 已由預設機制取得。只由資源管理員設定，不應由呼叫端設定。</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.RMControlValid">
      <summary>指定保留欄位的內容有效。</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SelfRelative">
      <summary>指定安全性描述元的二進位表示使用自我相關格式。一定會設定這個旗標。</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.ServerSecurity">
      <summary>已忽略。</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclAutoInherited">
      <summary>指定已自動從父代繼承系統存取控制清單 (SACL)。僅由資源管理員設定。</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclAutoInheritRequired">
      <summary>已忽略。</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclDefaulted">
      <summary>指定 SACL 已由預設機制取得。僅由資源管理員設定。</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclPresent">
      <summary>指定 SACL 不為 null。由資源管理員或使用者設定。</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclProtected">
      <summary>指定資源管理員防止自動繼承。由資源管理員或使用者設定。</summary>
    </member>
    <member name="T:System.Security.AccessControl.CustomAce">
      <summary>表示未由其中一個 <see cref="T:System.Security.AccessControl.AceType" /> 列舉型別 (Enumeration) 成員定義的存取控制項目 (ACE)。</summary>
    </member>
    <member name="M:System.Security.AccessControl.CustomAce.#ctor(System.Security.AccessControl.AceType,System.Security.AccessControl.AceFlags,System.Byte[])">
      <summary>初始化 <see cref="T:System.Security.AccessControl.CustomAce" /> 類別的新執行個體。</summary>
      <param name="type">新存取控制項目 (ACE) 的型別。這個值必須大於 <see cref="F:System.Security.AccessControl.AceType.MaxDefinedAceType" />。</param>
      <param name="flags">旗標，指定新 ACE 之繼承 (Inheritance)、繼承傳用 (Propagation) 和稽核條件的相關資訊。</param>
      <param name="opaque">包含新 ACE 之資料的位元組值陣列。這個值可以是 null。這個陣列的長度不能大於 <see cref="F:System.Security.AccessControl.CustomAce.MaxOpaqueLength" /> 欄位的值，且必須為四的倍數。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="type" /> 參數的值未大於 <see cref="F:System.Security.AccessControl.AceType.MaxDefinedAceType" />，或者 <paramref name="opaque" /> 陣列的長度大於 <see cref="F:System.Security.AccessControl.CustomAce.MaxOpaqueLength" /> 欄位的值，或不是四的倍數。</exception>
    </member>
    <member name="P:System.Security.AccessControl.CustomAce.BinaryLength">
      <summary>取得目前 <see cref="T:System.Security.AccessControl.CustomAce" /> 物件的二進位表示長度 (以位元組為單位)。使用 <see cref="M:System.Security.AccessControl.CustomAce.GetBinaryForm" /> 方法，將 ACL 封送處理 (Marshaling) 至二進位陣列之前，應使用這個長度。</summary>
      <returns>目前 <see cref="T:System.Security.AccessControl.CustomAce" /> 物件的二進位表示長度 (以位元組為單位)。</returns>
    </member>
    <member name="M:System.Security.AccessControl.CustomAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>將 <see cref="T:System.Security.AccessControl.CustomAce" /> 物件的內容封送處理到開始於指定位移的指定位元組陣列。</summary>
      <param name="binaryForm">位元組陣列，<see cref="T:System.Security.AccessControl.CustomAce" /> 的內容會封送處理至此陣列。</param>
      <param name="offset">要開始封送處理的位移。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 為負值，或是過大而無法將整個 <see cref="T:System.Security.AccessControl.CustomAce" /> 複製到 <paramref name="array" />。</exception>
    </member>
    <member name="M:System.Security.AccessControl.CustomAce.GetOpaque">
      <summary>傳回與這個 <see cref="T:System.Security.AccessControl.CustomAce" /> 物件相關聯的不透明資料。</summary>
      <returns>元組值陣列，表示與這個 <see cref="T:System.Security.AccessControl.CustomAce" /> 物件相關聯的不透明資料。</returns>
    </member>
    <member name="F:System.Security.AccessControl.CustomAce.MaxOpaqueLength">
      <summary>傳回這個 <see cref="T:System.Security.AccessControl.CustomAce" /> 物件之不透明資料 Blob 允許的最大長度。</summary>
    </member>
    <member name="P:System.Security.AccessControl.CustomAce.OpaqueLength">
      <summary>取得與這個 <see cref="T:System.Security.AccessControl.CustomAce" /> 物件相關聯的不透明資料長度。</summary>
      <returns>不透明回呼 (Callback) 資料的長度。</returns>
    </member>
    <member name="M:System.Security.AccessControl.CustomAce.SetOpaque(System.Byte[])">
      <summary>設定與這個 <see cref="T:System.Security.AccessControl.CustomAce" /> 物件相關聯的不透明回呼資料。</summary>
      <param name="opaque">位元組值陣列，表示這個 <see cref="T:System.Security.AccessControl.CustomAce" /> 物件的不透明回呼資料。</param>
    </member>
    <member name="T:System.Security.AccessControl.DiscretionaryAcl">
      <summary>表示 Discretionary 存取控制清單 (DACL)。</summary>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.#ctor(System.Boolean,System.Boolean,System.Byte,System.Int32)">
      <summary>使用指定的值，初始化 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 類別的新執行個體。</summary>
      <param name="isContainer">true if the new <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> object is a container.</param>
      <param name="isDS">true if the new <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> object is a directory object Access Control List (ACL).</param>
      <param name="revision">新 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 物件的修訂層級。</param>
      <param name="capacity">此 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 物件可以容納的存取控制項目 (ACE) 數。這個數字只做為提示之用。</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.#ctor(System.Boolean,System.Boolean,System.Int32)">
      <summary>使用指定的值，初始化 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 類別的新執行個體。</summary>
      <param name="isContainer">true if the new <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> object is a container.</param>
      <param name="isDS">true if the new <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> object is a directory object Access Control List (ACL).</param>
      <param name="capacity">此 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 物件可以容納的存取控制項目 (ACE) 數。這個數字只做為提示之用。</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.#ctor(System.Boolean,System.Boolean,System.Security.AccessControl.RawAcl)">
      <summary>使用指定的 <see cref="T:System.Security.AccessControl.RawAcl" /> 物件中的指定值，初始化 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 類別的新執行個體。</summary>
      <param name="isContainer">true if the new <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> object is a container.</param>
      <param name="isDS">true if the new <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> object is a directory object Access Control List (ACL).</param>
      <param name="rawAcl">The underlying <see cref="T:System.Security.AccessControl.RawAcl" /> object for the new <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> object.您可以指定 null 以建立空的 ACL。</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.AddAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>將包含指定設定的存取控制項目 (ACE) 加入目前的 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 物件。</summary>
      <param name="accessType">要加入的存取控制型別 (允許或拒絕)。</param>
      <param name="sid">要加入 ACE 的 <see cref="T:System.Security.Principal.SecurityIdentifier" />。</param>
      <param name="accessMask">新 ACE 的存取規則。</param>
      <param name="inheritanceFlags">旗標，指定新 ACE 的繼承屬性。</param>
      <param name="propagationFlags">旗標，指定新 ACE 的繼承傳用屬性。</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.AddAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>將包含指定設定的存取控制項目 (ACE) 加入目前的 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 物件。為新的 ACE 指定物件型別或繼承的物件型別時，您可以將這個方法用於目錄物件存取控制清單 (ACL)。</summary>
      <param name="accessType">要加入的存取控制型別 (允許或拒絕)。</param>
      <param name="sid">要加入 ACE 的 <see cref="T:System.Security.Principal.SecurityIdentifier" />。</param>
      <param name="accessMask">新 ACE 的存取規則。</param>
      <param name="inheritanceFlags">旗標，指定新 ACE 的繼承屬性。</param>
      <param name="propagationFlags">旗標，指定新 ACE 的繼承傳用屬性。</param>
      <param name="objectFlags">旗標，指定 <paramref name="objectType" /> 和 <paramref name="inheritedObjectType" /> 參數是否包含非 null 值。</param>
      <param name="objectType">要套用新 ACE 之物件類別的識別。</param>
      <param name="inheritedObjectType">可繼承新 ACE 之子物件類別的識別。</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.AddAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAccessRule)">
      <summary>將包含指定設定的存取控制項目 (ACE) 加入目前的 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 物件。</summary>
      <param name="accessType">要加入的存取控制型別 (允許或拒絕)。</param>
      <param name="sid">要加入 ACE 的 <see cref="T:System.Security.Principal.SecurityIdentifier" />。</param>
      <param name="rule">
        <see cref="T:System.Security.AccessControl.ObjectAccessRule" />新的存取權。</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>從目前的 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 物件移除指定的存取控制規則。</summary>
      <returns>如果這個方法成功移除指定的存取，則為 true，否則為 false。</returns>
      <param name="accessType">要移除的存取控制型別 (允許或拒絕)。</param>
      <param name="sid">要移除存取控制規則的 <see cref="T:System.Security.Principal.SecurityIdentifier" />。</param>
      <param name="accessMask">要移除之規則的存取遮罩。</param>
      <param name="inheritanceFlags">旗標，指定要移除之規則的繼承屬性。</param>
      <param name="propagationFlags">旗標，指定要移除之規則的繼承傳用屬性。</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>從目前的 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 物件移除指定的存取控制規則。指定物件型別或繼承物件型別時，將這個方法用於目錄物件存取控制清單 (ACL)。</summary>
      <returns>如果這個方法成功移除指定的存取，則為 true，否則為 false。</returns>
      <param name="accessType">要移除的存取控制型別 (允許或拒絕)。</param>
      <param name="sid">要移除存取控制規則的 <see cref="T:System.Security.Principal.SecurityIdentifier" />。</param>
      <param name="accessMask">要移除之存取控制規則的存取遮罩。</param>
      <param name="inheritanceFlags">旗標，指定要移除之存取控制規則的繼承屬性。</param>
      <param name="propagationFlags">旗標，指定要移除之存取控制規則的繼承傳用屬性。</param>
      <param name="objectFlags">旗標，指定 <paramref name="objectType" /> 和 <paramref name="inheritedObjectType" /> 參數是否包含非 null 值。</param>
      <param name="objectType">要套用移除之存取控制規則的物件類別識別。</param>
      <param name="inheritedObjectType">可繼承移除之存取控制規則的子物件類別識別。</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAccessRule)">
      <summary>從目前的 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 物件移除指定的存取控制規則。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。</returns>
      <param name="accessType">要移除的存取控制型別 (允許或拒絕)。</param>
      <param name="sid">要移除存取控制規則的 <see cref="T:System.Security.Principal.SecurityIdentifier" />。</param>
      <param name="rule">
        <see cref="T:System.Security.AccessControl.ObjectAccessRule" />要移除存取權。</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccessSpecific(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>從目前的 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 物件，移除指定的存取控制項目 (ACE)。</summary>
      <param name="accessType">要移除的存取控制型別 (允許或拒絕)。</param>
      <param name="sid">要移除 ACE 的 <see cref="T:System.Security.Principal.SecurityIdentifier" />。</param>
      <param name="accessMask">要移除之 ACE 的存取遮罩。</param>
      <param name="inheritanceFlags">旗標，指定要移除之 ACE 的繼承屬性。</param>
      <param name="propagationFlags">旗標，指定要移除之 ACE 的繼承傳用屬性。</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccessSpecific(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>從目前的 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 物件，移除指定的存取控制項目 (ACE)。為要移除的 ACE 指定物件型別或繼承的物件型別時，您可以將這個方法用於目錄物件存取控制清單 (ACL)。</summary>
      <param name="accessType">要移除的存取控制型別 (允許或拒絕)。</param>
      <param name="sid">要移除 ACE 的 <see cref="T:System.Security.Principal.SecurityIdentifier" />。</param>
      <param name="accessMask">要移除之 ACE 的存取遮罩。</param>
      <param name="inheritanceFlags">旗標，指定要移除之 ACE 的繼承屬性。</param>
      <param name="propagationFlags">旗標，指定要移除之 ACE 的繼承傳用屬性。</param>
      <param name="objectFlags">旗標，指定 <paramref name="objectType" /> 和 <paramref name="inheritedObjectType" /> 參數是否包含非 null 值。</param>
      <param name="objectType">要套用移除之 ACE 的物件類別識別。</param>
      <param name="inheritedObjectType">可繼承移除之 ACE 的子物件類別識別。</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccessSpecific(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAccessRule)">
      <summary>從目前的 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 物件，移除指定的存取控制項目 (ACE)。</summary>
      <param name="accessType">要移除的存取控制型別 (允許或拒絕)。</param>
      <param name="sid">要移除 ACE 的 <see cref="T:System.Security.Principal.SecurityIdentifier" />。</param>
      <param name="rule">
        <see cref="T:System.Security.AccessControl.ObjectAccessRule" />要移除存取權。</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.SetAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>為指定的 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 物件設定指定的存取控制。</summary>
      <param name="accessType">要設定的存取控制型別 (允許或拒絕)。</param>
      <param name="sid">要設定 ACE 的 <see cref="T:System.Security.Principal.SecurityIdentifier" />。</param>
      <param name="accessMask">新 ACE 的存取規則。</param>
      <param name="inheritanceFlags">旗標，指定新 ACE 的繼承屬性。</param>
      <param name="propagationFlags">旗標，指定新 ACE 的繼承傳用屬性。</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.SetAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>為指定的 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 物件設定指定的存取控制。</summary>
      <param name="accessType">要設定的存取控制型別 (允許或拒絕)。</param>
      <param name="sid">要設定 ACE 的 <see cref="T:System.Security.Principal.SecurityIdentifier" />。</param>
      <param name="accessMask">新 ACE 的存取規則。</param>
      <param name="inheritanceFlags">旗標，指定新 ACE 的繼承屬性。</param>
      <param name="propagationFlags">旗標，指定新 ACE 的繼承傳用屬性。</param>
      <param name="objectFlags">旗標，指定 <paramref name="objectType" /> 和 <paramref name="inheritedObjectType" /> 參數是否包含非 null 值。</param>
      <param name="objectType">要套用新 ACE 之物件類別的識別。</param>
      <param name="inheritedObjectType">可繼承新 ACE 之子物件類別的識別。</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.SetAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAccessRule)">
      <summary>為指定的 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 物件設定指定的存取控制。</summary>
      <param name="accessType">要設定的存取控制型別 (允許或拒絕)。</param>
      <param name="sid">要設定 ACE 的 <see cref="T:System.Security.Principal.SecurityIdentifier" />。</param>
      <param name="rule">
        <see cref="T:System.Security.AccessControl.ObjectAccessRule" />要將存取權限。</param>
    </member>
    <member name="T:System.Security.AccessControl.GenericAce">
      <summary>表示存取控制項目 (ACE)，且為所有其他 ACE 類別的基底類別。</summary>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.AceFlags">
      <summary>取得或設定與這個 <see cref="T:System.Security.AccessControl.GenericAce" /> 物件相關聯的 <see cref="T:System.Security.AccessControl.AceFlags" />。</summary>
      <returns>與這個 <see cref="T:System.Security.AccessControl.GenericAce" /> 物件相關聯的 <see cref="T:System.Security.AccessControl.AceFlags" />。</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.AceType">
      <summary>取得這個存取控制項目 (ACE) 的型別。</summary>
      <returns>這個 ACE 的型別。</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.AuditFlags">
      <summary>取得與這個存取控制項目 (ACE) 相關聯的稽核資訊。</summary>
      <returns>與這個存取控制項目 (ACE) 相關聯的稽核資訊。</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.BinaryLength">
      <summary>取得目前 <see cref="T:System.Security.AccessControl.GenericAce" /> 物件的二進位表示長度 (以位元組為單位)。使用 <see cref="M:System.Security.AccessControl.GenericAce.GetBinaryForm" /> 方法，將 ACL 封送處理至二進位陣列之前，應使用這個長度。</summary>
      <returns>目前 <see cref="T:System.Security.AccessControl.GenericAce" /> 物件的二進位表示長度 (以位元組為單位)。</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.Copy">
      <summary>建立這個存取控制項目 (ACE) 的深層複本 (Deep Copy)。</summary>
      <returns>這個方法建立的 <see cref="T:System.Security.AccessControl.GenericAce" /> 物件。</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.CreateFromBinaryForm(System.Byte[],System.Int32)">
      <summary>從指定的二進位資料建立 <see cref="T:System.Security.AccessControl.GenericAce" /> 物件。</summary>
      <returns>這個方法建立的 <see cref="T:System.Security.AccessControl.GenericAce" /> 物件。</returns>
      <param name="binaryForm">二進位資料，從中建立新的 <see cref="T:System.Security.AccessControl.GenericAce" /> 物件。</param>
      <param name="offset">要開始解封送處理 (Unmarshaling) 的位移。</param>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.Equals(System.Object)">
      <summary>判斷指定的 <see cref="T:System.Security.AccessControl.GenericAce" /> 物件是否等於目前的 <see cref="T:System.Security.AccessControl.GenericAce" /> 物件。</summary>
      <returns>如果指定的 <see cref="T:System.Security.AccessControl.GenericAce" /> 物件等於目前的 <see cref="T:System.Security.AccessControl.GenericAce" /> 物件，則為 true，否則為 false。</returns>
      <param name="o">要與目前 <see cref="T:System.Security.AccessControl.GenericAce" /> 物件進行比較的 <see cref="T:System.Security.AccessControl.GenericAce" /> 物件。</param>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>將 <see cref="T:System.Security.AccessControl.GenericAce" /> 物件的內容封送處理到開始於指定位移的指定位元組陣列。</summary>
      <param name="binaryForm">位元組陣列，<see cref="T:System.Security.AccessControl.GenericAce" /> 的內容會封送處理至此陣列。</param>
      <param name="offset">要開始封送處理的位移。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 為負值，或是過大而無法將整個 <see cref="T:System.Security.AccessControl.GenericAcl" /> 複製到 <paramref name="array" />。</exception>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.GetHashCode">
      <summary>做為 <see cref="T:System.Security.AccessControl.GenericAce" /> 類別的雜湊函式。<see cref="M:System.Security.AccessControl.GenericAce.GetHashCode" /> 方法適用於雜湊演算法和雜湊資料表這類的資料結構。</summary>
      <returns>目前 <see cref="T:System.Security.AccessControl.GenericAce" /> 物件的雜湊程式碼。</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.InheritanceFlags">
      <summary>取得旗標，指定這個存取控制項目 (ACE) 的繼承屬性。</summary>
      <returns>旗標，指定這個 ACE 的繼承屬性。</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.IsInherited">
      <summary>取得布林值 (Boolean)，指定這個存取控制項目 (ACE) 是繼承的還是明確設定的。</summary>
      <returns>如果這個 ACE 是繼承的，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.op_Equality(System.Security.AccessControl.GenericAce,System.Security.AccessControl.GenericAce)">
      <summary>判斷指定的 <see cref="T:System.Security.AccessControl.GenericAce" /> 物件是否視為相等。</summary>
      <returns>如果兩個 <see cref="T:System.Security.AccessControl.GenericAce" /> 物件相等則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個 <see cref="T:System.Security.AccessControl.GenericAce" /> 物件。</param>
      <param name="right">要比較的第二個 <see cref="T:System.Security.AccessControl.GenericAce" />。</param>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.op_Inequality(System.Security.AccessControl.GenericAce,System.Security.AccessControl.GenericAce)">
      <summary>判斷指定的 <see cref="T:System.Security.AccessControl.GenericAce" /> 物件是否視為不相等。</summary>
      <returns>如果兩個 <see cref="T:System.Security.AccessControl.GenericAce" /> 物件不相等，則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個 <see cref="T:System.Security.AccessControl.GenericAce" /> 物件。</param>
      <param name="right">要比較的第二個 <see cref="T:System.Security.AccessControl.GenericAce" />。</param>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.PropagationFlags">
      <summary>取得旗標，指定這個存取控制項目 (ACE) 的繼承傳用屬性。</summary>
      <returns>旗標，指定這個 ACE 的繼承傳用屬性。</returns>
    </member>
    <member name="T:System.Security.AccessControl.GenericAcl">
      <summary>表示存取控制清單 (ACL)，且為 <see cref="T:System.Security.AccessControl.CommonAcl" />、<see cref="T:System.Security.AccessControl.DiscretionaryAcl" />、<see cref="T:System.Security.AccessControl.RawAcl" /> 和 <see cref="T:System.Security.AccessControl.SystemAcl" /> 類別的基底類別。</summary>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.#ctor">
      <summary>初始化 <see cref="T:System.Security.AccessControl.GenericAcl" /> 類別的新執行個體。</summary>
    </member>
    <member name="F:System.Security.AccessControl.GenericAcl.AclRevision">
      <summary>目前 <see cref="T:System.Security.AccessControl.GenericAcl" /> 的修訂層級。這個值是由存取控制清單 (ACL) 的 <see cref="P:System.Security.AccessControl.GenericAcl.Revision" /> 屬性傳回，而這些存取控制清單未與目錄服務物件相關聯。</summary>
    </member>
    <member name="F:System.Security.AccessControl.GenericAcl.AclRevisionDS">
      <summary>目前 <see cref="T:System.Security.AccessControl.GenericAcl" /> 的修訂層級。這個值是由存取控制清單 (ACL) 的 <see cref="P:System.Security.AccessControl.GenericAcl.Revision" /> 屬性傳回，而這些存取控制清單與目錄服務物件相關聯。</summary>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.BinaryLength">
      <summary>取得目前 <see cref="T:System.Security.AccessControl.GenericAcl" /> 物件的二進位表示長度 (以位元組為單位)。使用 <see cref="M:System.Security.AccessControl.GenericAcl.GetBinaryForm" /> 方法，將 ACL 封送處理 (Marshaling) 至二進位陣列之前，應使用這個長度。</summary>
      <returns>目前 <see cref="T:System.Security.AccessControl.GenericAcl" /> 物件的二進位表示長度 (以位元組為單位)。</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.CopyTo(System.Security.AccessControl.GenericAce[],System.Int32)">
      <summary>複製目前 <see cref="T:System.Security.AccessControl.GenericAcl" /> 的每個 <see cref="T:System.Security.AccessControl.GenericAce" /> 至指定的陣列。</summary>
      <param name="array">陣列，放置目前 <see cref="T:System.Security.AccessControl.GenericAcl" /> 包含之 <see cref="T:System.Security.AccessControl.GenericAce" /> 物件的複本。</param>
      <param name="index">
        <paramref name="array" /> 中以零起始的索引，位於複製開始的位置。</param>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.Count">
      <summary>取得目前 <see cref="T:System.Security.AccessControl.GenericAcl" /> 物件中的存取控制項目 (ACE) 數目。</summary>
      <returns>目前 <see cref="T:System.Security.AccessControl.GenericAcl" /> 物件中的 ACE 數目。</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>將 <see cref="T:System.Security.AccessControl.GenericAcl" /> 物件的內容封送處理到開始於指定位移的指定位元組陣列。</summary>
      <param name="binaryForm">位元組陣列，<see cref="T:System.Security.AccessControl.GenericAcl" /> 的內容會封送處理至此陣列。</param>
      <param name="offset">要開始封送處理的位移。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 為負值，或是過大而無法將整個 <see cref="T:System.Security.AccessControl.GenericAcl" /> 複製到 <paramref name="array" />。</exception>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.GetEnumerator">
      <summary>傳回 <see cref="T:System.Security.AccessControl.AceEnumerator" /> 類別的新執行個體。</summary>
      <returns>這個方法傳回的 <see cref="T:Security.AccessControl.AceEnumerator" />。</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.IsSynchronized">
      <summary>這個屬性永遠會設為 false。它只會因為 <see cref="T:System.Collections.ICollection" /> 介面的實作 (Implementation) 需要而進行實作。</summary>
      <returns>一定是 false。</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.Item(System.Int32)">
      <summary>取得或設定在指定索引處的 <see cref="T:System.Security.AccessControl.GenericAce" />。</summary>
      <returns>在指定索引處的 <see cref="T:System.Security.AccessControl.GenericAce" />。</returns>
      <param name="index">要取得或設定之 <see cref="T:System.Security.AccessControl.GenericAce" /> 的以零起始的索引。</param>
    </member>
    <member name="F:System.Security.AccessControl.GenericAcl.MaxBinaryLength">
      <summary>允許的 <see cref="T:System.Security.AccessControl.GenericAcl" /> 物件最大二進位長度。</summary>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.Revision">
      <summary>取得 <see cref="T:System.Security.AccessControl.GenericAcl" /> 的修訂層級。</summary>
      <returns>位元組值，指定 <see cref="T:System.Security.AccessControl.GenericAcl" /> 的修訂層級。</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.SyncRoot">
      <summary>這個屬性永遠傳回 null。它只會因為 <see cref="T:System.Collections.ICollection" /> 介面的實作 (Implementation) 需要而進行實作。</summary>
      <returns>永遠傳回 null。</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>複製目前 <see cref="T:System.Security.AccessControl.GenericAcl" /> 的每個 <see cref="T:System.Security.AccessControl.GenericAce" /> 至指定的陣列。</summary>
      <param name="array">陣列，放置目前 <see cref="T:System.Security.AccessControl.GenericAcl" /> 包含之 <see cref="T:System.Security.AccessControl.GenericAce" /> 物件的複本。</param>
      <param name="index">
        <paramref name="array" /> 中以零起始的索引，位於複製開始的位置。</param>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.System#Collections#IEnumerable#GetEnumerator">
      <summary>傳回 <see cref="T:System.Security.AccessControl.AceEnumerator" /> 類別的新執行個體，轉型為 <see cref="T:System.Collections.IEnumerator" /> 介面的執行個體。</summary>
      <returns>新的 <see cref="T:System.Security.AccessControl.AceEnumerator" /> 物件，轉型為 <see cref="T:System.Collections.IEnumerator" /> 介面的執行個體。</returns>
    </member>
    <member name="T:System.Security.AccessControl.GenericSecurityDescriptor">
      <summary>表示安全性描述元 (Security Descriptor)。安全性描述元包括擁有人、主要群組、Discretionary 存取控制清單 (DACL) 和系統存取控制清單 (SACL)。</summary>
    </member>
    <member name="M:System.Security.AccessControl.GenericSecurityDescriptor.#ctor">
      <summary>初始化 <see cref="T:System.Security.AccessControl.GenericSecurity" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.BinaryLength">
      <summary>取得目前 <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> 物件的二進位表示長度 (以位元組為單位)。使用 <see cref="M:System.Security.AccessControl.GenericSecurityDescriptor.GetBinaryForm" /> 方法，將 ACL 封送處理 (Marshaling) 至二進位陣列之前，應使用這個長度。</summary>
      <returns>目前 <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> 物件的二進位表示長度 (以位元組為單位)。</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.ControlFlags">
      <summary>取得指定 <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> 物件之行為的值。</summary>
      <returns>與邏輯 OR 運算結合的一或多個 <see cref="T:System.Security.AccessControl.ControlFlags" /> 列舉型別的值。</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericSecurityDescriptor.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>傳回位元組值陣列，表示這個 <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> 物件中包含的資訊。</summary>
      <param name="binaryForm">位元組陣列，<see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> 的內容會封送處理至此陣列。</param>
      <param name="offset">要開始封送處理的位移。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 為負值，或是過大而無法將整個 <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> 複製到 <paramref name="array" />。</exception>
    </member>
    <member name="M:System.Security.AccessControl.GenericSecurityDescriptor.GetSddlForm(System.Security.AccessControl.AccessControlSections)">
      <summary>傳回此 <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> 物件所表示之安全性描述元中指定區段的安全性描述元定義語言 (SDDL) 表示。</summary>
      <returns>與這個 <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> 物件相關聯之安全性描述元中指定區段的 SDDL 表示。</returns>
      <param name="includeSections">指定要取得安全性描述元的哪個區段 (存取規則、稽核規則、主要群組或擁有人)。</param>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.Group">
      <summary>取得或設定這個 <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> 物件的主要群組。</summary>
      <returns>這個 <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> 物件的主要群組。</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericSecurityDescriptor.IsSddlConversionSupported">
      <summary>傳回布林 (Boolean) 值，指定與這個 <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> 物件相關聯的安全性描述元是否可轉換為安全性描述元定義語言 (SDDL) 格式。</summary>
      <returns>如果與這個 <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> 物件相關聯的安全性描述元可轉換為安全性描述元定義語言 (SDDL) 格式，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.Owner">
      <summary>取得或設定與這個 <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> 物件相關聯的物件擁有人。</summary>
      <returns>與這個 <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> 物件相關聯的物件擁有人。</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.Revision">
      <summary>取得 <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> 物件的修訂層級。</summary>
      <returns>位元組值，指定 <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> 的修訂層級。</returns>
    </member>
    <member name="T:System.Security.AccessControl.InheritanceFlags">
      <summary>繼承 (Inheritance) 旗標會指定存取控制項目 (ACE) 的繼承語意。</summary>
    </member>
    <member name="F:System.Security.AccessControl.InheritanceFlags.ContainerInherit">
      <summary>ACE 由子容器 (Container) 物件繼承。</summary>
    </member>
    <member name="F:System.Security.AccessControl.InheritanceFlags.None">
      <summary>子物件不繼承 ACE。</summary>
    </member>
    <member name="F:System.Security.AccessControl.InheritanceFlags.ObjectInherit">
      <summary>ACE 由子分葉物件繼承。</summary>
    </member>
    <member name="T:System.Security.AccessControl.KnownAce">
      <summary>封裝 Microsoft Corporation 目前定義的所有存取控制項目 (ACE) 型別。所有的 <see cref="T:System.Security.AccessControl.KnownAce" /> 物件都包含 32 位元的存取遮罩和 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 物件。</summary>
    </member>
    <member name="P:System.Security.AccessControl.KnownAce.AccessMask">
      <summary>取得或設定這個 <see cref="T:System.Security.AccessControl.KnownAce" /> 物件的存取遮罩。</summary>
      <returns>這個 <see cref="T:System.Security.AccessControl.KnownAce" /> 物件的存取遮罩。</returns>
    </member>
    <member name="P:System.Security.AccessControl.KnownAce.SecurityIdentifier">
      <summary>取得或設定與這個 <see cref="T:System.Security.AccessControl.KnownAce" /> 物件相關聯的 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 物件。</summary>
      <returns>與這個 <see cref="T:System.Security.AccessControl.KnownAce" /> 物件關聯的 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 物件。</returns>
    </member>
    <member name="T:System.Security.AccessControl.NativeObjectSecurity">
      <summary>提供無需直接管理存取控制清單 (ACL)，即可控制對原生物件之存取的功能。原生物件型別是由 <see cref="T:System.Security.AccessControl.ResourceType" /> 列舉型別 (Enumeration) 所定義。</summary>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType)">
      <summary>使用指定的值，初始化 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 類別的新執行個體。</summary>
      <param name="isContainer">如果新 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 物件是容器物件，則為 true。</param>
      <param name="resourceType">與新 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 物件相關聯的安全物件型別。</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections)">
      <summary>使用指定的值，初始化 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 類別的新執行個體。我們建議您最好將 <paramref name="includeSections" /> 參數的值傳遞給建構函式，並將方法保存為相同的方法。如需詳細資訊，請參閱「備註」。</summary>
      <param name="isContainer">如果新 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 物件是容器物件，則為 true。</param>
      <param name="resourceType">與新 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 物件相關聯的安全物件型別。</param>
      <param name="handle">與新 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 物件相關聯之安全物件的控制代碼。</param>
      <param name="includeSections">其中一個 <see cref="T:System.Security.AccessControl.AccessControlSections" /> 列舉值，指定要包含在這個 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 物件中之安全物件安全性描述元 (存取規則、稽核規則、擁有人、主要群組) 的區段。</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>使用指定的值，初始化 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 類別的新執行個體。我們建議您最好將 <paramref name="includeSections" /> 參數的值傳遞給建構函式，並將方法保存為相同的方法。如需詳細資訊，請參閱「備註」。</summary>
      <param name="isContainer">如果新 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 物件是容器物件，則為 true。</param>
      <param name="resourceType">與新 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 物件相關聯的安全物件型別。</param>
      <param name="handle">與新 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 物件相關聯之安全物件的控制代碼。</param>
      <param name="includeSections">其中一個 <see cref="T:System.Security.AccessControl.AccessControlSections" /> 列舉值，指定要包含在這個 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 物件中之安全物件安全性描述元 (存取規則、稽核規則、擁有人、主要群組) 的區段。</param>
      <param name="exceptionFromErrorCode">委派由提供自訂例外狀況的整合子實作。</param>
      <param name="exceptionContext">物件，包含關於例外狀況之來源或目的端的內容資訊。</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>使用指定的值，初始化 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 類別的新執行個體。</summary>
      <param name="isContainer">如果新 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 物件是容器物件，則為 true。</param>
      <param name="resourceType">與新 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 物件相關聯的安全物件型別。</param>
      <param name="exceptionFromErrorCode">委派由提供自訂例外狀況的整合子實作。</param>
      <param name="exceptionContext">物件，包含關於例外狀況之來源或目的端的內容資訊。</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>使用指定的值，初始化 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 類別的新執行個體。我們建議您最好將 <paramref name="includeSections" /> 參數的值傳遞給建構函式，並將方法保存為相同的方法。如需詳細資訊，請參閱「備註」。</summary>
      <param name="isContainer">如果新 <see cref="T:System.Security.AccessControl.NativObjectSecurity" /> 物件是容器物件，則為 true。</param>
      <param name="resourceType">與新 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 物件相關聯的安全物件型別。</param>
      <param name="name">與新 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 物件相關聯之安全物件的名稱。</param>
      <param name="includeSections">其中一個 <see cref="T:System.Security.AccessControl.AccessControlSections" /> 列舉值，指定要包含在這個 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 物件中之安全物件安全性描述元 (存取規則、稽核規則、擁有人、主要群組) 的區段。</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.String,System.Security.AccessControl.AccessControlSections,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>使用指定的值，初始化 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 類別的新執行個體。我們建議您最好將 <paramref name="includeSections" /> 參數的值傳遞給建構函式，並將方法保存為相同的方法。如需詳細資訊，請參閱「備註」。</summary>
      <param name="isContainer">如果新 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 物件是容器物件，則為 true。</param>
      <param name="resourceType">與新 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 物件相關聯的安全物件型別。</param>
      <param name="name">與新 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 物件相關聯之安全物件的名稱。</param>
      <param name="includeSections">其中一個 <see cref="T:System.Security.AccessControl.AccessControlSections" /> 列舉值，指定要包含在這個 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 物件中之安全物件安全性描述元 (存取規則、稽核規則、擁有人、主要群組) 的區段。</param>
      <param name="exceptionFromErrorCode">委派由提供自訂例外狀況的整合子實作。</param>
      <param name="exceptionContext">物件，包含關於例外狀況之來源或目的端的內容資訊。</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.Persist(System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections)">
      <summary>將與此 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 物件相關聯之安全性描述元的指定區段，儲存至永久儲存區。我們建議您最好將 <paramref name="includeSections" /> 參數的值傳遞給建構函式，並將方法保存為相同的方法。如需詳細資訊，請參閱「備註」。</summary>
      <param name="handle">與這個 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 物件相關聯之安全物件的控制代碼。</param>
      <param name="includeSections">其中一個 <see cref="T:System.Security.AccessControl.AccessControlSections" /> 列舉值，指定要儲存之安全物件安全性描述元 (Security Descriptor) (存取規則、稽核規則、擁有人、主要群組) 的區段。</param>
      <exception cref="T:System.IO.FileNotFoundException">與這個 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 物件相關聯的安全物件為目錄或檔案，且找不到該目錄或檔案。</exception>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.Persist(System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections,System.Object)">
      <summary>將與此 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 物件相關聯之安全性描述元的指定區段，儲存至永久儲存區。我們建議您最好將 <paramref name="includeSections" /> 參數的值傳遞給建構函式，並將方法保存為相同的方法。如需詳細資訊，請參閱「備註」。</summary>
      <param name="handle">與這個 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 物件相關聯之安全物件的控制代碼。</param>
      <param name="includeSections">其中一個 <see cref="T:System.Security.AccessControl.AccessControlSections" /> 列舉值，指定要儲存之安全物件安全性描述元 (Security Descriptor) (存取規則、稽核規則、擁有人、主要群組) 的區段。</param>
      <param name="exceptionContext">物件，包含關於例外狀況之來源或目的端的內容資訊。</param>
      <exception cref="T:System.IO.FileNotFoundException">與這個 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 物件相關聯的安全物件為目錄或檔案，且找不到該目錄或檔案。</exception>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.Persist(System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>將與此 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 物件相關聯之安全性描述元的指定區段，儲存至永久儲存區。我們建議您最好將 <paramref name="includeSections" /> 參數的值傳遞給建構函式，並將方法保存為相同的方法。如需詳細資訊，請參閱「備註」。</summary>
      <param name="name">與這個 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 物件相關聯之安全物件的名稱。</param>
      <param name="includeSections">其中一個 <see cref="T:System.Security.AccessControl.AccessControlSections" /> 列舉值，指定要儲存之安全物件安全性描述元 (Security Descriptor) (存取規則、稽核規則、擁有人、主要群組) 的區段。</param>
      <exception cref="T:System.IO.FileNotFoundException">與這個 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 物件相關聯的安全物件為目錄或檔案，且找不到該目錄或檔案。</exception>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.Persist(System.String,System.Security.AccessControl.AccessControlSections,System.Object)">
      <summary>將與此 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 物件相關聯之安全性描述元的指定區段，儲存至永久儲存區。我們建議您最好將 <paramref name="includeSections" /> 參數的值傳遞給建構函式，並將方法保存為相同的方法。如需詳細資訊，請參閱「備註」。</summary>
      <param name="name">與這個 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 物件相關聯之安全物件的名稱。</param>
      <param name="includeSections">其中一個 <see cref="T:System.Security.AccessControl.AccessControlSections" /> 列舉值，指定要儲存之安全物件安全性描述元 (Security Descriptor) (存取規則、稽核規則、擁有人、主要群組) 的區段。</param>
      <param name="exceptionContext">物件，包含關於例外狀況之來源或目的端的內容資訊。</param>
      <exception cref="T:System.IO.FileNotFoundException">與這個 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 物件相關聯的安全物件為目錄或檔案，且找不到該目錄或檔案。</exception>
    </member>
    <member name="T:System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode">
      <summary>提供方法，便於整合子 (Integrator) 將數字錯誤碼對應至其建立的特定例外狀況。</summary>
      <returns>這個委派 (Delegate) 建立的 <see cref="T:System.Exception" />。</returns>
      <param name="errorCode">數字錯誤碼。</param>
      <param name="name">與 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 物件相關聯之安全物件的名稱。</param>
      <param name="handle">與 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 物件相關聯之安全物件的控制代碼。</param>
      <param name="context">物件，包含關於例外狀況之來源或目的端的內容資訊。</param>
    </member>
    <member name="T:System.Security.AccessControl.ObjectAccessRule">
      <summary>表示使用者的識別 (Identity)、存取遮罩和存取控制 (Access Control) 型別 (允許或拒絕) 的組合。<see cref="T:System.Security.AccessControl.ObjectAccessRule" /> 物件還包含要套用規則的物件型別、可繼承規則的子物件型別、子物件如何繼承規則，以及如何傳用繼承的相關資訊。</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAccessRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Guid,System.Guid,System.Security.AccessControl.AccessControlType)">
      <summary>使用指定的值，初始化 <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> 類別的新執行個體。</summary>
      <param name="identity">要套用存取規則的識別 (Identity)。它必須是可以轉型為 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 的物件。</param>
      <param name="accessMask">這個規則的存取遮罩。存取遮罩為 32 位元的匿名位元集合，其意義由個別的整合子定義。</param>
      <param name="isInherited">如果這個規則是從父容器繼承，則為 true。</param>
      <param name="inheritanceFlags">指定存取規則的繼承 (Inheritance) 屬性。</param>
      <param name="propagationFlags">指定是否要自動傳用繼承的存取規則。如果 <paramref name="inheritanceFlags" /> 設為 <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />，將略過傳用旗標。</param>
      <param name="objectType">要套用規則的物件型別。</param>
      <param name="inheritedObjectType">可繼承規則的子物件型別。</param>
      <param name="type">指定這個規則是允許存取還是拒絕存取。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="identity" /> 參數的值不能轉型成 <see cref="T:System.Security.Principal.SecurityIdentifier" />，或是 <paramref name="type" /> 參數包含無效的值。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="accessMask" /> 參數的值為 0，或 <paramref name="inheritanceFlags" /> 或 <paramref name="propagationFlags" /> 參數包含無法辨認的旗標值。</exception>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAccessRule.InheritedObjectType">
      <summary>取得可繼承 <see cref="System.Security.AccessControl.ObjectAccessRule" /> 物件的子物件型別。</summary>
      <returns>可繼承 <see cref="System.Security.AccessControl.ObjectAccessRule" /> 物件的子物件型別。</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAccessRule.ObjectFlags">
      <summary>取得旗標，指定 <see cref="System.Security.AccessControl.ObjectAccessRule" /> 物件的 <see cref="P:System.Security.AccessControl.ObjectAccessRule.ObjectType" /> 和 <see cref="P:System.Security.AccessControl.ObjectAccessRule.InheritedObjectType" /> 屬性是否包含有效值。</summary>
      <returns>
        <see cref="F:System.Security.AccessControl.ObjectAceFlags.ObjectAceTypePresent" /> 會指定 <see cref="P:System.Security.AccessControl.ObjectAccessRule.ObjectType" /> 屬性包含有效值。<see cref="F:System.Security.AccessControl.ObjectAceFlags.InheritedObjectAceTypePresent" /> 會指定 <see cref="P:System.Security.AccessControl.ObjectAccessRule.InheritedObjectType" /> 屬性包含有效值。這些值可與邏輯 OR 組合。</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAccessRule.ObjectType">
      <summary>取得要套用 <see cref="System.Security.AccessControl.ObjectAccessRule" /> 的物件型別。</summary>
      <returns>要套用 <see cref="System.Security.AccessControl.ObjectAccessRule" /> 的物件型別。</returns>
    </member>
    <member name="T:System.Security.AccessControl.ObjectAce">
      <summary>控制對目錄服務物件的存取。這個類別會表示與目錄物件相關聯的存取控制項目 (ACE)。</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAce.#ctor(System.Security.AccessControl.AceFlags,System.Security.AccessControl.AceQualifier,System.Int32,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid,System.Boolean,System.Byte[])">
      <summary>初始化 <see cref="T:System.Security.AccessControl.ObjectAce" /> 類別的新執行個體。</summary>
      <param name="aceFlags">新存取控制項目 (ACE) 的繼承 (Inheritance)、繼承傳用 (Propagation) 和稽核條件。</param>
      <param name="qualifier">新 ACE 的使用方式。</param>
      <param name="accessMask">ACE 的存取遮罩。</param>
      <param name="sid">與新 ACE 相關聯的 <see cref="T:System.Security.Principal.SecurityIdentifier" />。</param>
      <param name="flags">
        <paramref name="type" /> 和 <paramref name="inheritedType" /> 參數是否包含有效的物件 GUID。</param>
      <param name="type">GUID，可識別要套用新 ACE 的物件型別。</param>
      <param name="inheritedType">GUID，可識別可繼承新 ACE 的物件型別。</param>
      <param name="isCallback">如果新 ACE 為回呼 (Callback) 型別 ACE，則為 true。</param>
      <param name="opaque">與新 ACE 相關聯的不透明資料。這只適用於回呼 ACE 型別。這個陣列的長度不能大於 <see cref="M:System.Security.AccessControl.ObjectAceMaxOpaqueLength" /> 方法的傳回值。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">限定詞 (Qualifier) 參數包含無效值或不透明參數值的長度大於 <see cref="M:System.Security.AccessControl.ObjectAceMaxOpaqueLength" /> 方法的傳回值。</exception>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAce.BinaryLength">
      <summary>取得目前 <see cref="T:System.Security.AccessControl.ObjectAce" /> 物件的二進位表示長度 (以位元組為單位)。使用 <see cref="M:System.Security.AccessControl.ObjectAce.GetBinaryForm" /> 方法，將 ACL 封送處理至二進位陣列之前，應使用這個長度。</summary>
      <returns>目前 <see cref="T:System.Security.AccessControl.ObjectAce" /> 物件的二進位表示長度 (以位元組為單位)。</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>將 <see cref="T:System.Security.AccessControl.ObjectAce" /> 物件的內容封送處理到開始於指定位移的指定位元組陣列。</summary>
      <param name="binaryForm">位元組陣列，<see cref="T:System.Security.AccessControl.ObjectAce" /> 的內容會封送處理至此陣列。</param>
      <param name="offset">要開始封送處理的位移。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 為負值，或是過大而無法將整個 <see cref="T:System.Security.AccessControl.ObjectAce" /> 複製到 <paramref name="array" />。</exception>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAce.InheritedObjectAceType">
      <summary>取得或設定物件型別的 GUID，這個物件型別可以繼承這個 <see cref="T:System.Security.AccessControl.ObjectAce" /> 物件表示的存取控制項目 (ACE)。</summary>
      <returns>物件型別的 GUID，這個物件型別可繼承這個 <see cref="T:System.Security.AccessControl.ObjectAce" /> 物件表示的存取控制項目 (ACE)。</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAce.MaxOpaqueLength(System.Boolean)">
      <summary>傳回回呼存取控制項目 (ACE) 之不透明資料 BLOB 的最大允許長度 (以位元組為單位)。</summary>
      <returns>回呼存取控制項目 (ACE) 之不透明資料 BLOB 的最大允許長度 (以位元組為單位)。</returns>
      <param name="isCallback">如果 <see cref="T:System.Security.AccessControl.ObjectAce" /> 為回呼 ACE 型別，則為 True。</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAce.ObjectAceFlags">
      <summary>取得或設定旗標，指定 <see cref="P:System.Security.AccessControl.ObjectAce.ObjectAceType" /> 和 <see cref="P:System.Security.AccessControl.ObjectAce.InheritedObjectAceType" /> 屬性是否包含識別有效物件型別的值。</summary>
      <returns>與邏輯 OR 運算結合之一或多個 <see cref="T:System.Security.AccessControl.ObjectAceFlags" /> 列舉型別的成員。</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAce.ObjectAceType">
      <summary>取得或設定與這個 <see cref="T:System.Security.AccessControl.ObjectAce" /> 物件相關聯的物件型別 GUID。</summary>
      <returns>與這個 <see cref="T:System.Security.AccessControl.ObjectAce" /> 物件相關聯的物件型別 GUID。</returns>
    </member>
    <member name="T:System.Security.AccessControl.ObjectAceFlags">
      <summary>指定存取控制項目 (ACE) 的物件型別是否存在。</summary>
    </member>
    <member name="F:System.Security.AccessControl.ObjectAceFlags.InheritedObjectAceTypePresent">
      <summary>可繼承 ACE 的物件型別。</summary>
    </member>
    <member name="F:System.Security.AccessControl.ObjectAceFlags.None">
      <summary>物件型別不存在。</summary>
    </member>
    <member name="F:System.Security.AccessControl.ObjectAceFlags.ObjectAceTypePresent">
      <summary>與 ACE 相關聯的物件型別存在。</summary>
    </member>
    <member name="T:System.Security.AccessControl.ObjectAuditRule">
      <summary>表示使用者識別 (Identity)、存取遮罩和稽核條件的組合。<see cref="T:System.Security.AccessControl.ObjectAuditRule" /> 物件還包含要套用規則的物件型別、可繼承規則的子物件型別、子物件如何繼承規則，以及如何傳用繼承的相關資訊。</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAuditRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Guid,System.Guid,System.Security.AccessControl.AuditFlags)">
      <summary>初始化 <see cref="T:System.Security.AccessControl.ObjectAuditRule" /> 類別的新執行個體。</summary>
      <param name="identity">要套用存取規則的識別 (Identity)。它必須是可以轉型為 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 的物件。</param>
      <param name="accessMask">這個規則的存取遮罩。存取遮罩為 32 位元的匿名位元集合，其意義由個別的整合子定義。</param>
      <param name="isInherited">如果這個規則是從父容器繼承，則為 true。</param>
      <param name="inheritanceFlags">指定存取規則的繼承 (Inheritance) 屬性。</param>
      <param name="propagationFlags">是否要自動傳用繼承的存取規則。如果 <paramref name="inheritanceFlags" /> 設為 <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />，將略過傳用旗標。</param>
      <param name="objectType">要套用規則的物件型別。</param>
      <param name="inheritedObjectType">可繼承規則的子物件型別。</param>
      <param name="auditFlags">稽核條件。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="identity" /> 參數的值不能轉型成 <see cref="T:System.Security.Principal.SecurityIdentifier" />，或是 <paramref name="type" /> 參數包含無效的值。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="accessMask" /> 參數的值為 0，或 <paramref name="inheritanceFlags" /> 或 <paramref name="propagationFlags" /> 參數包含無法辨認的旗標值。</exception>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAuditRule.InheritedObjectType">
      <summary>取得可繼承 <see cref="System.Security.AccessControl.ObjectAuditRule" /> 物件的子物件型別。</summary>
      <returns>可繼承 <see cref="System.Security.AccessControl.ObjectAuditRule" /> 物件的子物件型別。</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAuditRule.ObjectFlags">
      <summary>
        <see cref="System.Security.AccessControl.ObjectAuditRule" /> 物件的 <see cref="P:System.Security.AccessControl.ObjectAuditRule.ObjectType" /> 和 <see cref="P:System.Security.AccessControl.ObjectAuditRule.InheritedObjectType" /> 屬性包含有效值。</summary>
      <returns>
        <see cref="F:System.Security.AccessControl.ObjectAceFlags.ObjectAceTypePresent" /> 會指定 <see cref="P:System.Security.AccessControl.ObjectAuditRule.ObjectType" /> 屬性包含有效值。<see cref="F:System.Security.AccessControl.ObjectAceFlags.InheritedObjectAceTypePresent" /> 會指定 <see cref="P:System.Security.AccessControl.ObjectAuditRule.InheritedObjectType" /> 屬性包含有效值。這些值可與邏輯 OR 組合。</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAuditRule.ObjectType">
      <summary>取得要套用 <see cref="System.Security.AccessControl.ObjectAuditRule" /> 的物件型別。</summary>
      <returns>要套用 <see cref="System.Security.AccessControl.ObjectAuditRule" /> 的物件型別。</returns>
    </member>
    <member name="T:System.Security.AccessControl.ObjectSecurity">
      <summary>提供無需直接管理存取控制清單 (ACL)，即可控制對物件之存取的功能。這個類別是 <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> 和 <see cref="T:System.Security.AccessControl.DirectoryObjectSecurity" /> 類別的抽象基底類別。</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.#ctor">
      <summary>初始化 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.#ctor(System.Boolean,System.Boolean)">
      <summary>初始化 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 類別的新執行個體。</summary>
      <param name="isContainer">如果新的 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件是容器 (Container) 物件，則為 true。</param>
      <param name="isDS">如果新的 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件是目錄物件，則為 true。</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.#ctor(System.Security.AccessControl.CommonSecurityDescriptor)">
      <summary>初始化 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 類別的新執行個體。</summary>
      <param name="securityDescriptor">新 <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> 執行個體的 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />。</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AccessRightType">
      <summary>Gets the <see cref="T:System.Type" /> of the securable object associated with this <see cref="T:System.Security.AccessControl.ObjectSecurity" /> object.</summary>
      <returns>與這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件相關聯的安全物件型別。</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.AccessRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>使用指定的值，初始化 <see cref="T:System.Security.AccessControl.AccessRule" /> 類別的新執行個體。</summary>
      <returns>這個方法建立的 <see cref="T:System.Security.AccessControl.AccessRule" /> 物件。</returns>
      <param name="identityReference">要套用存取規則的識別 (Identity)。它必須是可以轉型為 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 的物件。</param>
      <param name="accessMask">這個規則的存取遮罩。存取遮罩為 32 位元的匿名位元集合，其意義由個別的整合子定義。</param>
      <param name="isInherited">如果這個規則是從父容器繼承，則為 true。</param>
      <param name="inheritanceFlags">指定存取規則的繼承 (Inheritance) 屬性。</param>
      <param name="propagationFlags">指定是否要自動傳用繼承的存取規則。如果 <paramref name="inheritanceFlags" /> 設為 <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />，將略過傳用旗標。</param>
      <param name="type">指定有效的存取控制型別。</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AccessRulesModified">
      <summary>取得或設定布林值，指定是否已修改與這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件相關聯的存取規則。</summary>
      <returns>如果已修改與這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件相關聯的存取規則，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AccessRuleType">
      <summary>Gets the <see cref="T:System.Type" /> of the object associated with the access rules of this <see cref="T:System.Security.AccessControl.ObjectSecurity" /> object.<see cref="T:System.Type" /> 物件必須是可轉型為 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 物件的物件。</summary>
      <returns>與這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件之存取規則相關聯的物件型別。</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AreAccessRulesCanonical">
      <summary>取得布林值，指定與這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件相關聯的存取規則是否為標準順序。</summary>
      <returns>如果存取規則為標準順序，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AreAccessRulesProtected">
      <summary>取得布林值，指定與這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件相關聯的 Discretionary 存取控制清單 (DACL) 是否受保護。</summary>
      <returns>如果 DACL 受保護，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AreAuditRulesCanonical">
      <summary>取得布林值，指定與這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件相關聯的稽核規則是否為標準順序。</summary>
      <returns>如果稽核規則為標準順序，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AreAuditRulesProtected">
      <summary>取得布林值，指定與這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件相關聯的系統存取控制清單 (SACL) 是否受保護。</summary>
      <returns>如果 SACL 受保護，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.AuditRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>使用指定的值，初始化 <see cref="T:System.Security.AccessControl.AuditRule" /> 類別的新執行個體。</summary>
      <returns>這個方法建立的 <see cref="T:System.Security.AccessControl.AuditRule" /> 物件。</returns>
      <param name="identityReference">要套用稽核規則的識別。它必須是可以轉型為 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 的物件。</param>
      <param name="accessMask">這個規則的存取遮罩。存取遮罩為 32 位元的匿名位元集合，其意義由個別的整合子定義。</param>
      <param name="isInherited">如果這個規則是從父容器繼承，則為 true。</param>
      <param name="inheritanceFlags">指定稽核規則的繼承屬性。</param>
      <param name="propagationFlags">指定是否要自動傳用繼承的稽核規則。如果 <paramref name="inheritanceFlags" /> 設為 <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />，將略過傳用旗標。</param>
      <param name="flags">指定稽核規則的條件。</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AuditRulesModified">
      <summary>取得或設定布林值，指定是否已修改與這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件相關聯的稽核規則。</summary>
      <returns>如果已修改與這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件相關聯的稽核規則，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AuditRuleType">
      <summary>Gets the <see cref="T:System.Type" /> object associated with the audit rules of this <see cref="T:System.Security.AccessControl.ObjectSecurity" /> object.<see cref="T:System.Type" /> 物件必須是可轉型為 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 物件的物件。</summary>
      <returns>與這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件之稽核規則相關聯的物件型別。</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.GetGroup(System.Type)">
      <summary>取得與指定之擁有人相關聯的主要群組。</summary>
      <returns>與指定之擁有人相關聯的主要群組。</returns>
      <param name="targetType">要取得其主要群組的擁有人。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.GetOwner(System.Type)">
      <summary>取得與指定之主要群組相關聯的擁有人。</summary>
      <returns>與指定之群組相關聯的擁有人。</returns>
      <param name="targetType">要取得其擁有人的主要群組。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.GetSecurityDescriptorBinaryForm">
      <summary>傳回位元組值陣列，表示這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件的安全性描述元 (Security Descriptor) 資訊。</summary>
      <returns>位元組值陣列，表示這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件的安全性描述元。如果這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件中沒有安全性資訊，則這個方法會傳回 null。</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.GetSecurityDescriptorSddlForm(System.Security.AccessControl.AccessControlSections)">
      <summary>傳回與這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件相關聯之安全性描述元中指定區段的安全性描述元定義語言 (SDDL) 表示。</summary>
      <returns>與這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件相關聯之安全性描述元中指定區段的 SDDL 表示。</returns>
      <param name="includeSections">指定要取得安全性描述元的哪個區段 (存取規則、稽核規則、主要群組或擁有人)。</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.GroupModified">
      <summary>取得或設定布林值 (Boolean)，指定是否已修改與安全物件相關聯的群組。 </summary>
      <returns>如果已修改與安全物件相關聯的群組，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.IsContainer">
      <summary>取得布林值，指定這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件是否為容器物件。</summary>
      <returns>如果 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 是容器物件，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.IsDS">
      <summary>取得布林值，指定這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件是否為目錄物件。</summary>
      <returns>如果 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件是目錄物件，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.IsSddlConversionSupported">
      <summary>傳回布林值，指定與這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件相關聯的安全性描述元是否可轉換為安全性描述元定義語言 (SDDL) 格式。</summary>
      <returns>如果與這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件相關聯的安全性描述元可轉換為安全性描述元定義語言 (SDDL) 格式，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ModifyAccess(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AccessRule,System.Boolean@)">
      <summary>將指定的修改套用至與這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件相關聯的 Discretionary 存取控制清單 (DACL)。</summary>
      <returns>如果 DACL 修改成功，則為 true，否則為 false。</returns>
      <param name="modification">要套用至 DACL 的修改。</param>
      <param name="rule">要修改的存取規則。</param>
      <param name="modified">如果 DACL 修改成功，則為 true，否則為 false。</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ModifyAccessRule(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AccessRule,System.Boolean@)">
      <summary>將指定的修改套用至與這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件相關聯的 Discretionary 存取控制清單 (DACL)。</summary>
      <returns>如果 DACL 修改成功，則為 true，否則為 false。</returns>
      <param name="modification">要套用至 DACL 的修改。</param>
      <param name="rule">要修改的存取規則。</param>
      <param name="modified">如果 DACL 修改成功，則為 true，否則為 false。</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ModifyAudit(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AuditRule,System.Boolean@)">
      <summary>將指定的修改套用至與這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件相關聯的系統存取控制清單 (SACL)。</summary>
      <returns>如果 SACL 修改成功，則為 true，否則為 false。</returns>
      <param name="modification">要套用至 SACL 的修改。</param>
      <param name="rule">要修改的稽核規則。</param>
      <param name="modified">如果 SACL 修改成功，則為 true，否則為 false。</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ModifyAuditRule(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AuditRule,System.Boolean@)">
      <summary>將指定的修改套用至與這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件相關聯的系統存取控制清單 (SACL)。</summary>
      <returns>如果 SACL 修改成功，則為 true，否則為 false。</returns>
      <param name="modification">要套用至 SACL 的修改。</param>
      <param name="rule">要修改的稽核規則。</param>
      <param name="modified">如果 SACL 修改成功，則為 true，否則為 false。</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.OwnerModified">
      <summary>取得或設定布林值，指定是否已修改安全物件的擁有人。</summary>
      <returns>如果已修改安全物件的擁有人，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.Persist(System.Boolean,System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>將與此 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件相關聯之安全性描述元的指定區段，儲存至永久儲存區。我們建議您最好將 <paramref name="includeSections" /> 參數的值傳遞給建構函式，並將方法保存為相同的方法。如需詳細資訊，請參閱＜備註＞。</summary>
      <param name="enableOwnershipPrivilege">true 表示啟用允許呼叫端取得物件擁有權的權限。</param>
      <param name="name">用於擷取保存之資訊的名稱。</param>
      <param name="includeSections">其中一個 <see cref="T:System.Security.AccessControl.AccessControlSections" /> 列舉值，指定要儲存之安全物件安全性描述元 (Security Descriptor) (存取規則、稽核規則、擁有人、主要群組) 的區段。</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.Persist(System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections)">
      <summary>將與此 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件相關聯之安全性描述元的指定區段，儲存至永久儲存區。我們建議您最好將 <paramref name="includeSections" /> 參數的值傳遞給建構函式，並將方法保存為相同的方法。如需詳細資訊，請參閱＜備註＞。</summary>
      <param name="handle">用於擷取保存之資訊的控制代碼。</param>
      <param name="includeSections">其中一個 <see cref="T:System.Security.AccessControl.AccessControlSections" /> 列舉值，指定要儲存之安全物件安全性描述元 (Security Descriptor) (存取規則、稽核規則、擁有人、主要群組) 的區段。</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.Persist(System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>將與此 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件相關聯之安全性描述元的指定區段，儲存至永久儲存區。我們建議您最好將 <paramref name="includeSections" /> 參數的值傳遞給建構函式，並將方法保存為相同的方法。如需詳細資訊，請參閱＜備註＞。</summary>
      <param name="name">用於擷取保存之資訊的名稱。</param>
      <param name="includeSections">其中一個 <see cref="T:System.Security.AccessControl.AccessControlSections" /> 列舉值，指定要儲存之安全物件安全性描述元 (Security Descriptor) (存取規則、稽核規則、擁有人、主要群組) 的區段。</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.PurgeAccessRules(System.Security.Principal.IdentityReference)">
      <summary>移除與指定之 <see cref="T:System.Security.Principal.IdentityReference" /> 相關聯的所有存取規則。</summary>
      <param name="identity">要移除所有存取規則的 <see cref="T:System.Security.Principal.IdentityReference" />。</param>
      <exception cref="T:System.InvalidOperationException">並非所有存取規則都為標準順序。</exception>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.PurgeAuditRules(System.Security.Principal.IdentityReference)">
      <summary>移除與指定之 <see cref="T:System.Security.Principal.IdentityReference" /> 相關聯的所有稽核規則。</summary>
      <param name="identity">要移除所有稽核規則的 <see cref="T:System.Security.Principal.IdentityReference" />。</param>
      <exception cref="T:System.InvalidOperationException">並非所有稽核規則都為標準順序。</exception>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ReadLock">
      <summary>鎖定這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件以進行讀取存取。</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ReadUnlock">
      <summary>解除鎖定這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件以進行讀取存取。</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetAccessRuleProtection(System.Boolean,System.Boolean)">
      <summary>設定或移除與這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件相關聯的存取規則保護。受保護的存取規則無法由父物件透過繼承進行修改。</summary>
      <param name="isProtected">true 表示保護與這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件相關聯的存取規則，以避免繼承，而 false 則表示允許繼承。</param>
      <param name="preserveInheritance">true 表示保留繼承的存取規則，而 false 則表示移除繼承的存取規則。如果 <paramref name="isProtected" /> 為 false，則忽略這個參數。</param>
      <exception cref="T:System.InvalidOperationException">這個方法嘗試移除非標準 Discretionary 存取控制清單 (DACL) 的繼承規則。</exception>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetAuditRuleProtection(System.Boolean,System.Boolean)">
      <summary>設定或移除與這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件相關聯的稽核規則保護。受保護的稽核規則無法由父物件透過繼承進行修改。</summary>
      <param name="isProtected">true 表示保護與這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件相關聯的稽核規則，以避免繼承，而 false 則表示允許繼承。</param>
      <param name="preserveInheritance">true 表示保留繼承的稽核規則，而 false 則表示移除繼承的稽核規則。如果 <paramref name="isProtected" /> 為 false，則忽略這個參數。</param>
      <exception cref="T:System.InvalidOperationException">這個方法嘗試移除非標準系統存取控制清單 (SACL) 的繼承規則。</exception>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetGroup(System.Security.Principal.IdentityReference)">
      <summary>設定與這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件相關聯之安全性描述元的主要群組。</summary>
      <param name="identity">要設定的主要群組。</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetOwner(System.Security.Principal.IdentityReference)">
      <summary>設定與這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件相關聯之安全性描述元的擁有人。</summary>
      <param name="identity">要設定的擁有人。</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetSecurityDescriptorBinaryForm(System.Byte[])">
      <summary>在指定的位元組值陣列中，設定這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件的安全性描述元。</summary>
      <param name="binaryForm">要從中設定安全性描述元的位元組陣列。</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetSecurityDescriptorBinaryForm(System.Byte[],System.Security.AccessControl.AccessControlSections)">
      <summary>在指定的位元組值陣列中，設定這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件之安全性描述元的指定區段。</summary>
      <param name="binaryForm">要從中設定安全性描述元的位元組陣列。</param>
      <param name="includeSections">要設定的安全性描述元區段 (存取規則、稽核規則、擁有人或主要群組)。</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetSecurityDescriptorSddlForm(System.String)">
      <summary>在指定的安全性描述元定義語言 (SDDL) 字串中，設定這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件的安全性描述元。</summary>
      <param name="sddlForm">要從中設定安全性描述元的 SDDL 字串。</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetSecurityDescriptorSddlForm(System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>在指定的安全性描述元定義語言 (SDDL) 字串中，設定這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件之安全性描述元的指定區段。</summary>
      <param name="sddlForm">要從中設定安全性描述元的 SDDL 字串。</param>
      <param name="includeSections">要設定的安全性描述元區段 (存取規則、稽核規則、擁有人或主要群組)。</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.WriteLock">
      <summary>鎖定這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件以進行寫入存取。</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.WriteUnlock">
      <summary>解除鎖定這個 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 物件以進行寫入存取。</summary>
    </member>
    <member name="T:System.Security.AccessControl.ObjectSecurity`1">
      <summary>提供不直接操作存取控制清單 (ACL) 即可控制對物件之存取的功能，同時也授與對存取權限進行類型轉換的能力。</summary>
      <typeparam name="T">物件的存取權限。</typeparam>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType)">
      <summary>初始化 ObjectSecurity`1 類別的新執行個體。</summary>
      <param name="isContainer">如果新的 <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> 物件是容器 (Container) 物件，則為 true。</param>
      <param name="resourceType">資源的型別。</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections)">
      <summary>初始化 ObjectSecurity`1 類別的新執行個體。</summary>
      <param name="isContainer">如果新的 <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> 物件是容器 (Container) 物件，則為 true。</param>
      <param name="resourceType">資源的型別。</param>
      <param name="safeHandle">控制代碼。</param>
      <param name="includeSections">要包含的區段。</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>初始化 ObjectSecurity`1 類別的新執行個體。</summary>
      <param name="isContainer">如果新的 <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> 物件是容器 (Container) 物件，則為 true。</param>
      <param name="resourceType">資源的型別。</param>
      <param name="safeHandle">控制代碼。</param>
      <param name="includeSections">要包含的區段。</param>
      <param name="exceptionFromErrorCode">委派由提供自訂例外狀況的整合子實作。</param>
      <param name="exceptionContext">物件，包含關於例外狀況之來源或目的端的內容資訊。</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>初始化 ObjectSecurity`1 類別的新執行個體。</summary>
      <param name="isContainer">如果新的 <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> 物件是容器 (Container) 物件，則為 true。</param>
      <param name="resourceType">資源的型別。</param>
      <param name="name">與新 <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> 物件相關聯之安全物件的名稱。</param>
      <param name="includeSections">要包含的區段。</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.String,System.Security.AccessControl.AccessControlSections,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>初始化 ObjectSecurity`1 類別的新執行個體。</summary>
      <param name="isContainer">如果新的 <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> 物件是容器 (Container) 物件，則為 true。</param>
      <param name="resourceType">資源的型別。</param>
      <param name="name">與新 <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> 物件相關聯之安全物件的名稱。</param>
      <param name="includeSections">要包含的區段。</param>
      <param name="exceptionFromErrorCode">委派由提供自訂例外狀況的整合子實作。</param>
      <param name="exceptionContext">物件，包含關於例外狀況之來源或目的端的內容資訊。</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity`1.AccessRightType">
      <summary>取得與這個 ObjectSecurity`1 物件相關聯之安全物件的類型。</summary>
      <returns>與目前執行個體相關聯的安全物件類型。</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.AccessRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>初始化 ObjectAccessRule 類別的新執行個體，這個執行個體表示相關聯安全物件的新存取控制規則。</summary>
      <returns>表示指定之使用者的新存取控制規則，具有指定的存取權限、存取控制和旗標。</returns>
      <param name="identityReference">代表使用者帳戶。</param>
      <param name="accessMask">存取類型。</param>
      <param name="isInherited">如果繼承存取規則，則為 true，否則為 false。</param>
      <param name="inheritanceFlags">指定如何將存取遮罩散佈到子物件。</param>
      <param name="propagationFlags">指定如何將存取控制項目 (ACE) 傳用至子物件。</param>
      <param name="type">指定允許存取還是拒絕存取。</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity`1.AccessRuleType">
      <summary>取得與這個 ObjectSecurity`1 物件之存取規則相關聯的物件類型。</summary>
      <returns>與目前執行個體之存取規則相關聯的物件類型。</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.AddAccessRule(System.Security.AccessControl.AccessRule{`0})">
      <summary>將指定的存取規則加入與這個 ObjectSecurity`1 物件相關聯的判別存取控制清單 (DACL)。</summary>
      <param name="rule">要加入的規則。</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.AddAuditRule(System.Security.AccessControl.AuditRule{`0})">
      <summary>將指定的稽核規則加入與這個 ObjectSecurity`1 物件相關聯的系統存取控制清單 (SACL)。</summary>
      <param name="rule">要加入的稽核規則。</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.AuditRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>初始化 <see cref="T:System.Security.AccessControl.AuditRule" /> 類別 (表示指定之使用者的指定稽核規則) 的新執行個體。</summary>
      <returns>使用指定之使用者的指定稽核規則。</returns>
      <param name="identityReference">代表使用者帳戶。</param>
      <param name="accessMask">整數，指定存取類型。</param>
      <param name="isInherited">如果繼承存取規則，則為 true，否則為 false。</param>
      <param name="inheritanceFlags">指定如何將存取遮罩散佈到子物件。</param>
      <param name="propagationFlags">指定如何將存取控制項目 (ACE) 傳用至子物件。</param>
      <param name="flags">描述要執行的稽核類型。</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity`1.AuditRuleType">
      <summary>取得與這個 ObjectSecurity`1 物件之稽核規則相關聯的 Type 物件。</summary>
      <returns>與目前執行個體之稽核規則相關聯的 Type 物件。</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.Persist(System.Runtime.InteropServices.SafeHandle)">
      <summary>使用指定的控制代碼，將與此 ObjectSecurity`1 物件相關聯之安全性描述元儲存至永久儲存區。</summary>
      <param name="handle">與這個 ObjectSecurity`1 物件相關聯之安全物件的控制代碼。</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.Persist(System.String)">
      <summary>使用指定的名稱，將與此 ObjectSecurity`1 物件相關聯之安全性描述元儲存至永久儲存區。</summary>
      <param name="name">與這個 ObjectSecurity`1 物件相關聯之安全物件的名稱。</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAccessRule(System.Security.AccessControl.AccessRule{`0})">
      <summary>從與這個 ObjectSecurity`1 物件相關聯的判別存取控制清單 (DACL)，移除與指定的存取規則包含相同安全識別項和存取遮罩的存取規則。</summary>
      <returns>如果成功移除存取規則，則傳回 true，否則傳回 false。</returns>
      <param name="rule">要移除的規則。</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAccessRuleAll(System.Security.AccessControl.AccessRule{`0})">
      <summary>從與這個 ObjectSecurity`1 物件相關聯的判別存取控制清單 (DACL)，移除與指定的存取規則具有相同安全識別項的所有存取規則。</summary>
      <param name="rule">要移除的存取規則。</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAccessRuleSpecific(System.Security.AccessControl.AccessRule{`0})">
      <summary>從與這個 ObjectSecurity`1 物件相關聯的判別存取控制清單 (DACL)，移除與指定的存取規則完全相符的所有存取規則。</summary>
      <param name="rule">要移除的存取規則。</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAuditRule(System.Security.AccessControl.AuditRule{`0})">
      <summary>從與這個 ObjectSecurity`1 物件相關聯的系統存取控制清單 (SACL)，移除與指定的稽核規則包含相同安全識別項和存取遮罩的稽核規則。</summary>
      <returns>如果已移除指定的物件則傳回 true，否則傳回 false。</returns>
      <param name="rule">要移除的稽核規則</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAuditRuleAll(System.Security.AccessControl.AuditRule{`0})">
      <summary>從與這個 ObjectSecurity`1 物件相關聯的系統存取控制清單 (SACL)，移除與指定的稽核規則包含相同安全識別項的所有稽核規則。</summary>
      <param name="rule">要移除的稽核規則。</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAuditRuleSpecific(System.Security.AccessControl.AuditRule{`0})">
      <summary>從與這個 ObjectSecurity`1 物件相關聯的系統存取控制清單 (SACL)，移除與指定稽核規則完全相符的所有稽核規則。</summary>
      <param name="rule">要移除的稽核規則。</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.ResetAccessRule(System.Security.AccessControl.AccessRule{`0})">
      <summary>移除與這個 ObjectSecurity`1 物件相關聯之判別存取控制清單 (DACL) 中的所有存取規則，然後加入指定的存取規則。</summary>
      <param name="rule">要重設的存取規則。</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.SetAccessRule(System.Security.AccessControl.AccessRule{`0})">
      <summary>從與這個 ObjectSecurity`1 物件相關聯之判別存取控制清單 (DACL)，移除與指定的存取規則包含相同安全識別項和限定詞的所有存取規則，然後加入指定的存取規則。</summary>
      <param name="rule">要設定的存取規則。</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.SetAuditRule(System.Security.AccessControl.AuditRule{`0})">
      <summary>從與這個 ObjectSecurity`1 物件相關聯之系統存取控制清單 (SACL)，移除與指定的稽核規則包含相同安全識別項和限定詞的所有稽核規則，然後加入指定的稽核規則。</summary>
      <param name="rule">要設定的稽核規則。</param>
    </member>
    <member name="T:System.Security.AccessControl.PrivilegeNotHeldException">
      <summary>當 <see cref="N:System.Security.AccessControl" /> 命名空間中的方法嘗試啟用本身沒有的權限時，所擲回的例外狀況。</summary>
    </member>
    <member name="M:System.Security.AccessControl.PrivilegeNotHeldException.#ctor">
      <summary>初始化 <see cref="T:System.Security.AccessControl.PrivilegeNotHeldException" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Security.AccessControl.PrivilegeNotHeldException.#ctor(System.String)">
      <summary>使用指定的權限，初始化 <see cref="T:System.Security.AccessControl.PrivilegeNotHeldException" /> 類別的新執行個體。</summary>
      <param name="privilege">未啟用的權限。</param>
    </member>
    <member name="M:System.Security.AccessControl.PrivilegeNotHeldException.#ctor(System.String,System.Exception)">
      <summary>使用指定的例外狀況，初始化 <see cref="T:System.Security.AccessControl.PrivilegeNotHeldException" /> 類別的新執行個體。</summary>
      <param name="privilege">未啟用的權限。</param>
      <param name="inner">導致目前例外狀況的例外。如果 <paramref name="innerException" /> 參數不是 Null 參考 (在 Visual Basic 中為 Nothing)，則會在處理內部例外的 catch 區塊中引發目前的例外狀況。</param>
    </member>
    <member name="P:System.Security.AccessControl.PrivilegeNotHeldException.PrivilegeName">
      <summary>取得未啟用之權限的名稱。</summary>
      <returns>方法無法啟用之權限的名稱。</returns>
    </member>
    <member name="T:System.Security.AccessControl.PropagationFlags">
      <summary>指定如何將存取控制項目 (ACE) 傳用至子物件。只有存在繼承 (Inheritance) 旗標時，這些旗標才有意義。</summary>
    </member>
    <member name="F:System.Security.AccessControl.PropagationFlags.InheritOnly">
      <summary>指定 ACE 只傳用至子物件。這同時包含容器和分葉子物件。</summary>
    </member>
    <member name="F:System.Security.AccessControl.PropagationFlags.None">
      <summary>指定未設定繼承旗標。</summary>
    </member>
    <member name="F:System.Security.AccessControl.PropagationFlags.NoPropagateInherit">
      <summary>指定 ACE 不傳用至子物件。</summary>
    </member>
    <member name="T:System.Security.AccessControl.QualifiedAce">
      <summary>表示包含限定詞 (Qualifier) 的存取控制項目 (ACE)。由 <see cref="T:System.Security.AccessControl.AceQualifier" /> 物件表示的限定詞，指定 ACE 是允許存取、拒絕存取、引起系統稽核還是引起系統警示。<see cref="T:System.Security.AccessControl.QualifiedAce" /> 類別是 <see cref="T:System.Security.AccessControl.CommonAce" /> 和 <see cref="T:System.Security.AccessControl.ObjectAce" /> 類別的抽象基底類別。</summary>
    </member>
    <member name="P:System.Security.AccessControl.QualifiedAce.AceQualifier">
      <summary>取得值，指定 ACE 是允許存取、拒絕存取、引起系統稽核還是引起系統警示。</summary>
      <returns>值，指定 ACE 是允許存取、拒絕存取、引起系統稽核還是引起系統警示。</returns>
    </member>
    <member name="M:System.Security.AccessControl.QualifiedAce.GetOpaque">
      <summary>傳回與這個 <see cref="T:System.Security.AccessControl.QualifiedAce" /> 物件相關聯的不透明回呼資料。</summary>
      <returns>位元組值陣列，表示與這個 <see cref="T:System.Security.AccessControl.QualifiedAce" /> 物件相關聯的不透明回呼資料。</returns>
    </member>
    <member name="P:System.Security.AccessControl.QualifiedAce.IsCallback">
      <summary>指定這個 <see cref="T:System.Security.AccessControl.QualifiedAce" /> 物件是否包含回呼 (Callback) 資料。</summary>
      <returns>如果這個 <see cref="T:System.Security.AccessControl.QualifiedAce" /> 物件包含回呼資料則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Security.AccessControl.QualifiedAce.OpaqueLength">
      <summary>取得與這個 <see cref="T:System.Security.AccessControl.QualifiedAce" /> 物件相關聯的不透明回呼 (Callback) 資料長度。這個屬性只對回呼存取控制項目 (ACE) 有效。</summary>
      <returns>不透明回呼 (Callback) 資料的長度。</returns>
    </member>
    <member name="M:System.Security.AccessControl.QualifiedAce.SetOpaque(System.Byte[])">
      <summary>設定與這個 <see cref="T:System.Security.AccessControl.QualifiedAce" /> 物件相關聯的不透明回呼資料。</summary>
      <param name="opaque">位元組值陣列，表示這個 <see cref="T:System.Security.AccessControl.QualifiedAce" /> 物件的不透明回呼資料。</param>
    </member>
    <member name="T:System.Security.AccessControl.RawAcl">
      <summary>表示存取控制清單 (ACL)。</summary>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.#ctor(System.Byte,System.Int32)">
      <summary>使用指定的修訂層級，初始化 <see cref="T:System.Security.AccessControl.RawAcl" /> 類別的新執行個體。</summary>
      <param name="revision">新存取控制清單 (ACL) 的修訂層級。</param>
      <param name="capacity">這個 <see cref="T:System.Security.AccessControl.RawAcl" /> 物件可以包含的存取控制項目 (ACE) 數。這個數字只做為提示之用。</param>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.#ctor(System.Byte[],System.Int32)">
      <summary>從指定的二進位格式，初始化 <see cref="T:System.Security.AccessControl.RawAcl" /> 類別的新執行個體。</summary>
      <param name="binaryForm">表示存取控制清單 (ACL) 的位元組值陣列。</param>
      <param name="offset">
        <paramref name="binaryForm" /> 參數中開始解封送處理 (Unmarshaling) 資料的位移。</param>
    </member>
    <member name="P:System.Security.AccessControl.RawAcl.BinaryLength">
      <summary>取得目前 <see cref="T:System.Security.AccessControl.RawAcl" /> 物件的二進位表示長度 (以位元組為單位)。使用 <see cref="M:System.Security.AccessControl.RawAcl.GetBinaryForm" /> 方法，將 ACL 封送處理 (Marshaling) 至二進位陣列之前，應使用這個長度。</summary>
      <returns>目前 <see cref="T:System.Security.AccessControl.RawAcl" /> 物件的二進位表示長度 (以位元組為單位)。</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawAcl.Count">
      <summary>取得目前 <see cref="T:System.Security.AccessControl.RawAcl" /> 物件中的存取控制項目 (ACE) 數目。</summary>
      <returns>目前 <see cref="T:System.Security.AccessControl.RawAcl" /> 物件中的 ACE 數目。</returns>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>將 <see cref="T:System.Security.AccessControl.RawAcl" /> 物件的內容封送處理到開始於指定位移的指定位元組陣列。</summary>
      <param name="binaryForm">位元組陣列，<see cref="T:System.Security.AccessControl.RawAcl" /> 的內容會封送處理至此陣列。</param>
      <param name="offset">要開始封送處理的位移。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 為負值，或是過大而無法將整個 <see cref="T:System.Security.AccessControl.RawAcl" /> 複製到 <paramref name="array" />。</exception>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.InsertAce(System.Int32,System.Security.AccessControl.GenericAce)">
      <summary>在指定之索引處插入指定的存取控制項目 (ACE)。</summary>
      <param name="index">要加入新 ACE 的位置。您可以指定 <see cref="P:System.Security.AccessControl.RawAcl.Count" /> 屬性的值，以在 <see cref="T:System.Security.AccessControl.RawAcl" /> 物件的結尾插入 ACE。</param>
      <param name="ace">要插入的 ACE。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 為負值，或是過大而無法將整個 <see cref="T:System.Security.AccessControl.GenericAcl" /> 複製到 <paramref name="array" />。</exception>
    </member>
    <member name="P:System.Security.AccessControl.RawAcl.Item(System.Int32)">
      <summary>取得或設定指定之索引處的存取控制項目 (ACE)。</summary>
      <returns>在指定索引處的 ACE。</returns>
      <param name="index">要取得或設定 ACE 之以零起始的索引。</param>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.RemoveAce(System.Int32)">
      <summary>移除指定之位置的存取控制項目 (ACE)。</summary>
      <param name="index">要移除 ACE 之以零起始的索引。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 參數的值大於 <see cref="P:System.Security.AccessControl.RawAcl.Count" /> 屬性的值減去一或是負值。</exception>
    </member>
    <member name="P:System.Security.AccessControl.RawAcl.Revision">
      <summary>取得 <see cref="T:System.Security.AccessControl.RawAcl" /> 的修訂層級。</summary>
      <returns>位元組值，指定 <see cref="T:System.Security.AccessControl.RawAcl" /> 的修訂層級。</returns>
    </member>
    <member name="T:System.Security.AccessControl.RawSecurityDescriptor">
      <summary>表示安全性描述元 (Security Descriptor)。安全性描述元包括擁有人、主要群組、Discretionary 存取控制清單 (DACL) 和系統存取控制清單 (SACL)。</summary>
    </member>
    <member name="M:System.Security.AccessControl.RawSecurityDescriptor.#ctor(System.Byte[],System.Int32)">
      <summary>從指定的位元組值陣列，初始化 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 類別的新執行個體。</summary>
      <param name="binaryForm">位元組值陣列，從其中建立新的 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 物件。</param>
      <param name="offset">
        <paramref name="binaryForm" /> 陣列中開始複製的位移。</param>
    </member>
    <member name="M:System.Security.AccessControl.RawSecurityDescriptor.#ctor(System.Security.AccessControl.ControlFlags,System.Security.Principal.SecurityIdentifier,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.RawAcl,System.Security.AccessControl.RawAcl)">
      <summary>使用指定的值，初始化 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 類別的新執行個體。</summary>
      <param name="flags">旗標，指定新 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 物件的行為。</param>
      <param name="owner">新 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 物件擁有人。</param>
      <param name="group">新 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 物件的主要群組。</param>
      <param name="systemAcl">新 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 物件的系統存取控制清單 (SACL)。</param>
      <param name="discretionaryAcl">新 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 物件的 Discretionary 存取控制清單 (DACL)。</param>
    </member>
    <member name="M:System.Security.AccessControl.RawSecurityDescriptor.#ctor(System.String)">
      <summary>從指定的安全性描述元定義語言 (SDDL) 字串，初始化 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 類別的新執行個體。</summary>
      <param name="sddlForm">SDDL 字串，從其中建立新的 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 物件。</param>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.ControlFlags">
      <summary>取得指定 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 物件之行為的值。</summary>
      <returns>與邏輯 OR 運算結合的一或多個 <see cref="T:System.Security.AccessControl.ControlFlags" /> 列舉型別的值。</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.DiscretionaryAcl">
      <summary>取得或設定這個 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 物件的 Discretionary 存取控制清單 (DACL)。DACL 包含存取規則。</summary>
      <returns>這個 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 物件的 DACL。</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.Group">
      <summary>取得或設定這個 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 物件的主要群組。</summary>
      <returns>這個 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 物件的主要群組。</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.Owner">
      <summary>取得或設定與這個 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 物件相關聯的物件擁有人。</summary>
      <returns>與這個 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 物件相關聯的物件擁有人。</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.ResourceManagerControl">
      <summary>取得或設定位元組值，表示與這個 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 物件相關聯的資源管理員控制位元。</summary>
      <returns>位元組值，表示與這個 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 物件相關聯的資源管理員控制位元。</returns>
    </member>
    <member name="M:System.Security.AccessControl.RawSecurityDescriptor.SetFlags(System.Security.AccessControl.ControlFlags)">
      <summary>將這個 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 物件的 <see cref="P:System.Security.AccessControl.RawSecurityDescriptor.ControlFlags" /> 屬性設為指定值。</summary>
      <param name="flags">與邏輯 OR 運算結合的一或多個 <see cref="T:System.Security.AccessControl.ControlFlags" /> 列舉型別的值。</param>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.SystemAcl">
      <summary>取得或設定這個 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 物件的系統存取控制清單 (SACL)。SACL 包含稽核規則。</summary>
      <returns>這個 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 物件的 SACL。</returns>
    </member>
    <member name="T:System.Security.AccessControl.ResourceType">
      <summary>指定定義的原生 (Native) 物件型別。</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.DSObject">
      <summary>目錄服務 (DS) 物件或目錄服務物件的屬性集 (Property Set) 或屬性。</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.DSObjectAll">
      <summary>目錄服務物件及其所有屬性集和屬性。</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.FileObject">
      <summary>檔案或目錄。</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.KernelObject">
      <summary>本機核心物件。</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.LMShare">
      <summary>網路共用。</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.Printer">
      <summary>印表機。</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.ProviderDefined">
      <summary>提供者定義的物件。</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.RegistryKey">
      <summary>登錄機碼 (Registry Key)。</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.RegistryWow6432Key">
      <summary>WOW64 下的登錄項目物件。</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.Service">
      <summary>Windows 服務。</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.Unknown">
      <summary>未知的物件型別。</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.WindowObject">
      <summary>本機電腦上的視窗工作站或桌面物件。</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.WmiGuidObject">
      <summary>Windows Management Instrumentation (WMI) 物件。</summary>
    </member>
    <member name="T:System.Security.AccessControl.SecurityInfos">
      <summary>指定要查詢或設定的安全性描述元 (Security Descriptor) 區段。</summary>
    </member>
    <member name="F:System.Security.AccessControl.SecurityInfos.DiscretionaryAcl">
      <summary>指定 Discretionary 存取控制清單 (DACL)。</summary>
    </member>
    <member name="F:System.Security.AccessControl.SecurityInfos.Group">
      <summary>指定主要群組識別項。</summary>
    </member>
    <member name="F:System.Security.AccessControl.SecurityInfos.Owner">
      <summary>指定擁有人識別項。</summary>
    </member>
    <member name="F:System.Security.AccessControl.SecurityInfos.SystemAcl">
      <summary>指定系統存取控制名單 (SACL)。</summary>
    </member>
    <member name="T:System.Security.AccessControl.SystemAcl">
      <summary>表示系統存取控制清單 (SACL)。</summary>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.#ctor(System.Boolean,System.Boolean,System.Byte,System.Int32)">
      <summary>使用指定的值，初始化 <see cref="T:System.Security.AccessControl.SystemAcl" /> 類別的新執行個體。</summary>
      <param name="isContainer">如果新的 <see cref="T:System.Security.AccessControl.SystemAcl" /> 物件是容器，則為 true。</param>
      <param name="isDS">如果新的 <see cref="T:System.Security.AccessControl.SystemAcl" /> 物件是目錄物件存取控制清單 (ACL)，則為 true。</param>
      <param name="revision">新 <see cref="T:System.Security.AccessControl.SystemAcl" /> 物件的修訂層級。</param>
      <param name="capacity">此 <see cref="T:System.Security.AccessControl.SystemAcl" /> 物件可以容納的存取控制項目 (ACE) 數。這個數字只做為提示之用。</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.#ctor(System.Boolean,System.Boolean,System.Int32)">
      <summary>使用指定的值，初始化 <see cref="T:System.Security.AccessControl.SystemAcl" /> 類別的新執行個體。</summary>
      <param name="isContainer">如果新的 <see cref="T:System.Security.AccessControl.SystemAcl" /> 物件是容器，則為 true。</param>
      <param name="isDS">如果新的 <see cref="T:System.Security.AccessControl.SystemAcl" /> 物件是目錄物件存取控制清單 (ACL)，則為 true。</param>
      <param name="capacity">此 <see cref="T:System.Security.AccessControl.SystemAcl" /> 物件可以容納的存取控制項目 (ACE) 數。這個數字只做為提示之用。</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.#ctor(System.Boolean,System.Boolean,System.Security.AccessControl.RawAcl)">
      <summary>使用指定的 <see cref="T:System.Security.AccessControl.RawAcl" /> 物件中的指定值，初始化 <see cref="T:System.Security.AccessControl.SystemAcl" /> 類別的新執行個體。</summary>
      <param name="isContainer">如果新的 <see cref="T:System.Security.AccessControl.SystemAcl" /> 物件是容器，則為 true。</param>
      <param name="isDS">如果新的 <see cref="T:System.Security.AccessControl.SystemAcl" /> 物件是目錄物件存取控制清單 (ACL)，則為 true。</param>
      <param name="rawAcl">新 <see cref="T:System.Security.AccessControl.SystemAcl" /> 物件的基礎 <see cref="T:System.Security.AccessControl.RawAcl" /> 物件。您可以指定 null 以建立空的 ACL。</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.AddAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>將稽核規則加入目前 <see cref="T:System.Security.AccessControl.SystemAcl" /> 物件。</summary>
      <param name="auditFlags">要加入的稽核規則型別。</param>
      <param name="sid">要加入稽核規則的 <see cref="T:System.Security.Principal.SecurityIdentifier" />。</param>
      <param name="accessMask">新稽核規則的存取遮罩。</param>
      <param name="inheritanceFlags">旗標，指定新稽核規則的繼承屬性。</param>
      <param name="propagationFlags">旗標，指定新稽核規則的繼承傳用屬性。</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.AddAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>將具有指定設定的稽核規則加入目前 <see cref="T:System.Security.AccessControl.SystemAcl" /> 物件。為新的稽核規則指定物件型別或繼承的物件型別時，您可以將這個方法用於目錄物件存取控制清單 (ACL)。</summary>
      <param name="auditFlags">要加入的稽核規則型別。</param>
      <param name="sid">要加入稽核規則的 <see cref="T:System.Security.Principal.SecurityIdentifier" />。</param>
      <param name="accessMask">新稽核規則的存取遮罩。</param>
      <param name="inheritanceFlags">旗標，指定新稽核規則的繼承屬性。</param>
      <param name="propagationFlags">旗標，指定新稽核規則的繼承傳用屬性。</param>
      <param name="objectFlags">旗標，指定 <paramref name="objectType" /> 和 <paramref name="inheritedObjectType" /> 參數是否包含非 null 值。</param>
      <param name="objectType">要套用新稽核規則之物件類別的識別。</param>
      <param name="inheritedObjectType">可繼承新稽核規則之子物件類別的識別。</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.AddAudit(System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAuditRule)">
      <summary>將稽核規則加入目前 <see cref="T:System.Security.AccessControl.SystemAcl" /> 物件。</summary>
      <param name="sid">要加入稽核規則的 <see cref="T:System.Security.Principal.SecurityIdentifier" />。</param>
      <param name="rule">
        <see cref="T:System.Security.AccessControl.ObjectAuditRule" />新稽核規則。</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>從目前的 <see cref="T:System.Security.AccessControl.SystemAcl" /> 物件移除指定的稽核規則。</summary>
      <returns>如果這個方法成功移除指定的稽核規則，則為 true，否則為 false。</returns>
      <param name="auditFlags">要移除的稽核規則型別。</param>
      <param name="sid">要移除稽核規則的 <see cref="T:System.Security.Principal.SecurityIdentifier" />。</param>
      <param name="accessMask">要移除之規則的存取遮罩。</param>
      <param name="inheritanceFlags">旗標，指定要移除之規則的繼承屬性。</param>
      <param name="propagationFlags">旗標，指定要移除之規則的繼承傳用屬性。</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>從目前的 <see cref="T:System.Security.AccessControl.SystemAcl" /> 物件移除指定的稽核規則。指定物件型別或繼承物件型別時，將這個方法用於目錄物件存取控制清單 (ACL)。</summary>
      <returns>如果這個方法成功移除指定的稽核規則，則為 true，否則為 false。</returns>
      <param name="auditFlags">要移除的稽核規則型別。</param>
      <param name="sid">要移除稽核規則的 <see cref="T:System.Security.Principal.SecurityIdentifier" />。</param>
      <param name="accessMask">要移除之規則的存取遮罩。</param>
      <param name="inheritanceFlags">旗標，指定要移除之規則的繼承屬性。</param>
      <param name="propagationFlags">旗標，指定要移除之規則的繼承傳用屬性。</param>
      <param name="objectFlags">旗標，指定 <paramref name="objectType" /> 和 <paramref name="inheritedObjectType" /> 參數是否包含非 null 值。</param>
      <param name="objectType">要套用移除之稽核控制規則的物件類別識別。</param>
      <param name="inheritedObjectType">可繼承移除之稽核規則的子物件類別識別。</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAudit(System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAuditRule)">
      <summary>從目前的 <see cref="T:System.Security.AccessControl.SystemAcl" /> 物件移除指定的稽核規則。</summary>
      <returns>如果這個方法成功移除指定的稽核規則，則為 true，否則為 false。</returns>
      <param name="sid">要移除稽核規則的 <see cref="T:System.Security.Principal.SecurityIdentifier" />。</param>
      <param name="rule">要移除稽核規則的 <see cref="T:System.Security.AccessControl.ObjectAuditRule" />。</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAuditSpecific(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>從目前的 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 物件移除指定的稽核規則。</summary>
      <param name="auditFlags">要移除的稽核規則型別。</param>
      <param name="sid">要移除稽核規則的 <see cref="T:System.Security.Principal.SecurityIdentifier" />。</param>
      <param name="accessMask">要移除之規則的存取遮罩。</param>
      <param name="inheritanceFlags">旗標，指定要移除之規則的繼承屬性。</param>
      <param name="propagationFlags">旗標，指定要移除之規則的繼承傳用屬性。</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAuditSpecific(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>從目前的 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 物件移除指定的稽核規則。指定物件型別或繼承物件型別時，將這個方法用於目錄物件存取控制清單 (ACL)。</summary>
      <param name="auditFlags">要移除的稽核規則型別。</param>
      <param name="sid">要移除稽核規則的 <see cref="T:System.Security.Principal.SecurityIdentifier" />。</param>
      <param name="accessMask">要移除之規則的存取遮罩。</param>
      <param name="inheritanceFlags">旗標，指定要移除之規則的繼承屬性。</param>
      <param name="propagationFlags">旗標，指定要移除之規則的繼承傳用屬性。</param>
      <param name="objectFlags">旗標，指定 <paramref name="objectType" /> 和 <paramref name="inheritedObjectType" /> 參數是否包含非 null 值。</param>
      <param name="objectType">要套用移除之稽核控制規則的物件類別識別。</param>
      <param name="inheritedObjectType">可繼承移除之稽核規則的子物件類別識別。</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAuditSpecific(System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAuditRule)">
      <summary>從目前的 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 物件移除指定的稽核規則。</summary>
      <param name="sid">要移除稽核規則的 <see cref="T:System.Security.Principal.SecurityIdentifier" />。</param>
      <param name="rule">
        <see cref="T:System.Security.AccessControl.ObjectAuditRule" />要移除的規則。</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.SetAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>為指定的 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 物件設定指定的稽核規則。</summary>
      <param name="auditFlags">要設定的稽核條件。</param>
      <param name="sid">要設定稽核規則的 <see cref="T:System.Security.Principal.SecurityIdentifier" />。</param>
      <param name="accessMask">新稽核規則的存取遮罩。</param>
      <param name="inheritanceFlags">旗標，指定新稽核規則的繼承屬性。</param>
      <param name="propagationFlags">旗標，指定新稽核規則的繼承傳用屬性。</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.SetAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>為指定的 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 物件設定指定的稽核規則。指定物件型別或繼承物件型別時，將這個方法用於目錄物件存取控制清單 (ACL)。</summary>
      <param name="auditFlags">要設定的稽核條件。</param>
      <param name="sid">要設定稽核規則的 <see cref="T:System.Security.Principal.SecurityIdentifier" />。</param>
      <param name="accessMask">新稽核規則的存取遮罩。</param>
      <param name="inheritanceFlags">旗標，指定新稽核規則的繼承屬性。</param>
      <param name="propagationFlags">旗標，指定新稽核規則的繼承傳用屬性。</param>
      <param name="objectFlags">旗標，指定 <paramref name="objectType" /> 和 <paramref name="inheritedObjectType" /> 參數是否包含非 null 值。</param>
      <param name="objectType">要套用新稽核規則之物件類別的識別。</param>
      <param name="inheritedObjectType">可繼承新稽核規則之子物件類別的識別。</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.SetAudit(System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAuditRule)">
      <summary>為指定的 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 物件設定指定的稽核規則。</summary>
      <param name="sid">要設定稽核規則的 <see cref="T:System.Security.Principal.SecurityIdentifier" />。</param>
      <param name="rule">要設定稽核規則的 <see cref="T:System.Security.AccessControl.ObjectAuditRule" />。</param>
    </member>
  </members>
</doc>