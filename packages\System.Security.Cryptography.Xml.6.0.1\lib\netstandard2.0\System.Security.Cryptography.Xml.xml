<?xml version="1.0"?>
<doc>
    <assembly>
        <name>System.Security.Cryptography.Xml</name>
    </assembly>
    <members>
        <member name="T:System.Security.Cryptography.Xml.CryptoSignedXmlRecursionException">
            <summary>
            This exception helps catch the signed XML recursion limit error.
            This is being caught in the SignedXml class while computing the
            hash. ComputeHash can throw different kind of exceptions.
            This unique exception helps catch the recursion limit issue.
            </summary>
        </member>
        <member name="M:System.Security.Cryptography.Xml.DSAKeyValue.GetXml">
            <summary>
            Create an XML representation.
            </summary>
            <remarks>
            Based upon https://www.w3.org/TR/xmldsig-core/#sec-DSAKeyValue.
            </remarks>
            <returns>
            An <see cref="T:System.Xml.XmlElement"/> containing the XML representation.
            </returns>
        </member>
        <member name="M:System.Security.Cryptography.Xml.DSAKeyValue.LoadXml(System.Xml.XmlElement)">
            <summary>
            Deserialize from the XML representation.
            </summary>
            <remarks>
            Based upon https://www.w3.org/TR/xmldsig-core/#sec-DSAKeyValue.
            </remarks>
            <param name="value">
            An <see cref="T:System.Xml.XmlElement"/> containing the XML representation. This cannot be null.
            </param>
            <exception cref="T:System.ArgumentNullException">
            <paramref name="value"/> cannot be null.
            </exception>
            <exception cref="T:System.Security.Cryptography.CryptographicException">
            The XML has the incorrect schema or the DSA parameters are invalid.
            </exception>
        </member>
        <member name="M:System.Security.Cryptography.Xml.EncryptedXml.IsOverXmlDsigRecursionLimit">
            <summary>
            This method validates the _xmlDsigSearchDepthCounter counter
            if the counter is over the limit defined by admin or developer.
            </summary>
            <returns>returns true if the limit has reached otherwise false</returns>
        </member>
        <member name="P:System.Security.Cryptography.Xml.EncryptedXml.XmlDSigSearchDepth">
            <summary>
            Gets / Sets the max limit for recursive search of encryption key in signed XML
            </summary>
        </member>
        <member name="M:System.Security.Cryptography.Xml.EncryptionPropertyCollection.System#Collections#IList#Add(System.Object)">
            <internalonly/>
        </member>
        <member name="M:System.Security.Cryptography.Xml.EncryptionPropertyCollection.System#Collections#IList#Contains(System.Object)">
            <internalonly/>
        </member>
        <member name="M:System.Security.Cryptography.Xml.EncryptionPropertyCollection.System#Collections#IList#IndexOf(System.Object)">
            <internalonly/>
        </member>
        <member name="M:System.Security.Cryptography.Xml.EncryptionPropertyCollection.System#Collections#IList#Insert(System.Int32,System.Object)">
            <internalonly/>
        </member>
        <member name="M:System.Security.Cryptography.Xml.EncryptionPropertyCollection.System#Collections#IList#Remove(System.Object)">
            <internalonly/>
        </member>
        <member name="P:System.Security.Cryptography.Xml.EncryptionPropertyCollection.System#Collections#IList#Item(System.Int32)">
            <internalonly/>
        </member>
        <member name="P:System.Security.Cryptography.Xml.ReferenceList.System#Collections#IList#Item(System.Int32)">
            <internalonly/>
        </member>
        <member name="M:System.Security.Cryptography.Xml.RSAKeyValue.GetXml">
            <summary>
            Create an XML representation.
            </summary>
            <remarks>
            Based upon https://www.w3.org/TR/xmldsig-core/#sec-RSAKeyValue.
            </remarks>
            <returns>
            An <see cref="T:System.Xml.XmlElement"/> containing the XML representation.
            </returns>
        </member>
        <member name="M:System.Security.Cryptography.Xml.RSAKeyValue.LoadXml(System.Xml.XmlElement)">
            <summary>
            Deserialize from the XML representation.
            </summary>
            <remarks>
            Based upon https://www.w3.org/TR/xmldsig-core/#sec-RSAKeyValue.
            </remarks>
            <param name="value">
            An <see cref="T:System.Xml.XmlElement"/> containing the XML representation. This cannot be null.
            </param>
            <exception cref="T:System.ArgumentNullException">
            <paramref name="value"/> cannot be null.
            </exception>
            <exception cref="T:System.Security.Cryptography.CryptographicException">
            The XML has the incorrect schema or the RSA parameters are invalid.
            </exception>
        </member>
        <member name="P:System.Security.Cryptography.Xml.SignedXml.SigningKeyName">
            <internalonly/>
        </member>
        <member name="T:System.Security.Cryptography.Xml.SignedXmlDebugLog">
            <summary>
                Trace support for debugging issues signing and verifying XML signatures.
            </summary>
        </member>
        <member name="T:System.Security.Cryptography.Xml.SignedXmlDebugLog.SignedXmlDebugEvent">
            <summary>
                Types of events that are logged to the debug log
            </summary>
        </member>
        <member name="F:System.Security.Cryptography.Xml.SignedXmlDebugLog.SignedXmlDebugEvent.BeginCanonicalization">
            <summary>
                Canonicalization of input XML has begun
            </summary>
        </member>
        <member name="F:System.Security.Cryptography.Xml.SignedXmlDebugLog.SignedXmlDebugEvent.BeginCheckSignatureFormat">
            <summary>
                Verification of the signature format itself is beginning
            </summary>
        </member>
        <member name="F:System.Security.Cryptography.Xml.SignedXmlDebugLog.SignedXmlDebugEvent.BeginCheckSignedInfo">
            <summary>
                Verification of a signed info is beginning
            </summary>
        </member>
        <member name="F:System.Security.Cryptography.Xml.SignedXmlDebugLog.SignedXmlDebugEvent.BeginSignatureComputation">
            <summary>
                Signing is beginning
            </summary>
        </member>
        <member name="F:System.Security.Cryptography.Xml.SignedXmlDebugLog.SignedXmlDebugEvent.BeginSignatureVerification">
            <summary>
                Signature verification is beginning
            </summary>
        </member>
        <member name="F:System.Security.Cryptography.Xml.SignedXmlDebugLog.SignedXmlDebugEvent.CanonicalizedData">
            <summary>
                Input data has been transformed to its canonicalized form
            </summary>
        </member>
        <member name="F:System.Security.Cryptography.Xml.SignedXmlDebugLog.SignedXmlDebugEvent.FormatValidationResult">
            <summary>
                The result of signature format validation
            </summary>
        </member>
        <member name="F:System.Security.Cryptography.Xml.SignedXmlDebugLog.SignedXmlDebugEvent.NamespacePropagation">
            <summary>
                Namespaces are being propigated into the signature
            </summary>
        </member>
        <member name="F:System.Security.Cryptography.Xml.SignedXmlDebugLog.SignedXmlDebugEvent.ReferenceData">
            <summary>
                Output from a Reference
            </summary>
        </member>
        <member name="F:System.Security.Cryptography.Xml.SignedXmlDebugLog.SignedXmlDebugEvent.SignatureVerificationResult">
            <summary>
                The result of a signature verification
            </summary>
        </member>
        <member name="F:System.Security.Cryptography.Xml.SignedXmlDebugLog.SignedXmlDebugEvent.Signing">
            <summary>
                Calculating the final signature
            </summary>
        </member>
        <member name="F:System.Security.Cryptography.Xml.SignedXmlDebugLog.SignedXmlDebugEvent.SigningReference">
            <summary>
                A reference is being hashed
            </summary>
        </member>
        <member name="F:System.Security.Cryptography.Xml.SignedXmlDebugLog.SignedXmlDebugEvent.VerificationFailure">
            <summary>
                A signature has failed to verify
            </summary>
        </member>
        <member name="F:System.Security.Cryptography.Xml.SignedXmlDebugLog.SignedXmlDebugEvent.VerifyReference">
            <summary>
                Verify that a reference has the correct hash value
            </summary>
        </member>
        <member name="F:System.Security.Cryptography.Xml.SignedXmlDebugLog.SignedXmlDebugEvent.VerifySignedInfo">
            <summary>
                Verification is processing the SignedInfo section of the signature
            </summary>
        </member>
        <member name="F:System.Security.Cryptography.Xml.SignedXmlDebugLog.SignedXmlDebugEvent.X509Verification">
            <summary>
                Verification status on the x.509 certificate in use
            </summary>
        </member>
        <member name="F:System.Security.Cryptography.Xml.SignedXmlDebugLog.SignedXmlDebugEvent.UnsafeCanonicalizationMethod">
            <summary>
                The signature is being rejected by the signature format verifier due to having
                a canonicalization algorithm which is not on the known valid list.
            </summary>
        </member>
        <member name="F:System.Security.Cryptography.Xml.SignedXmlDebugLog.SignedXmlDebugEvent.UnsafeTransformMethod">
            <summary>
                The signature is being rejected by the signature verifier due to having
                a transform algorithm which is not on the known valid list.
            </summary>
        </member>
        <member name="P:System.Security.Cryptography.Xml.SignedXmlDebugLog.InformationLoggingEnabled">
            <summary>
                Check to see if logging should be done in this process
            </summary>
        </member>
        <member name="P:System.Security.Cryptography.Xml.SignedXmlDebugLog.VerboseLoggingEnabled">
            <summary>
                Check to see if verbose log messages should be generated
            </summary>
        </member>
        <member name="M:System.Security.Cryptography.Xml.SignedXmlDebugLog.FormatBytes(System.Byte[])">
            <summary>
                Convert the byte array into a hex string
            </summary>
        </member>
        <member name="M:System.Security.Cryptography.Xml.SignedXmlDebugLog.GetKeyName(System.Object)">
            <summary>
                Map a key to a string describing the key
            </summary>
        </member>
        <member name="M:System.Security.Cryptography.Xml.SignedXmlDebugLog.GetObjectId(System.Object)">
            <summary>
                Map an object to a string describing the object
            </summary>
        </member>
        <member name="M:System.Security.Cryptography.Xml.SignedXmlDebugLog.GetOidName(System.Security.Cryptography.Oid)">
            <summary>
                Map an OID to the friendliest name possible
            </summary>
        </member>
        <member name="M:System.Security.Cryptography.Xml.SignedXmlDebugLog.LogBeginCanonicalization(System.Security.Cryptography.Xml.SignedXml,System.Security.Cryptography.Xml.Transform)">
            <summary>
                Log that canonicalization has begun on input data
            </summary>
            <param name="signedXml">SignedXml object doing the signing or verification</param>
            <param name="canonicalizationTransform">transform canonicalizing the input</param>
        </member>
        <member name="M:System.Security.Cryptography.Xml.SignedXmlDebugLog.LogBeginCheckSignatureFormat(System.Security.Cryptography.Xml.SignedXml,System.Func{System.Security.Cryptography.Xml.SignedXml,System.Boolean})">
            <summary>
                Log that we're going to be validating the signature format itself
            </summary>
            <param name="signedXml">SignedXml object doing the verification</param>
            <param name="formatValidator">Callback delegate which is being used for format verification</param>
        </member>
        <member name="M:System.Security.Cryptography.Xml.SignedXmlDebugLog.LogBeginCheckSignedInfo(System.Security.Cryptography.Xml.SignedXml,System.Security.Cryptography.Xml.SignedInfo)">
            <summary>
                Log that checking SignedInfo is beginning
            </summary>
            <param name="signedXml">SignedXml object doing the verification</param>
            <param name="signedInfo">SignedInfo object being verified</param>
        </member>
        <member name="M:System.Security.Cryptography.Xml.SignedXmlDebugLog.LogBeginSignatureComputation(System.Security.Cryptography.Xml.SignedXml,System.Xml.XmlElement)">
            <summary>
                Log that signature computation is beginning
            </summary>
            <param name="signedXml">SignedXml object doing the signing</param>
            <param name="context">Context of the signature</param>
        </member>
        <member name="M:System.Security.Cryptography.Xml.SignedXmlDebugLog.LogBeginSignatureVerification(System.Security.Cryptography.Xml.SignedXml,System.Xml.XmlElement)">
            <summary>
                Log that signature verification is beginning
            </summary>
            <param name="signedXml">SignedXml object doing the verification</param>
            <param name="context">Context of the verification</param>
        </member>
        <member name="M:System.Security.Cryptography.Xml.SignedXmlDebugLog.LogCanonicalizedOutput(System.Security.Cryptography.Xml.SignedXml,System.Security.Cryptography.Xml.Transform)">
            <summary>
                Log the canonicalized data
            </summary>
            <param name="signedXml">SignedXml object doing the signing or verification</param>
            <param name="canonicalizationTransform">transform canonicalizing the input</param>
        </member>
        <member name="M:System.Security.Cryptography.Xml.SignedXmlDebugLog.LogFormatValidationResult(System.Security.Cryptography.Xml.SignedXml,System.Boolean)">
            <summary>
                Log that the signature format callback has rejected the signature
            </summary>
            <param name="signedXml">SignedXml object doing the signature verification</param>
            <param name="result">result of the signature format verification</param>
        </member>
        <member name="M:System.Security.Cryptography.Xml.SignedXmlDebugLog.LogUnsafeCanonicalizationMethod(System.Security.Cryptography.Xml.SignedXml,System.String,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
                Log that a signature is being rejected as having an invalid format due to its canonicalization
                algorithm not being on the valid list.
            </summary>
            <param name="signedXml">SignedXml object doing the signature verification</param>
            <param name="algorithm">Canonicalization algorithm</param>
            <param name="validAlgorithms">List of valid canonicalization algorithms</param>
        </member>
        <member name="M:System.Security.Cryptography.Xml.SignedXmlDebugLog.LogUnsafeTransformMethod(System.Security.Cryptography.Xml.SignedXml,System.String,System.Collections.Generic.IEnumerable{System.String},System.Collections.Generic.IEnumerable{System.String})">
            <summary>
                Log that a signature is being rejected as having an invalid signature due to a transform
                algorithm not being on the valid list.
            </summary>
            <param name="signedXml">SignedXml object doing the signature verification</param>
            <param name="algorithm">Transform algorithm that was not allowed</param>
            <param name="validC14nAlgorithms">The valid C14N algorithms</param>
            <param name="validTransformAlgorithms">The valid C14N algorithms</param>
        </member>
        <member name="M:System.Security.Cryptography.Xml.SignedXmlDebugLog.LogNamespacePropagation(System.Security.Cryptography.Xml.SignedXml,System.Xml.XmlNodeList)">
            <summary>
                Log namespaces which are being propagated into the signature
            </summary>
            <param name="signedXml">SignedXml doing the signing or verification</param>
            <param name="namespaces">namespaces being propagated</param>
        </member>
        <member name="M:System.Security.Cryptography.Xml.SignedXmlDebugLog.LogReferenceData(System.Security.Cryptography.Xml.Reference,System.IO.Stream)">
            <summary>
                Log the output of a reference
            </summary>
            <param name="reference">The reference being processed</param>
            <param name="data">Stream containing the output of the reference</param>
            <returns>Stream containing the output of the reference</returns>
        </member>
        <member name="M:System.Security.Cryptography.Xml.SignedXmlDebugLog.LogSigning(System.Security.Cryptography.Xml.SignedXml,System.Object,System.Security.Cryptography.SignatureDescription,System.Security.Cryptography.HashAlgorithm,System.Security.Cryptography.AsymmetricSignatureFormatter)">
            <summary>
                Log the computation of a signature value when signing with an asymmetric algorithm
            </summary>
            <param name="signedXml">SignedXml object calculating the signature</param>
            <param name="key">key used for signing</param>
            <param name="signatureDescription">signature description being used to create the signature</param>
            <param name="hash">hash algorithm used to digest the output</param>
            <param name="asymmetricSignatureFormatter">signature formatter used to do the signing</param>
        </member>
        <member name="M:System.Security.Cryptography.Xml.SignedXmlDebugLog.LogSigning(System.Security.Cryptography.Xml.SignedXml,System.Security.Cryptography.KeyedHashAlgorithm)">
            <summary>
                Log the computation of a signature value when signing with a keyed hash algorithm
            </summary>
            <param name="signedXml">SignedXml object calculating the signature</param>
            <param name="key">key the signature is created with</param>
        </member>
        <member name="M:System.Security.Cryptography.Xml.SignedXmlDebugLog.LogSigningReference(System.Security.Cryptography.Xml.SignedXml,System.Security.Cryptography.Xml.Reference)">
            <summary>
                Log the calculation of a hash value of a reference
            </summary>
            <param name="signedXml">SignedXml object driving the signature</param>
            <param name="reference">Reference being hashed</param>
        </member>
        <member name="M:System.Security.Cryptography.Xml.SignedXmlDebugLog.LogVerificationFailure(System.Security.Cryptography.Xml.SignedXml,System.String)">
            <summary>
                Log the specific point where a signature is determined to not be verifiable
            </summary>
            <param name="signedXml">SignedXml object doing the verification</param>
            <param name="failureLocation">location that the signature was determined to be invalid</param>
        </member>
        <member name="M:System.Security.Cryptography.Xml.SignedXmlDebugLog.LogVerificationResult(System.Security.Cryptography.Xml.SignedXml,System.Object,System.Boolean)">
            <summary>
                Log the success or failure of a signature verification operation
            </summary>
            <param name="signedXml">SignedXml object doing the verification</param>
            <param name="key">public key used to verify the signature</param>
            <param name="verified">true if the signature verified, false otherwise</param>
        </member>
        <member name="M:System.Security.Cryptography.Xml.SignedXmlDebugLog.LogVerifyKeyUsage(System.Security.Cryptography.Xml.SignedXml,System.Security.Cryptography.X509Certificates.X509Certificate,System.Security.Cryptography.X509Certificates.X509KeyUsageExtension)">
            <summary>
                Log the check for appropriate X509 key usage
            </summary>
            <param name="signedXml">SignedXml doing the signature verification</param>
            <param name="certificate">certificate having its key usages checked</param>
            <param name="keyUsages">key usages being examined</param>
        </member>
        <member name="M:System.Security.Cryptography.Xml.SignedXmlDebugLog.LogVerifyReference(System.Security.Cryptography.Xml.SignedXml,System.Security.Cryptography.Xml.Reference)">
            <summary>
                Log that we are verifying a reference
            </summary>
            <param name="signedXml">SignedXMl object doing the verification</param>
            <param name="reference">reference being verified</param>
        </member>
        <member name="M:System.Security.Cryptography.Xml.SignedXmlDebugLog.LogVerifyReferenceHash(System.Security.Cryptography.Xml.SignedXml,System.Security.Cryptography.Xml.Reference,System.Byte[],System.Byte[])">
            <summary>
                Log the hash comparison when verifying a reference
            </summary>
            <param name="signedXml">SignedXml object verifying the signature</param>
            <param name="reference">reference being verified</param>
            <param name="actualHash">actual hash value of the reference</param>
            <param name="expectedHash">hash value the signature expected the reference to have</param>
        </member>
        <member name="M:System.Security.Cryptography.Xml.SignedXmlDebugLog.LogVerifySignedInfo(System.Security.Cryptography.Xml.SignedXml,System.Security.Cryptography.AsymmetricAlgorithm,System.Security.Cryptography.SignatureDescription,System.Security.Cryptography.HashAlgorithm,System.Security.Cryptography.AsymmetricSignatureDeformatter,System.Byte[],System.Byte[])">
            <summary>
                Log the verification parameters when verifying the SignedInfo section of a signature using an
                asymmetric key
            </summary>
            <param name="signedXml">SignedXml object doing the verification</param>
            <param name="key">key being used to verify the signed info</param>
            <param name="signatureDescription">type of signature description class used</param>
            <param name="hashAlgorithm">type of hash algorithm used</param>
            <param name="asymmetricSignatureDeformatter">type of signature deformatter used</param>
            <param name="actualHashValue">hash value of the signed info</param>
            <param name="signatureValue">raw signature value</param>
        </member>
        <member name="M:System.Security.Cryptography.Xml.SignedXmlDebugLog.LogVerifySignedInfo(System.Security.Cryptography.Xml.SignedXml,System.Security.Cryptography.KeyedHashAlgorithm,System.Byte[],System.Byte[])">
            <summary>
                Log the verification parameters when verifying the SignedInfo section of a signature using a
                keyed hash algorithm
            </summary>
            <param name="signedXml">SignedXml object doing the verification</param>
            <param name="mac">hash algorithm doing the verification</param>
            <param name="actualHashValue">hash value of the signed info</param>
            <param name="signatureValue">raw signature value</param>
        </member>
        <member name="M:System.Security.Cryptography.Xml.SignedXmlDebugLog.LogVerifyX509Chain(System.Security.Cryptography.Xml.SignedXml,System.Security.Cryptography.X509Certificates.X509Chain,System.Security.Cryptography.X509Certificates.X509Certificate)">
            <summary>
                Log that an X509 chain is being built for a certificate
            </summary>
            <param name="signedXml">SignedXml object building the chain</param>
            <param name="chain">chain built for the certificate</param>
            <param name="certificate">certificate having the chain built for it</param>
        </member>
        <member name="M:System.Security.Cryptography.Xml.SignedXmlDebugLog.LogSignedXmlRecursionLimit(System.Security.Cryptography.Xml.SignedXml,System.Security.Cryptography.Xml.Reference)">
            <summary>
            Write information when user hits the Signed XML recursion depth limit issue.
            This is helpful in debugging this kind of issues.
            </summary>
            <param name="signedXml">SignedXml object verifying the signature</param>
            <param name="reference">reference being verified</param>
        </member>
        <member name="M:System.Security.Cryptography.Xml.SignedXmlDebugLog.WriteLine(System.Object,System.Diagnostics.TraceEventType,System.Security.Cryptography.Xml.SignedXmlDebugLog.SignedXmlDebugEvent,System.String)">
            <summary>
                Write data to the log
            </summary>
            <param name="source">object doing the trace</param>
            <param name="eventType">severity of the debug event</param>
            <param name="data">data being written</param>
            <param name="eventId">type of event being traced</param>
        </member>
        <member name="M:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform.#ctor(System.Boolean)">
            <internalonly/>
        </member>
        <member name="P:System.HexConverter.CharToHexLookup">
            <summary>Map from an ASCII char to its hex value, e.g. arr['b'] == 11. 0xFF means it's not a hex digit.</summary>
        </member>
        <member name="P:System.SR.ArgumentOutOfRange_Index">
            <summary>Index was out of range. Must be non-negative and less than the size of the collection.</summary>
        </member>
        <member name="P:System.SR.Arg_EmptyOrNullString">
            <summary>String cannot be empty or null.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Partial_Chain">
            <summary>A certificate chain could not be built to a trusted root authority.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_BadWrappedKeySize">
            <summary>Bad wrapped key size.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_CipherValueElementRequired">
            <summary>A Cipher Data element should have either a CipherValue or a CipherReference element.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_CreateHashAlgorithmFailed">
            <summary>Could not create hash algorithm object.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_CreateTransformFailed">
            <summary>Could not create the XML transformation identified by the URI {0}.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_CreatedKeyFailed">
            <summary>Failed to create signing key.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_DigestMethodRequired">
            <summary>A DigestMethod must be specified on a Reference prior to generating XML.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_DigestValueRequired">
            <summary>A Reference must contain a DigestValue.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_EnvelopedSignatureRequiresContext">
            <summary>An XmlDocument context is required for enveloped transforms.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_InvalidElement">
            <summary>Malformed element {0}.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_InvalidEncryptionProperty">
            <summary>Malformed encryption property element.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_InvalidKeySize">
            <summary>The key size should be a non negative integer.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_InvalidReference">
            <summary>Malformed reference element.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_InvalidSignatureLength">
            <summary>The length of the signature with a MAC should be less than the hash output length.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_InvalidSignatureLength2">
            <summary>The length in bits of the signature with a MAC should be a multiple of 8.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_InvalidX509IssuerSerialNumber">
            <summary>X509 issuer serial number is invalid.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_KeyInfoRequired">
            <summary>A KeyInfo element is required to check the signature.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_KW_BadKeySize">
            <summary>The length of the encrypted data in Key Wrap is either 32, 40 or 48 bytes.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_LoadKeyFailed">
            <summary>Signing key is not loaded.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_MissingAlgorithm">
            <summary>Symmetric algorithm is not specified.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_MissingCipherData">
            <summary>Cipher data is not specified.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_MissingDecryptionKey">
            <summary>Unable to retrieve the decryption key.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_MissingEncryptionKey">
            <summary>Unable to retrieve the encryption key.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_NotSupportedCryptographicTransform">
            <summary>The specified cryptographic transform is not supported.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_ReferenceElementRequired">
            <summary>At least one Reference element is required.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_ReferenceTypeRequired">
            <summary>The Reference type must be set in an EncryptedReference object.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_SelfReferenceRequiresContext">
            <summary>An XmlDocument context is required to resolve the Reference Uri {0}.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_SignatureDescriptionNotCreated">
            <summary>SignatureDescription could not be created for the signature algorithm supplied.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_SignatureMethodKeyMismatch">
            <summary>The key does not fit the SignatureMethod.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_SignatureMethodRequired">
            <summary>A signature method is required.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_SignatureValueRequired">
            <summary>Signature requires a SignatureValue.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_SignedInfoRequired">
            <summary>Signature requires a SignedInfo.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_TransformIncorrectInputType">
            <summary>The input type was invalid for this transform.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_IncorrectObjectType">
            <summary>Type of input object is invalid.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_UnknownTransform">
            <summary>Unknown transform has been encountered.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_UriNotResolved">
            <summary>Unable to resolve Uri {0}.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_UriNotSupported">
            <summary>The specified Uri is not supported.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_UriRequired">
            <summary>A Uri attribute is required for a CipherReference element.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_XrmlMissingContext">
            <summary>Null Context property encountered.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_XrmlMissingIRelDecryptor">
            <summary>IRelDecryptor is required.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_XrmlMissingIssuer">
            <summary>Issuer node is required.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_XrmlMissingLicence">
            <summary>License node is required.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_XrmlUnableToDecryptGrant">
            <summary>Unable to decrypt grant content.</summary>
        </member>
        <member name="P:System.SR.NotSupported_KeyAlgorithm">
            <summary>The certificate key algorithm is not supported.</summary>
        </member>
        <member name="P:System.SR.Log_ActualHashValue">
            <summary>Actual hash value: {0}</summary>
        </member>
        <member name="P:System.SR.Log_BeginCanonicalization">
            <summary>Beginning canonicalization using "{0}" ({1}).</summary>
        </member>
        <member name="P:System.SR.Log_BeginSignatureComputation">
            <summary>Beginning signature computation.</summary>
        </member>
        <member name="P:System.SR.Log_BeginSignatureVerification">
            <summary>Beginning signature verification.</summary>
        </member>
        <member name="P:System.SR.Log_BuildX509Chain">
            <summary>Building and verifying the X509 chain for certificate {0}.</summary>
        </member>
        <member name="P:System.SR.Log_CanonicalizationSettings">
            <summary>Canonicalization transform is using resolver {0} and base URI "{1}".</summary>
        </member>
        <member name="P:System.SR.Log_CanonicalizedOutput">
            <summary>Output of canonicalization transform: {0}</summary>
        </member>
        <member name="P:System.SR.Log_CertificateChain">
            <summary>Certificate chain:</summary>
        </member>
        <member name="P:System.SR.Log_CheckSignatureFormat">
            <summary>Checking signature format using format validator "[{0}] {1}.{2}".</summary>
        </member>
        <member name="P:System.SR.Log_CheckSignedInfo">
            <summary>Checking signature on SignedInfo with id "{0}".</summary>
        </member>
        <member name="P:System.SR.Log_FormatValidationSuccessful">
            <summary>Signature format validation was successful.</summary>
        </member>
        <member name="P:System.SR.Log_FormatValidationNotSuccessful">
            <summary>Signature format validation failed.</summary>
        </member>
        <member name="P:System.SR.Log_KeyUsages">
            <summary>Found key usages "{0}" in extension {1} on certificate {2}.</summary>
        </member>
        <member name="P:System.SR.Log_NoNamespacesPropagated">
            <summary>No namespaces are being propagated.</summary>
        </member>
        <member name="P:System.SR.Log_PropagatingNamespace">
            <summary>Propagating namespace {0}="{1}".</summary>
        </member>
        <member name="P:System.SR.Log_RawSignatureValue">
            <summary>Raw signature: {0}</summary>
        </member>
        <member name="P:System.SR.Log_ReferenceHash">
            <summary>Reference {0} hashed with "{1}" ({2}) has hash value {3}, expected hash value {4}.</summary>
        </member>
        <member name="P:System.SR.Log_RevocationMode">
            <summary>Revocation mode for chain building: {0}.</summary>
        </member>
        <member name="P:System.SR.Log_RevocationFlag">
            <summary>Revocation flag for chain building: {0}.</summary>
        </member>
        <member name="P:System.SR.Log_SigningAsymmetric">
            <summary>Calculating signature with key {0} using signature description {1}, hash algorithm {2}, and asymmetric signature formatter {3}.</summary>
        </member>
        <member name="P:System.SR.Log_SigningHmac">
            <summary>Calculating signature using keyed hash algorithm {0}.</summary>
        </member>
        <member name="P:System.SR.Log_SigningReference">
            <summary>Hashing reference {0}, Uri "{1}", Id "{2}", Type "{3}" with hash algorithm "{4}" ({5}).</summary>
        </member>
        <member name="P:System.SR.Log_TransformedReferenceContents">
            <summary>Transformed reference contents: {0}</summary>
        </member>
        <member name="P:System.SR.Log_UnsafeCanonicalizationMethod">
            <summary>Canonicalization method "{0}" is not on the safe list. Safe canonicalization methods are: {1}.</summary>
        </member>
        <member name="P:System.SR.Log_UrlTimeout">
            <summary>URL retrieval timeout for chain building: {0}.</summary>
        </member>
        <member name="P:System.SR.Log_VerificationFailed">
            <summary>Verification failed checking {0}.</summary>
        </member>
        <member name="P:System.SR.Log_VerificationFailed_References">
            <summary>references</summary>
        </member>
        <member name="P:System.SR.Log_VerificationFailed_SignedInfo">
            <summary>SignedInfo</summary>
        </member>
        <member name="P:System.SR.Log_VerificationFailed_X509Chain">
            <summary>X509 chain verification</summary>
        </member>
        <member name="P:System.SR.Log_VerificationFailed_X509KeyUsage">
            <summary>X509 key usage verification</summary>
        </member>
        <member name="P:System.SR.Log_VerificationFlag">
            <summary>Verification flags for chain building: {0}.</summary>
        </member>
        <member name="P:System.SR.Log_VerificationTime">
            <summary>Verification time for chain building: {0}.</summary>
        </member>
        <member name="P:System.SR.Log_VerificationWithKeySuccessful">
            <summary>Verification with key {0} was successful.</summary>
        </member>
        <member name="P:System.SR.Log_VerificationWithKeyNotSuccessful">
            <summary>Verification with key {0} was not successful.</summary>
        </member>
        <member name="P:System.SR.Log_VerifyReference">
            <summary>Processing reference {0}, Uri "{1}", Id "{2}", Type "{3}".</summary>
        </member>
        <member name="P:System.SR.Log_VerifySignedInfoAsymmetric">
            <summary>Verifying SignedInfo using key {0}, signature description {1}, hash algorithm {2}, and asymmetric signature deformatter {3}.</summary>
        </member>
        <member name="P:System.SR.Log_VerifySignedInfoHmac">
            <summary>Verifying SignedInfo using keyed hash algorithm {0}.</summary>
        </member>
        <member name="P:System.SR.Log_X509ChainError">
            <summary>Error building X509 chain: {0}: {1}.</summary>
        </member>
        <member name="P:System.SR.Log_XmlContext">
            <summary>Using context: {0}</summary>
        </member>
        <member name="P:System.SR.Log_SignedXmlRecursionLimit">
            <summary>Signed xml recursion limit hit while trying to decrypt the key. Reference {0} hashed with "{1}" and ({2}).</summary>
        </member>
        <member name="P:System.SR.Log_UnsafeTransformMethod">
            <summary>Transform method "{0}" is not on the safe list. Safe transform methods are: {1}.</summary>
        </member>
        <member name="P:System.SR.ElementCombinationMissing">
            <summary>{0} and {1} can only occur in combination</summary>
        </member>
        <member name="P:System.SR.ElementMissing">
            <summary>{0} is missing</summary>
        </member>
        <member name="P:System.SR.MustContainChildElement">
            <summary>{0} must contain child element {1}</summary>
        </member>
        <member name="P:System.SR.WrongRootElement">
            <summary>Root element must be {0} element in namespace {1}</summary>
        </member>
        <member name="T:System.Runtime.Versioning.OSPlatformAttribute">
            <summary>
            Base type for all platform-specific API attributes.
            </summary>
        </member>
        <member name="T:System.Runtime.Versioning.TargetPlatformAttribute">
            <summary>
            Records the platform that the project targeted.
            </summary>
        </member>
        <member name="T:System.Runtime.Versioning.SupportedOSPlatformAttribute">
             <summary>
             Records the operating system (and minimum version) that supports an API. Multiple attributes can be
             applied to indicate support on multiple operating systems.
             </summary>
             <remarks>
             Callers can apply a <see cref="T:System.Runtime.Versioning.SupportedOSPlatformAttribute" />
             or use guards to prevent calls to APIs on unsupported operating systems.
            
             A given platform should only be specified once.
             </remarks>
        </member>
        <member name="T:System.Runtime.Versioning.UnsupportedOSPlatformAttribute">
            <summary>
            Marks APIs that were removed in a given operating system version.
            </summary>
            <remarks>
            Primarily used by OS bindings to indicate APIs that are only available in
            earlier versions.
            </remarks>
        </member>
        <member name="T:System.Runtime.Versioning.SupportedOSPlatformGuardAttribute">
             <summary>
             Annotates a custom guard field, property or method with a supported platform name and optional version.
             Multiple attributes can be applied to indicate guard for multiple supported platforms.
             </summary>
             <remarks>
             Callers can apply a <see cref="T:System.Runtime.Versioning.SupportedOSPlatformGuardAttribute" /> to a field, property or method
             and use that field, property or method in a conditional or assert statements in order to safely call platform specific APIs.
            
             The type of the field or property should be boolean, the method return type should be boolean in order to be used as platform guard.
             </remarks>
        </member>
        <member name="T:System.Runtime.Versioning.UnsupportedOSPlatformGuardAttribute">
             <summary>
             Annotates the custom guard field, property or method with an unsupported platform name and optional version.
             Multiple attributes can be applied to indicate guard for multiple unsupported platforms.
             </summary>
             <remarks>
             Callers can apply a <see cref="T:System.Runtime.Versioning.UnsupportedOSPlatformGuardAttribute" /> to a field, property or method
             and use that  field, property or method in a conditional or assert statements as a guard to safely call APIs unsupported on those platforms.
            
             The type of the field or property should be boolean, the method return type should be boolean in order to be used as platform guard.
             </remarks>
        </member>
    </members>
</doc>
