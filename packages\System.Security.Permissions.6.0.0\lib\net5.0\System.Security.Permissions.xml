<?xml version="1.0"?>
<doc>
    <assembly>
        <name>System.Security.Permissions</name>
    </assembly>
    <members>
        <member name="P:System.SR.Argument_InvalidPermissionState">
            <summary>Invalid permission state.</summary>
        </member>
        <member name="P:System.SR.Argument_NotAPermissionElement">
            <summary>'elem' was not a permission element.</summary>
        </member>
        <member name="P:System.SR.Argument_InvalidXMLBadVersion">
            <summary>Invalid Xml - can only parse elements of version one.</summary>
        </member>
        <member name="P:System.SR.Argument_WrongType">
            <summary>Operation on type '{0}' attempted with target of incorrect type.</summary>
        </member>
        <member name="P:System.SR.HostProtection_ProtectedResources">
            <summary>The protected resources (only available with full trust) were:</summary>
        </member>
        <member name="P:System.SR.HostProtection_DemandedResources">
            <summary>The demanded resources were:</summary>
        </member>
        <member name="P:System.SR.Security_PrincipalPermission">
            <summary>Request for principal permission failed.</summary>
        </member>
        <member name="P:System.SR.PlatformNotSupported_CAS">
            <summary>Code Access Security is not supported on this platform.</summary>
        </member>
    </members>
</doc>
