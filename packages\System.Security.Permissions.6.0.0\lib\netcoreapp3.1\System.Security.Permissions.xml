<?xml version="1.0"?>
<doc>
    <assembly>
        <name>System.Security.Permissions</name>
    </assembly>
    <members>
        <member name="P:System.SR.Argument_InvalidPermissionState">
            <summary>Invalid permission state.</summary>
        </member>
        <member name="P:System.SR.Argument_NotAPermissionElement">
            <summary>'elem' was not a permission element.</summary>
        </member>
        <member name="P:System.SR.Argument_InvalidXMLBadVersion">
            <summary>Invalid Xml - can only parse elements of version one.</summary>
        </member>
        <member name="P:System.SR.Argument_WrongType">
            <summary>Operation on type '{0}' attempted with target of incorrect type.</summary>
        </member>
        <member name="P:System.SR.HostProtection_ProtectedResources">
            <summary>The protected resources (only available with full trust) were:</summary>
        </member>
        <member name="P:System.SR.HostProtection_DemandedResources">
            <summary>The demanded resources were:</summary>
        </member>
        <member name="P:System.SR.Security_PrincipalPermission">
            <summary>Request for principal permission failed.</summary>
        </member>
        <member name="P:System.SR.PlatformNotSupported_CAS">
            <summary>Code Access Security is not supported on this platform.</summary>
        </member>
        <member name="T:System.Runtime.Versioning.OSPlatformAttribute">
            <summary>
            Base type for all platform-specific API attributes.
            </summary>
        </member>
        <member name="T:System.Runtime.Versioning.TargetPlatformAttribute">
            <summary>
            Records the platform that the project targeted.
            </summary>
        </member>
        <member name="T:System.Runtime.Versioning.SupportedOSPlatformAttribute">
             <summary>
             Records the operating system (and minimum version) that supports an API. Multiple attributes can be
             applied to indicate support on multiple operating systems.
             </summary>
             <remarks>
             Callers can apply a <see cref="T:System.Runtime.Versioning.SupportedOSPlatformAttribute" />
             or use guards to prevent calls to APIs on unsupported operating systems.
            
             A given platform should only be specified once.
             </remarks>
        </member>
        <member name="T:System.Runtime.Versioning.UnsupportedOSPlatformAttribute">
            <summary>
            Marks APIs that were removed in a given operating system version.
            </summary>
            <remarks>
            Primarily used by OS bindings to indicate APIs that are only available in
            earlier versions.
            </remarks>
        </member>
        <member name="T:System.Runtime.Versioning.SupportedOSPlatformGuardAttribute">
             <summary>
             Annotates a custom guard field, property or method with a supported platform name and optional version.
             Multiple attributes can be applied to indicate guard for multiple supported platforms.
             </summary>
             <remarks>
             Callers can apply a <see cref="T:System.Runtime.Versioning.SupportedOSPlatformGuardAttribute" /> to a field, property or method
             and use that field, property or method in a conditional or assert statements in order to safely call platform specific APIs.
            
             The type of the field or property should be boolean, the method return type should be boolean in order to be used as platform guard.
             </remarks>
        </member>
        <member name="T:System.Runtime.Versioning.UnsupportedOSPlatformGuardAttribute">
             <summary>
             Annotates the custom guard field, property or method with an unsupported platform name and optional version.
             Multiple attributes can be applied to indicate guard for multiple unsupported platforms.
             </summary>
             <remarks>
             Callers can apply a <see cref="T:System.Runtime.Versioning.UnsupportedOSPlatformGuardAttribute" /> to a field, property or method
             and use that  field, property or method in a conditional or assert statements as a guard to safely call APIs unsupported on those platforms.
            
             The type of the field or property should be boolean, the method return type should be boolean in order to be used as platform guard.
             </remarks>
        </member>
    </members>
</doc>
